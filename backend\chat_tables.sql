-- 聊天功能相关数据库表
-- MySQL 5.7 兼容版本

-- 1. 聊天会话表
CREATE TABLE IF NOT EXISTS `chat_sessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user1_id` int(11) NOT NULL COMMENT '用户1ID',
  `user2_id` int(11) NOT NULL COMMENT '用户2ID',
  `last_message_id` int(11) DEFAULT NULL COMMENT '最后一条消息ID',
  `last_message_time` datetime DEFAULT NULL COMMENT '最后消息时间',
  `user1_unread_count` int(11) DEFAULT 0 COMMENT '用户1未读消息数',
  `user2_unread_count` int(11) DEFAULT 0 COMMENT '用户2未读消息数',
  `status` varchar(20) DEFAULT 'active' COMMENT '会话状态：active, deleted',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_users` (`user1_id`, `user2_id`),
  KEY `idx_user1` (`user1_id`),
  KEY `idx_user2` (`user2_id`),
  KEY `idx_last_message_time` (`last_message_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='聊天会话表';

-- 2. 聊天消息表
CREATE TABLE IF NOT EXISTS `chat_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` int(11) NOT NULL COMMENT '会话ID',
  `sender_id` int(11) NOT NULL COMMENT '发送者ID',
  `receiver_id` int(11) NOT NULL COMMENT '接收者ID',
  `content` text NOT NULL COMMENT '消息内容',
  `message_type` varchar(20) DEFAULT 'text' COMMENT '消息类型：text, image, emoji, video_call',
  `extra_data` json DEFAULT NULL COMMENT '额外数据（图片URL、表情包ID等）',
  `is_read` tinyint(1) DEFAULT 0 COMMENT '是否已读',
  `read_at` datetime DEFAULT NULL COMMENT '阅读时间',
  `status` varchar(20) DEFAULT 'active' COMMENT '消息状态：active, deleted, recalled',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_sender_id` (`sender_id`),
  KEY `idx_receiver_id` (`receiver_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_is_read` (`is_read`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='聊天消息表';

-- 3. 表情包分类表
CREATE TABLE IF NOT EXISTS `emoji_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `icon` varchar(200) DEFAULT NULL COMMENT '分类图标',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `status` varchar(20) DEFAULT 'active' COMMENT '状态：active, inactive',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表情包分类表';

-- 4. 表情包表
CREATE TABLE IF NOT EXISTS `emojis` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `name` varchar(100) NOT NULL COMMENT '表情包名称',
  `image_url` varchar(500) NOT NULL COMMENT '图片URL',
  `keywords` varchar(200) DEFAULT NULL COMMENT '关键词，用于搜索',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `usage_count` int(11) DEFAULT 0 COMMENT '使用次数',
  `status` varchar(20) DEFAULT 'active' COMMENT '状态：active, inactive',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_usage_count` (`usage_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表情包表';

-- 5. 视频通话记录表
CREATE TABLE IF NOT EXISTS `video_calls` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `caller_id` int(11) NOT NULL COMMENT '发起者ID',
  `receiver_id` int(11) NOT NULL COMMENT '接收者ID',
  `call_type` varchar(20) DEFAULT 'video' COMMENT '通话类型：video, audio',
  `status` varchar(20) DEFAULT 'calling' COMMENT '通话状态：calling, accepted, rejected, ended, missed',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `duration` int(11) DEFAULT 0 COMMENT '通话时长（秒）',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_caller_id` (`caller_id`),
  KEY `idx_receiver_id` (`receiver_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='视频通话记录表';

-- 插入表情包分类数据
INSERT INTO `emoji_categories` (`name`, `icon`, `sort_order`) VALUES
('经典表情', '😀', 1),
('动物表情', '🐱', 2),
('手势表情', '👍', 3),
('爱心表情', '❤️', 4),
('食物表情', '🍎', 5),
('运动表情', '⚽', 6);

-- 插入表情包数据
INSERT INTO `emojis` (`category_id`, `name`, `image_url`, `keywords`, `sort_order`) VALUES
-- 经典表情
(1, '开心', '😀', '开心,高兴,笑', 1),
(1, '大笑', '😂', '大笑,哈哈,搞笑', 2),
(1, '眨眼', '😉', '眨眼,调皮', 3),
(1, '亲吻', '😘', '亲吻,么么哒', 4),
(1, '爱心眼', '😍', '爱心,喜欢,迷恋', 5),
(1, '酷', '😎', '酷,墨镜,帅', 6),
(1, '思考', '🤔', '思考,疑问', 7),
(1, '哭泣', '😭', '哭泣,伤心,难过', 8),
(1, '生气', '😠', '生气,愤怒', 9),
(1, '惊讶', '😱', '惊讶,震惊', 10),

-- 动物表情
(2, '猫咪', '🐱', '猫,猫咪,可爱', 1),
(2, '狗狗', '🐶', '狗,狗狗,汪', 2),
(2, '熊猫', '🐼', '熊猫,国宝', 3),
(2, '兔子', '🐰', '兔子,可爱', 4),
(2, '老虎', '🐯', '老虎,威猛', 5),
(2, '狮子', '🦁', '狮子,王者', 6),

-- 手势表情
(3, '点赞', '👍', '点赞,好,赞', 1),
(3, '差评', '👎', '差评,不好', 2),
(3, 'OK', '👌', 'OK,好的', 3),
(3, '胜利', '✌️', '胜利,耶', 4),
(3, '拳头', '👊', '拳头,加油', 5),
(3, '鼓掌', '👏', '鼓掌,棒', 6),

-- 爱心表情
(4, '红心', '❤️', '爱心,红心,爱', 1),
(4, '粉心', '💕', '粉心,爱,喜欢', 2),
(4, '蓝心', '💙', '蓝心,爱', 3),
(4, '绿心', '💚', '绿心,爱', 4),
(4, '黄心', '💛', '黄心,爱', 5),
(4, '紫心', '💜', '紫心,爱', 6),

-- 食物表情
(5, '苹果', '🍎', '苹果,水果', 1),
(5, '香蕉', '🍌', '香蕉,水果', 2),
(5, '蛋糕', '🎂', '蛋糕,生日', 3),
(5, '披萨', '🍕', '披萨,美食', 4),
(5, '汉堡', '🍔', '汉堡,美食', 5),
(5, '咖啡', '☕', '咖啡,饮品', 6),

-- 运动表情
(6, '足球', '⚽', '足球,运动', 1),
(6, '篮球', '🏀', '篮球,运动', 2),
(6, '网球', '🎾', '网球,运动', 3),
(6, '游泳', '🏊', '游泳,运动', 4),
(6, '跑步', '🏃', '跑步,运动', 5),
(6, '骑行', '🚴', '骑行,运动', 6);

<view class="container">
  <view class="custom-header">
    <view class="header-content">
      <view class="page-title">选择班级</view>
      <view class="subtitle">选择后将自动进入学习系统</view>
    </view>
    <view class="header-tools">
      <view class="tool-item">
        <view class="tool-icon dot-menu"></view>
      </view>
      <view class="tool-item">
        <view class="tool-icon circle"></view>
      </view>
    </view>
  </view>

  <view class="class-list">
    <view wx:if="{{loading}}" class="loading">
      <view class="loading-icon"></view>
      <text>加载班级列表中...</text>
    </view>
    
    <view wx:elif="{{error}}" class="error">
      <icon type="warn" size="40"></icon>
      <text>{{errorMsg}}</text>
      <button class="retry-btn" bindtap="loadClasses">重试</button>
    </view>
    
    <view wx:elif="{{classes.length === 0}}" class="empty">
      <icon type="info" size="40"></icon>
      <text>暂无可选班级，请联系管理员</text>
    </view>
    
    <view wx:else class="class-cards">
      <view 
        wx:for="{{classes}}" 
        wx:key="id" 
        class="class-card {{selectedClassId === item.id ? 'selected' : ''}}"
        bindtap="selectClass" 
        data-id="{{item.id}}"
        data-name="{{item.name}}">
        <view class="class-card-content">
          <view class="class-icon">
            <view class="icon-circle">{{item.name[0]}}</view>
          </view>
          <view class="class-info">
            <view class="class-name">{{item.name}}</view>
            <view class="class-desc">{{item.description || item.name + '专业班级'}}</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <view class="footer">
    <button 
      class="confirm-btn {{selectedClassId ? 'active' : 'disabled'}}" 
      disabled="{{!selectedClassId || submitting}}"
      bindtap="confirmSelection">
      确认选择
    </button>
  </view>
</view> 
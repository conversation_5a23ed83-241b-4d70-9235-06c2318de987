<!-- course.wxml -->
<view class="container">
  <!-- 顶部区域（状态栏+头部）-->
  <view class="header-area">
    <!-- 状态栏安全区域，与头部背景融合 -->
    <view class="status-bar-placeholder" style="height: {{statusBarHeight}}px; background: linear-gradient(135deg, {{courseColor || '#4e8df7'}}, {{courseColorDarker || '#3c78e0'}});"></view>
    
    <!-- 头部背景 -->
    <view class="header-bg" style="background: linear-gradient(135deg, {{courseColor || '#4e8df7'}}, {{courseColorDarker || '#3c78e0'}});"></view>
    
    <!-- 课程头部内容 -->
    <view class="course-header">
      <view class="header-content">
        <view class="back-btn" bindtap="navigateBack">
          <text class="back-icon">←</text>
        </view>
        <view class="course-title">{{courseName}}</view>
        <view class="placeholder"></view> <!-- 用于平衡布局 -->
      </view>
    </view>
    
    <!-- 统计卡片（浮动设计） -->
    <view class="stats-card">
      <view class="course-stats">
        <view class="stat-item">
          <text class="stat-value">{{singleCount}}</text>
          <text class="stat-label">单选题</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{multipleCount}}</text>
          <text class="stat-label">多选题</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{judgmentCount}}</text>
          <text class="stat-label">判断题</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{fillBlankCount}}</text>
          <text class="stat-label">填空题</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{singleCount + multipleCount + judgmentCount + fillBlankCount}}</text>
          <text class="stat-label">总题数</text>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 开始练习部分 -->
  <view class="card-section">
    <view class="section-header">
      <text class="section-title">开始练习</text>
      <!-- 顺序/随机切换按钮 -->
      <view class="toggle-button" bindtap="toggleQuestionOrder">
        <text class="toggle-icon">{{isRandomMode ? '🔀' : '↓↓'}}</text>
        <text class="toggle-text">{{isRandomMode ? '随机模式' : '顺序模式'}}</text>
      </view>
    </view>
    
    <!-- 背题模式卡片 -->
    <view class="question-card review-card" bindtap="startReviewMode" wx:if="{{singleCount > 0 || multipleCount > 0 || judgmentCount > 0 || fillBlankCount > 0}}">
      <view class="question-card-left">
        <view class="question-icon review-icon">
          <text class="icon-text">背</text>
        </view>
        <view class="question-info">
          <text class="question-title">背题模式</text>
          <text class="question-count">查看所有题目及答案</text>
        </view>
      </view>
      <view class="question-action">
        <text class="action-text">开始</text>
        <text class="arrow">→</text>
      </view>
    </view>
    
    <!-- 单选题卡片 -->
    <view class="question-card" bindtap="startSingleQuiz" wx:if="{{singleCount > 0}}">
      <view class="question-card-left">
        <view class="question-icon single-icon">
          <text class="icon-text">单</text>
        </view>
        <view class="question-info">
          <text class="question-title">单选题练习</text>
          <text class="question-count">{{singleCount}}道题目</text>
        </view>
      </view>
      <view class="question-action">
        <text class="action-text">开始</text>
        <text class="arrow">→</text>
      </view>
    </view>
    
    <!-- 多选题卡片 -->
    <view class="question-card multiple-card" bindtap="startMultipleQuiz" wx:if="{{multipleCount > 0}}">
      <view class="question-card-left">
        <view class="question-icon multiple-icon">
          <text class="icon-text">多</text>
        </view>
        <view class="question-info">
          <text class="question-title">多选题练习</text>
          <text class="question-count">{{multipleCount}}道题目</text>
        </view>
      </view>
      <view class="question-action">
        <text class="action-text">开始</text>
        <text class="arrow">→</text>
      </view>
    </view>
    
    <!-- 判断题卡片 -->
    <view class="question-card judgment-card" bindtap="startJudgmentQuiz" wx:if="{{judgmentCount > 0}}">
      <view class="question-card-left">
        <view class="question-icon judgment-icon">
          <text class="icon-text">判</text>
        </view>
        <view class="question-info">
          <text class="question-title">判断题练习</text>
          <text class="question-count">{{judgmentCount}}道题目</text>
        </view>
      </view>
      <view class="question-action">
        <text class="action-text">开始</text>
        <text class="arrow">→</text>
      </view>
    </view>
    
    <!-- 填空题卡片 -->
    <view class="question-card fillblank-card" bindtap="startFillBlankQuiz" wx:if="{{fillBlankCount > 0}}">
      <view class="question-card-left">
        <view class="question-icon fillblank-icon">
          <text class="icon-text">填</text>
        </view>
        <view class="question-info">
          <text class="question-title">填空题练习</text>
          <text class="question-count">{{fillBlankCount}}道题目</text>
        </view>
      </view>
      <view class="question-action">
        <text class="action-text">开始</text>
        <text class="arrow">→</text>
      </view>
    </view>
    
    <!-- 考试模式卡片 -->
    <view class="question-card exam-card" bindtap="startExamMode" wx:if="{{singleCount > 0 || multipleCount > 0 || judgmentCount > 0 || fillBlankCount > 0}}">
      <view class="question-card-left">
        <view class="question-icon exam-icon">
          <text class="icon-text">考</text>
        </view>
        <view class="question-info">
          <text class="question-title">考试模式</text>
          <text class="question-count">随机抽题</text>
        </view>
      </view>
      <view class="question-action">
        <text class="action-text">开始</text>
        <text class="arrow">→</text>
      </view>
    </view>
  </view>
  
  <!-- 学习工具 -->
  <view class="card-section">
    <view class="section-header">
      <text class="section-title">学习工具</text>
    </view>
    
    <view class="tools-grid">
      <!-- 错题本 -->
      <view class="tool-card" bindtap="goToWrongQuestions">
        <view class="tool-icon">❌</view>
        <text class="tool-title">错题本</text>
        <text class="tool-desc">记录错题</text>
      </view>
      
      <!-- 答题统计 -->
      <view class="tool-card" bindtap="goToStatistics">
        <view class="tool-icon">📊</view>
        <text class="tool-title">答题统计</text>
        <text class="tool-desc">学习进度</text>
      </view>
    </view>
  </view>
  
  <!-- 加载提示 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view> 
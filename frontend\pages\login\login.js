// login.js
const app = getApp();

// 导入用户管理工具
const userManager = require('../../utils/activity');

Page({
  data: {
    isLogging: false,
    loginFailed: false,
    errorMsg: '',
    avatarUrl: userManager.getDefaultAvatarUrl(), // 默认头像
    nickName: '', // 用户昵称
    statusBarHeight: 20, // 默认状态栏高度
    navigationBarHeight: 90, // 默认导航栏总高度
    selectedAvatarFile: null // 保存选中的头像文件路径
  },
  
  onLoad: function() {
    // 获取系统信息设置状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight;
    const navigationBarHeight = statusBarHeight + 50; // 状态栏 + 导航栏高度
    
    this.setData({
      statusBarHeight,
      navigationBarHeight
    });
    
    // 检查是否已经登录
    if (app.checkLogin()) {
      wx.switchTab({
        url: '/pages/index/index'
      });
    } else {
      // 清除旧的登录信息，但保留openid
      app.globalData.token = '';
      app.globalData.userInfo = null;
      wx.removeStorageSync('token');
      wx.removeStorageSync('userInfo');
      // 注意：不要删除openid，这样可以在重新登录时识别相同用户
    }
  },
  
  // 选择头像处理 - 使用微信小程序官方方式
  onChooseAvatar: function(e) {
    const { avatarUrl } = e.detail;
    if (!avatarUrl) {
      console.warn('未获取到头像URL');
      return;
    }

    this.setData({
      avatarUrl,
      selectedAvatarFile: avatarUrl // 保存选中的文件路径用于上传
    });
    console.log('已选择头像:', avatarUrl);
  },

  // 上传头像到服务器
  uploadAvatar: function() {
    return new Promise((resolve, reject) => {
      if (!this.data.selectedAvatarFile) {
        // 如果没有选择新头像，使用默认头像
        resolve(this.data.avatarUrl);
        return;
      }

      const app = getApp();

      wx.showLoading({
        title: '上传头像中...',
        mask: true
      });

      wx.uploadFile({
        url: `${app.globalData.baseUrl}/upload/avatar`,
        filePath: this.data.selectedAvatarFile,
        name: 'avatar',
        header: {
          'Content-Type': 'multipart/form-data'
        },
        success: (res) => {
          wx.hideLoading();
          console.log('头像上传响应:', res);

          try {
            const data = JSON.parse(res.data);
            if (data.code === 200 && data.data && data.data.avatar_url) {
              console.log('头像上传成功:', data.data.avatar_url);
              resolve(data.data.avatar_url);
            } else {
              console.error('头像上传失败:', data.message || '未知错误');
              reject(new Error(data.message || '头像上传失败'));
            }
          } catch (e) {
            console.error('解析上传响应失败:', e);
            reject(new Error('头像上传响应格式错误'));
          }
        },
        fail: (err) => {
          wx.hideLoading();
          console.error('头像上传请求失败:', err);
          reject(new Error('头像上传失败'));
        }
      });
    });
  },
  
  // 昵称输入处理
  onNicknameInput: function(e) {
    const nickName = e.detail.value.trim();
    this.setData({ nickName });
  },
  
  // 登录表单提交
  onSubmitLogin: function(e) {
    if (this.data.isLogging) return;
    
    const formData = e.detail.value;
    const nickName = formData.nickname ? formData.nickname.trim() : '';
    
    if (!nickName) {
      wx.showToast({
        title: '请输入昵称',
        icon: 'none'
      });
      return;
    }
    
    this.setData({ 
      isLogging: true,
      loginFailed: false,
      errorMsg: '',
      nickName
    });
    
    console.log('开始登录处理');
    
    // 创建用户信息 - 同时包含两种属性名格式
    const userInfo = {
      nickName: nickName,
      nickname: nickName,
      avatarUrl: this.data.avatarUrl,
      avatar: this.data.avatarUrl
    };
    
    console.log('用户信息:', userInfo);
    this.handleLogin(userInfo);
  },
  
  // 游客登录
  visitorLogin: function() {
    if (this.data.isLogging) return;
    
    this.setData({ 
      isLogging: true,
      loginFailed: false,
      errorMsg: ''
    });
    
    console.log('开始游客登录');
    
    const visitorNickname = '游客' + Math.floor(Math.random() * 10000); // 添加随机数以区分不同游客
    const defaultAvatar = userManager.getDefaultAvatarUrl();
    
    // 创建模拟用户信息 - 同时包含两种属性名格式
    const userInfo = {
      nickName: visitorNickname,
      nickname: visitorNickname,
      avatarUrl: defaultAvatar,
      avatar: defaultAvatar
    };
    
    console.log('游客信息:', userInfo);
    this.handleLogin(userInfo);
  },
  
  // 处理登录逻辑
  handleLogin: function(userInfo) {
    // 先上传头像，然后再进行登录
    this.uploadAvatar()
      .then(avatarUrl => {
        console.log('头像处理完成:', avatarUrl);

        // 更新用户信息中的头像URL
        userInfo.avatarUrl = avatarUrl;
        userInfo.avatar = avatarUrl;

        // 确保用户信息有效
        if (!userInfo.avatarUrl) {
          const defaultAvatar = userManager.getDefaultAvatarUrl();
          userInfo.avatarUrl = defaultAvatar;
          userInfo.avatar = defaultAvatar;
        }

        // 先保存用户信息到本地
        userManager.saveUserInfo(userInfo);

        return app.login(userInfo);
      })
      .then(res => {
        return this.handleLoginSuccess(res);
      })
      .catch(err => {
        return this.handleLoginError(err);
      });
  },

  // 处理登录成功
  handleLoginSuccess: function(res) {
    console.log('登录成功:', res);
    this.setData({ isLogging: false });

    // 检查服务器响应是否表明用户被禁用
    if (res.error && res.banned) {
      console.error('用户已被禁用 (从成功响应检测)');
      wx.showModal({
        title: '账号已被禁用',
        content: '您的账号已被管理员禁用，如需帮助请联系客服邮箱：<EMAIL>',
        showCancel: false,
        confirmText: '确定',
        success: () => {
          // 保存用户信息到全局数据
          if (res.user) {
            app.globalData.userInfo = res.user;
          }
          // 清除token
          app.globalData.token = '';

          // 跳转到禁用页面
          wx.redirectTo({
            url: '/pages/banned/banned'
          });
        }
      });
      return;
    }

    // 确保更新了全局的用户信息
    if (app.globalData && res && res.user) {
      // 确保服务器返回的用户信息包含两种属性名格式
      const serverUserInfo = res.user;
      if (serverUserInfo.avatar && !serverUserInfo.avatarUrl) {
        serverUserInfo.avatarUrl = serverUserInfo.avatar;
      } else if (serverUserInfo.avatarUrl && !serverUserInfo.avatar) {
        serverUserInfo.avatar = serverUserInfo.avatarUrl;
      }

      if (serverUserInfo.nickname && !serverUserInfo.nickName) {
        serverUserInfo.nickName = serverUserInfo.nickname;
      } else if (serverUserInfo.nickName && !serverUserInfo.nickname) {
        serverUserInfo.nickname = serverUserInfo.nickName;
      }

      // 检查用户是否被禁用
      if (serverUserInfo.is_active === false) {
        console.error('用户已被禁用 (从用户信息检测)');
        wx.showModal({
          title: '账号已被禁用',
          content: '您的账号已被管理员禁用，如需帮助请联系客服邮箱：<EMAIL>',
          showCancel: false,
          confirmText: '确定',
          success: () => {
            // 清除登录状态，但保留用户信息以便在禁用页面显示
            app.globalData.token = '';

            // 跳转到禁用页面
            wx.redirectTo({
              url: '/pages/banned/banned'
            });
          }
        });

        return;
      }

      app.globalData.userInfo = serverUserInfo;

      // 同步头像URL以确保一致性
      if (!app.globalData.userInfo.avatarUrl || !app.globalData.userInfo.avatar) {
        const defaultAvatar = userManager.getDefaultAvatarUrl();
        app.globalData.userInfo.avatarUrl = defaultAvatar;
        app.globalData.userInfo.avatar = defaultAvatar;
      }

      console.log('已更新全局用户信息:', app.globalData.userInfo);
    }

    wx.showToast({
      title: '登录成功',
      icon: 'success'
    });

    setTimeout(() => {
      // 登录成功后先检查是否已绑定班级
      app.checkUserHasClass().then(hasClass => {
        if (hasClass) {
          // 已有班级，直接进入主页
          wx.switchTab({
            url: '/pages/index/index'
          });
        } else {
          // 没有班级，先跳转到班级选择页面
          wx.redirectTo({
            url: '/pages/class-selection/class-selection'
          });
        }
      }).catch(err => {
        console.error('检查班级状态出错:', err);
        // 出错时默认跳转到班级选择页面
        wx.redirectTo({
          url: '/pages/class-selection/class-selection'
        });
      });
    }, 1000);
  },

  // 处理登录错误
  handleLoginError: function(err) {
    console.error('登录失败:', err);
    console.log('错误对象内容:', JSON.stringify(err));

    // 检查是否是用户被禁用的错误 - 使用更灵活的方式检测
    const errorObj = err.response || err;
    const responseData = errorObj.data || {};
    const statusCode = errorObj.statusCode || errorObj.status || 0;

    // 多种方式检查是否被禁用
    const isBanned =
      (statusCode === 403 && responseData.banned) || // 标准响应
      (responseData.banned === true) ||             // 直接在数据中
      (responseData.error && responseData.banned) || // 错误信息中
      (typeof err === 'object' && err.banned);       // 错误对象中

    if (isBanned) {
      console.error('账号被禁用 (从错误响应检测)');
      wx.showModal({
        title: '账号已被禁用',
        content: '您的账号已被管理员禁用，如需帮助请联系客服邮箱：<EMAIL>',
        showCancel: false,
        confirmText: '确定',
        success: () => {
          // 保留用户信息以便在禁用页面显示
          if (responseData.user) {
            app.globalData.userInfo = responseData.user;
          }

          wx.redirectTo({
            url: '/pages/banned/banned'
          });
        }
      });
      return;
    }

    this.setData({
      isLogging: false,
      loginFailed: true,
      errorMsg: '登录失败，请重试'
    });

    wx.showToast({
      title: '登录失败，请重试',
      icon: 'none'
    });
  },

  // 原来的handleLogin逻辑保留作为备用
  handleLoginOld: function(userInfo) {
    // 确保用户信息有效
    if (!userInfo.avatarUrl) {
      const defaultAvatar = userManager.getDefaultAvatarUrl();
      userInfo.avatarUrl = defaultAvatar;
      userInfo.avatar = defaultAvatar;
    }

    // 先保存用户信息到本地
    userManager.saveUserInfo(userInfo);


  }
}); 
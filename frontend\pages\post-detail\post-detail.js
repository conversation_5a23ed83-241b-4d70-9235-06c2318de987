// 动态详情页
const app = getApp()
const globalNotificationMixin = require('../../utils/global-notification-mixin')

// 时间格式化函数
function formatTime(dateString) {
  if (!dateString) return ''

  const date = new Date(dateString)
  const now = new Date()
  const diff = now - date

  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour

  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return Math.floor(diff / minute) + '分钟前'
  } else if (diff < day) {
    return Math.floor(diff / hour) + '小时前'
  } else if (diff < 7 * day) {
    return Math.floor(diff / day) + '天前'
  } else {
    return date.toLocaleDateString()
  }
}

Page(Object.assign({}, globalNotificationMixin, {
  data: Object.assign({}, globalNotificationMixin.data, {
    postId: null,
    postDetail: null,
    comments: [],
    commentContent: '',
    replyToCommentId: null,
    replyPlaceholder: '写评论...',
    loading: true,
    lastCommentId: 0,
    pollingTimer: null,
    hasContent: false
  }),

  onLoad: function(options) {
    const postId = options.id
    if (postId) {
      this.setData({ postId: parseInt(postId) })
      this.loadPostDetail()
      this.loadComments()
      this.startPolling()
      this.markNotificationsAsRead()
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  onShow: function() {
    // 页面显示时开始轮询
    this.startPolling()
  },

  onHide: function() {
    // 页面隐藏时停止轮询
    this.stopPolling()
  },

  // 返回上一页
  goBack: function() {
    // 设置一个全局标志，表示需要刷新上一页
    app.globalData.needRefreshCatFriends = true;
    wx.navigateBack()
  },

  // 加载动态详情
  loadPostDetail: function() {
    const userInfo = wx.getStorageSync('userInfo')
    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    // 获取用户特定的点赞和收藏缓存
    const likedPosts = wx.getStorageSync('liked_posts') || {}
    const collectedPosts = wx.getStorageSync('collected_posts') || {}
    
    // 确保用户ID的缓存对象存在
    if (!likedPosts[userInfo.id]) likedPosts[userInfo.id] = {}
    if (!collectedPosts[userInfo.id]) collectedPosts[userInfo.id] = {}

    // 先尝试获取单个动态详情API
    wx.request({
      url: `${app.globalData.baseUrl}/cat_circle/posts/${this.data.postId}`,
      method: 'GET',
      data: {
        user_id: userInfo.id
      },
      success: (res) => {
        if (res.data.code === 200) {
          const post = res.data.data.post
          if (post) {
            // 格式化时间
            post.created_at = formatTime(post.created_at)

            // 确保用户信息完整
            if (post.user_info) {
              console.log('用户信息:', post.user_info)
            }

            // 使用当前用户的点赞和收藏状态
            if (likedPosts[userInfo.id][post.id] !== undefined) {
              post.is_liked = likedPosts[userInfo.id][post.id]
            }
            
            if (collectedPosts[userInfo.id][post.id] !== undefined) {
              post.is_collected = collectedPosts[userInfo.id][post.id]
            }

            this.setData({
              postDetail: post,
              loading: false
            })
          } else {
            wx.showToast({
              title: '动态不存在',
              icon: 'none'
            })
            setTimeout(() => {
              wx.navigateBack()
            }, 1500)
          }
        } else {
          // 如果单个详情API失败，回退到列表API
          this.loadPostDetailFromList()
        }
      },
      fail: () => {
        // 如果单个详情API失败，回退到列表API
        this.loadPostDetailFromList()
      }
    })
  },

  // 从动态列表中获取详情（备用方法）
  loadPostDetailFromList: function() {
    const userInfo = wx.getStorageSync('userInfo')

    // 获取用户特定的点赞和收藏缓存
    const likedPosts = wx.getStorageSync('liked_posts') || {}
    const collectedPosts = wx.getStorageSync('collected_posts') || {}
    
    // 确保用户ID的缓存对象存在
    if (!likedPosts[userInfo.id]) likedPosts[userInfo.id] = {}
    if (!collectedPosts[userInfo.id]) collectedPosts[userInfo.id] = {}

    wx.request({
      url: `${app.globalData.baseUrl}/cat_circle/posts`,
      method: 'GET',
      data: {
        user_id: userInfo.id,
        page: 1,
        per_page: 50
      },
      success: (res) => {
        if (res.data.code === 200) {
          const posts = res.data.data.posts || []
          const post = posts.find(p => p.id === this.data.postId)
          if (post) {
            // 格式化时间
            post.created_at = formatTime(post.created_at)

            // 确保用户信息完整
            if (post.user_info) {
              console.log('用户信息:', post.user_info)
            }

            // 使用当前用户的点赞和收藏状态
            if (likedPosts[userInfo.id][post.id] !== undefined) {
              post.is_liked = likedPosts[userInfo.id][post.id]
            }
            
            if (collectedPosts[userInfo.id][post.id] !== undefined) {
              post.is_collected = collectedPosts[userInfo.id][post.id]
            }

            this.setData({
              postDetail: post,
              loading: false
            })
            // 增加浏览量
            this.incrementViewCount()
          } else {
            wx.showToast({
              title: '动态不存在',
              icon: 'none'
            })
            setTimeout(() => {
              wx.navigateBack()
            }, 1500)
          }
        }
      },
      fail: () => {
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        })
        this.setData({ loading: false })
      }
    })
  },

  // 增加浏览量
  incrementViewCount: function() {
    const userInfo = wx.getStorageSync('userInfo')
    if (!userInfo || !userInfo.id) return

    // 这里可以调用增加浏览量的API
    // 暂时在前端模拟增加
    const postDetail = this.data.postDetail
    if (postDetail) {
      postDetail.view_count = (postDetail.view_count || 0) + 1
      this.setData({ postDetail })
    }
  },

  // 加载评论
  loadComments: function() {
    const userInfo = wx.getStorageSync('userInfo')
    if (!userInfo || !userInfo.id) return

    wx.request({
      url: `${app.globalData.baseUrl}/cat_circle/posts/${this.data.postId}/comments`,
      method: 'GET',
      data: { user_id: userInfo.id },
      success: (res) => {
        if (res.data.code === 200) {
          const comments = res.data.data.comments || []
          // 格式化评论时间并初始化回复显示状态
          comments.forEach(comment => {
            comment.created_at = formatTime(comment.created_at)
            // 初始化回复显示状态，默认不展开
            comment.showAllReplies = false
            if (comment.replies) {
              comment.replies.forEach(reply => {
                reply.created_at = formatTime(reply.created_at)
              })
            }
          })
          this.setData({ comments })

          // 更新最后评论ID
          if (comments.length > 0) {
            const lastComment = comments[comments.length - 1]
            this.setData({ lastCommentId: lastComment.id })
          }
        }
      }
    })
  },

  // 点赞/取消点赞
  toggleLike: function() {
    const userInfo = wx.getStorageSync('userInfo')
    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    // 获取当前用户的点赞状态缓存
    let likedPosts = wx.getStorageSync('liked_posts') || {}
    if (!likedPosts[userInfo.id]) {
      likedPosts[userInfo.id] = {}
    }

    wx.request({
      url: `${app.globalData.baseUrl}/cat_circle/posts/${this.data.postId}/like`,
      method: 'POST',
      data: { user_id: userInfo.id },
      success: (res) => {
        if (res.data.code === 200) {
          const postDetail = this.data.postDetail
          postDetail.is_liked = res.data.data.is_liked
          postDetail.like_count = res.data.data.like_count
          
          // 更新点赞缓存
          likedPosts[userInfo.id][this.data.postId] = res.data.data.is_liked
          wx.setStorageSync('liked_posts', likedPosts)
          
          this.setData({ postDetail })
        } else {
          wx.showToast({
            title: res.data.message || '操作失败',
            icon: 'none'
          })
        }
      }
    })
  },

  // 收藏/取消收藏
  toggleCollect: function() {
    const userInfo = wx.getStorageSync('userInfo')
    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    // 获取当前用户的收藏状态缓存
    let collectedPosts = wx.getStorageSync('collected_posts') || {}
    if (!collectedPosts[userInfo.id]) {
      collectedPosts[userInfo.id] = {}
    }

    wx.request({
      url: `${app.globalData.baseUrl}/cat_circle/posts/${this.data.postId}/collect`,
      method: 'POST',
      data: { user_id: userInfo.id },
      success: (res) => {
        if (res.data.code === 200) {
          const postDetail = this.data.postDetail
          postDetail.is_collected = res.data.data.is_collected
          postDetail.collect_count = res.data.data.collect_count
          
          // 更新收藏缓存
          collectedPosts[userInfo.id][this.data.postId] = res.data.data.is_collected
          wx.setStorageSync('collected_posts', collectedPosts)
          
          this.setData({ postDetail })
        } else {
          wx.showToast({
            title: res.data.message || '操作失败',
            icon: 'none'
          })
        }
      }
    })
  },

  // 预览图片
  previewImage: function(e) {
    const current = e.currentTarget.dataset.current
    const urls = e.currentTarget.dataset.urls
    wx.previewImage({
      current: current,
      urls: urls
    })
  },

  // 评论输入
  onCommentInput: function(e) {
    const value = e.detail.value
    this.setData({
      commentContent: value
    })
    console.log('输入内容:', value) // 调试用
  },

  // 回复评论
  replyComment: function(e) {
    const commentId = e.currentTarget.dataset.id
    const userName = e.currentTarget.dataset.user
    this.setData({
      replyToCommentId: commentId,
      replyPlaceholder: `回复 ${userName}：`
    })
  },

  // 提交评论
  submitComment: function() {
    const content = this.data.commentContent.trim()
    if (!content) return

    const userInfo = wx.getStorageSync('userInfo')
    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    const commentData = {
      user_id: userInfo.id,
      content: content,
      post_id: this.data.postId
    }

    if (this.data.replyToCommentId) {
      commentData.reply_to_comment_id = this.data.replyToCommentId
    }

    wx.request({
      url: `${app.globalData.baseUrl}/cat_circle/comments`,
      method: 'POST',
      data: commentData,
      success: (res) => {
        if (res.data.code === 200) {
          wx.showToast({
            title: '评论成功',
            icon: 'success'
          })
          this.setData({
            commentContent: '',
            hasContent: false,
            replyToCommentId: null,
            replyPlaceholder: '写评论...'
          })
          // 重新加载评论
          this.loadComments()
          // 更新动态的评论数
          const postDetail = this.data.postDetail
          if (postDetail) {
            postDetail.comment_count = (postDetail.comment_count || 0) + 1
            this.setData({ postDetail })
          }
        } else {
          wx.showToast({
            title: res.data.message || '评论失败',
            icon: 'none'
          })
        }
      }
    })
  },

  // 开始轮询新评论
  startPolling: function() {
    this.stopPolling() // 先停止之前的轮询

    const userInfo = wx.getStorageSync('userInfo')
    if (!userInfo || !userInfo.id || !this.data.postId) return

    this.data.pollingTimer = setInterval(() => {
      this.checkNewComments()
    }, 3000) // 每3秒检查一次新评论
  },

  // 停止轮询
  stopPolling: function() {
    if (this.data.pollingTimer) {
      clearInterval(this.data.pollingTimer)
      this.setData({ pollingTimer: null })
    }
  },

  // 检查新评论
  checkNewComments: function() {
    const userInfo = wx.getStorageSync('userInfo')
    if (!userInfo || !userInfo.id) return

    wx.request({
      url: `${app.globalData.baseUrl}/cat_circle/posts/${this.data.postId}/new_comments`,
      method: 'GET',
      data: {
        user_id: userInfo.id,
        last_comment_id: this.data.lastCommentId
      },
      success: (res) => {
        if (res.data.code === 200 && res.data.data.comments.length > 0) {
          const newComments = res.data.data.comments

          // 格式化新评论时间并初始化回复显示状态
          newComments.forEach(comment => {
            comment.created_at = formatTime(comment.created_at)
            // 初始化回复显示状态，默认不展开
            comment.showAllReplies = false
            if (comment.replies) {
              comment.replies.forEach(reply => {
                reply.created_at = formatTime(reply.created_at)
              })
            }
          })

          // 将新评论添加到现有评论列表
          const currentComments = this.data.comments
          const updatedComments = [...currentComments, ...newComments]

          // 更新最后评论ID
          const lastComment = newComments[newComments.length - 1]

          this.setData({
            comments: updatedComments,
            lastCommentId: lastComment.id
          })

          // 显示通知（只有当新评论不是当前用户发的时候）
          const hasOtherUserComments = newComments.some(comment =>
            comment.user_id !== userInfo.id
          )

          // 新评论会通过全局通知系统显示，这里不需要额外处理

          // 更新动态评论数
          const postDetail = this.data.postDetail
          if (postDetail) {
            postDetail.comment_count = (postDetail.comment_count || 0) + newComments.length
            this.setData({ postDetail })
          }
        }
      },
      fail: (err) => {
        console.error('检查新评论失败:', err)
      }
    })
  },



  // 滚动到底部
  scrollToBottom: function() {
    wx.pageScrollTo({
      scrollTop: 99999,
      duration: 300
    })
  },

  // 评论输入变化
  onCommentInput: function(e) {
    const value = e.detail.value
    this.setData({
      commentContent: value,
      hasContent: value.trim().length > 0
    })
  },

  // 标记相关通知为已读
  markNotificationsAsRead: function() {
    const userInfo = wx.getStorageSync('userInfo')
    if (!userInfo || !userInfo.id || !this.data.postId) return

    wx.request({
      url: `${app.globalData.baseUrl}/cat_circle/notifications/comments/mark_post_read`,
      method: 'POST',
      data: {
        user_id: userInfo.id,
        post_id: this.data.postId
      },
      success: (res) => {
        if (res.data.code === 200) {
          console.log(`动态${this.data.postId}的通知已标记为已读，共${res.data.data.marked_count}条`)
          // 通知猫友圈页面刷新未读数量
          app.globalData.needRefreshCatFriends = true
        }
      },
      fail: (err) => {
        console.error('标记通知已读失败:', err)
      }
    })
  },

  // 切换回复显示状态
  toggleReplies: function(e) {
    const commentId = e.currentTarget.dataset.commentid
    const comments = this.data.comments.map(comment => {
      if (comment.id === commentId) {
        return {
          ...comment,
          showAllReplies: !comment.showAllReplies
        }
      }
      return comment
    })
    this.setData({ comments })
  },

  // 页面卸载时触发
  onUnload: function() {
    // 停止轮询
    this.stopPolling()
    // 设置标志，让猫友圈页面刷新
    app.globalData.needRefreshCatFriends = true;
  }
}))

<!-- index.wxml -->
<view class="container">
  <!-- 全局通知组件 -->
  <notification-toast
    show="{{showGlobalNotification}}"
    title="{{globalNotificationTitle}}"
    message="{{globalNotificationMessage}}"
    postId="{{globalNotificationPostId}}"
    bind:hide="onGlobalNotificationHide"
    bind:tap="onGlobalNotificationTap">
  </notification-toast>

  <!-- 小猫AI组件 - 确保包含并有效 -->
  <cat-component id="cat-component" wx:if="{{catComponentVisible !== false}}" name="AI喵" />
  
  <!-- 顶部欢迎横幅 - 优化版本 -->
  <view class="header enhanced">
    <view class="header-background"></view>
    <view class="header-content">
      <!-- 欢迎文本 -->
      <view class="welcome-text">
        <text class="welcome">欢迎回来</text>
        <text class="username">{{userInfo.nickname || userInfo.nickName || '同学'}}</text>
        <view class="user-status">
          <view class="status-badge">
            <view class="status-icon"></view>
            <text class="status-text">学习中</text>
          </view>
        </view>
      </view>
      
      <!-- 头像和刷新按钮区域 -->
      <view class="avatar-section">
        <!-- 刷新按钮放在头像左侧 -->
        <view class="refresh-button-container">
          <!-- 提示气泡 -->
          <view class="refresh-tooltip">点我刷新</view>
          <!-- 刷新按钮 -->
          <view class="refresh-button {{isRefreshing ? 'refreshing' : ''}}" bindtap="refreshData">
            <view class="refresh-icon">
              <view class="refresh-unified-icon"></view>
            </view>
          </view>
        </view>
        
        <!-- 头像容器 -->
        <view class="avatar-container">
          <image class="avatar" src="{{userInfo.avatar || userInfo.avatarUrl || '/images/profile.png'}}" mode="aspectFill"></image>
          <view class="avatar-badge"></view>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 课程列表区域 -->
  <view class="card-section courses-section" wx:if="{{courses.length > 0}}">
    <view class="section-header">
      <text class="section-title">{{className}}课程</text>
      <view class="more-button" bindtap="navigateToAllCourses">
        <text class="section-more-text">更多</text>
        <text class="section-more-arrow">→</text>
      </view>
    </view>
    
    <!-- 添加滑动提示 -->
    <view class="swipe-tip" wx:if="{{courses.length > 2}}">
      <view class="swipe-tip-content">
        <text class="swipe-tip-text">向右滑动查看更多课程</text>
        <view class="swipe-tip-arrows">
          <text class="swipe-tip-arrow">→</text>
        </view>
      </view>
    </view>
    
    <view class="course-scroll-container">
      <scroll-view 
        scroll-x="true" 
        class="course-scroll" 
        enhanced 
        show-scrollbar="{{false}}"
        bindscroll="onCourseScroll"
        bindscrolltolower="onScrollToEnd"
        bindscrolltoupper="onScrollToStart">
        <view class="course-scroll-content">
          <view wx:for="{{courses}}" wx:key="id" class="course-card" bindtap="navigateToCourse" data-id="{{item.id}}" data-name="{{item.name}}">
            <view class="course-icon" style="background-color: {{item.color || '#4e8df7'}}">
              <text class="course-icon-text">{{item.name[0]}}</text>
            </view>
            <view class="course-info">
              <text class="course-name">{{item.name}}</text>
              <text class="course-question-count">{{item.question_count.total}}题</text>
            </view>
          </view>
        </view>
      </scroll-view>
      
      <!-- 滑动导航点 -->
      <view class="scroll-dots" wx:if="{{courses.length > 2}}">
        <block wx:if="{{courses.length <= 3}}">
          <view wx:for="{{courses}}" wx:key="*this" class="scroll-dot {{currentDotIndex === index ? 'active' : ''}}"></view>
        </block>
        <block wx:else>
          <view class="scroll-dot {{currentDotIndex === 0 ? 'active' : ''}}"></view>
          <view class="scroll-dot {{currentDotIndex === 1 ? 'active' : ''}}"></view>
          <view class="scroll-dot {{currentDotIndex === 2 ? 'active' : ''}}"></view>
        </block>
      </view>
    </view>
  </view>
  
  <!-- 功能卡片区域 -->
  <view class="card-section">
    <view class="section-header">
      <text class="section-title">答题模式</text>
    </view>
    
    <!-- 单选题卡片 -->
    <view class="quiz-card" bindtap="startSingleQuiz">
      <view class="quiz-card-left">
        <view class="quiz-icon single-icon">
          <text class="quiz-icon-text">单</text>
        </view>
        <view class="quiz-info">
          <text class="quiz-title">单选题</text>
          <text class="quiz-description">共 {{singleChoiceList.length}} 道题目</text>
        </view>
      </view>
      <view class="quiz-action">
        <text class="action-text">开始答题</text>
        <text class="arrow">></text>
      </view>
    </view>
    
    <!-- 多选题卡片 -->
    <view class="quiz-card multiple-card" bindtap="startMultipleQuiz">
      <view class="quiz-card-left">
        <view class="quiz-icon multiple-icon">
          <text class="quiz-icon-text">多</text>
        </view>
        <view class="quiz-info">
          <text class="quiz-title">多选题</text>
          <text class="quiz-description">共 {{multipleChoiceList.length}} 道题目</text>
        </view>
      </view>
      <view class="quiz-action">
        <text class="action-text">开始答题</text>
        <text class="arrow">></text>
      </view>
    </view>
    
    <!-- 判断题卡片 -->
    <view class="quiz-card judgment-card" bindtap="startJudgmentQuiz">
      <view class="quiz-card-left">
        <view class="quiz-icon judgment-icon">
          <text class="quiz-icon-text">判</text>
        </view>
        <view class="quiz-info">
          <text class="quiz-title">判断题</text>
          <text class="quiz-description">共 {{judgmentList.length}} 道题目</text>
        </view>
      </view>
      <view class="quiz-action">
        <text class="action-text">开始答题</text>
        <text class="arrow">></text>
      </view>
    </view>
    
    <!-- 填空题卡片 -->
    <view class="quiz-card fillblank-card" bindtap="startFillBlankQuiz">
      <view class="quiz-card-left">
        <view class="quiz-icon fillblank-icon">
          <text class="quiz-icon-text">填</text>
        </view>
        <view class="quiz-info">
          <text class="quiz-title">填空题</text>
          <text class="quiz-description">共 {{fillblankList.length}} 道题目</text>
        </view>
      </view>
      <view class="quiz-action">
        <text class="action-text">开始答题</text>
        <text class="arrow">></text>
      </view>
    </view>
  </view>
  
  <!-- 学习工具区域 -->
  <view class="card-section">
    <view class="section-header">
      <text class="section-title">学习工具</text>
    </view>
    
    <view class="tools-grid">
      <!-- 错题本卡片 -->
      <view class="tool-card" bindtap="goToWrongQuestions">
        <view class="tool-icon">❌</view>
        <text class="tool-title">错题本</text>
        <text class="tool-desc">记录错题</text>
      </view>
      
      <!-- 数据统计卡片 -->
      <view class="tool-card" bindtap="goToStatistics">
        <view class="tool-icon">📊</view>
        <text class="tool-title">答题统计</text>
        <text class="tool-desc">学习进度</text>
      </view>
    </view>
  </view>
  
  <!-- 加载提示 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view> 
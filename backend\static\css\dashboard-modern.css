/* Modern Dashboard CSS - Enhanced Visual Design */
:root {
  --primary-color: #4e73df;
  --primary-light: #eaedff;
  --secondary-color: #6d757d;
  --success-color: #2dce89;
  --info-color: #11cdef;
  --warning-color: #ffb84c;
  --danger-color: #f5365c;
  --light-color: #f8f9fe;
  --dark-color: #172b4d;
  --white: #ffffff;
  
  --gradient-primary: linear-gradient(87deg, #5e72e4 0, #825ee4 100%);
  --gradient-secondary: linear-gradient(87deg, #f7fafc 0, #f7f8fc 100%);
  --gradient-success: linear-gradient(87deg, #2dce89 0, #2fcca0 100%);
  --gradient-info: linear-gradient(87deg, #11cdef 0, #1da2cf 100%);
  --gradient-warning: linear-gradient(87deg, #fb6340 0, #fbb140 100%);
  --gradient-danger: linear-gradient(87deg, #f5365c 0, #f56036 100%);
  
  --shadow-sm: 0 .125rem .25rem rgba(0,0,0,.075);
  --shadow: 0 .5rem 1rem rgba(0,0,0,.08);
  --shadow-lg: 0 1rem 3rem rgba(0,0,0,.1);
  
  --card-border-radius: 1rem;
  --btn-border-radius: 0.5rem;
  --transition-normal: all 0.3s ease;
  --transition-slow: all 0.5s ease;
}

/* Body and general overrides */
body {
  background-color: #f8f9fe;
  color: #525f7f;
  font-family: 'Nunito', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* Enhanced card styling */
.card {
  border: none;
  border-radius: var(--card-border-radius);
  background: var(--white);
  box-shadow: var(--shadow);
  transition: var(--transition-normal);
  overflow: hidden;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.card-header {
  background-color: transparent;
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid rgba(0,0,0,0.05);
  font-weight: 600;
  display: flex;
  align-items: center;
}

.card-header i {
  margin-right: 0.5rem;
  color: var(--primary-color);
}

.card-body {
  padding: 1.5rem;
}

/* Welcome panel improvements */
.dashboard-welcome {
  background: var(--gradient-primary);
  border-radius: var(--card-border-radius);
  padding: 2.5rem;
  position: relative;
  overflow: hidden;
  margin-bottom: 2rem;
  box-shadow: var(--shadow-lg);
}

.dashboard-welcome::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm-39-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM80 49c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm-11 35c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm-39-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3z' fill='rgba(255,255,255,.05)' fill-rule='evenodd'/%3E%3C/svg%3E");
  opacity: 0.3;
  z-index: 0;
}

.welcome-icon {
  background-color: rgba(255, 255, 255, 0.2);
  width: 70px;
  height: 70px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 25px;
  font-size: 28px;
  backdrop-filter: blur(4px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
  transition: var(--transition-normal);
}

.welcome-icon:hover {
  transform: scale(1.1) rotate(5deg);
}

.welcome-text h3 {
  font-weight: 700;
  margin-bottom: 5px;
  font-size: 1.8rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.welcome-stat {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 15px;
  padding: 18px;
  backdrop-filter: blur(4px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: var(--transition-normal);
  transform: translateY(0);
}

.welcome-stat:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-5px);
}

.welcome-stat-value {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 5px;
  transition: var(--transition-normal);
}

.welcome-stat:hover .welcome-stat-value {
  transform: scale(1.05);
}

.welcome-actions .btn {
  border-radius: 12px;
  font-weight: 600;
  padding: 10px 20px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(4px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: var(--transition-normal);
}

.welcome-actions .btn:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

/* Stat cards improvements */
.top-stat-card {
  border: none;
  border-radius: var(--card-border-radius);
  background: white;
  box-shadow: var(--shadow);
  padding: 1.75rem;
  height: 100%;
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.top-stat-card::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 150px;
  height: 100%;
  background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(246,249,252,1) 100%);
  opacity: 0.8;
  z-index: -1;
}

.top-stat-card::after {
  content: "";
  position: absolute;
  bottom: 0;
  right: 0;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background-color: rgba(0,0,0,0.02);
  z-index: -1;
}

.top-stat-card:hover {
  transform: translateY(-7px);
  box-shadow: var(--shadow-lg);
}

.top-stat-card .icon-container {
  width: 60px;
  height: 60px;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.25rem;
  font-size: 1.5rem;
  transition: transform 0.3s ease;
}

.top-stat-card:hover .icon-container {
  transform: scale(1.1) rotate(5deg);
}

.top-stat-card .card-title {
  color: #8898aa;
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.top-stat-card .card-value {
  font-size: 2.25rem;
  font-weight: 700;
  color: #32325d;
  margin-bottom: 0.75rem;
  position: relative;
  display: inline-block;
}

.top-stat-card .card-footer {
  margin-top: 1.25rem;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  font-size: 0.875rem;
  border-top: 1px solid rgba(0,0,0,0.05);
  padding-top: 1rem;
}

.top-stat-card .progress {
  height: 6px;
  background-color: #e9ecef;
  border-radius: 10px;
  margin-top: 0.75rem;
  overflow: hidden;
}

.top-stat-card .progress-bar {
  border-radius: 10px;
  transition: width 1s ease-in-out;
}

/* Improve buttons */
.btn {
  font-weight: 600;
  border-radius: var(--btn-border-radius);
  transition: var(--transition-normal);
}

.btn-primary {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.btn-group .btn {
  border-radius: var(--btn-border-radius);
}

/* Chart containers */
.chart-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 280px;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  background: linear-gradient(to bottom, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.3) 100%);
}

.chart-canvas {
  width: 100% !important;
  height: 100% !important;
  max-height: 350px;
}

/* Highlight the user activity chart specifically */
#userActivityChart {
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
  border-radius: 8px;
  transition: all 0.3s ease;
}

#userActivityChart:hover {
  box-shadow: 0 6px 16px rgba(79, 70, 229, 0.25);
}

/* Style for data labels */
.chart-data-label {
  background-color: rgba(255, 106, 0, 0.9);
  color: white;
  padding: 5px 8px;
  border-radius: 4px;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  pointer-events: none;
  z-index: 10;
}

/* Today highlight styling */
.today-highlight {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.9;
  }
  100% {
    transform: scale(1);
    opacity: 0.6;
  }
}

/* Enhanced tooltip appearance */
.tooltip-enhanced {
  background-color: #1e293b !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  padding: 10px 12px !important;
  border: none !important;
  pointer-events: none;
}

/* Chart card styling */
.chart-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s ease;
}

.chart-card:hover {
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.chart-card .card-header {
  background: linear-gradient(90deg, #4f46e5 0%, #6366f1 100%);
  color: white;
  border-bottom: none;
  padding: 15px 20px;
}

.chart-card .card-body {
  padding: 15px;
  background: linear-gradient(to bottom, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.3) 100%);
}

/* User Activity Analysis Chart specific styling */
.user-activity-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 300px;
}

.current-day-indicator {
  position: absolute;
  background-color: rgba(255, 106, 0, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: bold;
  font-size: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  pointer-events: none;
  z-index: 10;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
  100% {
    transform: translateY(0px);
  }
}

/* Table improvements */
.table {
  border-collapse: separate;
  border-spacing: 0;
}

.table th {
  font-weight: 600;
  color: #8898aa;
  border-bottom: 1px solid #e9ecef;
}

.table td {
  padding: 1rem;
  vertical-align: middle;
  border-top: 1px solid #e9ecef;
}

.table-hover tbody tr:hover {
  background-color: rgba(0,0,0,.02);
}

/* Badge enhancements */
.badge {
  padding: 0.5em 0.75em;
  font-weight: 600;
  border-radius: 6px;
}

/* Avatar placeholder */
.avatar-placeholder {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--primary-light);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
}

/* Progress bars */
.progress {
  height: 8px;
  border-radius: 10px;
  overflow: hidden;
  background-color: #e9ecef;
}

.progress-bar {
  border-radius: 10px;
  transition: width 1s ease;
}

/* Animations for elements */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

/* Staggered animation delays for cards */
.stats-card-container > div:nth-child(1) { animation-delay: 0.1s; }
.stats-card-container > div:nth-child(2) { animation-delay: 0.2s; }
.stats-card-container > div:nth-child(3) { animation-delay: 0.3s; }
.stats-card-container > div:nth-child(4) { animation-delay: 0.4s; }

/* Enhanced dropdown menus */
.dropdown-menu {
  border: none;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  padding: 0.5rem 0;
}

.dropdown-item {
  padding: 0.5rem 1.25rem;
  font-weight: 500;
  transition: all 0.2s;
}

.dropdown-item:hover {
  background-color: var(--primary-light);
  color: var(--primary-color);
}

.dropdown-item i {
  margin-right: 0.5rem;
  color: var(--secondary-color);
}

.dropdown-item:hover i {
  color: var(--primary-color);
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .dashboard-welcome {
    padding: 1.5rem;
  }
  
  .welcome-stats {
    flex-wrap: wrap;
  }
  
  .welcome-stat {
    flex: 1 0 45%;
    margin-bottom: 10px;
  }
  
  .card-body {
    padding: 1.25rem;
  }
}

@media (max-width: 768px) {
  .welcome-stats {
    margin-top: 1.5rem;
  }
  
  .welcome-stat {
    flex: 1 0 100%;
    margin-bottom: 10px;
  }
  
  .dashboard-welcome-content {
    flex-direction: column;
    text-align: center;
  }
  
  .welcome-icon {
    margin-right: 0;
    margin-bottom: 15px;
  }
  
  .welcome-actions {
    margin-left: 0;
    margin-top: 20px;
    flex-direction: row;
    justify-content: center;
  }
}

/* Timeline items */
.timeline-wrapper {
  position: relative;
}

.timeline-item {
  position: relative;
  padding-left: 20px;
  margin-bottom: 15px;
}

.timeline-badge {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1; 
  border-radius: 10px;
}
 
::-webkit-scrollbar-thumb {
  background: #c1c9d6; 
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a3aeb9; 
}

/* Utilities */
.shadow-hover {
  transition: box-shadow 0.3s ease, transform 0.3s ease;
}

.shadow-hover:hover {
  box-shadow: 0 .5rem 1.5rem rgba(0,0,0,.1);
  transform: translateY(-3px);
}

.rounded-lg {
  border-radius: 1rem;
}

.overflow-hidden {
  overflow: hidden;
}

.bg-gradient-primary {
  background: var(--gradient-primary);
}

.bg-gradient-success {
  background: var(--gradient-success);
}

.bg-gradient-info {
  background: var(--gradient-info);
}

.bg-gradient-warning {
  background: var(--gradient-warning);
}

.bg-gradient-danger {
  background: var(--gradient-danger);
}

.text-gradient {
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Enhanced notification toasts */
.toast {
  border: none !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  border-radius: 10px;
}

/* 为未渲染的图表添加加载动画 */
.chart-container:not(.chart-initialized) {
  position: relative;
  min-height: 250px;
}

.chart-container:not(.chart-initialized)::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  border: 4px solid rgba(79, 70, 229, 0.2);
  border-radius: 50%;
  border-top-color: #4f46e5;
  animation: spin 1s linear infinite;
  z-index: 10;
}

.chart-container:not(.chart-initialized)::after {
  content: "正在加载图表...";
  position: absolute;
  top: calc(50% + 30px);
  left: 50%;
  transform: translateX(-50%);
  font-size: 14px;
  color: #4f46e5;
  font-weight: 500;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* 图表容器样式优化 */
.chart-container {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  overflow: visible !important;
  height: 100%;
  width: 100%;
  min-height: 250px;
  padding: 15px;
}

.chart-container canvas {
  width: 100% !important;
  height: 100% !important;
}

.chart-container.chart-initialized {
  animation: fadeIn 0.6s ease-in-out;
}

/* 渐变出现的动画 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 确保图表卡片内容居中 */
.card-body.d-flex.align-items-center.justify-content-center {
  padding: 1.5rem;
  min-height: 300px;
}

/* 图表实际元素确保可见 */
canvas[id$="Chart"] {
  display: block !important;
  visibility: visible !important;
  max-width: 100%;
  max-height: 100%;
} 
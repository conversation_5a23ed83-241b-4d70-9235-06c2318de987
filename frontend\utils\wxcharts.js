/*
 * wxcharts.js v1.0
 * 图表组件，绘制饼图、折线图
 */

let config = {
  fontSize: 10,
  animation: true,
  timing: 'easeInOut',
  duration: 1000,
  color: ['#1890FF', '#2FC25B', '#FACC14', '#223273', '#8543E0', '#13C2C2', '#3436C7', '#F04864']
};

function drawPieChart(opts) {
  let context = wx.createCanvasContext(opts.canvasId);
  let series = opts.series;
  let pieRadius = Math.min(opts.width, opts.height) / 2 - 30;
  let centerPosition = {
    x: opts.width / 2,
    y: opts.height / 2
  };
  
  // 计算总数
  let total = 0;
  for (let i = 0; i < series.length; i++) {
    total += series[i].data;
  }
  
  // 绘制扇形
  let startAngle = 0;
  for (let i = 0; i < series.length; i++) {
    let item = series[i];
    let percentage = item.data / total;
    let endAngle = startAngle + percentage * 2 * Math.PI;
    
    // 绘制扇形
    context.beginPath();
    context.moveTo(centerPosition.x, centerPosition.y);
    context.arc(centerPosition.x, centerPosition.y, pieRadius, startAngle, endAngle);
    context.closePath();
    context.setFillStyle(item.color || config.color[i % config.color.length]);
    context.fill();
    
    // 绘制标签
    if (opts.dataLabel) {
      let labelAngle = startAngle + (endAngle - startAngle) / 2;
      let labelPosition = {
        x: centerPosition.x + (pieRadius * 0.7) * Math.cos(labelAngle),
        y: centerPosition.y + (pieRadius * 0.7) * Math.sin(labelAngle)
      };
      
      context.setFillStyle('#FFFFFF');
      context.setFontSize(config.fontSize);
      context.setTextAlign('center');
      context.setTextBaseline('middle');
      let percentText = Math.round(percentage * 100) + '%';
      context.fillText(percentText, labelPosition.x, labelPosition.y);
    }
    
    // 绘制图例
    let legendPosition = {
      x: centerPosition.x - pieRadius - 10,
      y: centerPosition.y + pieRadius + 20 + i * 25
    };
    
    context.beginPath();
    context.setFillStyle(item.color || config.color[i % config.color.length]);
    context.fillRect(legendPosition.x, legendPosition.y, 15, 15);
    
    context.setFillStyle('#333333');
    context.setFontSize(config.fontSize);
    context.setTextAlign('left');
    context.setTextBaseline('middle');
    context.fillText(item.name, legendPosition.x + 20, legendPosition.y + 7.5);
    
    startAngle = endAngle;
  }
  
  context.draw();
}

function drawLineChart(opts) {
  let context = wx.createCanvasContext(opts.canvasId);
  let categories = opts.categories;
  let series = opts.series[0];
  let data = series.data;
  
  // 计算图表区域
  let chartArea = {
    x: 50,
    y: 30,
    width: opts.width - 70,
    height: opts.height - 80
  };
  
  // Y轴
  let yAxisOptions = opts.yAxis || {};
  let yMin = yAxisOptions.min !== undefined ? yAxisOptions.min : Math.min(...data);
  let yMax = yAxisOptions.max !== undefined ? yAxisOptions.max : Math.max(...data);
  let yStep = (yMax - yMin) / 5;
  
  // 绘制Y轴
  context.beginPath();
  context.setStrokeStyle('#CCCCCC');
  context.moveTo(chartArea.x, chartArea.y);
  context.lineTo(chartArea.x, chartArea.y + chartArea.height);
  context.stroke();
  
  // 绘制Y轴刻度和网格线
  for (let i = 0; i <= 5; i++) {
    let value = yMax - i * yStep;
    let y = chartArea.y + (i / 5) * chartArea.height;
    
    // 刻度值
    context.setFillStyle('#666666');
    context.setFontSize(config.fontSize);
    context.setTextAlign('right');
    context.setTextBaseline('middle');
    let formatFn = yAxisOptions.format || (val => val);
    context.fillText(formatFn(value), chartArea.x - 5, y);
    
    // 网格线
    context.beginPath();
    context.setStrokeStyle('#EEEEEE');
    context.moveTo(chartArea.x, y);
    context.lineTo(chartArea.x + chartArea.width, y);
    context.stroke();
  }
  
  // X轴
  context.beginPath();
  context.setStrokeStyle('#CCCCCC');
  context.moveTo(chartArea.x, chartArea.y + chartArea.height);
  context.lineTo(chartArea.x + chartArea.width, chartArea.y + chartArea.height);
  context.stroke();
  
  // 绘制X轴类别
  let xStep = chartArea.width / (categories.length - 1);
  for (let i = 0; i < categories.length; i++) {
    let x = chartArea.x + i * xStep;
    let y = chartArea.y + chartArea.height;
    
    context.setFillStyle('#666666');
    context.setFontSize(config.fontSize);
    context.setTextAlign('center');
    context.setTextBaseline('top');
    context.fillText(categories[i], x, y + 5);
  }
  
  // 绘制数据线
  context.beginPath();
  context.setStrokeStyle('#1890FF');
  context.setLineWidth(2);
  
  for (let i = 0; i < data.length; i++) {
    let x = chartArea.x + i * xStep;
    let y = chartArea.y + chartArea.height - ((data[i] - yMin) / (yMax - yMin)) * chartArea.height;
    
    if (i === 0) {
      context.moveTo(x, y);
    } else {
      context.lineTo(x, y);
    }
    
    // 绘制数据点
    if (opts.dataPointShape) {
      context.setFillStyle('#FFFFFF');
      context.setStrokeStyle('#1890FF');
      context.setLineWidth(2);
      context.beginPath();
      context.arc(x, y, 3, 0, 2 * Math.PI);
      context.closePath();
      context.fill();
      context.stroke();
    }
    
    // 绘制数据标签
    if (opts.dataLabel) {
      context.setFillStyle('#666666');
      context.setFontSize(config.fontSize);
      context.setTextAlign('center');
      context.setTextBaseline('bottom');
      let formatFn = series.format || (val => val);
      context.fillText(formatFn(data[i]), x, y - 10);
    }
  }
  
  context.stroke();
  
  // 绘制图例
  context.beginPath();
  context.setFillStyle('#1890FF');
  context.fillRect(chartArea.x, chartArea.y - 20, 15, 8);
  
  context.setFillStyle('#333333');
  context.setFontSize(config.fontSize);
  context.setTextAlign('left');
  context.setTextBaseline('middle');
  context.fillText(series.name, chartArea.x + 20, chartArea.y - 16);
  
  context.draw();
}

function wxCharts(opts) {
  opts = opts || {};
  if (!opts.canvasId) {
    throw new Error('canvasId must be set for wxCharts');
  }
  
  let chartType = opts.type || 'pie';
  switch (chartType) {
    case 'pie':
      drawPieChart(opts);
      break;
    case 'line':
      drawLineChart(opts);
      break;
    default:
      console.error('Unsupported chart type: ' + chartType);
  }
}

module.exports = wxCharts; 
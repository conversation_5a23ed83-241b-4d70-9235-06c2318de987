import os
import re

def update_html_templates():
    """更新HTML模板中的CDN引用为本地路径"""
    templates_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'templates')
    
    # CDN到本地资源的映射
    cdn_to_local = {
        'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css': '/static/css/bootstrap.min.css',
        'https://stackpath.bootstrapcdn.com/bootstrap/5.3.0/css/bootstrap.min.css': '/static/css/bootstrap.min.css',
        'https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css': '/static/css/bootstrap.min.css',
        'https://unpkg.com/bootstrap@5.3.0/dist/css/bootstrap.min.css': '/static/css/bootstrap.min.css',
        
        'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js': '/static/js/bootstrap.bundle.min.js',
        'https://stackpath.bootstrapcdn.com/bootstrap/5.3.0/js/bootstrap.bundle.min.js': '/static/js/bootstrap.bundle.min.js',
        'https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js': '/static/js/bootstrap.bundle.min.js',
        'https://unpkg.com/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js': '/static/js/bootstrap.bundle.min.js',
        
        'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css': '/static/css/all.min.css',
        'https://use.fontawesome.com/releases/v6.4.0/css/all.css': '/static/css/all.min.css',
        
        'https://cdn.jsdelivr.net/npm/@bootcss/www.bootcss.com@0.0.5/dist/img/favicon.ico': '/static/img/favicon.ico',
        'https://www.bootcss.com/favicon.ico': '/static/img/favicon.ico'
    }

    total_files = 0
    updated_files = 0
    
    # 遍历所有HTML文件
    for root, _, files in os.walk(templates_dir):
        for file in files:
            if file.endswith('.html'):
                file_path = os.path.join(root, file)
                total_files += 1
                print(f"正在处理: {file_path}")
                
                try:
                    # 读取文件内容
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 检查是否有任何CDN链接需要替换
                    original_content = content
                    
                    # 使用正则表达式查找更多可能的CDN链接模式
                    # 查找链接标签中的href属性
                    content = re.sub(
                        r'<link[^>]*href=[\'"]?(https?://[^\'"\s>]+)[\'"]?[^>]*>',
                        lambda m: m.group(0).replace(m.group(1), cdn_to_local.get(m.group(1), m.group(1))),
                        content
                    )
                    
                    # 查找脚本标签中的src属性
                    content = re.sub(
                        r'<script[^>]*src=[\'"]?(https?://[^\'"\s>]+)[\'"]?[^>]*>',
                        lambda m: m.group(0).replace(m.group(1), cdn_to_local.get(m.group(1), m.group(1))),
                        content
                    )
                    
                    # 直接替换已知的CDN URL
                    for cdn_url, local_path in cdn_to_local.items():
                        content = content.replace(cdn_url, local_path)
                    
                    # 如果内容有变化，写回文件
                    if content != original_content:
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(content)
                        updated_files += 1
                        print(f"已更新模板文件: {file_path}")
                except Exception as e:
                    print(f"更新模板 {file_path} 失败: {str(e)}")
    
    print(f"模板更新完成: 共处理 {total_files} 个文件，更新了 {updated_files} 个文件")
    return updated_files > 0

if __name__ == "__main__":
    print("===== 开始更新HTML模板中的CDN引用 =====")
    update_html_templates()
    print("===== 更新完成 =====")
    print("请重启Flask应用以使更改生效！") 
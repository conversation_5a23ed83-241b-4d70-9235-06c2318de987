#!/usr/bin/env python3
"""
Migration script to add online status fields to the User table in MySQL database
"""
import sys
import os
import pymysql

# Try to import config to get database connection details
try:
    # Add the current directory to the Python path
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    from config import get_config
    
    config = get_config()
    DB_USER = config.DB_USER
    DB_PASSWORD = config.DB_PASSWORD
    DB_HOST = config.DB_HOST
    DB_NAME = config.DB_NAME
    
    print(f"使用配置信息连接到MySQL数据库: {DB_NAME}")
except ImportError:
    print("无法导入配置，将使用默认配置")
    DB_USER = 'root'
    DB_PASSWORD = '123456'
    DB_HOST = 'localhost'
    DB_NAME = 'quiz_app'

def add_columns():
    """添加在线状态和最后活跃时间列"""
    conn = None
    try:
        # 连接到MySQL数据库
        conn = pymysql.connect(
            host=DB_HOST,
            user=DB_USER,
            password=DB_PASSWORD,
            database=DB_NAME
        )
        
        cursor = conn.cursor()
        
        # 检查是否存在is_online列
        cursor.execute("DESCRIBE user")
        columns = cursor.fetchall()
        column_names = [col[0] for col in columns]
        
        # 添加新列
        if 'is_online' not in column_names:
            cursor.execute("ALTER TABLE user ADD COLUMN is_online BOOLEAN DEFAULT 0")
            print("添加了is_online列")
        else:
            print("is_online列已存在")
            
        if 'last_active' not in column_names:
            cursor.execute("ALTER TABLE user ADD COLUMN last_active DATETIME")
            print("添加了last_active列")
        else:
            print("last_active列已存在")
        
        # 提交更改
        conn.commit()
        print("数据库更新成功")
        
    except Exception as e:
        print(f"错误: {e}")
        return False
    finally:
        if conn:
            conn.close()
    
    return True

if __name__ == "__main__":
    print("开始添加用户在线状态字段...")
    if add_columns():
        print("用户在线状态字段添加成功！")
    else:
        print("用户在线状态字段添加失败！")
        sys.exit(1) 
/**
 * 图表错误信息清理工具
 * 这个脚本会移除图表容器中和周围的错误信息
 */

(function() {
    // 在页面加载后清理图表错误
    document.addEventListener('DOMContentLoaded', cleanupChartErrors);
    window.addEventListener('load', cleanupChartErrors);
    
    // 定期清理错误信息
    setInterval(cleanupChartErrors, 1000);
    
    // 使用MutationObserver监视DOM变化
    const observer = new MutationObserver(function(mutations) {
        cleanupChartErrors();
    });
    
    // 开始观察DOM变化
    document.addEventListener('DOMContentLoaded', function() {
        observer.observe(document.body, { 
            childList: true, 
            subtree: true 
        });
        
        // 10秒后断开观察器以避免性能问题
        setTimeout(function() {
            observer.disconnect();
        }, 10000);
    });
    
    // 清理图表错误信息的函数
    function cleanupChartErrors() {
        // 清理图表容器中的错误文本
        const chartContainers = document.querySelectorAll('.chart-container');
        chartContainers.forEach(container => {
            const canvas = container.querySelector('canvas');
            if (!canvas) return;
            
            // 强制保持canvas可见
            canvas.style.display = 'block';
            canvas.style.visibility = 'visible';
            canvas.style.opacity = '1';
            
            // 移除容器中不是canvas的所有元素
            Array.from(container.childNodes).forEach(node => {
                if (node !== canvas && node.nodeType !== Node.COMMENT_NODE) {
                    container.removeChild(node);
                }
            });
        });
        
        // 清理所有图表周围的错误文本
        const canvases = document.querySelectorAll('canvas[id$="Chart"]');
        canvases.forEach(canvas => {
            const parent = canvas.parentNode;
            if (!parent) return;
            
            // 移除canvas旁边的所有文本节点
            Array.from(parent.childNodes).forEach(node => {
                if (node !== canvas && node.nodeType === Node.TEXT_NODE && node.textContent.trim() !== '') {
                    parent.removeChild(node);
                }
                
                // 移除可能包含错误信息的其他元素
                if (node !== canvas && node.nodeType === Node.ELEMENT_NODE) {
                    const text = node.textContent.toLowerCase();
                    if (text.includes('error') || text.includes('exception') || 
                        text.includes('failed') || text.includes('canvas')) {
                        parent.removeChild(node);
                    }
                }
            });
        });
    }
})(); 
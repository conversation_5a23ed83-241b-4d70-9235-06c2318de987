/* review.wxss */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f6fa;
  overflow: hidden;
  padding-top: env(safe-area-inset-top); /* 适配不同手机的安全区域 */
}

/* 标签导航栏样式 */
.tab-nav {
  display: flex;
  background-color: #fff;
  padding: 0 5px;
  border-bottom: 1rpx solid #eee;
  position: relative;
  z-index: 1;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.tab-item {
  flex: 1;
  padding: 15px 5px;
  text-align: center;
  font-size: 14px;
  color: #666;
  position: relative;
  transition: all 0.2s ease;
}

.tab-text {
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  transition: color 0.2s ease;
}

.tab-item.active {
  color: #3574f0;
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 3px;
  background-color: #3574f0;
  border-radius: 2px;
  transition: width 0.2s ease;
}

/* 问题滚动容器 */
.question-scroll-view {
  flex: 1;
  overflow: hidden;
  position: relative;
}

/* 问题容器 - 添加过渡动画 */
.questions-container {
  padding: 15px;
  opacity: 1;
  transition: opacity 0.2s ease;
  animation: fadeIn 0.3s ease-in-out;
  will-change: transform; /* 告诉浏览器这个元素会变化，优化渲染性能 */
  transform: translateZ(0); /* 强制GPU加速 */
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 问题卡片样式 - 优化渲染 */
.question-card {
  background-color: #fff;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transform: translateZ(0); /* 强制GPU加速 */
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.question-type {
  color: #3574f0;
  font-weight: bold;
  font-size: 16px;
}

.question-number {
  color: #999;
  font-size: 14px;
}

.question-content {
  margin-bottom: 15px;
}

.question-text {
  font-size: 16px;
  line-height: 1.6;
  color: #333;
}

/* 选项列表样式 */
.options-list {
  margin-bottom: 15px;
}

.option-item {
  display: flex;
  align-items: flex-start;
  padding: 10px 15px;
  margin-bottom: 8px;
  border-radius: 6px;
  background-color: #f7f8fa;
  position: relative;
  border: 1px solid transparent;
}

.option-item.correct {
  background-color: #e6f7e8 !important;
  border: 2px solid #4caf50 !important;
  box-shadow: 0 1px 3px rgba(76, 175, 80, 0.2) !important;
}

.option-letter {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #eee;
  color: #666;
  font-size: 14px;
  margin-right: 10px;
  flex-shrink: 0;
}

.option-item.correct .option-letter {
  background-color: #4caf50 !important;
  color: #fff !important;
  font-weight: bold !important;
}

.option-text {
  flex: 1;
  font-size: 15px;
  line-height: 1.5;
  color: #444;
  padding-top: 3px;
}

.option-item.correct .option-text {
  color: #1b5e20 !important;
  font-weight: 500 !important;
}

.correct-mark {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #4caf50;
  font-size: 20px;
  font-weight: bold;
}

/* 判断题选项样式 */
.judgment-options {
  display: flex;
  justify-content: center; /* 中央对齐 */
  margin-bottom: 15px;
  gap: 20px; /* 增加选项之间的间距 */
  padding: 10px; /* 增加内边距 */
}

.judgment-option {
  flex: 0 1 auto; /* 不再平分空间 */
  min-width: 90px; /* 设置最小宽度 */
  max-width: 120px; /* 设置最大宽度 */
  padding: 8px 15px; /* 减小内边距 */
  border-radius: 20px; /* 更圆润的边角 */
  background-color: #f7f8fa;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  border: 1px solid #e8e8e8;
  transition: all 0.2s ease;
}

.judgment-option.correct {
  background-color: #43c97c !important;
  border-color: #43c97c !important;
  box-shadow: 0 2px 4px rgba(67, 201, 124, 0.3) !important;
}

.judgment-text {
  font-size: 15px;
  color: #444;
  font-weight: 500;
  text-align: center;
}

.judgment-option.correct .judgment-text {
  color: #fff !important;
  font-weight: bold !important;
}

.judgment-option .correct-mark {
  margin-left: 5px;
  font-size: 15px;
  color: #fff;
}

/* 判断题选项样式 - 纵向排列 */
.judgment-options.vertical {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 15px;
}

.judgment-options.vertical .judgment-option {
  width: 120px;
  padding: 8px 15px;
  margin-bottom: 8px;
  border-radius: 6px;
  background-color: #f7f8fa;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.judgment-options.vertical .judgment-option.correct {
  background-color: #43c97c !important;
  border: 2px solid #1eae5b !important;
}

.judgment-options.vertical .judgment-option.correct .judgment-text {
  color: #fff !important;
  font-weight: bold;
}

/* 答案部分样式 */
.answer-section {
  margin-bottom: 10px;
  background-color: #f0f8ff;
  padding: 10px;
  border-radius: 6px;
  border-left: 3px solid #3574f0;
}

.answer-header {
  font-size: 14px;
  color: #3574f0;
  font-weight: bold;
  margin-bottom: 5px;
}

.answer-content {
  font-size: 16px;
  color: #1976d2;
  font-weight: bold;
  line-height: 1.7;
  background: #e3f2fd;
  border-radius: 4px;
  padding: 6px 10px;
  display: inline-block;
  margin-top: 2px;
}

/* 解析部分样式 */
.explanation-section {
  background-color: #fff9f0;
  padding: 10px;
  border-radius: 6px;
  border-left: 3px solid #ff9800;
}

.explanation-header {
  font-size: 14px;
  color: #ff9800;
  font-weight: bold;
  margin-bottom: 5px;
}

.explanation-content {
  font-size: 14px;
  color: #555;
  line-height: 1.5;
}

/* 空内容提示 */
.empty-tips {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 50px 0;
  color: #999;
  font-size: 14px;
}

/* 加载中样式 */
.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.loading {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3574f0;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 10px;
  font-size: 14px;
  color: #666;
}

/* 删除进度条滑块样式 */
.slider-container,
.slider-track,
.slider-thumb,
.slider-thumb:active,
.slider-thumb.dragging {
  display: none;
}

/* 导航箭头样式 */
.navigation-arrows {
  position: fixed;
  right: 10px;
  bottom: 150px;
  display: flex;
  flex-direction: column;
  gap: 20px; /* 增加箭头之间的间距 */
  z-index: 90;
  background-color: rgba(255, 255, 255, 0.4);
  padding: 10px 5px; /* 增加上下内边距 */
  border-radius: 18px;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.08);
}

.nav-arrow {
  width: 32px; /* 进一步减小尺寸 */
  height: 32px;
  background-color: rgba(53, 116, 240, 0.7);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.nav-arrow:active {
  transform: scale(0.9);
  background-color: rgba(44, 96, 201, 0.8);
  opacity: 1;
}

/* 悬停或触摸时恢复完全不透明 */
.navigation-arrows:hover,
.navigation-arrows.active {
  background-color: rgba(255, 255, 255, 0.7);
}

.nav-arrow:hover {
  background-color: rgba(53, 116, 240, 0.9);
  opacity: 1;
}

.arrow-icon {
  color: white;
  font-size: 16px; /* 减小字体 */
  font-weight: bold;
} 
/* pages/quiz/multiple/multiple.wxss */
.container {
  padding: 30rpx;
  min-height: 100vh;
  background-color: #f5f7fa;
  box-sizing: border-box;
}

/* 进度条 */
.progress-bar {
  width: 100%;
  height: 10rpx;
  background-color: #e0e0e0;
  border-radius: 5rpx;
  overflow: hidden;
}

.progress-inner {
  height: 100%;
  background-color: #FF9800;
  transition: width 0.2s ease;
}

.question-counter {
  display: block;
  text-align: right;
  margin: 10rpx 0 30rpx;
  font-size: 26rpx;
  color: #999;
}

/* 课程筛选样式 */
.course-filter {
  margin-bottom: 30rpx;
  background-color: #fff;
  padding: 20rpx 0;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.course-scroll {
  width: 100%;
  white-space: nowrap;
}

.course-tabs {
  padding: 0 20rpx;
  display: flex;
}

.course-tab {
  display: inline-flex;
  align-items: center;
  padding: 12rpx 24rpx;
  margin-right: 16rpx;
  font-size: 26rpx;
  color: #666;
  background-color: #f5f7fa;
  border-radius: 30rpx;
  position: relative;
  transition: all 0.3s;
}

.course-tab.active {
  color: #fff;
  background-color: #FF9800;
  font-weight: 500;
}

.course-count {
  margin-left: 8rpx;
  font-size: 22rpx;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 2rpx 10rpx;
  border-radius: 20rpx;
}

.course-tab.active .course-count {
  background-color: rgba(255, 255, 255, 0.3);
  color: #fff;
}

/* 题目容器 */
.question-container {
  background-color: white;
  border-radius: 15rpx;
  padding: 40rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.question {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
  line-height: 1.5;
}

.question-hint {
  font-size: 26rpx;
  color: #999;
  display: block;
  margin-bottom: 40rpx;
}

/* 选项列表 */
.options-list {
  margin-bottom: 30rpx;
}

/* 选项样式 */
.option {
  display: flex;
  align-items: center;
  padding: 25rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  background-color: #f9f9f9;
  border: 2rpx solid #eee;
  position: relative;
  overflow: hidden;
}

/* 添加左侧颜色条 */
.option::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 8rpx;
  background-color: #eee;
}

/* 选中状态 (只在未提交时显示) */
.option.selected {
  background-color: #fff8e6;
  border-color: #FF9800;
}

.option.selected::before {
  background-color: #FF9800;
}

/* 正确答案状态 */
.option.correct {
  background-color: #e8f8e8;
  border-color: #4CAF50;
}

.option.correct::before {
  background-color: #4CAF50;
}

/* 错误答案状态 */
.option.wrong {
  background-color: #ffe8e8;
  border-color: #F44336;
}

.option.wrong::before {
  background-color: #F44336;
}

.option-letter {
  width: 50rpx;
  height: 50rpx;
  line-height: 50rpx;
  text-align: center;
  border-radius: 25rpx;
  background-color: #eee;
  font-size: 28rpx;
  margin-right: 20rpx;
  color: #666;
}

.option.selected .option-letter {
  background-color: #FF9800;
  color: white;
}

.option.correct .option-letter {
  background-color: #4CAF50;
  color: white;
}

.option.wrong .option-letter {
  background-color: #F44336;
  color: white;
}

.option-text {
  font-size: 30rpx;
  color: #333;
  flex: 1;
  line-height: 1.4;
}

/* 正确答案标记 */
.answer-mark {
  margin-left: 10rpx;
}

.correct-mark {
  color: #4CAF50;
  font-size: 36rpx;
  font-weight: bold;
}

.wrong-mark {
  color: #F44336;
  font-size: 36rpx;
  font-weight: bold;
}

/* 正确答案提示 */
.correct-answers {
  margin: 20rpx 0 30rpx;
  background: #fff;
  padding: 20rpx;
  border-radius: 10rpx;
  position: relative;
  overflow: hidden;
}

/* 添加左侧颜色条 */
.correct-answers::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 8rpx;
  background-color: #FF9800;
}

.correct-answers-header {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  padding-left: 20rpx;
  font-weight: bold;
}

.correct-answers-content {
  display: flex;
  flex-wrap: wrap;
  padding-left: 20rpx;
}

.correct-answer-circle {
  width: 50rpx;
  height: 50rpx;
  line-height: 50rpx;
  border-radius: 25rpx;
  background-color: #4CAF50;
  color: white;
  text-align: center;
  font-size: 26rpx;
  margin-right: 15rpx;
  margin-bottom: 10rpx;
}

/* 结果提示 */
.result-tip {
  text-align: left;
  margin: 20rpx 0 30rpx;
  padding: 20rpx;
  background: #fff;
  border-radius: 10rpx;
  position: relative;
  overflow: hidden;
}

/* 添加左侧颜色条 */
.result-tip::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 8rpx;
  background-color: #FF9800;
}

.correct-tip, .wrong-tip {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  padding-left: 20rpx;
}

.correct-tip {
  color: #4CAF50;
}

.wrong-tip {
  color: #F44336;
}

/* 解析部分 */
.explanation-container {
  background-color: white;
  padding: 30rpx;
  border-radius: 12rpx;
  margin: 30rpx 0;
  border-left: 8rpx solid #ffc107;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.explanation-header {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #eee;
}

.explanation-text {
  font-size: 30rpx;
  color: #666;
  line-height: 1.5;
}

/* 模态对话框 */
.modal-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal {
  width: 80%;
  background-color: white;
  border-radius: 15rpx;
  overflow: hidden;
}

.modal-header {
  padding: 30rpx;
  text-align: center;
  border-bottom: 1rpx solid #eee;
}

.modal-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
}

.modal-content {
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.modal-content text {
  font-size: 30rpx;
  color: #666;
  line-height: 1.6;
  text-align: center;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #eee;
}

.modal-btn {
  flex: 1;
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  font-size: 32rpx;
  border-radius: 0;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background-color: #FF9800;
  color: white;
  font-weight: bold;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  justify-content: center;
}

.action-btn {
  width: 60%;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 45rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.submit-btn {
  background-color: #FF9800;
  color: white;
}

.submit-btn[disabled] {
  background-color: #ffd699;
  color: #fff;
}

.next-btn {
  background-color: #FF9800;
  color: white;
}

/* 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 100rpx;
}

.loading {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #FF9800;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999;
} 
<!-- exam.wxml -->
<wxs module="util">
  function formatPercentage(score, total) {
    if (total <= 0) return '0.0%';
    var percentage = score / total * 100;
    // 取一位小数
    var fixed = Math.round(percentage * 10) / 10;
    return fixed + '%';
  }

  module.exports = {
    formatPercentage: formatPercentage
  };
</wxs>

<view class="container">
  <!-- 顶部区域（状态栏+头部）-->
  <view class="header-area">
    <!-- 状态栏安全区域，与头部背景融合 -->
    <view class="status-bar-placeholder" style="height: {{statusBarHeight}}px; background: linear-gradient(135deg, {{courseColor}}, {{courseColor}});"></view>
    
    <!-- 头部背景 -->
    <view class="header-bg" style="background: linear-gradient(135deg, {{courseColor}}, {{courseColor}});"></view>
    
    <!-- 考试头部内容 -->
    <view class="exam-header">
      <view class="header-content">
        <!-- 添加返回按钮 -->
        <view class="back-btn" bindtap="tryExitExam">
          <text class="back-icon">←</text>
        </view>
        <view class="exam-title">{{courseTitle || '未知课程'}}考试</view>
        <view class="timer">{{formattedTime}}</view>
      </view>
    </view>
    
    <!-- 进度指示器 - 优化版 -->
    <view class="progress-container">
      <view class="progress-bar">
        <!-- 单选题进度段 -->
        <view class="progress-segment single-segment" 
              style="width: {{singlePercent}}%; background: {{singlePercent > 0 ? '#4e8df7' : 'transparent'}};">
          <view class="progress-inner" style="width: {{singleProgress}}%;"></view>
        </view>
        
        <!-- 多选题进度段 -->
        <view class="progress-segment multiple-segment" 
              style="width: {{multiplePercent}}%; background: {{multiplePercent > 0 ? '#7e57c2' : 'transparent'}};">
          <view class="progress-inner" style="width: {{multipleProgress}}%;"></view>
        </view>
        
        <!-- 判断题进度段 -->
        <view class="progress-segment judgment-segment" 
              style="width: {{judgmentPercent}}%; background: {{judgmentPercent > 0 ? '#ff9800' : 'transparent'}};">
          <view class="progress-inner" style="width: {{judgmentProgress}}%;"></view>
        </view>
        
        <!-- 填空题进度段 -->
        <view class="progress-segment fillblank-segment" 
              style="width: {{fillblankPercent}}%; background: {{fillblankPercent > 0 ? '#4caf50' : 'transparent'}};">
          <view class="progress-inner" style="width: {{fillblankProgress}}%;"></view>
        </view>
      </view>
      
      <view class="question-progress">
        <view class="question-types">
          <view class="type-indicator" wx:if="{{typeCounts.single > 0}}">
            <view class="type-dot single-dot"></view>
            <text>单选 {{currentTypeCounts.single}}/{{typeCounts.single}}</text>
          </view>
          <view class="type-indicator" wx:if="{{typeCounts.multiple > 0}}">
            <view class="type-dot multiple-dot"></view>
            <text>多选 {{currentTypeCounts.multiple}}/{{typeCounts.multiple}}</text>
          </view>
          <view class="type-indicator" wx:if="{{typeCounts.judgment > 0}}">
            <view class="type-dot judgment-dot"></view>
            <text>判断 {{currentTypeCounts.judgment}}/{{typeCounts.judgment}}</text>
          </view>
          <view class="type-indicator" wx:if="{{typeCounts.fillblank > 0}}">
            <view class="type-dot fillblank-dot"></view>
            <text>填空 {{currentTypeCounts.fillblank}}/{{typeCounts.fillblank}}</text>
          </view>
        </view>
        <text class="overall-progress">总进度：{{currentIndex + 1}}/{{questions.length}}</text>
      </view>
    </view>
  </view>

  <!-- 加载中 -->
  <view class="loading" wx:if="{{isLoading}}">
    <van-loading type="spinner" color="#1989fa" size="24px" />
    <text class="loading-text">正在准备考试题目...</text>
  </view>

  <!-- 考试内容 -->
  <block wx:if="{{!isLoading && questions.length > 0}}">

    <!-- 题目区域 -->
    <view class="question-area" wx:if="{{!isLoading && questions.length > 0 && !isSubmitted}}">
      <view class="question-container">
        <!-- 题目类型标签 -->
        <view class="question-type">
          <block wx:if="{{questions[currentIndex].type === 'single'}}">
            <text class="type-label single">单选题</text>
          </block>
          <block wx:elif="{{questions[currentIndex].type === 'multiple'}}">
            <text class="type-label multiple">多选题</text>
          </block>
          <block wx:elif="{{questions[currentIndex].type === 'judgment'}}">
            <text class="type-label judgment">判断题</text>
          </block>
          <block wx:elif="{{questions[currentIndex].type === 'fillblank'}}">
            <text class="type-label fillblank">填空题</text>
          </block>
        </view>
        
        <!-- 题目内容 -->
        <view class="question-content">
          <text wx:if="{{questions[currentIndex].content}}">{{questions[currentIndex].content}}</text>
          <text wx:else class="debug-info">题目内容缺失</text>
          
          <!-- 调试信息 -->
          <view class="debug-box" wx:if="{{!questions[currentIndex].content}}">
            <view>题目类型: {{questions[currentIndex].type || '未知'}}</view>
            <view>题目ID: {{questions[currentIndex].id || '未知'}}</view>
            <view>题目数据: {{questions[currentIndex] ? '已加载' : '未加载'}}</view>
          </view>
        </view>
        
        <!-- 选项列表 -->
        <view class="options-container" data-force-update="{{forceUpdate}}">
          <!-- 单选题选项 -->
          <block wx:if="{{questions[currentIndex].type === 'single'}}">
            <view
              wx:for="{{questions[currentIndex].options}}"
              wx:key="index"
              class="option {{answers[currentIndex].selected[0] === index ? 'selected' : ''}}"
              data-option-index="{{index}}"
              bindtap="selectOption"
            >
              <view class="option-prefix">{{['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'][index]}}</view>
              <view class="option-content">{{item}}</view>
              <view class="selected-mark"></view>
            </view>
          </block>
          
          <!-- 多选题最终版本 -->
          <block wx:if="{{questions[currentIndex].type === 'multiple'}}">
            <view class="multi-header">多选题 (可选多项)</view>
            <block wx:for="{{questions[currentIndex].options}}" wx:key="index">
              <view class="multi-option {{multiSelectedStatus[index] ? 'multi-selected' : ''}}" 
                    data-index="{{index}}" 
                    bindtap="multiSelect">
                <view class="multi-letter">{{['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'][index]}}</view>
                <view class="multi-content">{{item}}</view>
              </view>
            </block>
          </block>
          
          <!-- 判断题选项 -->
          <block wx:elif="{{questions[currentIndex].type === 'judgment'}}">
            <view
              wx:for="{{['正确', '错误']}}"
              wx:key="index"
              class="option judgment-option {{answers[currentIndex].selected[0] === index ? 'selected' : ''}}"
              data-option-index="{{index}}"
              bindtap="selectOption"
            >
              <view class="option-content">{{item}}</view>
              <view class="selected-mark"></view>
            </view>
          </block>
          
          <!-- 填空题输入 -->
          <block wx:elif="{{questions[currentIndex].type === 'fillblank'}}">
            <view class="fillblank-input-container">
              <input
                class="fillblank-input"
                placeholder="请输入答案"
                value="{{answers[currentIndex].selected[0] || ''}}"
                bindinput="inputBlankAnswer"
              />
            </view>
          </block>
        </view>
      </view>
    </view>

    <!-- 答题导航栏 -->
    <view class="navigation-bar" wx:if="{{!isLoading && questions.length > 0 && !isSubmitted}}">
      <view class="nav-btn prev" bindtap="prevQuestion" wx:if="{{currentIndex > 0}}">上一题</view>
      <view class="nav-btn next" bindtap="nextQuestion" wx:if="{{currentIndex < questions.length - 1}}">下一题</view>
      <view class="nav-btn submit" bindtap="trySubmitExam" wx:if="{{currentIndex === questions.length - 1}}">交卷</view>
    </view>

    <!-- 题目导航 -->
    <view class="question-nav" wx:if="{{!isLoading && questions.length > 0 && !isSubmitted}}">
      <view class="nav-header">
        <text>题目导航</text>
      </view>
      <view class="nav-grid">
        <view 
          wx:for="{{questions}}" 
          wx:key="index"
          class="nav-item {{currentIndex === index ? 'current' : ''}} {{answers[index].isAnswered ? 'answered' : ''}}"
          data-index="{{index}}"
          bindtap="goToQuestion"
        >
          {{index + 1}}
        </view>
      </view>
    </view>

    <!-- 考试结果 -->
    <view class="result-container" wx:if="{{isSubmitted}}">
      <view class="result-card">
        <!-- 科目信息区域 -->
        <view class="course-info">
          <view class="course-avatar" style="background-color: {{courseColor}}">
            <text>{{courseTitle && courseTitle.length > 0 ? courseTitle[0] : '未'}}</text>
          </view>
          <view class="course-details">
            <text class="course-title">{{courseTitle || '未知课程'}}考试</text>
            <text class="course-score">得分：{{actualScore}}分 (满分：{{totalPossibleScore}}分)</text>
          </view>
        </view>

        <!-- 分数显示 -->
        <view class="score-display">
          <view class="big-score">{{percentageScore}}</view>
          <view class="score-unit">分</view>
        </view>
        
        <!-- 统计信息 -->
        <view class="stats-row">
          <view class="stat-box">
            <text class="stat-label">正确题目</text>
            <text class="stat-value">{{score}}题</text>
          </view>
          <view class="stat-box">
            <text class="stat-label">错题数目</text>
            <text class="stat-value">{{totalScore - score}}题</text>
          </view>
          <view class="stat-box">
            <text class="stat-label">正确率</text>
            <text class="stat-value">{{util.formatPercentage(score, totalScore)}}</text>
          </view>
        </view>
        
        <!-- 答题用时 -->
        <view class="time-used">
          <text class="time-label">答题用时</text>
          <text class="time-value">{{examTimeUsed}}</text>
        </view>
        
        <!-- 答题情况 -->
        <view class="answer-status">
          <view class="answer-status-item">
            <text class="status-label">已答题目</text>
            <text class="status-value">{{answeredCount}}题</text>
          </view>
          <view class="answer-status-item">
            <text class="status-label">未答题目</text>
            <text class="status-value">{{unansweredCount}}题</text>
          </view>
        </view>

        <!-- 题型分值说明 -->
        <view class="score-rules">
          <view class="rules-title">题型分值</view>
          <view class="rules-list">
            <view class="rule-item">
              <text class="rule-label">单选题</text>
              <text class="rule-value">1分/题</text>
            </view>
            <view class="rule-item">
              <text class="rule-label">多选题</text>
              <text class="rule-value">1.5分/题</text>
            </view>
            <view class="rule-item">
              <text class="rule-label">判断题</text>
              <text class="rule-value">1分/题</text>
            </view>
            <view class="rule-item">
              <text class="rule-label">填空题</text>
              <text class="rule-value">2分/题</text>
            </view>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="action-buttons">
          <button class="review-btn" bindtap="viewWrongQuestions">查看错题</button>
          <button class="return-btn" bindtap="navigateBack">返回课程</button>
        </view>
      </view>
    </view>

    <!-- 退出确认对话框 -->
    <view class="modal" wx:if="{{showExitConfirm}}">
      <view class="modal-mask"></view>
      <view class="modal-content">
        <view class="modal-header">提示</view>
        <view class="modal-body">确定要退出考试吗？退出后答题记录将不会保存。</view>
        <view class="modal-footer">
          <button class="modal-btn cancel" bindtap="cancelExit">取消</button>
          <button class="modal-btn confirm" bindtap="confirmExit">确定</button>
        </view>
      </view>
    </view>
    
    <!-- 交卷确认对话框 -->
    <view class="modal" wx:if="{{showSubmitConfirm}}">
      <view class="modal-mask"></view>
      <view class="modal-content">
        <view class="modal-header">提示</view>
        <view class="modal-body">
          <view>您还有 {{unansweredCount}} 题未作答！</view>
          <view style="margin-top: 12px;">是否确定交卷？</view>
        </view>
        <view class="modal-footer">
          <button class="modal-btn cancel" bindtap="cancelSubmit">继续答题</button>
          <button class="modal-btn confirm" bindtap="confirmSubmit">确定交卷</button>
        </view>
      </view>
    </view>
  </block>
</view> 
import urllib.request
import os

def download_chartjs():
    chartjs_url = "https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"
    target_path = os.path.join('static', 'js', 'chart.min.js')
    
    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(target_path), exist_ok=True)
    
    print(f"Downloading Chart.js from {chartjs_url}")
    try:
        urllib.request.urlretrieve(chartjs_url, target_path)
        print(f"Successfully downloaded Chart.js to {target_path}")
        return True
    except Exception as e:
        print(f"Failed to download Chart.js: {e}")
        return False

if __name__ == "__main__":
    download_chartjs() 
// single.js
const app = getApp();

Page({
  data: {
    loading: true,
    questions: [],
    currentQuestionIndex: 0,
    selectedOption: null,
    isSubmitting: false,
    showResult: false, // 是否显示答案结果
    isCorrect: false, // 答案是否正确
    correctAnswer: null, // 正确答案
    progress: 0, // 进度百分比
    records: [], // 答题记录
    showConfirmFinish: false, // 是否显示完成确认对话框
    correctCount: 0, // 正确答案的数量
    courseId: null, // 课程ID
    mode: null, // 模式
    questionIds: [], // 题目ID数组
    currentIndex: 0, // 当前题目索引
    coursesList: [], // 课程列表
    activeCourse: 0, // 当前选中的课程ID，0表示全部
    courseTitle: '', // 课程标题
    fontSize: 'normal', // 字体大小
    startTime: 0, // 答题开始时间
    questionId: null // 题目ID
  },
  
  onLoad: function (options) {
    console.log('单选题页面加载，接收参数:', options);
    
    let mode = options.mode || 'normal';
    let courseId = options.courseId ? parseInt(options.courseId) : null;
    let courseTitle = options.courseTitle ? decodeURIComponent(options.courseTitle) : '';
    let questionId = options.questionId ? parseInt(options.questionId) : null;
    let fromWrong = options.fromWrong === 'true';
    
    this.setData({
      mode: mode,
      courseId: courseId,
      courseTitle: courseTitle,
      fontSize: app.globalData.fontSize || 'normal',
      startTime: Date.now() // 初始化答题开始时间
    });
    
    console.log(`页面模式: ${mode}, 课程ID: ${courseId}, 课程标题: ${courseTitle}, 题目ID: ${questionId}, 来自错题: ${fromWrong}`);
    
    // 检查缓存中是否有题目数据
    const cachedQuestions = wx.getStorageSync('currentQuestions');
    console.log('缓存中题目数据:', cachedQuestions ? `${cachedQuestions.length}道题目` : '无');
    
    // 处理重新练习模式 (retry)
    if (mode === 'retry' && cachedQuestions && cachedQuestions.length > 0) {
      console.log('检测到重新练习模式，使用缓存中的题目数据');
      
      // 如果是来自错题模式
      if (fromWrong) {
        console.log('来自错题模式的再次练习');
        // 筛选单选题 - 使用更宽松的条件
        const singleChoiceQuestions = cachedQuestions.filter(q => {
          // 检查各种可能的类型标识
          const isTypeSingle = q.type === 'single' || q.question_type === 'single';
          const hasCorrectAnswerNum = typeof q.answer === 'number' || 
                                     (Array.isArray(q.answer) && q.answer.length === 1) ||
                                     (typeof q.answer === 'string' && !q.answer.includes(','));
          
          // 详细记录每个题目的类型情况，帮助调试
          console.log(`题目ID:${q.id}, 题型检测:`, { 
            type: q.type, 
            question_type: q.question_type,
            answer: q.answer,
            isTypeSingle: isTypeSingle,
            hasCorrectAnswerNum: hasCorrectAnswerNum,
            willInclude: isTypeSingle || hasCorrectAnswerNum
          });
          
          // 只要任一条件符合即认为是单选题
          return isTypeSingle || hasCorrectAnswerNum;
        });
        
        console.log('过滤后的单选题数量:', singleChoiceQuestions.length);
        
        if (singleChoiceQuestions.length === 0) {
          // 如果过滤后没有题目，可能是题目格式问题，尝试使用全部题目
          console.log('没有找到符合条件的单选题，尝试使用全部题目...');
          
          // 尝试将所有题目视为单选题处理
          const processedQuestions = cachedQuestions.map(q => {
            console.log('处理题目:', q.id, q.question);
            // 处理题目，确保格式正确
            return {
              id: q.id || Math.random().toString(36).substr(2, 9),
              question: q.question || '题目内容缺失',
              options: Array.isArray(q.options) ? q.options : ['选项A', '选项B', '选项C', '选项D'],
              answer: typeof q.answer === 'number' ? q.answer : 0,
              type: 'single'
            };
          });
          
          if (processedQuestions.length > 0) {
            console.log('成功处理了所有题目，共', processedQuestions.length, '道');
            this.setData({
              questions: processedQuestions,
              loading: false,
              progress: 0,
              currentQuestionIndex: 0,
              selectedOption: null,
              showResult: false,
              records: [],
              mode: 'wrong' // 设置为错题模式
            });
            
            // 清除缓存，防止下次进入时仍使用这些题目
            wx.removeStorageSync('currentQuestions');
            
            this.updateProgress();
            return;
          }
          
          wx.showToast({
            title: '题目数据不完整',
            icon: 'none'
          });
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
          return;
        }
        
        // 处理已筛选的单选题，确保格式正确
        const processedQuestions = singleChoiceQuestions.map(q => {
          // 确保选项是数组
          let options = [];
          if (Array.isArray(q.options)) {
            options = q.options;
          } else if (q.options && typeof q.options === 'object') {
            // 如果是对象格式，提取值
            options = Object.values(q.options);
          } else {
            // 默认选项
            options = ['选项A', '选项B', '选项C', '选项D'];
          }
          
          // 确保答案是数字
          let answer = 0;
          if (q.answer !== undefined) {
            if (typeof q.answer === 'number') {
              answer = q.answer;
            } else if (Array.isArray(q.answer) && q.answer.length > 0) {
              answer = Number(q.answer[0]);
            } else if (typeof q.answer === 'string') {
              try {
                // 尝试解析JSON
                const parsed = JSON.parse(q.answer);
                if (Array.isArray(parsed) && parsed.length > 0) {
                  answer = Number(parsed[0]);
                } else {
                  answer = Number(parsed);
                }
              } catch (e) {
                answer = Number(q.answer);
              }
            }
          }
          
          return {
            id: q.id,
            question: q.question,
            options: options,
            answer: isNaN(answer) ? 0 : answer,
            explanation: q.explanation || ""
          };
        });
        
        console.log('处理后的题目数据:', processedQuestions.map(q => ({ id: q.id, options: q.options.length })));
        
        this.setData({
          questions: processedQuestions,
          loading: false,
          progress: 0,
          currentQuestionIndex: 0,
          selectedOption: null,
          showResult: false,
          records: [],
          mode: 'wrong' // 设置为错题模式
        });
        
        // 清除缓存，防止下次进入时仍使用这些题目
        wx.removeStorageSync('currentQuestions');
        
        this.updateProgress();
        return;
      }
      
      // 常规再次练习模式
      this.setData({
        questions: cachedQuestions,
        loading: false,
        progress: 0,
        currentQuestionIndex: 0,
        selectedOption: null,
        showResult: false,
        records: []
      });
      
      // 清除缓存，防止下次进入时仍使用这些题目
      wx.removeStorageSync('currentQuestions');
      
      this.updateProgress();
      return;
    }
    
    // 如果是练习单个题目模式
    if (mode === 'practice' && questionId) {
      console.log('加载单个题目模式，题目ID:', questionId);
      this.loadQuestion(questionId);
      return;
    }
    
    if (mode === 'wrong') {
      console.log('加载错题模式');
      // 清除可能干扰的缓存
      wx.removeStorageSync('currentQuestions');
      this.loadWrongQuestions();
    } else if (mode === 'course' || courseId) {
      // 如果指定了课程ID，强制使用课程模式
      if (courseId && mode !== 'course') {
        console.log('检测到课程ID，将模式更改为course');
        this.setData({ mode: 'course' });
      }
      this.fetchQuestions();
    } else {
      console.log('加载普通模式题目');
      this.fetchQuestions();
    }
    
    // 只在非错题模式下获取课程列表
    if (mode !== 'wrong' && mode !== 'practice') {
      this.fetchCoursesList();
    }
  },
  
  // 获取课程列表
  fetchCoursesList: function() {
    app.request({
      url: '/classes',
      method: 'GET'
    }).then(res => {
      console.log('获取课程列表数据:', res);
      
      if (res && Array.isArray(res)) {
        // 处理从API获取的课程数据
        const coursesData = res.map(course => {
          return {
            id: course.id,
            name: course.name,
            question_count: course.question_count || 0
          };
        });
        
        this.setData({
          coursesList: coursesData
        });
      }
    }).catch(err => {
      console.error('获取课程列表失败:', err);
    });
  },
  
  // 切换课程筛选
  switchCourse: function(e) {
    // 如果是错题模式或单个题目练习模式，不允许切换课程
    if (this.data.mode === 'wrong' || this.data.mode === 'practice') {
      console.log('错题模式或单题练习模式下不允许切换课程');
      return;
    }
    
    const courseId = parseInt(e.currentTarget.dataset.id) || 0;
    
    this.setData({ 
      activeCourse: courseId,
      loading: true 
    });
    
    // 跳转到新的URL以保持一致性
    wx.redirectTo({
      url: `/pages/quiz/single/single?mode=${this.data.mode || 'normal'}${courseId ? '&courseId=' + courseId : ''}`
    });
  },
  
  // 获取单选题列表
  fetchQuestions: function() {
    this.setData({ loading: true });
    
    // 检查是否是课程模式
    if (this.data.mode === 'course') {
      // 从存储中获取课程题目
      const courseQuestions = wx.getStorageSync('currentQuestions');
      if (courseQuestions && courseQuestions.length > 0) {
        this.setData({
          questions: courseQuestions,
          loading: false,
          progress: 0,
          currentQuestionIndex: 0,
          selectedOption: null,
          showResult: false,
          records: []
        });
        
        this.updateProgress();
        return;
      }
      
      // 如果没有找到缓存的课程题目，使用API重新获取
      this.loadCourseQuestions();
      return;
    }
    
    // 如果不是课程模式，使用常规API获取所有题目
    app.request({
      url: '/questions/single'
    })
    .then(res => {
      if (!res || !res.length) {
        wx.showToast({
          title: '暂无单选题',
          icon: 'none'
        });
        
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }
      
      // 如果有课程ID筛选，过滤属于该课程的题目
      if (this.data.courseId) {
        res = res.filter(q => q.course_id === this.data.courseId);
        
        if (res.length === 0) {
          wx.showToast({
            title: '该课程暂无单选题',
            icon: 'none'
          });
          
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
          return;
        }
      }
      
      this.setData({
        questions: res,
        loading: false,
        progress: 0,
        currentQuestionIndex: 0,
        selectedOption: null,
        showResult: false,
        records: []
      });
      
      this.updateProgress();
    })
    .catch(err => {
      console.error('获取单选题失败:', err);
      this.setData({ loading: false });
      
      wx.showToast({
        title: '获取题目失败',
        icon: 'none'
      });
      
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    });
  },
  
  // 加载指定课程的单选题
  loadCourseQuestions: function() {
    if (!this.data.courseId) {
      console.error('加载课程题目失败：缺少课程ID');
      wx.showToast({
        title: '课程信息不完整',
        icon: 'none'
      });
      return;
    }
    
    this.setData({ loading: true });
    
    app.request({
      url: `/course/${this.data.courseId}/questions/single`
    })
    .then(res => {
      this.setData({ loading: false });
      
      if (!res || !res.length) {
        wx.showToast({
          title: '该课程暂无单选题',
          icon: 'none'
        });
        
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }
      
      this.setData({
        questions: res,
        progress: 0,
        currentQuestionIndex: 0,
        selectedOption: null,
        showResult: false,
        records: []
      });
      
      // 缓存课程题目
      wx.setStorageSync('currentQuestions', res);
      
      this.updateProgress();
    })
    .catch(err => {
      console.error('获取课程单选题失败:', err);
      this.setData({ loading: false });
      
      wx.showToast({
        title: '获取题目失败',
        icon: 'none'
      });
      
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    });
  },
  
  // 加载错题
  loadWrongQuestions() {
    this.setData({ loading: true });
    
    app.request({
      url: '/wrong-questions',
      method: 'GET'
    }).then(res => {
      console.log('获取错题本数据:', res);
      
      // 筛选单选题
      let singleChoiceQuestions = res.filter(q => q.type === 'single');
      console.log(`共找到${singleChoiceQuestions.length}道单选错题`);
      
      // 如果有指定课程ID，进一步筛选
      if (this.data.courseId) {
        console.log(`按课程ID ${this.data.courseId} 筛选错题`);
        const beforeFilter = singleChoiceQuestions.length;
        
        singleChoiceQuestions = singleChoiceQuestions.filter(q => {
          // 确保类型匹配：将两者都转换为数字进行比较
          const qCourseId = typeof q.course_id === 'string' ? parseInt(q.course_id, 10) : q.course_id;
          const thisCourseId = typeof this.data.courseId === 'string' ? parseInt(this.data.courseId, 10) : this.data.courseId;
          return qCourseId === thisCourseId;
        });
        
        console.log(`筛选前 ${beforeFilter} 道题，筛选后 ${singleChoiceQuestions.length} 道题`);
      }
      
      if (singleChoiceQuestions.length === 0) {
        wx.showToast({
          title: '没有单选错题',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }

      // 缓存错题数据以备后用
      console.log(`准备加载全部 ${singleChoiceQuestions.length} 道错题`);
      
      // 获取完整的题目数据
      app.request({
        url: '/questions/single',
        method: 'GET'
      }).then(allQuestions => {
        // 筛选出错题列表中的题目
        const wrongIds = singleChoiceQuestions.map(q => q.id);
        const wrongQuestionDetails = allQuestions.filter(q => wrongIds.includes(q.id));
        
        console.log(`从${allQuestions.length}道题目中找到${wrongQuestionDetails.length}道错题`);
        
        if (wrongQuestionDetails.length === 0) {
          wx.showToast({
            title: '题目数据不完整',
            icon: 'none'
          });
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
          return;
        }
        
        // 随机排序错题
        this.shuffleArray(wrongQuestionDetails);
        
        this.setData({
          questions: wrongQuestionDetails,
          loading: false,
          progress: 0,
          currentQuestionIndex: 0,
          selectedOption: null,
          showResult: false,
          records: []
        });
        
        this.updateProgress();
      }).catch(err => {
        console.error('获取题目详情失败:', err);
        this.setData({ loading: false });
        wx.showToast({
          title: '获取题目失败',
          icon: 'none'
        });
      });
      
    }).catch(err => {
      console.error('获取错题失败:', err);
      this.setData({ loading: false });
      wx.showToast({
        title: '获取错题失败',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    });
  },
  
  // 获取特定题目
  fetchSpecificQuestion: function(questionId) {
    this.setData({ loading: true });
    
    console.log('获取特定题目，ID:', questionId);
    
    app.request({
      url: `/questions/single/${questionId}`,
      method: 'GET'
    })
    .then(res => {
      if (!res) {
        wx.showToast({
          title: '获取题目失败',
          icon: 'none'
        });
        
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }
      
      // 单个题目的情况，直接使用返回的题目
      this.setData({
        questions: [res],
        loading: false,
        progress: 100,  // 只有一道题，进度为100%
        currentQuestionIndex: 0,
        selectedOption: null,
        showResult: false,
        records: []
      });
    })
    .catch(err => {
      console.error('获取特定题目失败:', err);
      this.setData({ loading: false });
      
      // 尝试从所有题目中筛选特定题目
      this.fallbackGetQuestion(questionId);
    });
  },
  
  // 备用方式：从所有题目中找到指定ID的题目
  fallbackGetQuestion: function(questionId) {
    console.log('使用备用方式获取题目:', questionId);
    
    app.request({
      url: '/questions/single',
      method: 'GET'
    })
    .then(res => {
      if (!res || !res.length) {
        wx.showToast({
          title: '获取题目失败',
          icon: 'none'
        });
        
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }
      
      // 查找特定ID的题目
      const question = res.find(q => q.id == questionId);
      
      if (!question) {
        wx.showToast({
          title: '题目不存在',
          icon: 'none'
        });
        
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }
      
      this.setData({
        questions: [question],
        loading: false,
        progress: 100,  // 只有一道题，进度为100%
        currentQuestionIndex: 0,
        selectedOption: null,
        showResult: false,
        records: []
      });
    })
    .catch(err => {
      console.error('备用方式获取题目失败:', err);
      this.setData({ loading: false });
      
      wx.showToast({
        title: '获取题目失败',
        icon: 'none'
      });
      
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    });
  },
  
  // 加载特定题目
  loadQuestion: function(questionId) {
    if (!questionId) {
      console.error('无效的题目ID');
      wx.showToast({
        title: '题目信息不完整',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }
    
    console.log('加载单个题目，ID:', questionId);
    
    // 优先检查是否有缓存的练习题目
    const cachedQuestion = wx.getStorageSync('practiceQuestion');
    if (cachedQuestion && cachedQuestion.id == questionId) {
      console.log('使用缓存的练习题目:', cachedQuestion);
      
      // 处理题目数据
      const processedQuestion = {
        id: cachedQuestion.id,
        question: cachedQuestion.question,
        options: Array.isArray(cachedQuestion.options) ? 
                 cachedQuestion.options : [],
        answer: cachedQuestion.answer,
        explanation: cachedQuestion.explanation || ""
      };
      
      this.setData({
        questions: [processedQuestion],
        loading: false,
        progress: 100,  // 只有一道题，进度为100%
        currentQuestionIndex: 0,
        selectedOption: null,
        showResult: false,
        records: []
      });
      
      // 清除缓存
      wx.removeStorageSync('practiceQuestion');
      return;
    }
    
    // 如果没有缓存，调用API获取题目
    this.fetchSpecificQuestion(questionId);
  },
  
  // 选择选项
  selectOption: function(e) {
    if (this.data.showResult || this.data.isSubmitting) return;
    
    const index = e.currentTarget.dataset.index;
    this.setData({
      selectedOption: index
    });
  },
  
  // 提交答案
  submitAnswer: function() {
    if (this.data.selectedOption === null) {
      wx.showToast({
        title: '请选择一个选项',
        icon: 'none'
      });
      return;
    }
    
    if (this.data.isSubmitting) return;
    
    this.setData({ isSubmitting: true });
    
    const currentQuestion = this.data.questions[this.data.currentQuestionIndex];
    
    app.request({
      url: '/submit',
      method: 'POST',
      data: {
        questionType: 'single',
        questionId: currentQuestion.id,
        userAnswer: this.data.selectedOption
      }
    })
    .then(res => {
      // 记录答题情况
      const record = {
        questionId: currentQuestion.id,
        question: currentQuestion.question,
        options: currentQuestion.options,
        userAnswer: this.data.selectedOption,
        correctAnswer: res.correctAnswer,
        isCorrect: res.isCorrect
      };
      
      const records = this.data.records.concat(record);
      const newCorrectCount = res.isCorrect ? this.data.correctCount + 1 : this.data.correctCount;
      
      this.setData({
        isSubmitting: false,
        showResult: true,
        isCorrect: res.isCorrect,
        correctAnswer: res.correctAnswer,
        records: records,
        correctCount: newCorrectCount
      });
      
      console.log('答题结果:', { 
        isCorrect: res.isCorrect, 
        correctCount: newCorrectCount, 
        totalAnswered: records.length 
      });
    })
    .catch(err => {
      console.error('提交答案失败:', err);
      this.setData({ isSubmitting: false });
      
      wx.showToast({
        title: '提交答案失败',
        icon: 'none'
      });
    });
  },
  
  // 下一题
  nextQuestion: function() {
    const nextIndex = this.data.currentQuestionIndex + 1;
    
    if (nextIndex >= this.data.questions.length) {
      // 已经是最后一题，显示完成确认对话框
      this.setData({
        showConfirmFinish: true
      });
      return;
    }
    
    this.setData({
      currentQuestionIndex: nextIndex,
      selectedOption: null,
      showResult: false,
      isCorrect: false,
      correctAnswer: null
    });
    
    this.updateProgress();
  },
  
  // 更新进度条
  updateProgress: function() {
    const progress = ((this.data.currentQuestionIndex + 1) / this.data.questions.length) * 100;
    this.setData({ progress });
  },
  
  // 确认完成答题
  confirmFinish: function() {
    this.setData({
      showConfirmFinish: false
    });
    
    // 获取课程信息
    const currentCourse = wx.getStorageSync('currentCourse') || {};
    
    // 计算答题时间
    const endTime = Date.now();
    const startTime = this.data.startTime;
    const timeTaken = (endTime - startTime) / 1000; // 转换为秒
    const minutes = Math.floor(timeTaken / 60);
    const seconds = Math.round(timeTaken % 60);
    
    // 前往结果页面
    const result = {
      totalQuestions: this.data.questions.length,
      correctCount: this.data.correctCount,
      records: this.data.records,
      questionType: 'single',
      courseId: this.data.courseId,
      courseName: this.data.courseTitle,
      courseColor: currentCourse.color || '#4e8df7',
      timeTaken: `${minutes}分${seconds}秒`,
      // 保存原始题目数据，供再次练习使用
      questions: this.data.questions,
      // 保存当前模式，尤其是对错题本模式很重要
      mode: this.data.mode
    };
    
    console.log('传递答题结果数据:', result);
    
    wx.navigateTo({
      url: '/pages/result/result',
      success: (res) => {
        // 传递结果数据给结果页面
        res.eventChannel.emit('acceptResultData', result);
      }
    });
  },
  
  // 取消完成
  cancelFinish: function() {
    this.setData({
      showConfirmFinish: false
    });
  },
  
  // 返回首页
  backToHome: function() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },
  
  // 加载所有题目
  loadAllQuestions: function() {
    this.fetchQuestions();
  },
  
  // 打乱数组
  shuffleArray: function(array) {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]];
    }
  },
})
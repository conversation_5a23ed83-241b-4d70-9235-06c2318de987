/* 
 * Enhanced Admin Interface Styles
 * Created for Big Data Question Bank System
 */

:root {
  --primary: #4776E6;
  --primary-light: #5C7CFA;
  --primary-dark: #3964D8;
  --secondary: #8E54E9;
  --secondary-light: #A66FF0;
  --secondary-dark: #7840D8;
  --success: #2DCE89;
  --info: #11CDEF;
  --warning: #FB6340;
  --danger: #F5365C;
  --text: #2c3e50;
  --text-light: #7C8CA0;
  --light: #f5f7ff;
  --dark: #172b4d;
  --white: #ffffff;
  --border-color: #e1e5f2;
  --shadow-color: rgba(0, 0, 0, 0.1);
}

/* Modern Card Styles */
.modern-card {
  background-color: var(--white);
  border-radius: 16px;
  box-shadow: 0 8px 24px var(--shadow-color);
  transition: all 0.3s ease;
  border: none;
  overflow: hidden;
}

.modern-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.modern-card .card-header {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: var(--white);
  font-weight: 600;
  border: none;
  padding: 1rem 1.5rem;
}

.modern-card .card-body {
  padding: 1.5rem;
}

/* Enhanced Buttons */
.btn-modern {
  border-radius: 12px;
  padding: 0.6rem 1.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn-modern:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.btn-modern:active {
  transform: translateY(0);
}

.btn-modern-primary {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  border: none;
  color: var(--white);
}

.btn-modern-secondary {
  background: linear-gradient(135deg, var(--secondary), var(--secondary-dark));
  border: none;
  color: var(--white);
}

.btn-modern-success {
  background: linear-gradient(135deg, var(--success), #1aae6f);
  border: none;
  color: var(--white);
}

.btn-modern-danger {
  background: linear-gradient(135deg, var(--danger), #e82042);
  border: none;
  color: var(--white);
}

/* Enhanced Form Controls */
.form-control-modern {
  border-radius: 12px;
  border: 2px solid var(--border-color);
  padding: 0.75rem 1rem;
  transition: all 0.3s ease;
  background-color: var(--light);
}

.form-control-modern:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 4px rgba(71, 118, 230, 0.1);
  transform: translateY(-2px);
}

.form-label-modern {
  font-weight: 600;
  color: var(--text);
  margin-bottom: 0.5rem;
}

/* Tables with modern design */
.table-modern {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
}

.table-modern th {
  background: linear-gradient(135deg, var(--primary-light), var(--primary));
  color: var(--white);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.8rem;
  letter-spacing: 0.5px;
  padding: 1rem;
  border: none;
}

.table-modern th:first-child {
  border-top-left-radius: 12px;
}

.table-modern th:last-child {
  border-top-right-radius: 12px;
}

.table-modern td {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  vertical-align: middle;
}

.table-modern tbody tr {
  transition: all 0.3s ease;
}

.table-modern tbody tr:hover {
  background-color: rgba(92, 124, 250, 0.05);
}

.table-modern tbody tr:last-child td:first-child {
  border-bottom-left-radius: 12px;
}

.table-modern tbody tr:last-child td:last-child {
  border-bottom-right-radius: 12px;
}

/* Badges with modern look */
.badge-modern {
  padding: 0.5rem 0.8rem;
  border-radius: 10px;
  font-weight: 600;
  font-size: 0.75rem;
}

.badge-primary-soft {
  background-color: rgba(71, 118, 230, 0.1);
  color: var(--primary);
}

.badge-success-soft {
  background-color: rgba(45, 206, 137, 0.1);
  color: var(--success);
}

.badge-warning-soft {
  background-color: rgba(251, 99, 64, 0.1);
  color: var(--warning);
}

.badge-danger-soft {
  background-color: rgba(245, 54, 92, 0.1);
  color: var(--danger);
}

/* Modern Nav Tabs */
.nav-tabs-modern {
  border-bottom: none;
  gap: 0.5rem;
}

.nav-tabs-modern .nav-link {
  border: none;
  border-radius: 10px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  color: var(--text-light);
  transition: all 0.3s ease;
}

.nav-tabs-modern .nav-link:hover {
  color: var(--primary);
  background-color: rgba(71, 118, 230, 0.05);
}

.nav-tabs-modern .nav-link.active {
  color: var(--primary);
  background-color: rgba(71, 118, 230, 0.1);
  border: none;
}

/* Animations for page transitions */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.slide-up {
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom switch toggle */
.switch-modern {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 26px;
}

.switch-modern input {
  opacity: 0;
  width: 0;
  height: 0;
}

.switch-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #e1e5f2;
  transition: .4s;
  border-radius: 34px;
}

.switch-slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .switch-slider {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
}

input:checked + .switch-slider:before {
  transform: translateX(24px);
}

/* Modern pagination */
.pagination-modern .page-link {
  margin: 0 3px;
  border-radius: 8px;
  border: none;
  padding: 0.5rem 0.75rem;
  color: var(--text);
  transition: all 0.3s ease;
}

.pagination-modern .page-link:hover {
  background-color: rgba(71, 118, 230, 0.1);
  color: var(--primary);
}

.pagination-modern .page-item.active .page-link {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
} 
import os
import re
import urllib.request
import shutil
import ssl

# 创建目录结构
def create_directories():
    dirs = [
        "static/css",
        "static/js",
        "static/webfonts",
        "static/img"
    ]
    for dir_path in dirs:
        os.makedirs(dir_path, exist_ok=True)
    print("已创建目录结构")

# 下载文件函数
def download_file(url, local_path):
    # 忽略SSL证书验证（为了处理任何可能的SSL问题）
    ssl_context = ssl._create_unverified_context()
    try:
        # 创建本地文件目录（如果不存在）
        os.makedirs(os.path.dirname(local_path), exist_ok=True)
        # 下载文件
        with urllib.request.urlopen(url, context=ssl_context) as response, open(local_path, 'wb') as out_file:
            shutil.copyfileobj(response, out_file)
        print(f"已下载: {url} -> {local_path}")
        return True
    except Exception as e:
        print(f"下载失败 {url}: {str(e)}")
        return False

# 下载所有资源
def download_all_assets():
    # 为每个资源提供多个备用源
    assets = [
        # Bootstrap CSS (使用多个CDN源)
        [
            ("https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css", "static/css/bootstrap.min.css"),
            ("https://stackpath.bootstrapcdn.com/bootstrap/5.3.0/css/bootstrap.min.css", "static/css/bootstrap.min.css"),
            ("https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css", "static/css/bootstrap.min.css"),
            ("https://unpkg.com/bootstrap@5.3.0/dist/css/bootstrap.min.css", "static/css/bootstrap.min.css")
        ],
        # Bootstrap JS
        [
            ("https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js", "static/js/bootstrap.bundle.min.js"),
            ("https://stackpath.bootstrapcdn.com/bootstrap/5.3.0/js/bootstrap.bundle.min.js", "static/js/bootstrap.bundle.min.js"),
            ("https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js", "static/js/bootstrap.bundle.min.js"),
            ("https://unpkg.com/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js", "static/js/bootstrap.bundle.min.js")
        ],
        # Font Awesome CSS
        [
            ("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css", "static/css/all.min.css"),
            ("https://use.fontawesome.com/releases/v6.4.0/css/all.css", "static/css/all.min.css")
        ],
        # Font Awesome Webfonts - fa-solid-900.woff2
        [
            ("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-solid-900.woff2", "static/webfonts/fa-solid-900.woff2"),
        ],
        # Font Awesome Webfonts - fa-solid-900.ttf
        [
            ("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-solid-900.ttf", "static/webfonts/fa-solid-900.ttf"),
        ],
        # Font Awesome Webfonts - fa-regular-400.woff2
        [
            ("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-regular-400.woff2", "static/webfonts/fa-regular-400.woff2"),
        ],
        # Font Awesome Webfonts - fa-regular-400.ttf
        [
            ("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-regular-400.ttf", "static/webfonts/fa-regular-400.ttf"),
        ],
        # Font Awesome Webfonts - fa-brands-400.woff2
        [
            ("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-brands-400.woff2", "static/webfonts/fa-brands-400.woff2"),
        ],
        # Font Awesome Webfonts - fa-brands-400.ttf
        [
            ("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-brands-400.ttf", "static/webfonts/fa-brands-400.ttf"),
        ],
        # Favicon
        [
            ("https://cdn.jsdelivr.net/npm/@bootcss/www.bootcss.com@0.0.5/dist/img/favicon.ico", "static/img/favicon.ico"),
            ("https://www.bootcss.com/favicon.ico", "static/img/favicon.ico")
        ],
    ]
    
    success_count = 0
    total_assets = len(assets)
    
    for sources in assets:
        success = False
        for url, local_path in sources:
            if download_file(url, local_path):
                success = True
                break
            # 如果下载失败，尝试下一个源
        
        if success:
            success_count += 1
    
    print(f"下载完成: {success_count}/{total_assets} 个文件成功")
    return success_count == total_assets

# 如果没有下载成功，创建空白文件
def create_empty_files():
    empty_files = [
        "static/css/bootstrap.min.css",
        "static/js/bootstrap.bundle.min.js",
        "static/css/all.min.css",
        "static/img/favicon.ico"
    ]
    
    for file_path in empty_files:
        if not os.path.exists(file_path) or os.path.getsize(file_path) == 0:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    if file_path.endswith('.css'):
                        f.write("/* 本地替换的CSS文件 */\n")
                    elif file_path.endswith('.js'):
                        f.write("// 本地替换的JS文件\n")
                print(f"创建了空白文件: {file_path}")
            except Exception as e:
                print(f"创建空白文件 {file_path} 失败: {str(e)}")

# 更新Font Awesome CSS中的资源引用
def fix_font_awesome_paths():
    css_file = "static/css/all.min.css"
    
    try:
        # 检查文件是否存在和是否有内容
        if not os.path.exists(css_file) or os.path.getsize(css_file) == 0:
            print(f"Font Awesome CSS文件不存在或为空: {css_file}")
            return False
            
        # 读取CSS文件内容
        with open(css_file, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # 替换webfont URL为本地路径 
        updated_content = re.sub(
            r'url\([\'"]?(?:https?:)?//cdnjs\.cloudflare\.com/ajax/libs/font-awesome/6\.4\.0/webfonts/([^\'"]+)[\'"]?\)',
            r'url("/static/webfonts/\1")',
            content
        )
        
        # 同样处理相对路径的情况
        updated_content = re.sub(
            r'url\([\'"]?(?:\.\./)?webfonts/([^\'"]+)[\'"]?\)',
            r'url("/static/webfonts/\1")',
            updated_content
        )
        
        # 将更新后的内容写回文件
        with open(css_file, 'w', encoding='utf-8') as file:
            file.write(updated_content)
        
        print(f"已更新Font Awesome CSS文件中的字体路径: {css_file}")
        return True
    except Exception as e:
        print(f"更新Font Awesome CSS失败: {str(e)}")
        return False

# 更新HTML模板中的CDN引用
def update_html_templates():
    templates_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'templates')
    
    # CDN到本地资源的映射
    cdn_to_local = {
        'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css': '/static/css/bootstrap.min.css',
        'https://stackpath.bootstrapcdn.com/bootstrap/5.3.0/css/bootstrap.min.css': '/static/css/bootstrap.min.css',
        'https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css': '/static/css/bootstrap.min.css',
        'https://unpkg.com/bootstrap@5.3.0/dist/css/bootstrap.min.css': '/static/css/bootstrap.min.css',
        
        'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js': '/static/js/bootstrap.bundle.min.js',
        'https://stackpath.bootstrapcdn.com/bootstrap/5.3.0/js/bootstrap.bundle.min.js': '/static/js/bootstrap.bundle.min.js',
        'https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js': '/static/js/bootstrap.bundle.min.js',
        'https://unpkg.com/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js': '/static/js/bootstrap.bundle.min.js',
        
        'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css': '/static/css/all.min.css',
        'https://use.fontawesome.com/releases/v6.4.0/css/all.css': '/static/css/all.min.css',
        
        'https://cdn.jsdelivr.net/npm/@bootcss/www.bootcss.com@0.0.5/dist/img/favicon.ico': '/static/img/favicon.ico',
        'https://www.bootcss.com/favicon.ico': '/static/img/favicon.ico'
    }
    
    updated_files = 0
    # 遍历所有HTML文件
    for root, _, files in os.walk(templates_dir):
        for file in files:
            if file.endswith('.html'):
                file_path = os.path.join(root, file)
                try:
                    # 读取文件内容
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 检查是否有任何CDN链接需要替换
                    original_content = content
                    for cdn_url, local_path in cdn_to_local.items():
                        content = content.replace(cdn_url, local_path)
                    
                    # 如果内容有变化，写回文件
                    if content != original_content:
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(content)
                        updated_files += 1
                        print(f"已更新模板文件: {file_path}")
                except Exception as e:
                    print(f"更新模板 {file_path} 失败: {str(e)}")
    
    print(f"模板更新完成: {updated_files} 个文件已更新")
    return updated_files > 0

if __name__ == "__main__":
    print("==== 开始下载和配置项目依赖 ====")
    
    # 步骤1: 创建目录结构
    create_directories()
    
    # 步骤2: 下载所有资源
    download_success = download_all_assets()
    
    # 步骤3: 如果有文件下载失败，创建空白文件
    if not download_success:
        create_empty_files()
    
    # 步骤4: 修复Font Awesome字体路径
    fix_font_awesome_paths()
    
    # 步骤5: 更新HTML模板
    update_html_templates()
    
    print("==== 依赖下载和配置完成 ====")
    print("请重启Flask应用以使更改生效！") 
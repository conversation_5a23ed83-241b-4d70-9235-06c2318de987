/* single.wxss */
.container {
  padding: 30rpx;
  min-height: 100vh;
  background-color: #f5f7fa;
  box-sizing: border-box;
}

/* 进度条 */
.progress-bar {
  width: 100%;
  height: 10rpx;
  background-color: #e0e0e0;
  border-radius: 5rpx;
  overflow: hidden;
}

.progress-inner {
  height: 100%;
  background-color: #4C84FF;
  transition: width 0.2s ease;
}

.question-counter {
  display: block;
  text-align: right;
  margin: 10rpx 0 30rpx;
  font-size: 26rpx;
  color: #999;
}

/* 课程筛选样式 */
.course-filter {
  margin-bottom: 30rpx;
  background-color: #fff;
  padding: 20rpx 0;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.course-scroll {
  width: 100%;
  white-space: nowrap;
}

.course-tabs {
  padding: 0 20rpx;
  display: flex;
}

.course-tab {
  display: inline-flex;
  align-items: center;
  padding: 12rpx 24rpx;
  margin-right: 16rpx;
  font-size: 26rpx;
  color: #666;
  background-color: #f5f7fa;
  border-radius: 30rpx;
  position: relative;
  transition: all 0.3s;
}

.course-tab.active {
  color: #fff;
  background-color: #4C84FF;
  font-weight: 500;
}

.course-count {
  margin-left: 8rpx;
  font-size: 22rpx;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 2rpx 10rpx;
  border-radius: 20rpx;
}

.course-tab.active .course-count {
  background-color: rgba(255, 255, 255, 0.3);
  color: #fff;
}

/* 题目容器 */
.question-container {
  background-color: white;
  border-radius: 15rpx;
  padding: 40rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.question {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 40rpx;
  display: block;
  line-height: 1.5;
}

/* 选项列表 */
.options-list {
  margin-bottom: 40rpx;
}

.option {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  background-color: #f9f9f9;
  border: 2rpx solid #eee;
  position: relative;
  overflow: hidden;
}

/* 添加左侧颜色条 */
.option::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 8rpx;
  background-color: #eee;
}

.option.selected {
  background-color: #e8f0ff;
  border-color: #4C84FF;
}

.option.selected::before {
  background-color: #4C84FF;
}

.option.correct {
  background-color: #e8f8e8;
  border-color: #4CAF50;
}

.option.correct::before {
  background-color: #4CAF50;
}

.option.wrong {
  background-color: #ffe8e8;
  border-color: #F44336;
}

.option.wrong::before {
  background-color: #F44336;
}

.option-letter {
  width: 50rpx;
  height: 50rpx;
  line-height: 50rpx;
  text-align: center;
  border-radius: 25rpx;
  background-color: #eee;
  font-size: 28rpx;
  margin-right: 20rpx;
  color: #666;
}

.option.selected .option-letter {
  background-color: #4C84FF;
  color: white;
}

.option-text {
  font-size: 30rpx;
  color: #333;
  flex: 1;
  line-height: 1.4;
}

/* 结果图标 */
.result-icon {
  margin-left: 10rpx;
}

.correct-icon {
  color: #4CAF50;
  font-size: 36rpx;
  font-weight: bold;
}

.wrong-icon {
  color: #F44336;
  font-size: 36rpx;
  font-weight: bold;
}

/* 结果提示 */
.result-tip {
  text-align: left;
  margin: 20rpx 0 30rpx;
  padding: 20rpx;
  background: #fff;
  border-radius: 10rpx;
  position: relative;
  overflow: hidden;
}

/* 添加左侧紫色条 */
.result-tip::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 8rpx;
  background-color: #4C84FF;
}

.correct-tip, .wrong-tip {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  padding-left: 20rpx;
}

.correct-tip {
  color: #4CAF50;
}

.wrong-tip {
  color: #F44336;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  justify-content: center;
}

.action-btn {
  width: 60%;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 45rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.submit-btn {
  background-color: #4C84FF;
  color: white;
}

.next-btn {
  background-color: #4C84FF;
  color: white;
}

/* 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 100rpx;
}

.loading {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #4C84FF;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999;
}

/* 模态框 */
.modal-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal {
  width: 80%;
  background-color: white;
  border-radius: 15rpx;
  overflow: hidden;
}

.modal-header {
  padding: 30rpx;
  text-align: center;
  border-bottom: 1rpx solid #eee;
}

.modal-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
}

.modal-content {
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.modal-content text {
  font-size: 30rpx;
  color: #666;
  line-height: 1.6;
  text-align: center;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #eee;
}

.modal-btn {
  flex: 1;
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  font-size: 32rpx;
  border-radius: 0;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background-color: #4C84FF;
  color: white;
  font-weight: bold;
} 
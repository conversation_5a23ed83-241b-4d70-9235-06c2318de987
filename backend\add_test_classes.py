"""
向数据库添加测试班级数据
"""
from app import app, db, Class

def add_test_classes():
    """添加测试班级数据"""
    with app.app_context():
        # 检查是否已有班级数据
        existing_classes = Class.query.all()
        if existing_classes:
            print(f'数据库中已存在 {len(existing_classes)} 个班级:')
            for cls in existing_classes:
                print(f'- ID: {cls.id}, 名称: {cls.name}, 描述: {cls.description or "无"}')
            return
        
        # 创建测试班级数据
        test_classes = [
            Class(name='计算机科学与技术', description='计算机科学与技术专业班级'),
            Class(name='软件工程', description='软件工程专业班级'),
            Class(name='数据科学', description='数据科学与大数据技术专业班级'),
            Class(name='人工智能', description='人工智能专业班级'),
            Class(name='网络工程', description='网络工程专业班级')
        ]
        
        # 添加到数据库
        for cls in test_classes:
            db.session.add(cls)
        
        # 提交事务
        db.session.commit()
        
        print(f'成功添加 {len(test_classes)} 个测试班级:')
        for cls in test_classes:
            print(f'- ID: {cls.id}, 名称: {cls.name}')

if __name__ == "__main__":
    add_test_classes() 
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - 大数据题库</title>
    <!-- Bootstrap CSS -->
    <link href="/static/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="/static/css/all.min.css">
    <!-- Enhanced Admin Styles -->
    <link rel="stylesheet" href="/static/admin-enhanced.css">
    <style>
        :root {
            --primary: #4776E6;
            --secondary: #8E54E9;
            --accent: #5C7CFA;
            --text: #2c3e50;
            --light-text: #fff;
            --bg: #f5f7ff;
            --input-bg: #f8faff;
            --border: #e1e5f2;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            min-height: 100vh;
            background: linear-gradient(135deg, #5f75e5 0%, #7755b3 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            position: relative;
        }
        
        .background-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }
        
        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.25);
            border-radius: 50%;
            animation-duration: 10s;
            animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
            animation-iteration-count: infinite;
            filter: blur(1px);
        }
        
        .shape:nth-child(1) {
            width: 320px;
            height: 320px;
            top: -50px;
            left: -80px;
            animation: float-1 18s infinite;
            opacity: 0.65;
            filter: blur(2px);
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.15));
        }
        
        .shape:nth-child(2) {
            width: 280px;
            height: 280px;
            bottom: -50px;
            left: -30px;
            animation: float-2 15s infinite;
            opacity: 0.5;
            filter: blur(2px);
            background: radial-gradient(circle, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.12));
        }
        
        .shape:nth-child(3) {
            width: 180px;
            height: 180px;
            top: 40%;
            left: 5%;
            animation: float-3 12s infinite;
            opacity: 0.5;
            background: linear-gradient(45deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.15));
        }
        
        .shape:nth-child(4) {
            width: 200px;
            height: 200px;
            top: 10%;
            right: 10%;
            animation: float-4 14s infinite;
            opacity: 0.5;
            filter: blur(2px);
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.1));
        }
        
        .shape:nth-child(5) {
            width: 150px;
            height: 150px;
            bottom: 20%;
            left: 30%;
            animation: float-5 13s infinite;
            opacity: 0.4;
            filter: blur(2px);
            background: radial-gradient(circle, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.1));
        }
        
        .shape:nth-child(6) {
            width: 230px;
            height: 230px;
            top: 45%;
            right: -60px;
            animation: float-6 16s infinite;
            opacity: 0.4;
            background: linear-gradient(-45deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.1));
            filter: blur(3px);
        }
        
        .shape:nth-child(7) {
            width: 120px;
            height: 120px;
            top: 25%;
            right: 25%;
            animation: float-7 11s infinite;
            opacity: 0.45;
            background: radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.15));
        }
        
        .shape:nth-child(8) {
            width: 170px;
            height: 170px;
            bottom: 30%;
            right: 20%;
            animation: float-8 14s infinite;
            opacity: 0.4;
            background: radial-gradient(ellipse, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.1));
            filter: blur(2px);
        }
        
        @keyframes float-1 {
            0% {
                transform: translate(0, 0) rotate(0deg) scale(1);
            }
            25% {
                transform: translate(100px, 50px) rotate(20deg) scale(1.1);
            }
            50% {
                transform: translate(150px, -30px) rotate(40deg) scale(1.2);
            }
            75% {
                transform: translate(80px, -80px) rotate(20deg) scale(1.1);
            }
            100% {
                transform: translate(0, 0) rotate(0deg) scale(1);
            }
        }
        
        @keyframes float-2 {
            0% {
                transform: translate(0, 0) rotate(0deg) scale(1);
            }
            33% {
                transform: translate(-80px, -100px) rotate(-25deg) scale(1.15);
            }
            66% {
                transform: translate(-140px, 0) rotate(-50deg) scale(1.3);
            }
            100% {
                transform: translate(0, 0) rotate(0deg) scale(1);
            }
        }
        
        @keyframes float-3 {
            0% {
                transform: translate(0, 0) rotate(0deg) scale(1);
            }
            25% {
                transform: translate(120px, 40px) rotate(15deg) scale(0.9);
            }
            50% {
                transform: translate(80px, 100px) rotate(30deg) scale(0.8);
            }
            75% {
                transform: translate(40px, 40px) rotate(15deg) scale(0.9);
            }
            100% {
                transform: translate(0, 0) rotate(0deg) scale(1);
            }
        }
        
        @keyframes float-4 {
            0% {
                transform: translate(0, 0) rotate(0deg) scale(1);
            }
            33% {
                transform: translate(-30px, 50px) rotate(-15deg) scale(0.85);
            }
            66% {
                transform: translate(-60px, 20px) rotate(-30deg) scale(0.9);
            }
            100% {
                transform: translate(0, 0) rotate(0deg) scale(1);
            }
        }
        
        @keyframes float-5 {
            0% {
                transform: translate(0, 0) rotate(0deg) scale(1);
            }
            20% {
                transform: translate(30px, -30px) rotate(10deg) scale(1.15);
            }
            40% {
                transform: translate(60px, -60px) rotate(20deg) scale(1.3);
            }
            60% {
                transform: translate(60px, -30px) rotate(20deg) scale(1.15);
            }
            80% {
                transform: translate(30px, 0) rotate(10deg) scale(1);
            }
            100% {
                transform: translate(0, 0) rotate(0deg) scale(1);
            }
        }
        
        @keyframes float-6 {
            0% {
                transform: translate(0, 0) rotate(0deg) scale(1);
            }
            25% {
                transform: translate(-40px, -30px) rotate(-10deg) scale(1.1);
            }
            50% {
                transform: translate(0, -60px) rotate(0deg) scale(1.2);
            }
            75% {
                transform: translate(40px, -30px) rotate(10deg) scale(1.1);
            }
            100% {
                transform: translate(0, 0) rotate(0deg) scale(1);
            }
        }
        
        @keyframes float-7 {
            0% {
                transform: translate(0, 0) rotate(0deg) scale(1);
            }
            33% {
                transform: translate(-30px, 50px) rotate(-15deg) scale(0.95);
            }
            66% {
                transform: translate(-60px, 0) rotate(-30deg) scale(0.9);
            }
            100% {
                transform: translate(0, 0) rotate(0deg) scale(1);
            }
        }
        
        @keyframes float-8 {
            0% {
                transform: translate(0, 0) rotate(0deg) scale(1);
            }
            20% {
                transform: translate(20px, 40px) rotate(10deg) scale(1.2);
            }
            40% {
                transform: translate(40px, 80px) rotate(20deg) scale(1.4);
            }
            60% {
                transform: translate(60px, 40px) rotate(30deg) scale(1.2);
            }
            80% {
                transform: translate(30px, 0) rotate(15deg) scale(1);
            }
            100% {
                transform: translate(0, 0) rotate(0deg) scale(1);
            }
        }
        
        @keyframes float {
            0% {
                transform: translateY(0) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(5deg);
            }
            100% {
                transform: translateY(0) rotate(0deg);
            }
        }
        
        .login-container {
            width: 100%;
            max-width: 520px;
            padding: 20px;
            position: relative;
            z-index: 1;
            animation: fadeInUp 0.8s ease-out;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(40px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .form-signin {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px 40px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .form-signin:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 60px rgba(0, 0, 0, 0.25);
        }
        
        .form-signin::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 6px;
            background: linear-gradient(to right, var(--primary), var(--secondary));
        }
        
        .login-title {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 25px;
            color: var(--primary);
            font-weight: 800;
            font-size: 2rem;
        }
        
        .login-title .icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border-radius: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
            box-shadow: 0 8px 20px rgba(71, 118, 230, 0.3);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(71, 118, 230, 0.4);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(71, 118, 230, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(71, 118, 230, 0);
            }
        }
        
        .login-title .text {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-size: 2rem;
        }
        
        .form-group {
            margin-bottom: 20px;
            position: relative;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--text);
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 20px;
            font-size: 1rem;
            border: 2px solid var(--border);
            border-radius: 14px;
            background: var(--input-bg);
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 4px rgba(71, 118, 230, 0.1);
            outline: none;
            transform: translateY(-2px);
        }
        
        .icon-input {
            position: relative;
        }
        
        .icon-input i {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            left: 18px;
            color: #a1a8c3;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }
        
        .icon-input input {
            padding-left: 50px;
        }
        
        .icon-input input:focus + i {
            color: var(--primary);
            transform: translateY(-50%) scale(1.1);
        }
        
        .btn-login {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border: none;
            border-radius: 14px;
            color: var(--light-text);
            font-weight: 600;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 10px 25px rgba(71, 118, 230, 0.3);
            margin-top: 18px;
            position: relative;
            overflow: hidden;
        }
        
        .btn-login:hover {
            background: linear-gradient(135deg, #396be0, #8248e6);
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(71, 118, 230, 0.4);
        }
        
        .btn-login:active {
            transform: translateY(0);
            box-shadow: 0 5px 15px rgba(71, 118, 230, 0.3);
        }
        
        .btn-login::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 5px;
            height: 5px;
            background: rgba(255, 255, 255, 0.5);
            opacity: 0;
            border-radius: 100%;
            transform: scale(1, 1) translate(-50%);
            transform-origin: 50% 50%;
        }
        
        @keyframes ripple {
            0% {
                transform: scale(0, 0);
                opacity: 0.5;
            }
            100% {
                transform: scale(20, 20);
                opacity: 0;
            }
        }
        
        .btn-login:focus:not(:active)::after {
            animation: ripple 1s ease-out;
        }
        
        .footer-text {
            text-align: center;
            margin-top: 25px;
            color: rgba(44, 62, 80, 0.7);
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .alert {
            border-radius: 16px;
            padding: 16px;
            margin-bottom: 25px;
            border: none;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            animation: slideInDown 0.5s ease-out;
        }
        
        @keyframes slideInDown {
            from {
                transform: translateY(-20px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
        
        .form-group.captcha-group {
            margin-bottom: 20px;
        }
        
        .captcha-row {
            display: flex;
            align-items: flex-start;
            gap: 15px;
        }
        
        .captcha-left {
            display: flex;
            gap: 10px;
        }
        
        .captcha-right {
            flex: 1;
        }
        
        .captcha-container {
            display: flex;
            align-items: center;
            margin-bottom: 0;
        }
        
        .captcha-image {
            border-radius: 12px;
            border: 2px solid var(--border);
            height: 44px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        }
        
        .captcha-image:hover {
            border-color: var(--primary);
            transform: scale(1.02);
        }
        
        .captcha-refresh {
            background: #f0f3fa;
            border: 2px solid #e1e5f2;
            border-radius: 12px;
            width: 44px;
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        }
        
        .captcha-refresh:hover {
            background: #e1e5f2;
            transform: rotate(180deg);
        }
        
        .captcha-refresh i {
            color: var(--primary);
            font-size: 1.1rem;
        }
    </style>
</head>
<body>
    <div class="background-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>
    
    <div class="login-container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <form class="form-signin" method="post" action="/admin/login">
            <div class="login-title">
                <div class="icon">
                    <i class="fas fa-user-shield"></i>
                </div>
                <div class="text">管理员登录</div>
            </div>
            
            <div class="form-group">
                <label for="username">用户名</label>
                <div class="icon-input">
                    <input type="text" class="form-control" id="username" name="username" placeholder="请输入用户名" required>
                    <i class="fas fa-user"></i>
                </div>
            </div>
            
            <div class="form-group">
                <label for="password">密码</label>
                <div class="icon-input">
                    <input type="password" class="form-control" id="password" name="password" placeholder="请输入密码" required>
                    <i class="fas fa-lock"></i>
                </div>
            </div>
            
            <div class="form-group captcha-group">
                <label for="captcha">验证码</label>
                <div class="captcha-row">
                    <div class="captcha-left">
                    <img src="/captcha" alt="验证码" class="captcha-image" id="captchaImage" onclick="refreshCaptcha()">
                    <div class="captcha-refresh" onclick="refreshCaptcha()">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                </div>
                    <div class="captcha-right">
                <div class="icon-input">
                    <input type="text" class="form-control" id="captcha" name="captcha" placeholder="请输入验证码" required>
                    <i class="fas fa-shield-alt"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <button class="btn-login" type="submit">登 录</button>
            
            <div class="footer-text">
                大数据题库管理系统 © <span id="currentYear"></span>
            </div>
        </form>
    </div>
    
    <script>
        function refreshCaptcha() {
            document.getElementById('captchaImage').src = "/captcha?" + new Date().getTime();
        }
        
        // 在页面加载完成后自动设置当前年份
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('currentYear').innerText = new Date().getFullYear();
            
            // Add ripple effect to login button
            document.querySelector('.btn-login').addEventListener('click', function(e) {
                let x = e.clientX - e.target.getBoundingClientRect().left;
                let y = e.clientY - e.target.getBoundingClientRect().top;
                
                let ripple = document.createElement('span');
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                
                this.appendChild(ripple);
                
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });
    </script>
</body>
</html> 
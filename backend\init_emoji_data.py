#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
表情包数据初始化脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from cat_circle_api import init_cat_circle_api

def init_emoji_data():
    """初始化表情包数据"""
    with app.app_context():
        try:
            # 初始化猫友圈API（包含表情包模型）
            init_cat_circle_api(db)
            
            # 导入模型
            from cat_circle_api import EmojiCategory, Emoji
            
            # 创建表
            db.create_all()
            
            # 检查是否已有数据
            if EmojiCategory.query.count() > 0:
                print("✅ 表情包分类数据已存在")
                return
            
            # 插入表情包分类数据
            categories_data = [
                {'name': '经典表情', 'icon': '😀', 'sort_order': 1},
                {'name': '动物表情', 'icon': '🐱', 'sort_order': 2},
                {'name': '手势表情', 'icon': '👍', 'sort_order': 3},
                {'name': '爱心表情', 'icon': '❤️', 'sort_order': 4},
                {'name': '食物表情', 'icon': '🍎', 'sort_order': 5},
                {'name': '运动表情', 'icon': '⚽', 'sort_order': 6}
            ]
            
            categories = []
            for cat_data in categories_data:
                category = EmojiCategory(**cat_data)
                db.session.add(category)
                categories.append(category)
            
            db.session.commit()
            print(f"✅ 已添加 {len(categories)} 个表情包分类")
            
            # 插入表情包数据
            emojis_data = [
                # 经典表情 (category_id=1)
                {'category_id': 1, 'name': '开心', 'image_url': '😀', 'keywords': '开心,高兴,笑', 'sort_order': 1},
                {'category_id': 1, 'name': '大笑', 'image_url': '😂', 'keywords': '大笑,哈哈,搞笑', 'sort_order': 2},
                {'category_id': 1, 'name': '眨眼', 'image_url': '😉', 'keywords': '眨眼,调皮', 'sort_order': 3},
                {'category_id': 1, 'name': '亲吻', 'image_url': '😘', 'keywords': '亲吻,么么哒', 'sort_order': 4},
                {'category_id': 1, 'name': '爱心眼', 'image_url': '😍', 'keywords': '爱心,喜欢,迷恋', 'sort_order': 5},
                {'category_id': 1, 'name': '酷', 'image_url': '😎', 'keywords': '酷,墨镜,帅', 'sort_order': 6},
                
                # 动物表情 (category_id=2)
                {'category_id': 2, 'name': '猫咪', 'image_url': '🐱', 'keywords': '猫,猫咪,可爱', 'sort_order': 1},
                {'category_id': 2, 'name': '狗狗', 'image_url': '🐶', 'keywords': '狗,狗狗,汪', 'sort_order': 2},
                {'category_id': 2, 'name': '熊猫', 'image_url': '🐼', 'keywords': '熊猫,国宝', 'sort_order': 3},
                {'category_id': 2, 'name': '兔子', 'image_url': '🐰', 'keywords': '兔子,可爱', 'sort_order': 4},
                {'category_id': 2, 'name': '老虎', 'image_url': '🐯', 'keywords': '老虎,威猛', 'sort_order': 5},
                {'category_id': 2, 'name': '狮子', 'image_url': '🦁', 'keywords': '狮子,王者', 'sort_order': 6},
                
                # 手势表情 (category_id=3)
                {'category_id': 3, 'name': '点赞', 'image_url': '👍', 'keywords': '点赞,好,赞', 'sort_order': 1},
                {'category_id': 3, 'name': '差评', 'image_url': '👎', 'keywords': '差评,不好', 'sort_order': 2},
                {'category_id': 3, 'name': 'OK', 'image_url': '👌', 'keywords': 'OK,好的', 'sort_order': 3},
                {'category_id': 3, 'name': '胜利', 'image_url': '✌️', 'keywords': '胜利,耶', 'sort_order': 4},
                {'category_id': 3, 'name': '拳头', 'image_url': '👊', 'keywords': '拳头,加油', 'sort_order': 5},
                {'category_id': 3, 'name': '鼓掌', 'image_url': '👏', 'keywords': '鼓掌,棒', 'sort_order': 6},
                
                # 爱心表情 (category_id=4)
                {'category_id': 4, 'name': '红心', 'image_url': '❤️', 'keywords': '爱心,红心,爱', 'sort_order': 1},
                {'category_id': 4, 'name': '粉心', 'image_url': '💕', 'keywords': '粉心,爱,喜欢', 'sort_order': 2},
                {'category_id': 4, 'name': '蓝心', 'image_url': '💙', 'keywords': '蓝心,爱', 'sort_order': 3},
                {'category_id': 4, 'name': '绿心', 'image_url': '💚', 'keywords': '绿心,爱', 'sort_order': 4},
                {'category_id': 4, 'name': '黄心', 'image_url': '💛', 'keywords': '黄心,爱', 'sort_order': 5},
                {'category_id': 4, 'name': '紫心', 'image_url': '💜', 'keywords': '紫心,爱', 'sort_order': 6},
                
                # 食物表情 (category_id=5)
                {'category_id': 5, 'name': '苹果', 'image_url': '🍎', 'keywords': '苹果,水果', 'sort_order': 1},
                {'category_id': 5, 'name': '香蕉', 'image_url': '🍌', 'keywords': '香蕉,水果', 'sort_order': 2},
                {'category_id': 5, 'name': '蛋糕', 'image_url': '🎂', 'keywords': '蛋糕,生日', 'sort_order': 3},
                {'category_id': 5, 'name': '披萨', 'image_url': '🍕', 'keywords': '披萨,美食', 'sort_order': 4},
                {'category_id': 5, 'name': '汉堡', 'image_url': '🍔', 'keywords': '汉堡,美食', 'sort_order': 5},
                {'category_id': 5, 'name': '咖啡', 'image_url': '☕', 'keywords': '咖啡,饮品', 'sort_order': 6},
                
                # 运动表情 (category_id=6)
                {'category_id': 6, 'name': '足球', 'image_url': '⚽', 'keywords': '足球,运动', 'sort_order': 1},
                {'category_id': 6, 'name': '篮球', 'image_url': '🏀', 'keywords': '篮球,运动', 'sort_order': 2},
                {'category_id': 6, 'name': '网球', 'image_url': '🎾', 'keywords': '网球,运动', 'sort_order': 3},
                {'category_id': 6, 'name': '游泳', 'image_url': '🏊', 'keywords': '游泳,运动', 'sort_order': 4},
                {'category_id': 6, 'name': '跑步', 'image_url': '🏃', 'keywords': '跑步,运动', 'sort_order': 5},
                {'category_id': 6, 'name': '骑行', 'image_url': '🚴', 'keywords': '骑行,运动', 'sort_order': 6}
            ]
            
            for emoji_data in emojis_data:
                emoji = Emoji(**emoji_data)
                db.session.add(emoji)
            
            db.session.commit()
            print(f"✅ 已添加 {len(emojis_data)} 个表情包")
            
            # 验证数据
            category_count = EmojiCategory.query.count()
            emoji_count = Emoji.query.count()
            print(f"✅ 数据验证: {category_count} 个分类, {emoji_count} 个表情包")
            
            print("✅ 表情包数据初始化完成!")
            
        except Exception as e:
            print(f"❌ 表情包数据初始化失败: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    init_emoji_data()

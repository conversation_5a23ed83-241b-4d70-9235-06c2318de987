/* 页面容器 */
.profile-container {
  padding: 30rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

/* 页面标题 */
.profile-header {
  margin-bottom: 40rpx;
}

.profile-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

/* 头像部分 */
.profile-avatar-section, 
.profile-nickname-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  display: block;
  font-size: 30rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.avatar-wrapper {
  padding: 0;
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  margin: 20rpx auto;
  background: none;
  border: none;
}

.avatar-wrapper::after {
  border: none;
}

.avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  overflow: hidden;
  border: 2rpx solid #e5e5e5;
}

.tips {
  display: block;
  font-size: 24rpx;
  color: #999;
  text-align: center;
  margin-top: 10rpx;
}

/* 昵称部分 */
.nickname-input {
  height: 80rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  margin: 20rpx 0;
  font-size: 28rpx;
}

/* 按钮部分 */
.btn-section {
  margin-top: 60rpx;
  padding: 0 30rpx;
}

.save-btn {
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
  border-radius: 44rpx;
}

/* 加载中遮罩 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
} 
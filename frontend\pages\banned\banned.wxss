/* pages/banned/banned.wxss */
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  height: 100vh;
  background-color: #f8f8f8;
}

.banned-icon {
  margin-bottom: 40rpx;
}

.banned-icon image {
  width: 200rpx;
  height: 200rpx;
}

.banned-title {
  font-size: 46rpx;
  color: #ff4d4f;
  font-weight: bold;
  margin-bottom: 40rpx;
  text-align: center;
}

.banned-message {
  width: 90%;
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 60rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.banned-message text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
  line-height: 1.5;
}

.user-info {
  color: #576b95 !important;
  font-weight: bold;
}

.time-info {
  color: #999 !important;
  font-size: 28rpx !important;
}

.reason-info {
  color: #ff4d4f !important;
  margin-top: 10rpx;
}

.action-area {
  width: 90%;
  display: flex;
  flex-direction: column;
}

.contact-support,
.return-login {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  border-radius: 45rpx;
  margin-bottom: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.contact-support {
  background-color: #4C84FF;
  color: white;
}

.contact-support::after {
  border: none;
}

.return-login {
  background-color: #f0f0f0;
  color: #666;
}

.return-login::after {
  border: none;
} 
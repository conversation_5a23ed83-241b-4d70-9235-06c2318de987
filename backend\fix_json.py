import json

try:
    # First, attempt to load the JSON to see if we can get a more detailed error
    with open('questions.json', 'r', encoding='utf-8') as f:
        json.load(f)
except json.JSONDecodeError as e:
    print(f"JSON error: {e}")
    
    # Read the file as text
    with open('questions.json', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Get context around the error
    error_position = e.pos
    start = max(0, error_position - 50)
    end = min(len(content), error_position + 50)
    
    print(f"Error context: '{content[start:error_position]}' | HERE | '{content[error_position:end]}'")
    
    # 获取错误位置前后的几行
    lines = content.split('\n')
    line_num = 1
    current_pos = 0
    for i, line in enumerate(lines):
        if current_pos <= error_position < current_pos + len(line) + 1:  # +1 for newline
            line_num = i + 1
            col = error_position - current_pos + 1
            print(f"Error at line {line_num}, column {col}")
            # 打印错误前后的几行
            start_line = max(0, line_num - 5)
            end_line = min(len(lines), line_num + 5)
            for j in range(start_line - 1, end_line):
                prefix = "-> " if j+1 == line_num else "   "
                print(f"{prefix}{j+1}: {lines[j]}")
            break
        current_pos += len(line) + 1  # +1 for newline
    
    # 尝试修复错误
    if "Expecting ',' delimiter" in str(e):
        # 在错误位置插入逗号
        fixed_content = content[:error_position] + "," + content[error_position:]
        
        # 保存修复后的内容
        with open('questions_fixed.json', 'w', encoding='utf-8') as f:
            f.write(fixed_content)
        
        # 验证修复是否成功
        try:
            with open('questions_fixed.json', 'r', encoding='utf-8') as f:
                json.load(f)
            print("修复成功! 修复后的文件已保存为questions_fixed.json")
            
            # 将修复后的内容写回原文件
            with open('questions.json', 'w', encoding='utf-8') as f:
                f.write(fixed_content)
            print("已将修复后的内容写回原文件 questions.json")
        except json.JSONDecodeError as e2:
            print(f"修复尝试失败: {e2}") 
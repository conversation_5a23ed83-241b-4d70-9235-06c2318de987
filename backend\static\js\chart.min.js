/*!
 * Chart.js v3.9.1
 * https://www.chartjs.org
 * (c) 2022 Chart.js Contributors
 * Released under the MIT License
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).Chart=e()}(this,(function(){"use strict";class t{constructor(){this.color="#666",this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.padding={top:0,right:0,bottom:0,left:0},this.textAlign="start",this.textBaseline="middle"}}const e={_scriptable:t=>!t.startsWith("on"),_indexable:t=>"string"!=typeof t,_all:!0},i=new class{constructor(){this.fontFamily="'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",this.fontSize=12,this.fontStyle="normal",this.fontWeight="normal",this.lineHeight=1.2,this.color="#666",this.textAlign="left",this.textBaseline="middle"}}; 

// Simplified Chart.js implementation for basic charts
function Chart(context, config) {
  this.ctx = context.getContext('2d');
  this.canvas = context;
  this.config = config;
  this.data = config.data || {};
  this.options = config.options || {};
  this.type = config.type;
  
  // Initialize and draw the chart
  this.init();
}

// Chart types
Chart.prototype.init = function() {
  // Clear canvas
  this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
  
  switch(this.type) {
    case 'pie':
    case 'doughnut':
      this.drawPie();
      break;
    case 'bar':
      this.drawBar();
      break;
    case 'line':
      this.drawLine();
      break;
    default:
      console.error('Unsupported chart type:', this.type);
  }
  
  // Draw legend if needed
  if (this.options.plugins && this.options.plugins.legend && this.options.plugins.legend.display !== false) {
    this.drawLegend();
  }
};

// Draw pie/doughnut chart
Chart.prototype.drawPie = function() {
  const width = this.ctx.canvas.width;
  const height = this.ctx.canvas.height;
  const centerX = width / 2;
  const centerY = height / 2;
  const radius = Math.min(centerX, centerY) * 0.8;
  
  console.log('Drawing pie chart with radius:', radius);
  this.ctx.clearRect(0, 0, width, height);
  
  // Draw each segment
  let startAngle = 0;
  let totalValue = this.data.datasets[0].data.reduce((sum, value) => sum + value, 0);
  
  // If totalValue is zero, create a simple placeholder circle
  if (totalValue === 0) {
    this.ctx.beginPath();
    this.ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
    this.ctx.fillStyle = "#ccc";
    this.ctx.fill();
    this.ctx.fillStyle = "#000";
    this.ctx.font = "14px Arial";
    this.ctx.textAlign = "center";
    this.ctx.textBaseline = "middle";
    this.ctx.fillText("No Data", centerX, centerY);
    return;
  }
  
  // Draw each segment
  this.data.datasets[0].data.forEach((value, i) => {
    const segmentAngle = (value / totalValue) * Math.PI * 2;
    
    // Only draw if the segment has a visible angle
    if (segmentAngle > 0.01) {
      this.ctx.beginPath();
      this.ctx.moveTo(centerX, centerY);
      this.ctx.arc(centerX, centerY, radius, startAngle, startAngle + segmentAngle);
      this.ctx.closePath();
      
      const color = this.data.datasets[0].backgroundColor[i] || this.getRandomColor();
      this.ctx.fillStyle = color;
      this.ctx.fill();
      
      // Draw label if segment is large enough
      if (segmentAngle > 0.3) {
        const labelAngle = startAngle + segmentAngle / 2;
        const labelX = centerX + Math.cos(labelAngle) * (radius * 0.7);
        const labelY = centerY + Math.sin(labelAngle) * (radius * 0.7);
        
        this.ctx.fillStyle = "#fff";
        this.ctx.font = "12px Arial";
        this.ctx.textAlign = "center";
        this.ctx.textBaseline = "middle";
        
        if (this.data.labels && this.data.labels[i]) {
          this.ctx.fillText(this.data.labels[i], labelX, labelY);
        }
      }
      
      startAngle += segmentAngle;
    }
  });
  
  // Draw center circle for donut effect if requested
  if (this.options && this.options.cutout) {
    const cutoutRadius = radius * (this.options.cutout / 100);
    this.ctx.beginPath();
    this.ctx.arc(centerX, centerY, cutoutRadius, 0, Math.PI * 2);
    this.ctx.fillStyle = "#fff";
    this.ctx.fill();
  }
};

// Draw bar chart
Chart.prototype.drawBar = function() {
  const data = this.data.datasets[0].data;
  const labels = this.data.labels || [];
  const colors = this.data.datasets[0].backgroundColor || [];
  
  const chartWidth = this.canvas.width * 0.8;
  const chartHeight = this.canvas.height * 0.8;
  const marginLeft = this.canvas.width * 0.1;
  const marginBottom = this.canvas.height * 0.1;
  
  const barWidth = chartWidth / data.length * 0.8;
  const barSpacing = chartWidth / data.length * 0.2;
  
  const maxValue = Math.max(...data) * 1.1; // 10% higher than max value
  
  // Draw bars
  data.forEach((value, index) => {
    const barHeight = (value / maxValue) * chartHeight;
    const x = marginLeft + (barWidth + barSpacing) * index;
    const y = this.canvas.height - marginBottom - barHeight;
    
    this.ctx.fillStyle = colors[index] || this.getRandomColor();
    this.ctx.fillRect(x, y, barWidth, barHeight);
    
    // Draw labels
    if (labels[index]) {
      this.ctx.fillStyle = '#333';
      this.ctx.font = '10px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText(labels[index], x + barWidth/2, this.canvas.height - marginBottom + 15);
    }
    
    // Draw value on top of bar
    this.ctx.fillStyle = '#333';
    this.ctx.font = '10px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(value.toString(), x + barWidth/2, y - 5);
  });
};

// Draw line chart
Chart.prototype.drawLine = function() {
  const data = this.data.datasets[0].data;
  const labels = this.data.labels || [];
  
  const chartWidth = this.canvas.width * 0.8;
  const chartHeight = this.canvas.height * 0.8;
  const marginLeft = this.canvas.width * 0.1;
  const marginBottom = this.canvas.height * 0.1;
  const marginTop = this.canvas.height * 0.1;
  
  const maxValue = Math.max(...data) * 1.1; // 10% higher than max value
  const pointSpacing = chartWidth / (data.length - 1);
  
  // Draw axes
  this.ctx.beginPath();
  this.ctx.moveTo(marginLeft, marginTop);
  this.ctx.lineTo(marginLeft, this.canvas.height - marginBottom);
  this.ctx.lineTo(marginLeft + chartWidth, this.canvas.height - marginBottom);
  this.ctx.strokeStyle = '#ccc';
  this.ctx.lineWidth = 1;
  this.ctx.stroke();
  
  // Draw line
  this.ctx.beginPath();
  this.ctx.moveTo(marginLeft, this.canvas.height - marginBottom - (data[0] / maxValue) * chartHeight);
  
  data.forEach((value, index) => {
    const x = marginLeft + pointSpacing * index;
    const y = this.canvas.height - marginBottom - (value / maxValue) * chartHeight;
    
    this.ctx.lineTo(x, y);
  });
  
  this.ctx.strokeStyle = this.data.datasets[0].borderColor || 'blue';
  this.ctx.lineWidth = 2;
  this.ctx.stroke();
  
  // Fill area if specified
  if (this.data.datasets[0].fill) {
    this.ctx.lineTo(marginLeft + pointSpacing * (data.length - 1), this.canvas.height - marginBottom);
    this.ctx.lineTo(marginLeft, this.canvas.height - marginBottom);
    this.ctx.closePath();
    this.ctx.fillStyle = this.data.datasets[0].backgroundColor || 'rgba(0, 0, 255, 0.1)';
    this.ctx.fill();
  }
  
  // Draw points
  data.forEach((value, index) => {
    const x = marginLeft + pointSpacing * index;
    const y = this.canvas.height - marginBottom - (value / maxValue) * chartHeight;
    
    this.ctx.beginPath();
    this.ctx.arc(x, y, 4, 0, 2 * Math.PI);
    this.ctx.fillStyle = this.data.datasets[0].pointBackgroundColor || 'blue';
    this.ctx.fill();
    this.ctx.strokeStyle = '#fff';
    this.ctx.lineWidth = 1;
    this.ctx.stroke();
    
    // Draw labels on x-axis
    if (labels[index]) {
      this.ctx.fillStyle = '#333';
      this.ctx.font = '10px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText(labels[index], x, this.canvas.height - marginBottom + 15);
    }
  });
};

// Draw chart legend
Chart.prototype.drawLegend = function() {
  const data = this.data.datasets[0].data;
  const labels = this.data.labels || [];
  const colors = this.data.datasets[0].backgroundColor || [];
  
  const legendX = this.canvas.width * 0.7;
  const legendY = this.canvas.height * 0.1;
  const itemHeight = 20;
  
  labels.forEach((label, index) => {
    const y = legendY + index * itemHeight;
    
    // Draw colored square
    this.ctx.fillStyle = Array.isArray(colors) ? colors[index] : colors;
    this.ctx.fillRect(legendX, y, 15, 15);
    
    // Draw label text
    this.ctx.fillStyle = '#333';
    this.ctx.font = '12px Arial';
    this.ctx.textAlign = 'left';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText(label, legendX + 20, y + 7.5);
  });
};

// Helper to generate random colors
Chart.prototype.getRandomColor = function() {
  const r = Math.floor(Math.random() * 200);
  const g = Math.floor(Math.random() * 200);
  const b = Math.floor(Math.random() * 200);
  return `rgb(${r}, ${g}, ${b})`;
};

// Default chart options
Chart.defaults = {
  font: {
    family: "'Inter', 'Helvetica', 'Arial', sans-serif",
    size: 12
  },
  color: '#64748b',
  plugins: {
    tooltip: {
      backgroundColor: '#1e293b',
      titleColor: '#ffffff',
      bodyColor: '#e2e8f0',
      padding: 10,
      cornerRadius: 6,
      displayColors: false
    },
    legend: {
      labels: {
        usePointStyle: true
      }
    }
  },
  elements: {
    point: {
      radius: 4,
      hoverRadius: 6
    }
  }
};

// Chart registration function
Chart.register = function() {
  // Stub for compatibility
};

// Create new Chart
Chart.getChart = function(canvas) {
  return null; // Stub for compatibility
};

// Chart types
Chart.controllers = {};

return Chart;
})); 
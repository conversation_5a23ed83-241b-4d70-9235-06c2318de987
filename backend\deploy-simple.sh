#!/bin/bash

# 设置部署目录
DEPLOY_DIR="/www/wwwroot/www.qiangs.xyz"
LOG_DIR="/www/wwwlogs"

echo "===== 简化部署脚本 (使用 Gunicorn) ====="

# 创建部署目录(如果不存在)
if [ ! -d "$DEPLOY_DIR" ]; then
    mkdir -p "$DEPLOY_DIR"
    echo "创建部署目录 $DEPLOY_DIR"
fi

# 复制所有文件到部署目录
echo "复制文件到部署目录..."
cp -r * "$DEPLOY_DIR/"

# 创建虚拟环境(如果不存在)
if [ ! -d "$DEPLOY_DIR/venv" ]; then
    echo "创建Python虚拟环境..."
    cd "$DEPLOY_DIR"
    python3 -m venv venv
fi

# 激活虚拟环境并安装依赖
echo "安装Python依赖..."
cd "$DEPLOY_DIR"
source venv/bin/activate
pip install -r requirements.txt
pip install gunicorn

# 确保questions.json文件存在
if [ ! -f "$DEPLOY_DIR/questions.json" ]; then
    echo "questions.json文件不存在，尝试在上级目录查找..."
    if [ -f "../questions.json" ]; then
        cp "../questions.json" "$DEPLOY_DIR/"
        echo "已复制questions.json文件"
    else
        echo "警告: questions.json文件未找到！"
    fi
fi

# 创建启动脚本
echo "创建启动脚本..."
cat > "$DEPLOY_DIR/start.sh" << 'EOL'
#!/bin/bash
cd /www/wwwroot/www.qiangs.xyz
source venv/bin/activate
export FLASK_APP=app.py
export FLASK_ENV=production
gunicorn -w 4 -b 127.0.0.1:8080 app:app --daemon --access-logfile /www/wwwlogs/gunicorn_access.log --error-logfile /www/wwwlogs/gunicorn_error.log
echo "Application started with gunicorn!"
EOL

# 创建停止脚本
echo "创建停止脚本..."
cat > "$DEPLOY_DIR/stop.sh" << 'EOL'
#!/bin/bash
pkill -f "gunicorn.*app:app"
echo "Application stopped!"
EOL

# 设置权限
echo "设置执行权限..."
chmod +x "$DEPLOY_DIR/start.sh"
chmod +x "$DEPLOY_DIR/stop.sh"

# 重启应用
echo "重启应用..."
bash "$DEPLOY_DIR/stop.sh"
sleep 2
bash "$DEPLOY_DIR/start.sh"

# 设置文件权限
echo "设置文件权限..."
chown -R www:www "$DEPLOY_DIR"
chmod -R 755 "$DEPLOY_DIR"

echo "部署完成！应用已启动。"
echo "请确保Nginx配置已正确设置!" 
// pages/emoji/emoji.js
const app = getApp()

Page({
  data: {
    sessionId: null,
    receiverId: null,
    categories: [],
    currentCategoryId: null,
    emojis: [],
    searchKeyword: '',
    isSearching: false,
    loading: false
  },

  onLoad: function(options) {
    const sessionId = options.sessionId
    const receiverId = options.receiverId

    if (!sessionId || !receiverId) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    this.setData({
      sessionId: parseInt(sessionId),
      receiverId: parseInt(receiverId)
    })

    this.loadCategories()
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack()
  },

  // 加载表情包分类
  loadCategories: function() {
    const that = this
    wx.request({
      url: `${app.globalData.baseUrl}/cat_circle/chat/emoji/categories`,
      method: 'GET',
      success: function(res) {
        if (res.data.code === 200) {
          const categories = res.data.data.categories || []
          that.setData({
            categories: categories
          })
          
          // 默认选择第一个分类
          if (categories.length > 0) {
            that.setData({
              currentCategoryId: categories[0].id
            })
            that.loadEmojis(categories[0].id)
          }
        } else {
          wx.showToast({
            title: res.data.message || '加载分类失败',
            icon: 'none'
          })
        }
      },
      fail: function(err) {
        console.error('加载表情包分类失败:', err)
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
      }
    })
  },

  // 选择分类
  selectCategory: function(e) {
    const categoryId = e.currentTarget.dataset.id
    this.setData({
      currentCategoryId: categoryId,
      isSearching: false,
      searchKeyword: ''
    })
    this.loadEmojis(categoryId)
  },

  // 加载表情包
  loadEmojis: function(categoryId) {
    this.setData({ loading: true })

    const that = this
    wx.request({
      url: `${app.globalData.baseUrl}/cat_circle/chat/emoji/categories/${categoryId}/emojis`,
      method: 'GET',
      success: function(res) {
        that.setData({ loading: false })
        
        if (res.data.code === 200) {
          that.setData({
            emojis: res.data.data.emojis || []
          })
        } else {
          wx.showToast({
            title: res.data.message || '加载表情包失败',
            icon: 'none'
          })
        }
      },
      fail: function(err) {
        that.setData({ loading: false })
        console.error('加载表情包失败:', err)
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
      }
    })
  },

  // 搜索输入
  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    })
  },

  // 搜索表情包
  searchEmojis: function() {
    const keyword = this.data.searchKeyword.trim()
    if (!keyword) {
      this.setData({
        isSearching: false
      })
      // 回到当前分类
      if (this.data.currentCategoryId) {
        this.loadEmojis(this.data.currentCategoryId)
      }
      return
    }

    this.setData({ 
      loading: true,
      isSearching: true 
    })

    const that = this
    wx.request({
      url: `${app.globalData.baseUrl}/cat_circle/chat/emoji/search`,
      method: 'GET',
      data: {
        keyword: keyword
      },
      success: function(res) {
        that.setData({ loading: false })
        
        if (res.data.code === 200) {
          that.setData({
            emojis: res.data.data.emojis || []
          })
        } else {
          wx.showToast({
            title: res.data.message || '搜索失败',
            icon: 'none'
          })
        }
      },
      fail: function(err) {
        that.setData({ loading: false })
        console.error('搜索表情包失败:', err)
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
      }
    })
  },

  // 选择表情包
  selectEmoji: function(e) {
    const emoji = e.currentTarget.dataset.emoji
    
    // 发送表情包消息
    this.sendEmojiMessage(emoji)
  },

  // 发送表情包消息
  sendEmojiMessage: function(emoji) {
    const userInfo = wx.getStorageSync('userInfo')
    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    wx.showLoading({
      title: '发送中...'
    })

    const that = this
    wx.request({
      url: `${app.globalData.baseUrl}/cat_circle/chat/messages`,
      method: 'POST',
      data: {
        session_id: this.data.sessionId,
        sender_id: userInfo.id,
        receiver_id: this.data.receiverId,
        content: emoji.name,
        message_type: 'emoji',
        extra_data: {
          emoji_id: emoji.id,
          emoji_url: emoji.image_url
        }
      },
      success: function(res) {
        wx.hideLoading()
        
        if (res.data.code === 200) {
          wx.showToast({
            title: '发送成功',
            icon: 'success'
          })
          
          // 返回聊天页面
          setTimeout(() => {
            wx.navigateBack()
          }, 1000)
        } else {
          wx.showToast({
            title: res.data.message || '发送失败',
            icon: 'none'
          })
        }
      },
      fail: function(err) {
        wx.hideLoading()
        console.error('发送表情包失败:', err)
        wx.showToast({
          title: '发送失败',
          icon: 'none'
        })
      }
    })
  }
})

Component({
  data: {
    selected: 0,
    color: "#7A7E83",
    selectedColor: "#4e8df7",
    list: [
      {
        pagePath: "/pages/index/index",
        iconPath: "/icons/home.png",
        selectedIconPath: "/icons/home-active.png",
        text: "首页"
      },
      {
        pagePath: "/pages/wrong-questions/wrong-questions",
        iconPath: "/icons/wrong.png",
        selectedIconPath: "/icons/wrong-active.png",
        text: "错题本"
      },
      {
        pagePath: "/pages/cat-friends/cat-friends",
        iconPath: "/icons/cat.png",
        selectedIconPath: "/icons/cat.png",
        text: "猫友圈",
        isSpecial: true // 标记为特殊图标
      },
      {
        pagePath: "/pages/statistics/statistics",
        iconPath: "/icons/stats.png",
        selectedIconPath: "/icons/stats-active.png",
        text: "统计"
      },
      {
        pagePath: "/pages/profile/profile",
        iconPath: "/icons/profile.png",
        selectedIconPath: "/icons/profile-active.png",
        text: "我的"
      }
    ]
  },
  attached() {
    // 获取当前页面路径，设置选中状态
    const pages = getCurrentPages();
    if (pages.length === 0) return;

    const currentPage = pages[pages.length - 1];
    const currentPath = '/' + currentPage.route;

    const selected = this.data.list.findIndex(item => item.pagePath === currentPath);
    this.setData({
      selected: selected !== -1 ? selected : 0
    });
  },
  methods: {
    switchTab(e) {
      const data = e.currentTarget.dataset;
      const url = data.path;
      const index = data.index;
      
      // 如果点击的是当前页面，不进行跳转
      if (this.data.selected === index) {
        return;
      }
      
      wx.switchTab({
        url,
        success: () => {
          this.setData({
            selected: index
          });
        }
      });
    }
  }
});

// fillblank.js
const app = getApp();

Page({
  data: {
    loading: true,
    questions: [],
    currentQuestionIndex: 0,
    userAnswer: '', // 用户填写的答案
    isSubmitting: false,
    showResult: false, // 是否显示答案结果
    isCorrect: false, // 答案是否正确
    correctAnswer: null, // 正确答案
    progress: 0, // 进度百分比
    records: [], // 答题记录
    showConfirmFinish: false, // 是否显示完成确认对话框
    correctCount: 0, // 正确答案的数量
    courseId: null, // 课程ID
    mode: null, // 模式
    questionIds: [], // 题目ID数组
    currentIndex: 0, // 当前题目索引
    coursesList: [], // 课程列表
    activeCourse: 0, // 当前选中的课程ID，0表示全部
    courseTitle: '', // 课程标题
    fontSize: 'normal', // 字体大小
    startTime: 0, // 答题开始时间
    emptyState: false, // 是否显示空状态
    emptyStateMessage: '', // 空状态消息
    isFocused: false // 输入框是否获取焦点
  },
  
  onLoad: function (options) {
    console.log('填空题页面加载，接收参数:', options);
    
    let mode = options.mode || 'normal';
    let courseId = options.courseId ? parseInt(options.courseId) : null;
    let courseTitle = options.courseTitle ? decodeURIComponent(options.courseTitle) : '';
    
    this.setData({
      mode: mode,
      courseId: courseId,
      courseTitle: courseTitle,
      fontSize: app.globalData.fontSize || 'normal',
      startTime: Date.now() // 初始化答题开始时间
    });
    
    console.log(`页面模式: ${mode}, 课程ID: ${courseId}, 课程标题: ${courseTitle}`);
    
    // 检查缓存中是否有题目数据
    const cachedQuestions = wx.getStorageSync('currentQuestions');
    console.log('缓存中题目数据:', cachedQuestions ? `${cachedQuestions.length}道题目` : '无');
    if (cachedQuestions && cachedQuestions.length > 0) {
      console.log('缓存题目类型:', cachedQuestions[0].type);
    }
    
    // 如果是重试模式，直接使用保存的题目
    if (mode === 'retry') {
      console.log('重试模式，使用保存的题目数据');
      const savedQuestions = wx.getStorageSync('currentQuestions');
      
      if (savedQuestions && savedQuestions.length > 0) {
        console.log(`从缓存加载题目，数量: ${savedQuestions.length}道`);
        this.setData({
          questions: savedQuestions,
          loading: false,
          progress: 0,
          currentQuestionIndex: 0,
          userAnswer: '',
          showResult: false,
          records: []
        });
        
        this.updateProgress();
        return;
      } else {
        console.warn('未找到保存的题目数据，切换到常规模式');
        // 如果没找到缓存题目，回退到常规模式
        mode = courseId ? 'course' : 'normal';
        this.setData({ mode });
      }
    }
    
    // 先检查是否是从错题练习过来的
    if (mode === 'wrong') {
      console.log('错题模式，加载错题');
      // 清除之前可能存在的缓存，避免加载到旧数据
      wx.removeStorageSync('currentQuestions');
      wx.setStorageSync('fromWrongQuestions', true);
      this.loadWrongQuestions();
    } else {
      // 如果不是wrong模式，清除标记
      wx.removeStorageSync('fromWrongQuestions');
      
      // 如果是课程模式
      if (mode === 'course' && courseId) {
        console.log('加载课程模式填空题');
        this.loadCourseQuestions();
      } else {
        // 普通模式加载所有填空题
        console.log('加载普通模式填空题');
        this.fetchQuestions();
      }
    }
    
    // 尝试获取课程列表，但不阻塞题目加载
    try {
      this.fetchCoursesList();
    } catch (err) {
      console.error('获取课程列表时出错:', err);
    }
  },
  
  // 获取课程列表
  fetchCoursesList: function() {
    app.request({
      url: '/classes/current',  // 使用正确的API端点
      method: 'GET'
    }).then(res => {
      console.log('获取课程列表数据:', res);
      
      // 检查是否成功获取到班级信息
      if (res && res.has_class && res.class) {
        // 将班级信息存储到本地，供其他页面使用
        wx.setStorageSync('currentClass', res.class);
        
        // 获取班级下的课程
        this.fetchClassCourses(res.class.id);
      }
    }).catch(err => {
      console.error('获取课程列表失败:', err);
      // 即使课程列表获取失败，我们也不影响问题加载
    });
  },
  
  // 获取班级下的课程列表
  fetchClassCourses: function(classId) {
    app.request({
      url: '/user/courses',
      method: 'GET'
    }).then(res => {
      console.log('获取班级课程数据:', res);
      
      if (res && Array.isArray(res)) {
        // 处理课程数据
        const coursesData = res.map(course => {
          return {
            id: course.id,
            name: course.name,
            question_count: course.question_count || 0
          };
        });
        
        this.setData({
          coursesList: coursesData
        });
      }
    }).catch(err => {
      console.error('获取班级课程失败:', err);
    });
  },
  
  // 切换课程筛选
  switchCourse: function(e) {
    const courseId = parseInt(e.currentTarget.dataset.id) || 0;
    
    this.setData({ 
      activeCourse: courseId,
      loading: true 
    });
    
    // 跳转到新的URL以保持一致性
    wx.redirectTo({
      url: `/pages/quiz/fillblank/fillblank?mode=${this.data.mode || 'normal'}${courseId ? '&courseId=' + courseId : ''}`
    });
  },
  
  // 获取填空题列表
  fetchQuestions: function() {
    this.setData({ loading: true });
    
    // 检查是否是课程模式
    if (this.data.mode === 'course') {
      console.log('在课程模式下获取填空题');
      
      // 从存储中获取课程题目
      const currentQuestions = wx.getStorageSync('currentQuestions');
      
      // 确保我们有缓存的填空题目
      if (currentQuestions && Array.isArray(currentQuestions) && currentQuestions.length > 0 && 
          (currentQuestions[0].type === 'fillblank' || currentQuestions[0].type === 'fill_blank')) {
        console.log(`从缓存中读取填空题: ${currentQuestions.length}道题目`);
        
        this.setData({
          questions: currentQuestions,
          loading: false,
          progress: 0,
          currentQuestionIndex: 0,
          userAnswer: '',
          showResult: false,
          records: []
        });
        
        this.updateProgress();
        return;
      }
      
      // 如果没有找到缓存的课程题目，使用API重新获取
      console.log('缓存中没有课程填空题，调用loadCourseQuestions()获取');
      this.loadCourseQuestions();
      return;
    }
    
    // 如果不是课程模式，使用常规API获取所有题目
    console.log('使用常规API获取所有填空题');
    
    app.request({
      url: '/questions/fillblank'
    })
    .then(res => {
      if (!res || !Array.isArray(res) || res.length === 0) {
        console.log('API返回的填空题为空');
        wx.showToast({
          title: '暂无填空题',
          icon: 'none'
        });
        
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }
      
      console.log(`获取到填空题: ${res.length}道`);
      
      // 如果有课程ID筛选，过滤属于该课程的题目
      if (this.data.courseId) {
        const filteredRes = res.filter(q => {
          const qCourseId = typeof q.course_id === 'string' ? parseInt(q.course_id, 10) : q.course_id;
          const thisCourseId = typeof this.data.courseId === 'string' ? parseInt(this.data.courseId, 10) : this.data.courseId;
          return qCourseId === thisCourseId;
        });
        
        console.log(`过滤后的填空题: ${filteredRes.length}道`);
        res = filteredRes;
        
        if (res.length === 0) {
          wx.showToast({
            title: '该课程暂无填空题',
            icon: 'none'
          });
          
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
          return;
        }
      }
      
      this.setData({
        questions: res,
        loading: false,
        progress: 0,
        currentQuestionIndex: 0,
        userAnswer: '',
        showResult: false,
        records: []
      });
      
      this.updateProgress();
    })
    .catch(err => {
      console.error('获取填空题失败:', err);
      this.setData({ loading: false });
      
      wx.showToast({
        title: '获取题目失败',
        icon: 'none'
      });
      
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    });
  },
  
  // 加载指定课程的填空题
  loadCourseQuestions: function() {
    if (!this.data.courseId) {
      console.error('加载课程题目失败：缺少课程ID');
      wx.showToast({
        title: '课程信息不完整',
        icon: 'none'
      });
      return;
    }
    
    this.setData({ loading: true });
    
    // 先尝试从存储中获取课程题目
    console.log('尝试从缓存获取课程填空题');
    const currentQuestions = wx.getStorageSync('currentQuestions');
    
    // 确保我们有缓存的填空题目
    if (currentQuestions && Array.isArray(currentQuestions) && currentQuestions.length > 0 && 
        (currentQuestions[0].type === 'fillblank' || currentQuestions[0].type === 'fill_blank')) {
      console.log(`从缓存加载课程填空题: ${currentQuestions.length}道题目`);
      this.setData({
        questions: currentQuestions,
        loading: false,
        progress: 0,
        currentQuestionIndex: 0,
        userAnswer: '',
        showResult: false,
        records: []
      });
      this.updateProgress();
      return;
    }
    
    console.log(`从API加载课程ID ${this.data.courseId} 的填空题`);
    
    app.request({
      url: `/course/${this.data.courseId}/questions/fillblank`
    })
    .then(res => {
      this.setData({ loading: false });
      
      console.log(`获取到课程填空题: ${res ? res.length : 0}道`);
      
      if (!res || !Array.isArray(res) || res.length === 0) {
        // 如果没有找到填空题，尝试用通用API获取
        this.fetchGenericFillblankQuestions();
        return;
      }
      
      this.setData({
        questions: res,
        progress: 0,
        currentQuestionIndex: 0,
        userAnswer: '',
        showResult: false,
        records: []
      });
      
      // 缓存课程题目
      wx.setStorageSync('currentQuestions', res);
      
      this.updateProgress();
    })
    .catch(err => {
      console.error('获取课程填空题失败:', err);
      
      // 如果API失败，尝试通用填空题API
      this.fetchGenericFillblankQuestions();
    });
  },
  
  // 当课程专用API失败时，使用通用API并过滤
  fetchGenericFillblankQuestions: function() {
    console.log('尝试使用通用API获取填空题');
    
    app.request({
      url: '/questions/fillblank'
    })
    .then(res => {
      this.setData({ loading: false });
      
      if (!res || !Array.isArray(res) || res.length === 0) {
        console.log('通用API返回的填空题为空');
        this.showEmptyState('暂无填空题');
        return;
      }
      
      console.log(`通用API获取到所有填空题: ${res.length}道`);
      
      // 过滤该课程的题目
      const filteredQuestions = res.filter(q => {
        // 确保类型匹配：将两者都转换为数字进行比较
        const qCourseId = typeof q.course_id === 'string' ? parseInt(q.course_id, 10) : q.course_id;
        const thisCourseId = typeof this.data.courseId === 'string' ? parseInt(this.data.courseId, 10) : this.data.courseId;
        
        return qCourseId === thisCourseId;
      });
      
      console.log(`过滤前题目: ${res.length}道, 过滤后: ${filteredQuestions.length}道`);
      console.log('过滤后的题目:', filteredQuestions);
      
      if (filteredQuestions.length === 0) {
        this.showEmptyState('该课程暂无填空题');
        return;
      }
      
      this.setData({
        questions: filteredQuestions,
        progress: 0,
        currentQuestionIndex: 0,
        userAnswer: '',
        showResult: false,
        records: []
      });
      
      // 缓存课程题目，使用currentQuestions保持一致性
      wx.setStorageSync('currentQuestions', filteredQuestions);
      
      this.updateProgress();
    })
    .catch(err => {
      console.error('获取通用填空题失败:', err);
      this.setData({ loading: false });
      this.showEmptyState('获取题目失败');
    });
  },
  
  // 加载错题
  loadWrongQuestions() {
    console.log('开始加载填空错题');
    
    app.request({
      url: '/wrong-questions',
      method: 'GET'
    }).then(res => {
      console.log('获取错题本数据:', res);
      
      // 筛选填空题
      let fillBlankQuestions = res.filter(q => q.type === 'fill_blank');
      console.log(`共找到${fillBlankQuestions.length}道填空错题`);
      
      // 如果有指定课程ID，进一步筛选
      if (this.data.courseId) {
        console.log(`按课程ID ${this.data.courseId} 筛选错题`);
        const beforeFilter = fillBlankQuestions.length;
        
        fillBlankQuestions = fillBlankQuestions.filter(q => {
          // 确保类型匹配：将两者都转换为数字进行比较
          const qCourseId = typeof q.course_id === 'string' ? parseInt(q.course_id, 10) : q.course_id;
          const thisCourseId = typeof this.data.courseId === 'string' ? parseInt(this.data.courseId, 10) : this.data.courseId;
          
          return qCourseId === thisCourseId;
        });
        
        console.log(`筛选前: ${beforeFilter}道题, 筛选后: ${fillBlankQuestions.length}道题`);
      }
      
      if (fillBlankQuestions.length === 0) {
        console.log('没有找到符合条件的填空错题');
        wx.showToast({
          title: '没有填空错题',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }

      console.log(`将加载${fillBlankQuestions.length}道填空错题`);
      console.log('错题详情:', fillBlankQuestions);

      // 确保题目格式正确
      fillBlankQuestions = fillBlankQuestions.map(q => {
        // 补充可能缺失的字段
        return {
          ...q,
          type: 'fill_blank',
          // 确保answer字段格式正确
          answer: Array.isArray(q.answer) ? q.answer : 
                  (typeof q.answer === 'string' ? [q.answer] : 
                  (q.correctAnswer ? [q.correctAnswer] : []))
        };
      });

      // 将错题直接加载为questions数组
      this.setData({
        questions: fillBlankQuestions,
        loading: false,
        progress: 0,
        currentQuestionIndex: 0,
        userAnswer: '',
        showResult: false,
        records: []
      });
      
      // 缓存到当前题目，保持一致性
      wx.setStorageSync('currentQuestions', fillBlankQuestions);
      
      this.updateProgress();
      
    }).catch(err => {
      console.error('获取错题失败:', err);
      wx.showToast({
        title: '获取错题失败',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    });
  },
  
  // 获取特定题目
  fetchSpecificQuestion: function(questionId) {
    this.setData({ loading: true });
    console.log(`获取特定ID的填空题: ${questionId}`);
    
    app.request({
      url: '/questions/fillblank',
      method: 'GET'
    })
    .then(res => {
      if (!res || !Array.isArray(res) || res.length === 0) {
        console.log('API返回的填空题为空');
        wx.showToast({
          title: '获取题目失败',
          icon: 'none'
        });
        
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }
      
      console.log(`获取到${res.length}道填空题，搜索ID: ${questionId}`);
      
      // 查找特定ID的题目
      const question = res.find(q => q.id == questionId);
      
      if (!question) {
        console.log(`未找到ID为 ${questionId} 的题目`);
        wx.showToast({
          title: '题目不存在',
          icon: 'none'
        });
        
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }
      
      console.log('找到特定题目:', question);
      
      this.setData({
        questions: [question],
        loading: false,
        progress: 100,  // 只有一道题，进度为100%
        currentQuestionIndex: 0,
        userAnswer: '',
        showResult: false,
        records: []
      });
      
      // 缓存到当前题目，保持一致性
      wx.setStorageSync('currentQuestions', [question]);
    })
    .catch(err => {
      console.error('获取特定题目失败:', err);
      this.setData({ loading: false });
      
      wx.showToast({
        title: '获取题目失败',
        icon: 'none'
      });
      
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    });
  },
  
  // 处理输入框获得焦点
  handleFocus: function() {
    this.setData({
      isFocused: true
    });
  },
  
  // 处理输入框失去焦点
  handleBlur: function() {
    this.setData({
      isFocused: false
    });
  },

  // 处理输入内容
  handleInput: function(e) {
    this.setData({
      userAnswer: e.detail.value
    });
  },
  
  // 提交答案
  submitAnswer: function() {
    if (!this.data.userAnswer.trim()) {
      wx.showToast({
        title: '请输入答案',
        icon: 'none'
      });
      return;
    }
    
    if (this.data.isSubmitting) return;
    
    this.setData({ isSubmitting: true });
    
    const currentQuestion = this.data.questions[this.data.currentQuestionIndex];
    
    app.request({
      url: '/submit',
      method: 'POST',
      data: {
        questionType: 'fill_blank',
        questionId: currentQuestion.id,
        userAnswer: this.data.userAnswer.trim()
      }
    })
    .then(res => {
      // 记录答题情况
      const record = {
        questionId: currentQuestion.id,
        question: currentQuestion.question,
        userAnswer: this.data.userAnswer,
        correctAnswer: res.correctAnswer,
        isCorrect: res.isCorrect
      };
      
      const records = this.data.records.concat(record);
      const newCorrectCount = res.isCorrect ? this.data.correctCount + 1 : this.data.correctCount;
      
      this.setData({
        isSubmitting: false,
        showResult: true,
        isCorrect: res.isCorrect,
        correctAnswer: res.correctAnswer,
        records: records,
        correctCount: newCorrectCount
      });
      
      console.log('答题结果:', { 
        isCorrect: res.isCorrect, 
        correctCount: newCorrectCount, 
        totalAnswered: records.length 
      });
    })
    .catch(err => {
      console.error('提交答案失败:', err);
      this.setData({ isSubmitting: false });
      
      wx.showToast({
        title: '提交答案失败',
        icon: 'none'
      });
    });
  },
  
  // 下一题
  nextQuestion: function() {
    const nextIndex = this.data.currentQuestionIndex + 1;
    
    if (nextIndex >= this.data.questions.length) {
      // 已经是最后一题，显示完成确认对话框
      this.setData({
        showConfirmFinish: true
      });
      return;
    }
    
    this.setData({
      currentQuestionIndex: nextIndex,
      userAnswer: '',
      showResult: false,
      isCorrect: false,
      correctAnswer: null
    });
    
    this.updateProgress();
  },
  
  // 更新进度条
  updateProgress: function() {
    const progress = ((this.data.currentQuestionIndex + 1) / this.data.questions.length) * 100;
    this.setData({ progress });
  },
  
  // 确认完成答题
  confirmFinish: function() {
    this.setData({
      showConfirmFinish: false
    });
    
    // 获取课程信息
    const currentCourse = wx.getStorageSync('currentCourse') || {};
    
    // 计算答题时间
    const endTime = Date.now();
    const startTime = this.data.startTime;
    const timeTaken = (endTime - startTime) / 1000; // 转换为秒
    const minutes = Math.floor(timeTaken / 60);
    const seconds = Math.round(timeTaken % 60);
    
    // 前往结果页面
    const result = {
      totalQuestions: this.data.questions.length,
      correctCount: this.data.correctCount,
      records: this.data.records,
      questionType: 'fill_blank',
      courseId: this.data.courseId,
      courseName: this.data.courseTitle,
      courseColor: currentCourse.color || '#4e8df7',
      timeTaken: `${minutes}分${seconds}秒`,
      // 传递模式信息
      mode: this.data.mode,
      // 传递原始题目数据以便再次练习
      questions: this.data.questions
    };
    
    console.log('传递答题结果数据:', result);
    
    wx.navigateTo({
      url: '/pages/result/result',
      success: (res) => {
        // 传递结果数据给结果页面
        res.eventChannel.emit('acceptResultData', result);
      }
    });
  },
  
  // 取消完成
  cancelFinish: function() {
    this.setData({
      showConfirmFinish: false
    });
  },
  
  // 返回首页
  backToHome: function() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },
  
  // 返回上一页
  navigateBack: function() {
    wx.navigateBack();
  },
  
  // 显示空状态
  showEmptyState: function(message) {
    this.setData({ 
      loading: false,
      emptyState: true,
      emptyStateMessage: message || '暂无题目'
    });
    
    console.log('显示空状态:', message);
  },
  
  // 加载所有题目
  loadAllQuestions: function() {
    this.fetchQuestions();
  },
  
  // 加载特定题目
  loadQuestion: function(questionId) {
    this.fetchSpecificQuestion(questionId);
  },
  
  // 打乱数组
  shuffleArray: function(array) {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]];
    }
  },
}) 
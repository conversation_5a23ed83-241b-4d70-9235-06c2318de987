<!-- review.wxml -->
<view class="container">
  <!-- 标签导航栏 -->
  <view class="tab-nav">
    <view class="tab-item {{currentTab === 0 ? 'active' : ''}}" bindtap="switchTab" data-index="0">
      <text class="tab-text">单选题 ({{allQuestions.single.length}})</text>
    </view>
    <view class="tab-item {{currentTab === 1 ? 'active' : ''}}" bindtap="switchTab" data-index="1">
      <text class="tab-text">多选题 ({{allQuestions.multiple.length}})</text>
    </view>
    <view class="tab-item {{currentTab === 2 ? 'active' : ''}}" bindtap="switchTab" data-index="2">
      <text class="tab-text">判断题 ({{allQuestions.judgment.length}})</text>
    </view>
    <view class="tab-item {{currentTab === 3 ? 'active' : ''}}" bindtap="switchTab" data-index="3">
      <text class="tab-text">填空题 ({{allQuestions.fillblank.length}})</text>
    </view>
  </view>
  
  <!-- 内容区域 - 优化渲染方式 -->
  <scroll-view id="questionScrollView" class="question-scroll-view" scroll-y="true" enhanced="true" fast-deceleration="true" show-scrollbar="{{false}}" bindscroll="onScroll" scroll-event-throttle="16" scroll-top="{{scrollTop}}" scroll-anchoring="{{true}}" bounces="{{false}}" enable-flex="{{true}}">
    <!-- 单选题内容 -->
    <view hidden="{{currentTab !== 0}}" class="questions-container">
      <block wx:if="{{allQuestions.single && allQuestions.single.length > 0}}">
        <view wx:for="{{allQuestions.single}}" wx:key="index" class="question-card">
          <view class="question-header">
            <text class="question-type">单选题</text>
            <text class="question-number">{{index + 1}}/{{allQuestions.single.length}}</text>
          </view>
          <view class="question-content">
            <text class="question-text">{{item.question}}</text>
          </view>
          <view class="options-list">
            <view wx:for="{{item.options}}" wx:for-item="option" wx:for-index="optionIndex" wx:key="*this" 
                  class="option-item {{optionIndex == item.answer ? 'correct' : ''}}">
              <text class="option-letter">{{['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'][optionIndex]}}</text>
              <text class="option-text">{{option}}</text>
              <text wx:if="{{optionIndex == item.answer}}" class="correct-mark">✓</text>
            </view>
          </view>
          <view class="answer-section">
            <view class="answer-header">【正确答案】</view>
            <view class="answer-content">{{item.correctLetter}}</view>
          </view>
          <view wx:if="{{item.explanation}}" class="explanation-section">
            <view class="explanation-header">【解析】</view>
            <view class="explanation-content">{{item.explanation}}</view>
          </view>
        </view>
      </block>
      <view wx:else class="empty-tips">
        <text>暂无单选题</text>
      </view>
    </view>

    <!-- 多选题内容 -->
    <view hidden="{{currentTab !== 1}}" class="questions-container">
      <block wx:if="{{allQuestions.multiple && allQuestions.multiple.length > 0}}">
        <view wx:for="{{allQuestions.multiple}}" wx:key="index" class="question-card">
          <view class="question-header">
            <text class="question-type">多选题</text>
            <text class="question-number">{{index + 1}}/{{allQuestions.multiple.length}}</text>
          </view>
          <view class="question-content">
            <text class="question-text">{{item.question}}</text>
          </view>
          <view class="options-list">
            <!-- 使用processedOptions渲染选项，每个选项都有自己的isCorrect标志 -->
            <view wx:for="{{item.processedOptions}}" wx:for-item="option" wx:key="letter" 
                  class="option-item {{option.isCorrect ? 'correct' : ''}}">
              <text class="option-letter">{{option.letter}}</text>
              <text class="option-text">{{option.text}}</text>
              <text wx:if="{{option.isCorrect}}" class="correct-mark">✓</text>
            </view>
          </view>
          <view class="answer-section">
            <view class="answer-header">【正确答案】</view>
            <view class="answer-content">{{item.correctLetters}}</view>
          </view>
          <view wx:if="{{item.explanation}}" class="explanation-section">
            <view class="explanation-header">【解析】</view>
            <view class="explanation-content">{{item.explanation}}</view>
          </view>
        </view>
      </block>
      <view wx:else class="empty-tips">
        <text>暂无多选题</text>
      </view>
    </view>

    <!-- 判断题内容 -->
    <view hidden="{{currentTab !== 2}}" class="questions-container">
      <block wx:if="{{allQuestions.judgment && allQuestions.judgment.length > 0}}">
        <view wx:for="{{allQuestions.judgment}}" wx:key="index" class="question-card">
          <view class="question-header">
            <text class="question-type">判断题</text>
            <text class="question-number">{{index + 1}}/{{allQuestions.judgment.length}}</text>
          </view>
          <view class="question-content">
            <text class="question-text">{{item.question}}</text>
          </view>
          <view class="judgment-options">
            <view class="judgment-option {{item.correctText === '正确' ? 'correct' : ''}}">
              <text class="judgment-text">正确</text>
              <text wx:if="{{item.correctText === '正确'}}" class="correct-mark">✓</text>
            </view>
            <view class="judgment-option {{item.correctText === '错误' ? 'correct' : ''}}">
              <text class="judgment-text">错误</text>
              <text wx:if="{{item.correctText === '错误'}}" class="correct-mark">✓</text>
            </view>
          </view>
          <view class="answer-section">
            <view class="answer-header">【正确答案】</view>
            <view class="answer-content">{{item.correctText}}</view>
          </view>
          <view wx:if="{{item.explanation}}" class="explanation-section">
            <view class="explanation-header">【解析】</view>
            <view class="explanation-content">{{item.explanation}}</view>
          </view>
        </view>
      </block>
      <view wx:else class="empty-tips">
        <text>暂无判断题</text>
      </view>
    </view>

    <!-- 填空题内容 -->
    <view hidden="{{currentTab !== 3}}" class="questions-container">
      <block wx:if="{{allQuestions.fillblank && allQuestions.fillblank.length > 0}}">
        <view wx:for="{{allQuestions.fillblank}}" wx:key="index" class="question-card">
          <view class="question-header">
            <text class="question-type">填空题</text>
            <text class="question-number">{{index + 1}}/{{allQuestions.fillblank.length}}</text>
          </view>
          <view class="question-content">
            <text class="question-text">{{item.question}}</text>
          </view>
          <view class="answer-section">
            <view class="answer-header">【正确答案】</view>
            <view class="answer-content">{{item.correctText}}</view>
          </view>
          <view wx:if="{{item.explanation}}" class="explanation-section">
            <view class="explanation-header">【解析】</view>
            <view class="explanation-content">{{item.explanation}}</view>
          </view>
        </view>
      </block>
      <view wx:else class="empty-tips">
        <text>暂无填空题</text>
      </view>
    </view>
  </scroll-view>
  
  <!-- 导航箭头 -->
  <view class="navigation-arrows">
    <view class="nav-arrow nav-up" bindtap="scrollUp">
      <view class="arrow-icon">↑</view>
    </view>
    <view class="nav-arrow nav-down" bindtap="scrollDown">
      <view class="arrow-icon">↓</view>
    </view>
  </view>
  
  <!-- 加载提示 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view> 
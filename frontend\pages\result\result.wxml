<!--pages/result/result.wxml-->
<view class="container">
  <view class="result-header">
    <text class="title">答题结果</text>
    <text class="subtitle">{{
      questionType === 'single' ? '单选题' : 
      questionType === 'multiple' ? '多选题' : 
      questionType === 'judgment' ? '判断题' : 
      questionType === 'fill_blank' ? '填空题' : ''
    }}练习完成</text>
  </view>
  
  <view class="stats-card">
    <view class="stats-row">
      <view class="stats-item">
        <text class="stats-value">{{totalQuestions}}</text>
        <text class="stats-label">总题数</text>
      </view>
      <view class="stats-item">
        <text class="stats-value">{{correctCount}}</text>
        <text class="stats-label">答对</text>
      </view>
      <view class="stats-item">
        <text class="stats-value">{{wrongCount}}</text>
        <text class="stats-label">答错</text>
      </view>
    </view>
    
    <view class="stats-row">
      <view class="stats-item">
        <text class="stats-value">{{accuracy}}%</text>
        <text class="stats-label">正确率</text>
      </view>
      <view class="stats-item">
        <text class="stats-value">{{timeTaken}}</text>
        <text class="stats-label">用时</text>
      </view>
    </view>
  </view>
  
  <view class="buttons-container">
    <button class="primary-button" bindtap="backToHome">返回首页</button>
    <button class="secondary-button" bindtap="practiceAgain">再次练习</button>
  </view>
</view>
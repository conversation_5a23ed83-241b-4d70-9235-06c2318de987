{% extends "admin/base.html" %}

{% block title %}题目管理 - 大数据题库管理后台{% endblock %}

{% block additional_head %}
<link rel="stylesheet" href="/static/css/admin.css">
<style>
    .bg-purple {
        background-color: #6f42c1;
    }
</style>
{% endblock %}

{% block header %}题目管理{% endblock %}

{% block header_buttons %}
<form class="d-flex" role="search">
    <input class="form-control me-2" type="search" id="searchInput" placeholder="搜索题目..." aria-label="Search">
</form>
{% endblock %}

{% block content %}
<!-- 班级和课程筛选 -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <form id="filterForm" method="get" class="row g-3">
                    <div class="col-md-4">
                        <label for="classSelect" class="form-label">班级</label>
                        <select class="form-select" id="classSelect" name="class_id">
                            <option value="">全部班级</option>
                            {% for class_item in classes %}
                            <option value="{{ class_item.id }}" {% if selected_class_id == class_item.id %}selected{% endif %}>
                                {{ class_item.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-5">
                        <label for="courseSelect" class="form-label">课程</label>
                        <select class="form-select" id="courseSelect" name="course_id">
                            <option value="">选择课程</option>
                            {% for course in courses %}
                            <option value="{{ course.id }}" {% if selected_course_id == course.id %}selected{% endif %}>
                                {{ course.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid gap-2 d-md-flex">
                            <button type="submit" class="btn btn-primary" style="min-width: 160px;">筛选</button>
                            <button type="button" id="resetButton" class="btn btn-outline-secondary" style="min-width: 160px;">重置</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 题目统计 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card">
            <div class="card-body stats-card">
                <div class="number">{{ single_total }}</div>
                <div class="label">单选题数量</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body stats-card">
                <div class="number">{{ multiple_total }}</div>
                <div class="label">多选题数量</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body stats-card">
                <div class="number">{{ judgment_total }}</div>
                <div class="label">判断题数量</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body stats-card">
                <div class="number">{{ fill_blank_total }}</div>
                <div class="label">填空题数量</div>
            </div>
        </div>
    </div>
</div>

<!-- 添加题目按钮 -->
<div class="row mb-4">
    <div class="col-md-12 text-end">
        <a href="/admin/questions/add-single" class="btn btn-primary">
            <i class="fas fa-plus"></i> 添加单选题
        </a>
        <a href="/admin/questions/add-multiple" class="btn btn-success">
            <i class="fas fa-plus"></i> 添加多选题
        </a>
        <a href="/admin/questions/add-judgment" class="btn btn-info">
            <i class="fas fa-plus"></i> 添加判断题
        </a>
        <a href="/admin/questions/add-fill-blank" class="btn btn-secondary">
            <i class="fas fa-plus"></i> 添加填空题
        </a>
        <a href="/admin/questions/upload" class="btn btn-warning">
            <i class="fas fa-upload"></i> 批量导入题库
        </a>
        <button id="batchDeleteBtn" class="btn btn-danger" disabled>
            <i class="fas fa-trash"></i> 批量删除
        </button>
    </div>
</div>

<!-- 题目管理选项卡 -->
<ul class="nav nav-tabs mb-4" id="questionTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link {% if active_tab == 'single' %}active{% endif %}" id="single-tab" data-bs-toggle="tab" data-bs-target="#single-pane" type="button" role="tab" aria-controls="single-pane" aria-selected="{% if active_tab == 'single' %}true{% else %}false{% endif %}">
            单选题
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link {% if active_tab == 'multiple' %}active{% endif %}" id="multiple-tab" data-bs-toggle="tab" data-bs-target="#multiple-pane" type="button" role="tab" aria-controls="multiple-pane" aria-selected="{% if active_tab == 'multiple' %}true{% else %}false{% endif %}">
            多选题
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link {% if active_tab == 'judgment' %}active{% endif %}" id="judgment-tab" data-bs-toggle="tab" data-bs-target="#judgment-pane" type="button" role="tab" aria-controls="judgment-pane" aria-selected="{% if active_tab == 'judgment' %}true{% else %}false{% endif %}">
            判断题
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link {% if active_tab == 'fill_blank' %}active{% endif %}" id="fill-blank-tab" data-bs-toggle="tab" data-bs-target="#fill-blank-pane" type="button" role="tab" aria-controls="fill-blank-pane" aria-selected="{% if active_tab == 'fill_blank' %}true{% else %}false{% endif %}">
            填空题
        </button>
    </li>
</ul>

<div class="tab-content" id="questionTabContent">
    <!-- 单选题列表 -->
    <div class="tab-pane fade {% if active_tab == 'single' %}show active{% endif %}" id="single-pane" role="tabpanel" aria-labelledby="single-tab">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="singleQuestionsTable">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" class="select-all-checkbox" data-type="single">
                                </th>
                                <th>ID</th>
                                <th>题目</th>
                                <th>选项数</th>
                                <th>正确答案</th>
                                <th>分类</th>
                                <th>课程</th>
                                <th>难度</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for question in single_questions %}
                            <tr>
                                <td>
                                    <input type="checkbox" class="question-checkbox" data-id="{{ question.id }}" data-type="single">
                                </td>
                                <td>{{ question.id }}</td>
                                <td>
                                    <div class="question-text" style="max-width: 400px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;" title="{{ question.question }}">
                                        {{ question.question }}
                                    </div>
                                </td>
                                <td>{{ question.options|length }}</td>
                                <td>{{ question.answer }}</td>
                                <td>{{ question.category }}</td>
                                <td>{{ question.course.name if question.course else '-' }}</td>
                                <td>
                                    {% for i in range(question.difficulty) %}
                                    <i class="fas fa-star text-warning"></i>
                                    {% endfor %}
                                    {% for i in range(3 - question.difficulty) %}
                                    <i class="far fa-star text-muted"></i>
                                    {% endfor %}
                                </td>
                                <td>
                                    <a href="/admin/questions/edit-single/{{ question.id }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i> 编辑
                                    </a>
                                    <button class="btn btn-sm btn-outline-danger delete-question" data-id="{{ question.id }}" data-type="single">
                                        <i class="fas fa-trash"></i> 删除
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- 单选题分页 -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div class="pagination-info">
                        显示 {{ single_questions|length }} 条，共 {{ single_total }} 条
                    </div>
                    <div>
                        <select id="singlePerPage" class="form-select form-select-sm d-inline-block me-2" style="width: auto;">
                            <option value="10" {% if per_page == 10 %}selected{% endif %}>10条/页</option>
                            <option value="20" {% if per_page == 20 %}selected{% endif %}>20条/页</option>
                            <option value="50" {% if per_page == 50 %}selected{% endif %}>50条/页</option>
                            <option value="100" {% if per_page == 100 %}selected{% endif %}>100条/页</option>
                        </select>
                        
                        <nav aria-label="题目分页" class="d-inline-block">
                            <ul class="pagination pagination-sm mb-0">
                                {% if single_pagination.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('admin_questions', tab='single', page=single_pagination.prev_num, per_page=per_page, class_id=selected_class_id, course_id=selected_course_id) }}">上一页</a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#">上一页</a>
                                </li>
                                {% endif %}
                                
                                {% set start_page = [page - 2, 1]|max %}
                                {% set end_page = [start_page + 4, single_pagination.pages]|min %}
                                {% set start_page = [end_page - 4, 1]|max %}
                                
                                {% if start_page > 1 %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('admin_questions', tab='single', page=1, per_page=per_page, class_id=selected_class_id, course_id=selected_course_id) }}">1</a>
                                </li>
                                {% if start_page > 2 %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#">...</a>
                                </li>
                                {% endif %}
                                {% endif %}
                                
                                {% for p in range(start_page, end_page + 1) %}
                                <li class="page-item {% if p == page %}active{% endif %}">
                                    <a class="page-link" href="{{ url_for('admin_questions', tab='single', page=p, per_page=per_page, class_id=selected_class_id, course_id=selected_course_id) }}">{{ p }}</a>
                                </li>
                                {% endfor %}
                                
                                {% if end_page < single_pagination.pages %}
                                {% if end_page < single_pagination.pages - 1 %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#">...</a>
                                </li>
                                {% endif %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('admin_questions', tab='single', page=single_pagination.pages, per_page=per_page, class_id=selected_class_id, course_id=selected_course_id) }}">{{ single_pagination.pages }}</a>
                                </li>
                                {% endif %}
                                
                                {% if single_pagination.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('admin_questions', tab='single', page=single_pagination.next_num, per_page=per_page, class_id=selected_class_id, course_id=selected_course_id) }}">下一页</a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#">下一页</a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 多选题列表 -->
    <div class="tab-pane fade {% if active_tab == 'multiple' %}show active{% endif %}" id="multiple-pane" role="tabpanel" aria-labelledby="multiple-tab">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="multipleQuestionsTable">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" class="select-all-checkbox" data-type="multiple">
                                </th>
                                <th>ID</th>
                                <th>题目</th>
                                <th>选项数</th>
                                <th>正确答案</th>
                                <th>分类</th>
                                <th>课程</th>
                                <th>难度</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for question in multiple_questions %}
                            <tr>
                                <td>
                                    <input type="checkbox" class="question-checkbox" data-id="{{ question.id }}" data-type="multiple">
                                </td>
                                <td>{{ question.id }}</td>
                                <td>
                                    <div class="question-text" style="max-width: 400px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;" title="{{ question.question }}">
                                        {{ question.question }}
                                    </div>
                                </td>
                                <td>{{ question.options|length }}</td>
                                <td>{{ question.answer|join(', ') }}</td>
                                <td>{{ question.category }}</td>
                                <td>{{ question.course.name if question.course else '-' }}</td>
                                <td>
                                    {% for i in range(question.difficulty) %}
                                    <i class="fas fa-star text-warning"></i>
                                    {% endfor %}
                                    {% for i in range(3 - question.difficulty) %}
                                    <i class="far fa-star text-muted"></i>
                                    {% endfor %}
                                </td>
                                <td>
                                    <a href="/admin/questions/edit-multiple/{{ question.id }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i> 编辑
                                    </a>
                                    <button class="btn btn-sm btn-outline-danger delete-question" data-id="{{ question.id }}" data-type="multiple">
                                        <i class="fas fa-trash"></i> 删除
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- 多选题分页 -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div class="pagination-info">
                        显示 {{ multiple_questions|length }} 条，共 {{ multiple_total }} 条
                    </div>
                    <div>
                        <select id="multiplePerPage" class="form-select form-select-sm d-inline-block me-2" style="width: auto;">
                            <option value="10" {% if per_page == 10 %}selected{% endif %}>10条/页</option>
                            <option value="20" {% if per_page == 20 %}selected{% endif %}>20条/页</option>
                            <option value="50" {% if per_page == 50 %}selected{% endif %}>50条/页</option>
                            <option value="100" {% if per_page == 100 %}selected{% endif %}>100条/页</option>
                        </select>
                        
                        <nav aria-label="题目分页" class="d-inline-block">
                            <ul class="pagination pagination-sm mb-0">
                                {% if multiple_pagination.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('admin_questions', tab='multiple', page=multiple_pagination.prev_num, per_page=per_page, class_id=selected_class_id, course_id=selected_course_id) }}">上一页</a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#">上一页</a>
                                </li>
                                {% endif %}
                                
                                {% set start_page = [page - 2, 1]|max %}
                                {% set end_page = [start_page + 4, multiple_pagination.pages]|min %}
                                {% set start_page = [end_page - 4, 1]|max %}
                                
                                {% if start_page > 1 %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('admin_questions', tab='multiple', page=1, per_page=per_page, class_id=selected_class_id, course_id=selected_course_id) }}">1</a>
                                </li>
                                {% if start_page > 2 %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#">...</a>
                                </li>
                                {% endif %}
                                {% endif %}
                                
                                {% for p in range(start_page, end_page + 1) %}
                                <li class="page-item {% if p == page %}active{% endif %}">
                                    <a class="page-link" href="{{ url_for('admin_questions', tab='multiple', page=p, per_page=per_page, class_id=selected_class_id, course_id=selected_course_id) }}">{{ p }}</a>
                                </li>
                                {% endfor %}
                                
                                {% if end_page < multiple_pagination.pages %}
                                {% if end_page < multiple_pagination.pages - 1 %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#">...</a>
                                </li>
                                {% endif %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('admin_questions', tab='multiple', page=multiple_pagination.pages, per_page=per_page, class_id=selected_class_id, course_id=selected_course_id) }}">{{ multiple_pagination.pages }}</a>
                                </li>
                                {% endif %}
                                
                                {% if multiple_pagination.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('admin_questions', tab='multiple', page=multiple_pagination.next_num, per_page=per_page, class_id=selected_class_id, course_id=selected_course_id) }}">下一页</a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#">下一页</a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 判断题列表 -->
    <div class="tab-pane fade {% if active_tab == 'judgment' %}show active{% endif %}" id="judgment-pane" role="tabpanel" aria-labelledby="judgment-tab">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="card-title">判断题管理</h5>
                </div>
                
                <div class="table-responsive">
                    <form id="judgmentBatchDeleteForm" action="/admin/questions/batch-delete-judgment" method="post">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th width="5%">
                                        <input type="checkbox" id="selectAllJudgment">
                                    </th>
                                    <th width="5%">ID</th>
                                    <th width="40%">题目内容</th>
                                    <th width="10%">正确答案</th>
                                    <th width="15%">分类</th>
                                    <th width="10%">难度</th>
                                    <th width="15%">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for question in judgment_questions %}
                                <tr>
                                    <td>
                                        <input type="checkbox" name="judgment_ids" value="{{ question.id }}" class="judgment-checkbox">
                                    </td>
                                    <td>{{ question.id }}</td>
                                    <td>{{ question.question }}</td>
                                    <td>{{ "正确" if question.answer else "错误" }}</td>
                                    <td>{{ question.category }}</td>
                                    <td>{{ question.difficulty }}</td>
                                    <td>
                                        <a href="/admin/questions/edit-judgment/{{ question.id }}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-danger delete-judgment-btn" data-id="{{ question.id }}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="7" class="text-center">暂无判断题</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </form>
                </div>
                
                <!-- 判断题分页 -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        显示 {{ (judgment_pagination.page - 1) * judgment_pagination.per_page + 1 }} - 
                        {{ [judgment_pagination.page * judgment_pagination.per_page, judgment_total] | min }} 
                        条，共 {{ judgment_total }} 条
                    </div>
                    <div>
                        <label for="judgmentPerPage" class="me-2">每页显示:</label>
                        <select id="judgmentPerPage" class="form-select form-select-sm d-inline-block w-auto">
                            <!-- 选项会通过JavaScript动态添加 -->
                        </select>
                    </div>
                    <nav aria-label="题目分页" class="d-inline-block">
                        <ul class="pagination pagination-sm mb-0">
                            {% if judgment_pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin_questions', tab='judgment', page=judgment_pagination.prev_num, per_page=per_page, class_id=selected_class_id, course_id=selected_course_id) }}">上一页</a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#">上一页</a>
                            </li>
                            {% endif %}
                            
                            {% set start_page = [page - 2, 1]|max %}
                            {% set end_page = [start_page + 4, judgment_pagination.pages]|min %}
                            {% set start_page = [end_page - 4, 1]|max %}
                            
                            {% if start_page > 1 %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin_questions', tab='judgment', page=1, per_page=per_page, class_id=selected_class_id, course_id=selected_course_id) }}">1</a>
                            </li>
                            {% if start_page > 2 %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#">...</a>
                            </li>
                            {% endif %}
                            {% endif %}
                            
                            {% for p in range(start_page, end_page + 1) %}
                            <li class="page-item {% if p == page %}active{% endif %}">
                                <a class="page-link" href="{{ url_for('admin_questions', tab='judgment', page=p, per_page=per_page, class_id=selected_class_id, course_id=selected_course_id) }}">{{ p }}</a>
                            </li>
                            {% endfor %}
                            
                            {% if end_page < judgment_pagination.pages %}
                            {% if end_page < judgment_pagination.pages - 1 %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#">...</a>
                            </li>
                            {% endif %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin_questions', tab='judgment', page=judgment_pagination.pages, per_page=per_page, class_id=selected_class_id, course_id=selected_course_id) }}">{{ judgment_pagination.pages }}</a>
                            </li>
                            {% endif %}
                            
                            {% if judgment_pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin_questions', tab='judgment', page=judgment_pagination.next_num, per_page=per_page, class_id=selected_class_id, course_id=selected_course_id) }}">下一页</a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#">下一页</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 填空题列表 -->
    <div class="tab-pane fade {% if active_tab == 'fill_blank' %}show active{% endif %}" id="fill-blank-pane" role="tabpanel" aria-labelledby="fill-blank-tab">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="card-title">填空题管理</h5>
                </div>
                
                <div class="table-responsive">
                    <form id="fillBlankBatchDeleteForm" action="/admin/questions/batch-delete-fill-blank" method="post">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th width="5%">
                                        <input type="checkbox" id="selectAllFillBlank">
                                    </th>
                                    <th width="5%">ID</th>
                                    <th width="40%">题目内容</th>
                                    <th width="15%">答案</th>
                                    <th width="15%">分类</th>
                                    <th width="5%">难度</th>
                                    <th width="15%">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for question in fill_blank_questions %}
                                <tr>
                                    <td>
                                        <input type="checkbox" name="fill_blank_ids" value="{{ question.id }}" class="fill-blank-checkbox">
                                    </td>
                                    <td>{{ question.id }}</td>
                                    <td>{{ question.question }}</td>
                                    <td>{{ question.answer | join(', ') }}</td>
                                    <td>{{ question.category }}</td>
                                    <td>{{ question.difficulty }}</td>
                                    <td>
                                        <a href="/admin/questions/edit-fill-blank/{{ question.id }}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-danger delete-fill-blank-btn" data-id="{{ question.id }}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="7" class="text-center">暂无填空题</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </form>
                </div>
                
                <!-- 填空题分页 -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        显示 {{ (fill_blank_pagination.page - 1) * fill_blank_pagination.per_page + 1 }} - 
                        {{ [fill_blank_pagination.page * fill_blank_pagination.per_page, fill_blank_total] | min }} 
                        条，共 {{ fill_blank_total }} 条
                    </div>
                    <div>
                        <label for="fillBlankPerPage" class="me-2">每页显示:</label>
                        <select id="fillBlankPerPage" class="form-select form-select-sm d-inline-block w-auto">
                            <!-- 选项会通过JavaScript动态添加 -->
                        </select>
                    </div>
                    <nav aria-label="题目分页" class="d-inline-block">
                        <ul class="pagination pagination-sm mb-0">
                            {% if fill_blank_pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin_questions', tab='fill_blank', page=fill_blank_pagination.prev_num, per_page=per_page, class_id=selected_class_id, course_id=selected_course_id) }}">上一页</a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#">上一页</a>
                            </li>
                            {% endif %}
                            
                            {% set start_page = [page - 2, 1]|max %}
                            {% set end_page = [start_page + 4, fill_blank_pagination.pages]|min %}
                            {% set start_page = [end_page - 4, 1]|max %}
                            
                            {% if start_page > 1 %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin_questions', tab='fill_blank', page=1, per_page=per_page, class_id=selected_class_id, course_id=selected_course_id) }}">1</a>
                            </li>
                            {% if start_page > 2 %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#">...</a>
                            </li>
                            {% endif %}
                            {% endif %}
                            
                            {% for p in range(start_page, end_page + 1) %}
                            <li class="page-item {% if p == page %}active{% endif %}">
                                <a class="page-link" href="{{ url_for('admin_questions', tab='fill_blank', page=p, per_page=per_page, class_id=selected_class_id, course_id=selected_course_id) }}">{{ p }}</a>
                            </li>
                            {% endfor %}
                            
                            {% if end_page < fill_blank_pagination.pages %}
                            {% if end_page < fill_blank_pagination.pages - 1 %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#">...</a>
                            </li>
                            {% endif %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin_questions', tab='fill_blank', page=fill_blank_pagination.pages, per_page=per_page, class_id=selected_class_id, course_id=selected_course_id) }}">{{ fill_blank_pagination.pages }}</a>
                            </li>
                            {% endif %}
                            
                            {% if fill_blank_pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin_questions', tab='fill_blank', page=fill_blank_pagination.next_num, per_page=per_page, class_id=selected_class_id, course_id=selected_course_id) }}">下一页</a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#">下一页</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="deleteConfirmModalLabel">确认删除</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        您确定要删除这个题目吗？此操作无法撤销。
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-danger" id="confirmDelete">删除</button>
      </div>
    </div>
  </div>
</div>

<!-- 轻提示容器 -->
<div class="position-fixed top-0 end-0 p-3" style="z-index: 9999">
  <div id="deleteToast" class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
    <div class="d-flex">
      <div class="toast-body">
        <i class="fas fa-check-circle me-2"></i><span id="toastMessage">题目已成功删除</span>
      </div>
      <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
    </div>
  </div>
</div>

<!-- 批量删除确认模态框 -->
<div class="modal fade" id="batchDeleteConfirmModal" tabindex="-1" aria-labelledby="batchDeleteConfirmModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="batchDeleteConfirmModalLabel">确认批量删除</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        您确定要删除选中的 <span id="selectedCount" class="fw-bold">0</span> 道题目吗？此操作无法撤销。
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-danger" id="confirmBatchDelete">删除</button>
      </div>
    </div>
  </div>
</div>

<!-- 判断题删除确认模态框 -->
<div class="modal fade" id="deleteJudgmentModal" tabindex="-1" aria-labelledby="deleteJudgmentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteJudgmentModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                确定要删除这个判断题吗？此操作不可撤销！
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteJudgmentForm" method="post">
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 填空题删除确认模态框 -->
<div class="modal fade" id="deleteFillBlankModal" tabindex="-1" aria-labelledby="deleteFillBlankModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteFillBlankModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                确定要删除这个填空题吗？此操作不可撤销！
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteFillBlankForm" method="post">
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    // 题目搜索功能
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('keyup', function() {
            const searchText = this.value.toLowerCase();
            
            // 获取当前激活的选项卡
            const activeTabId = document.querySelector('.tab-pane.active').id;
            const tableId = activeTabId === 'single-pane' ? 'singleQuestionsTable' : 'multipleQuestionsTable';
            
            const table = document.getElementById(tableId);
            const rows = table.getElementsByTagName('tr');
            
            for (let i = 1; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.getElementsByTagName('td');
                let found = false;
                
                for (let j = 0; j < cells.length; j++) {
                    const cellText = cells[j].textContent || cells[j].innerText;
                    if (cellText.toLowerCase().indexOf(searchText) > -1) {
                        found = true;
                        break;
                    }
                }
                
                row.style.display = found ? '' : 'none';
            }
        });
    }
    
    // 更新搜索范围
    const questionTabs = document.querySelectorAll('#questionTabs button');
    if (questionTabs && questionTabs.length > 0) {
        questionTabs.forEach(button => {
            button.addEventListener('shown.bs.tab', () => {
                // 触发搜索，应用到新激活的表格
                const searchInput = document.getElementById('searchInput');
                if (searchInput) {
                    searchInput.dispatchEvent(new Event('keyup'));
                }
            });
        });
    }
    
    // 页面加载时检查是否已选择班级，并加载相应课程
    document.addEventListener('DOMContentLoaded', function() {
        console.log('页面加载完成，准备初始化课程选择功能');
        const classSelect = document.getElementById('classSelect');
        const courseSelect = document.getElementById('courseSelect');
        const resetButton = document.getElementById('resetButton');
        
        // 确保classSelect和courseSelect元素存在
        if (!classSelect) {
            console.error('未找到班级选择元素');
            return;
        }
        
        if (!courseSelect) {
            console.error('未找到课程选择元素');
            return;
        }
        
        // 强制初始化课程下拉菜单
        const initCourses = function() {
            const selectedClassId = classSelect.value;
            console.log(`当前选中的班级ID: ${selectedClassId || '无'}`);
            
            if (selectedClassId) {
                console.log(`加载班级 ${selectedClassId} 的课程`);
                loadCourses(selectedClassId);
                
                // 检查课程是否加载成功
                setTimeout(function() {
                    if (courseSelect.options.length <= 1) {
                        console.log('课程加载可能失败，尝试重新加载');
                        loadCourses(selectedClassId);
                    }
                }, 1000);
            } else {
                // 如果没有选择班级，加载所有课程
                console.log('没有选择班级，加载所有课程');
                loadAllCourses();
                
                // 检查课程是否加载成功
                setTimeout(function() {
                    if (courseSelect.options.length <= 1) {
                        console.log('课程加载可能失败，尝试重新加载所有课程');
                        loadAllCourses();
                    }
                }, 1000);
            }
        };
        
        // 初始化课程
        initCourses();
        
        // 班级选择变更时获取对应课程
        classSelect.addEventListener('change', function() {
            console.log(`班级选择变更为: ${this.value || '无'}`);
            loadCourses(this.value);
        });
        
        // 题目详情弹窗
        const questionTexts = document.querySelectorAll('.question-text');
        
        if (questionTexts && questionTexts.length > 0) {
            questionTexts.forEach(text => {
                text.addEventListener('click', function() {
                    // 可以在这里实现点击题目文本显示详情的功能
                    // 例如：显示一个带有完整题目内容的弹窗
                });
            });
        }
        
        // 处理重置按钮点击
        if (resetButton) {
            resetButton.addEventListener('click', function() {
                // 重置下拉框的选择
                classSelect.value = '';
                courseSelect.value = '';
                courseSelect.disabled = true;
                
                // 获取当前选项卡状态
                const url = new URL(window.location.href);
                const currentTab = url.searchParams.get('tab') || 'single';
                
                // 跳转到只包含选项卡参数的页面
                window.location.href = `/admin/questions?tab=${currentTab}`;
            });
        }
    });
    
    // 加载课程函数
    function loadCourses(classId) {
        const courseSelect = document.getElementById('courseSelect');
        
        // 确保courseSelect元素存在
        if (!courseSelect) {
            console.error('未找到courseSelect元素');
            return;
        }
        
        // 保存当前选中的课程值
        const currentSelectedCourse = courseSelect.value;
        
        // 重置课程选择框
        courseSelect.innerHTML = '<option value="">选择课程</option>';
        
        // 如果没有选择班级，则加载所有课程
        if (!classId) {
            console.log('未选择班级，加载所有课程');
            loadAllCourses();
            return;
        }
        
        // 添加加载状态
        courseSelect.classList.add('loading');
        courseSelect.disabled = true;
        const loadingOption = document.createElement('option');
        loadingOption.disabled = true;
        loadingOption.textContent = "加载中...";
        courseSelect.appendChild(loadingOption);
        
        console.log(`正在加载班级ID为 ${classId} 的课程数据...`);
        
        // API请求课程数据
        fetch(`/admin/courses/search?class_id=${classId}`, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin'
        })
            .then(response => {
                console.log(`API响应状态码: ${response.status}`);
                if (!response.ok) {
                    throw new Error(`服务器响应错误 (${response.status})`);
                }
                return response.json();
            })
            .then(data => {
                // 移除加载状态
                courseSelect.classList.remove('loading');
                courseSelect.disabled = false;
                
                // 重置选择框
                courseSelect.innerHTML = '<option value="">选择课程</option>';
                
                console.log('课程数据:', data);
                
                if (data && data.courses && Array.isArray(data.courses) && data.courses.length > 0) {
                    console.log(`找到 ${data.courses.length} 个课程`);
                    
                    // 将数据打印到控制台以便调试
                    data.courses.forEach((course, index) => {
                        console.log(`课程[${index}]: ID=${course.id}, 名称=${course.name}, 班级=${course.class_name}`);
                    });
                    
                    // 构建所有课程选项HTML
                    const courseOptions = data.courses.map(course => {
                        const isSelected = course.id == currentSelectedCourse ? 'selected' : '';
                        return `<option value="${course.id}" ${isSelected}>${course.name}</option>`;
                    }).join('');
                    
                    // 一次性添加所有课程选项
                    courseSelect.innerHTML = '<option value="">选择课程</option>' + courseOptions;
                    
                    // 如果有之前选中的课程，尝试重新选中
                    if (currentSelectedCourse) {
                        courseSelect.value = currentSelectedCourse;
                        console.log(`恢复选中课程ID: ${currentSelectedCourse}`);
                    }
                    
                    // 在控制台显示最终选项列表
                    console.log('课程选择框最终选项:');
                    for (let i = 0; i < courseSelect.options.length; i++) {
                        console.log(`- [${i}] 值=${courseSelect.options[i].value}, 文本=${courseSelect.options[i].text}`);
                    }
                } else {
                    console.log('未找到该班级下的课程数据或数据格式不正确', data);
                    const emptyOption = document.createElement('option');
                    emptyOption.disabled = true;
                    emptyOption.textContent = "该班级下暂无课程";
                    courseSelect.appendChild(emptyOption);
                }
            })
            .catch(error => {
                // 移除加载状态
                courseSelect.classList.remove('loading');
                courseSelect.disabled = false;
                
                // 显示错误
                console.error('加载课程失败:', error);
                courseSelect.innerHTML = '<option value="">选择课程</option>';
                const errorOption = document.createElement('option');
                errorOption.disabled = true;
                errorOption.textContent = "加载失败，请刷新重试";
                courseSelect.appendChild(errorOption);
            });
    }

    // 加载所有课程函数
    function loadAllCourses() {
        const courseSelect = document.getElementById('courseSelect');
        
        // 确保courseSelect元素存在
        if (!courseSelect) {
            console.error('未找到courseSelect元素');
            return;
        }
        
        // 保存当前选中的课程值
        const currentSelectedCourse = courseSelect.value;
        
        // 重置课程选择框
        courseSelect.innerHTML = '<option value="">选择课程</option>';
        
        // 添加加载状态
        courseSelect.classList.add('loading');
        courseSelect.disabled = true;
        const loadingOption = document.createElement('option');
        loadingOption.disabled = true;
        loadingOption.textContent = "加载中...";
        courseSelect.appendChild(loadingOption);
        
        console.log('正在加载所有课程数据...');
        
        // API请求所有课程数据
        fetch('/admin/courses/search', {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'same-origin'
        })
            .then(response => {
                console.log(`API响应状态码: ${response.status}`);
                if (!response.ok) {
                    throw new Error(`服务器响应错误 (${response.status})`);
                }
                return response.json();
            })
            .then(data => {
                // 移除加载状态
                courseSelect.classList.remove('loading');
                courseSelect.disabled = false;
                
                // 重置选择框
                courseSelect.innerHTML = '<option value="">选择课程</option>';
                
                console.log('课程数据:', data);
                
                if (data && data.courses && Array.isArray(data.courses) && data.courses.length > 0) {
                    console.log(`找到 ${data.courses.length} 个课程`);
                    
                    // 将数据打印到控制台以便调试
                    data.courses.forEach((course, index) => {
                        console.log(`课程[${index}]: ID=${course.id}, 名称=${course.name}, 班级=${course.class_name}`);
                    });
                    
                    // 构建所有课程选项HTML
                    const courseOptions = data.courses.map(course => {
                        const isSelected = course.id == currentSelectedCourse ? 'selected' : '';
                        return `<option value="${course.id}" ${isSelected}>${course.name}</option>`;
                    }).join('');
                    
                    // 一次性添加所有课程选项
                    courseSelect.innerHTML = '<option value="">选择课程</option>' + courseOptions;
                    
                    // 如果有之前选中的课程，尝试重新选中
                    if (currentSelectedCourse) {
                        courseSelect.value = currentSelectedCourse;
                        console.log(`恢复选中课程ID: ${currentSelectedCourse}`);
                    }
                    
                    // 在控制台显示最终选项列表
                    console.log('课程选择框最终选项:');
                    for (let i = 0; i < courseSelect.options.length; i++) {
                        console.log(`- [${i}] 值=${courseSelect.options[i].value}, 文本=${courseSelect.options[i].text}`);
                    }
                } else {
                    console.log('未找到任何课程数据或数据格式不正确', data);
                    const emptyOption = document.createElement('option');
                    emptyOption.disabled = true;
                    emptyOption.textContent = "暂无课程数据";
                    courseSelect.appendChild(emptyOption);
                }
            })
            .catch(error => {
                // 移除加载状态
                courseSelect.classList.remove('loading');
                courseSelect.disabled = false;
                
                // 显示错误
                console.error('加载课程失败:', error);
                courseSelect.innerHTML = '<option value="">选择课程</option>';
                const errorOption = document.createElement('option');
                errorOption.disabled = true;
                errorOption.textContent = "加载失败，请刷新重试";
                courseSelect.appendChild(errorOption);
            });
    }

    // 处理删除按钮点击事件
    document.addEventListener('DOMContentLoaded', function() {
        const deleteButtons = document.querySelectorAll('.delete-question');
        const deleteModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
        const confirmDeleteBtn = document.getElementById('confirmDelete');
        const deleteToast = new bootstrap.Toast(document.getElementById('deleteToast'), {
            delay: 3000
        });
        const toastMessage = document.getElementById('toastMessage');
        
        let currentQuestionId = null;
        let currentQuestionType = null;
        
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                currentQuestionId = this.dataset.id;
                currentQuestionType = this.dataset.type;
                deleteModal.show();
            });
        });
        
        confirmDeleteBtn.addEventListener('click', function() {
            if (currentQuestionId && currentQuestionType) {
                // 发送删除请求
                fetch(`/admin/questions/delete/${currentQuestionType}/${currentQuestionId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    deleteModal.hide();
                    
                    if (data.success) {
                        // 删除成功，移除表格中的行
                        const tableId = currentQuestionType === 'single' ? 'singleQuestionsTable' : 'multipleQuestionsTable';
                        const table = document.getElementById(tableId);
                        const rows = table.getElementsByTagName('tr');
                        
                        for (let i = 1; i < rows.length; i++) {
                            const idCell = rows[i].getElementsByTagName('td')[0];
                            if (idCell.textContent.trim() == currentQuestionId) {
                                rows[i].remove();
                                break;
                            }
                        }
                        
                        // 显示成功轻提示
                        toastMessage.textContent = '题目已成功删除';
                        deleteToast.show();
                    } else {
                        // 显示错误轻提示
                        toastMessage.textContent = '删除失败: ' + (data.message || '未知错误');
                        document.getElementById('deleteToast').classList.replace('bg-success', 'bg-danger');
                        deleteToast.show();
                    }
                })
                .catch(error => {
                    deleteModal.hide();
                    console.error('删除请求错误:', error);
                    
                    // 显示错误轻提示
                    toastMessage.textContent = '删除请求失败，请稍后重试';
                    document.getElementById('deleteToast').classList.replace('bg-success', 'bg-danger');
                    deleteToast.show();
                });
            }
        });
    });

    // 批量删除功能
    document.addEventListener('DOMContentLoaded', function() {
        const batchDeleteBtn = document.getElementById('batchDeleteBtn');
        const batchDeleteModal = new bootstrap.Modal(document.getElementById('batchDeleteConfirmModal'));
        const confirmBatchDeleteBtn = document.getElementById('confirmBatchDelete');
        const selectedCountSpan = document.getElementById('selectedCount');
        const deleteToast = document.getElementById('deleteToast');
        const toastMessage = document.getElementById('toastMessage');
        const toast = new bootstrap.Toast(deleteToast, { delay: 3000 });
        
        // 选择所有复选框
        const selectAllCheckboxes = document.querySelectorAll('.select-all-checkbox, #selectAllJudgment, #selectAllFillBlank');
        selectAllCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                let checkboxSelector;
                // 根据不同的选择器类型应用不同的查询
                if(this.id === 'selectAllJudgment') {
                    checkboxSelector = '.judgment-checkbox';
                } else if(this.id === 'selectAllFillBlank') {
                    checkboxSelector = '.fill-blank-checkbox';
                } else {
                    const type = this.dataset.type;
                    checkboxSelector = `.question-checkbox[data-type="${type}"]`;
                }
                
                const questionCheckboxes = document.querySelectorAll(checkboxSelector);
                questionCheckboxes.forEach(cb => {
                    cb.checked = this.checked;
                });
                
                updateBatchDeleteButton();
            });
        });
        
        // 单个复选框变更
        document.addEventListener('change', function(e) {
            if (e.target.classList.contains('question-checkbox') || 
                e.target.classList.contains('judgment-checkbox') ||
                e.target.classList.contains('fill-blank-checkbox')) {
                updateBatchDeleteButton();
                
                // 对应的分类选择器和全选框
                let allSelector, selectAllId;
                
                if(e.target.classList.contains('judgment-checkbox')) {
                    allSelector = '.judgment-checkbox';
                    selectAllId = 'selectAllJudgment';
                } else if(e.target.classList.contains('fill-blank-checkbox')) {
                    allSelector = '.fill-blank-checkbox';
                    selectAllId = 'selectAllFillBlank';
                } else {
                    const type = e.target.dataset.type;
                    allSelector = `.question-checkbox[data-type="${type}"]`;
                    selectAllId = null;
                }
                
                // 检查是否需要更新全选框
                const allCheckboxes = document.querySelectorAll(allSelector);
                const allChecked = Array.from(allCheckboxes).every(cb => cb.checked);
                
                let selectAllCheckbox;
                if(selectAllId) {
                    selectAllCheckbox = document.getElementById(selectAllId);
                } else {
                    const type = e.target.dataset.type;
                    selectAllCheckbox = document.querySelector(`.select-all-checkbox[data-type="${type}"]`);
                }
                
                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = allChecked && allCheckboxes.length > 0;
                }
            }
        });
        
        // 更新批量删除按钮状态
        function updateBatchDeleteButton() {
            const checkedBoxes = document.querySelectorAll('.question-checkbox:checked, .judgment-checkbox:checked, .fill-blank-checkbox:checked');
            batchDeleteBtn.disabled = checkedBoxes.length === 0;
            selectedCountSpan.textContent = checkedBoxes.length;
        }
        
        // 批量删除按钮点击
        batchDeleteBtn.addEventListener('click', function() {
            const checkedBoxes = document.querySelectorAll('.question-checkbox:checked, .judgment-checkbox:checked, .fill-blank-checkbox:checked');
            if (checkedBoxes.length > 0) {
                selectedCountSpan.textContent = checkedBoxes.length;
                batchDeleteModal.show();
            }
        });
        
        // 确认批量删除
        confirmBatchDeleteBtn.addEventListener('click', function() {
            const checkedSingleMultiple = document.querySelectorAll('.question-checkbox:checked');
            const checkedJudgment = document.querySelectorAll('.judgment-checkbox:checked');
            const checkedFillBlank = document.querySelectorAll('.fill-blank-checkbox:checked');
            
            if (checkedSingleMultiple.length === 0 && checkedJudgment.length === 0 && checkedFillBlank.length === 0) {
                batchDeleteModal.hide();
                return;
            }
            
            // 收集所有要删除的题目ID和类型
            const deleteTasks = [];
            
            // 单选和多选题
            checkedSingleMultiple.forEach(cb => {
                deleteTasks.push({
                    id: cb.dataset.id,
                    type: cb.dataset.type
                });
            });
            
            // 判断题
            if (checkedJudgment.length > 0) {
                // 使用表单提交处理判断题批量删除
                if (document.getElementById('judgmentBatchDeleteForm')) {
                    document.getElementById('judgmentBatchDeleteForm').submit();
                    batchDeleteModal.hide();
                    return;
                }
            }
            
            // 填空题
            if (checkedFillBlank.length > 0) {
                // 使用表单提交处理填空题批量删除
                if (document.getElementById('fillBlankBatchDeleteForm')) {
                    document.getElementById('fillBlankBatchDeleteForm').submit();
                    batchDeleteModal.hide();
                    return;
                }
            }
            
            // 只处理单选和多选题批量删除
            if (deleteTasks.length > 0) {
                // 发送批量删除请求
                fetch('/admin/questions/batch-delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({ questions: deleteTasks })
                })
                .then(response => response.json())
                .then(data => {
                    batchDeleteModal.hide();
                    
                    if (data.success) {
                        // 删除成功，移除表格中的行
                        deleteTasks.forEach(task => {
                            const row = document.querySelector(`.question-checkbox[data-id="${task.id}"][data-type="${task.type}"]`).closest('tr');
                            if (row) {
                                row.remove();
                            }
                        });
                        
                        // 更新批量删除按钮状态
                        updateBatchDeleteButton();
                        
                        // 更新全选复选框状态
                        selectAllCheckboxes.forEach(checkbox => {
                            checkbox.checked = false;
                        });
                        
                        // 显示成功轻提示
                        deleteToast.classList.remove('bg-danger');
                        deleteToast.classList.add('bg-success');
                        toastMessage.textContent = `成功删除 ${data.deletedCount} 道题目`;
                        toast.show();
                    } else {
                        // 显示错误轻提示
                        deleteToast.classList.remove('bg-success');
                        deleteToast.classList.add('bg-danger');
                        toastMessage.textContent = `删除失败: ${data.message || '未知错误'}`;
                        toast.show();
                    }
                })
                .catch(error => {
                    batchDeleteModal.hide();
                    console.error('批量删除请求错误:', error);
                    
                    // 显示错误轻提示
                    deleteToast.classList.remove('bg-success');
                    deleteToast.classList.add('bg-danger');
                    toastMessage.textContent = '删除请求失败，请稍后重试';
                    toast.show();
                });
            }
        });
        
        // 判断题单个删除
        const deleteJudgmentButtons = document.querySelectorAll('.delete-judgment-btn');
        const deleteJudgmentForm = document.getElementById('deleteJudgmentForm');
        
        if (deleteJudgmentButtons.length > 0 && deleteJudgmentForm) {
            deleteJudgmentButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const questionId = this.getAttribute('data-id');
                    deleteJudgmentForm.action = `/admin/questions/delete-judgment/${questionId}`;
                    
                    const deleteJudgmentModal = new bootstrap.Modal(document.getElementById('deleteJudgmentModal'));
                    deleteJudgmentModal.show();
                });
            });
        }
        
        // 填空题单个删除
        const deleteFillBlankButtons = document.querySelectorAll('.delete-fill-blank-btn');
        const deleteFillBlankForm = document.getElementById('deleteFillBlankForm');
        
        if (deleteFillBlankButtons.length > 0 && deleteFillBlankForm) {
            deleteFillBlankButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const questionId = this.getAttribute('data-id');
                    deleteFillBlankForm.action = `/admin/questions/delete-fill-blank/${questionId}`;
                    
                    const deleteFillBlankModal = new bootstrap.Modal(document.getElementById('deleteFillBlankModal'));
                    deleteFillBlankModal.show();
                });
            });
        }
        
        // 更新所有每页显示数量选择器和初始化分页功能
        const initializePerPageSelectors = () => {
            // 判断题和填空题的选择器，确保它们有选项
            const judgmentPerPage = document.getElementById('judgmentPerPage');
            const fillBlankPerPage = document.getElementById('fillBlankPerPage');
            
            // 确保所有选择器都有正确的选项
            const perPageSelects = document.querySelectorAll('#singlePerPage, #multiplePerPage, #judgmentPerPage, #fillBlankPerPage');
            perPageSelects.forEach(select => {
                // 判断题和填空题的下拉选择器需要动态添加选项
                if (select.id === 'judgmentPerPage' || select.id === 'fillBlankPerPage') {
                    if (select.options.length === 0) {
                        // 添加标准选项
                        const options = [10, 20, 50, 100];
                        const currentPerPage = {{ per_page }};
                        
                        options.forEach(value => {
                            const option = document.createElement('option');
                            option.value = value;
                            option.textContent = value + '条/页';
                            if (value === currentPerPage) {
                                option.selected = true;
                            }
                            select.appendChild(option);
                        });
                    }
                }
                
                // 添加变更事件
                select.addEventListener('change', function() {
                    const perPage = this.value;
                    let currentTab = '';
                    
                    // 根据选择器ID确定当前标签页
                    if (this.id === 'singlePerPage') {
                        currentTab = 'single';
                    } else if (this.id === 'multiplePerPage') {
                        currentTab = 'multiple';
                    } else if (this.id === 'judgmentPerPage') {
                        currentTab = 'judgment';
                    } else if (this.id === 'fillBlankPerPage') {
                        currentTab = 'fill_blank';
                    }
                    
                    // 构建URL参数
                    const url = new URL(window.location.href);
                    url.searchParams.set('per_page', perPage);
                    url.searchParams.set('page', 1); // 切换每页数量时重置为第1页
                    url.searchParams.set('tab', currentTab); // 确保保留选项卡状态
                    
                    // 保留筛选参数
                    const classId = url.searchParams.get('class_id');
                    const courseId = url.searchParams.get('course_id');
                    if (classId) url.searchParams.set('class_id', classId);
                    if (courseId) url.searchParams.set('course_id', courseId);
                    
                    // 跳转到新页面
                    window.location.href = url.toString();
                });
            });
            
            // 选项卡切换事件 - 更新URL中的tab参数
            const tabLinks = document.querySelectorAll('#questionTabs .nav-link');
            tabLinks.forEach(tabLink => {
                tabLink.addEventListener('shown.bs.tab', function(e) {
                    const tabId = e.target.id;
                    const tabType = tabId.replace('-tab', ''); // 'single-tab' -> 'single'
                    
                    // 更新URL参数但不刷新页面
                    const url = new URL(window.location.href);
                    url.searchParams.set('tab', tabType);
                    window.history.replaceState({}, '', url.toString());
                });
            });
        };
        
        // 初始化分页和选择器
        initializePerPageSelectors();
    });
</script>
{% endblock %} 
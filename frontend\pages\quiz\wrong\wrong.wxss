/* 错题页面样式 */
.wrong-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f6f6f6;
}

.header {
  padding: 30rpx 30rpx 20rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.subtitle {
  font-size: 26rpx;
  color: #999;
  margin-top: 6rpx;
}

/* 轻提示样式 */
.tip-container {
  background-color: #f8f8f8;
  padding: 12rpx 24rpx;
  margin-bottom: 10rpx;
}

.tip-content {
  display: flex;
  align-items: center;
  background-color: #fff7e6;
  border-radius: 8rpx;
  padding: 16rpx 20rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
  border-left: 4rpx solid #faad14;
}

.tip-icon {
  margin-right: 12rpx;
  color: #faad14;
  font-weight: bold;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 2rpx solid #faad14;
}

.tip-text {
  font-size: 24rpx;
  color: #8f6500;
  line-height: 1.5;
  flex: 1;
}

.tip-close {
  font-size: 30rpx;
  color: #faad14;
  margin-left: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  opacity: 0.8;
}

.tip-close:active {
  opacity: 1;
}

/* 滚动容器样式 */
.scroll-container {
  display: flex;
  flex-direction: row;
  height: calc(100vh - 240rpx);
  position: relative;
}

.wrong-list {
  flex: 1;
  height: 100%;
  padding-right: 80rpx;
}

/* 右侧导航滚动条样式 */
.scroll-navigator {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 60rpx;
  max-height: 80vh;
  background-color: rgba(245, 245, 245, 0.8);
  border-radius: 30rpx;
  padding: 10rpx 0;
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.scroll-indicator-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-height: 60vh;
  overflow-y: auto;
}

.scroll-indicator {
  width: 40rpx;
  height: 40rpx;
  line-height: 40rpx;
  text-align: center;
  font-size: 22rpx;
  color: #666;
  margin: 6rpx 0;
  border-radius: 50%;
  background-color: #fff;
  box-shadow: 0 0 5rpx rgba(0, 0, 0, 0.1);
}

.scroll-indicator-hover,
.scroll-indicator.active {
  background-color: #007aff;
  color: #fff;
}

.question-card {
  background-color: #fff;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.question-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.question-index {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.question-type {
  font-size: 24rpx;
  color: #666;
  background-color: #f1f1f1;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.question-content {
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 20rpx;
}

.options-container {
  margin-bottom: 20rpx;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 16rpx;
  border-radius: 8rpx;
  margin-bottom: 12rpx;
  background-color: #f8f8f8;
  border: 1rpx solid transparent;
}

/* 用户选择正确的选项 - 绿色背景 */
.correct-selected {
  background-color: #e8f5e9;
  border: 1rpx solid #4caf50;
}

/* 用户选择错误的选项 - 红色背景 */
.wrong-selected {
  background-color: #ffebee;
  border: 1rpx solid #f44336;
}

/* 选项圆圈 */
.option-circle {
  width: 40rpx;
  height: 40rpx;
  background-color: #ddd;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
  color: #666;
  margin-right: 16rpx;
}

/* 正确选择的圆圈 */
.correct-circle {
  background-color: #4caf50;
  color: white;
}

/* 错误选择的圆圈 */
.wrong-circle {
  background-color: #f44336;
  color: white;
}

.option-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

/* 填空题用户答案样式 */
.user-answer {
  font-size: 28rpx;
  color: #333;
  padding: 8rpx 0;
  font-weight: 500;
}

/* 答案解析部分 */
.answer-section {
  background-color: #f0f7ff;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-top: 16rpx;
}

.answer-header {
  font-size: 28rpx;
  font-weight: 600;
  color: #2b85e4;
  margin-bottom: 12rpx;
}

.correct-answer {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.answer-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 8rpx;
}

.answer-value {
  font-size: 28rpx;
  color: #4caf50;
  font-weight: 500;
}

.analysis-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-top: 8rpx;
}

.no-analysis {
  color: #999;
  font-size: 26rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.bottom-btns {
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-top: 1rpx solid #eee;
}

.back-btn {
  height: 80rpx;
  border-radius: 40rpx;
  background-color: #2b85e4;
  color: #fff;
  font-size: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
} 
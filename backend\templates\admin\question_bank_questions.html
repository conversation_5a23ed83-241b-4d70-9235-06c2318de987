{% extends "admin/base.html" %}

{% block title %}题库题目管理 - 题库管理系统{% endblock %}

{% block header %}
<div class="d-flex align-items-center">
    <a href="/admin/question-banks" class="btn btn-link text-decoration-none text-dark p-0 me-3">
        <i class="fas fa-arrow-left"></i> 返回题库列表
    </a>
    <span>题库题目管理：{{ bank.name }}</span>
</div>
{% endblock %}

{% block header_buttons %}{% endblock %}

{% block content %}
<!-- 题库信息 -->
<div class="card mb-4">
    <div class="card-header bg-white">
        <div class="d-flex align-items-center">
            <i class="fas fa-info-circle me-2 text-primary"></i>
            <span class="fw-bold">题库信息</span>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <p><strong>题库名称：</strong>{{ bank.name }}</p>
                <p><strong>所属课程：</strong>{{ bank.course.name }}</p>
                <p><strong>所属班级：</strong>
                {% if bank.course.class_obj is defined and bank.course.class_obj %}
                    {{ bank.course.class_obj.name }}
                {% elif bank.course.classes is defined and bank.course.classes %}
                    {{ bank.course.classes[0].name if bank.course.classes|length > 0 else '无班级' }}
                {% else %}
                    无班级
                {% endif %}
                </p>
            </div>
            <div class="col-md-6">
                <p><strong>状态：</strong>
                    {% if bank.is_active %}
                    <span class="badge bg-success">已上架</span>
                    {% else %}
                    <span class="badge bg-secondary">已下架</span>
                    {% endif %}
                </p>
                <p><strong>单选题数量：</strong><span class="badge bg-primary rounded-pill">{{ bank.single_questions|length }}</span></p>
                <p><strong>多选题数量：</strong><span class="badge bg-info rounded-pill">{{ bank.multiple_questions|length }}</span></p>
            </div>
        </div>
        {% if bank.description %}
        <div class="row mt-2">
            <div class="col-12">
                <p><strong>题库描述：</strong>{{ bank.description }}</p>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- 题目管理选项卡 -->
<ul class="nav nav-tabs mb-4" id="questionTypeTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="current-single-tab" data-bs-toggle="tab" data-bs-target="#current-single-pane" type="button" role="tab" aria-controls="current-single-pane" aria-selected="true">
            当前单选题 ({{ bank.single_questions|length }})
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="current-multiple-tab" data-bs-toggle="tab" data-bs-target="#current-multiple-pane" type="button" role="tab" aria-controls="current-multiple-pane" aria-selected="false">
            当前多选题 ({{ bank.multiple_questions|length }})
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="add-single-tab" data-bs-toggle="tab" data-bs-target="#add-single-pane" type="button" role="tab" aria-controls="add-single-pane" aria-selected="false">
            添加单选题
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="add-multiple-tab" data-bs-toggle="tab" data-bs-target="#add-multiple-pane" type="button" role="tab" aria-controls="add-multiple-pane" aria-selected="false">
            添加多选题
        </button>
    </li>
</ul>

<div class="tab-content" id="questionTypeTabContent">
    <!-- 当前单选题列表 -->
    <div class="tab-pane fade show active" id="current-single-pane" role="tabpanel" aria-labelledby="current-single-tab">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="currentSingleQuestionsTable">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>题目</th>
                                <th>选项数</th>
                                <th>正确答案</th>
                                <th>分类</th>
                                <th>难度</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for question in bank.single_questions %}
                            <tr>
                                <td>{{ question.id }}</td>
                                <td>
                                    <div class="question-text" style="max-width: 300px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;" title="{{ question.question }}">
                                        {{ question.question }}
                                    </div>
                                </td>
                                <td>{{ question.options|length }}</td>
                                <td>{{ question.answer }}</td>
                                <td>{{ question.category }}</td>
                                <td>
                                    {% for i in range(question.difficulty) %}
                                    <i class="fas fa-star text-warning"></i>
                                    {% endfor %}
                                    {% for i in range(3 - question.difficulty) %}
                                    <i class="far fa-star text-muted"></i>
                                    {% endfor %}
                                </td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-outline-danger delete-btn" data-id="{{ question.id }}" data-type="single">
                                        <i class="fas fa-trash"></i> 移除
                                    </button>
                                    
                                    <!-- 单选题删除确认内容（隐藏） -->
                                    <div class="delete-confirm-single-{{ question.id }} mt-2 confirm-delete-container" style="display: none;">
                                        <div class="alert alert-danger">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                                                <strong>确定要从题库中移除此单选题吗？</strong>
                                            </div>
                                            <div class="d-flex justify-content-end">
                                                <button type="button" class="btn btn-sm btn-secondary me-2 cancel-delete-btn" data-id="{{ question.id }}" data-type="single">
                                                    <i class="fas fa-times me-1"></i>取消
                                                </button>
                                                <form action="/admin/question-banks/{{ bank.id }}/remove-single-question/{{ question.id }}" method="post" class="delete-form">
                                                    <button type="submit" class="btn btn-sm btn-danger confirm-delete-btn">
                                                        <i class="fas fa-trash me-1"></i>确认移除
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 当前多选题列表 -->
    <div class="tab-pane fade" id="current-multiple-pane" role="tabpanel" aria-labelledby="current-multiple-tab">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="currentMultipleQuestionsTable">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>题目</th>
                                <th>选项数</th>
                                <th>正确答案</th>
                                <th>分类</th>
                                <th>难度</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for question in bank.multiple_questions %}
                            <tr>
                                <td>{{ question.id }}</td>
                                <td>
                                    <div class="question-text" style="max-width: 300px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;" title="{{ question.question }}">
                                        {{ question.question }}
                                    </div>
                                </td>
                                <td>{{ question.options|length }}</td>
                                <td>{{ question.answer|join(', ') }}</td>
                                <td>{{ question.category }}</td>
                                <td>
                                    {% for i in range(question.difficulty) %}
                                    <i class="fas fa-star text-warning"></i>
                                    {% endfor %}
                                    {% for i in range(3 - question.difficulty) %}
                                    <i class="far fa-star text-muted"></i>
                                    {% endfor %}
                                </td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-outline-danger delete-btn" data-id="{{ question.id }}" data-type="multiple">
                                        <i class="fas fa-trash"></i> 移除
                                    </button>
                                    
                                    <!-- 多选题删除确认内容（隐藏） -->
                                    <div class="delete-confirm-multiple-{{ question.id }} mt-2 confirm-delete-container" style="display: none;">
                                        <div class="alert alert-danger">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                                                <strong>确定要从题库中移除此多选题吗？</strong>
                                            </div>
                                            <div class="d-flex justify-content-end">
                                                <button type="button" class="btn btn-sm btn-secondary me-2 cancel-delete-btn" data-id="{{ question.id }}" data-type="multiple">
                                                    <i class="fas fa-times me-1"></i>取消
                                                </button>
                                                <form action="/admin/question-banks/{{ bank.id }}/remove-multiple-question/{{ question.id }}" method="post" class="delete-form">
                                                    <button type="submit" class="btn btn-sm btn-danger confirm-delete-btn">
                                                        <i class="fas fa-trash me-1"></i>确认移除
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 添加单选题 -->
    <div class="tab-pane fade" id="add-single-pane" role="tabpanel" aria-labelledby="add-single-tab">
        <div class="card">
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    选择下方题目后，点击"批量添加选中题目"按钮将所选题目添加到题库中。
                </div>
                
                <div class="table-responsive">
                    <table class="table table-hover" id="availableSingleQuestionsTable">
                        <thead>
                            <tr>
                                <th width="40px">
                                    <input type="checkbox" id="selectAllSingle" class="select-all-checkbox">
                                </th>
                                <th>ID</th>
                                <th>题目</th>
                                <th>选项数</th>
                                <th>正确答案</th>
                                <th>分类</th>
                                <th>难度</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for question in all_single_questions %}
                            {% if question not in bank.single_questions %}
                            <tr>
                                <td>
                                    <input type="checkbox" class="single-question-checkbox" value="{{ question.id }}">
                                </td>
                                <td>{{ question.id }}</td>
                                <td>
                                    <div class="question-text" style="max-width: 300px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;" title="{{ question.question }}">
                                        {{ question.question }}
                                    </div>
                                </td>
                                <td>{{ question.options|length }}</td>
                                <td>{{ question.answer }}</td>
                                <td>{{ question.category }}</td>
                                <td>
                                    {% for i in range(question.difficulty) %}
                                    <i class="fas fa-star text-warning"></i>
                                    {% endfor %}
                                    {% for i in range(3 - question.difficulty) %}
                                    <i class="far fa-star text-muted"></i>
                                    {% endfor %}
                                </td>
                            </tr>
                            {% endif %}
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- 批量添加单选题按钮 -->
                <div class="mt-3">
                    <button id="batchAddSingleBtn" class="btn btn-primary">
                        <i class="fas fa-plus-circle me-1"></i>批量添加选中题目 (<span id="selectedSingleCount">0</span>)
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 添加多选题 -->
    <div class="tab-pane fade" id="add-multiple-pane" role="tabpanel" aria-labelledby="add-multiple-tab">
        <div class="card">
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    选择下方题目后，点击"批量添加选中题目"按钮将所选题目添加到题库中。
                </div>
                
                <div class="table-responsive">
                    <table class="table table-hover" id="availableMultipleQuestionsTable">
                        <thead>
                            <tr>
                                <th width="40px">
                                    <input type="checkbox" id="selectAllMultiple" class="select-all-checkbox">
                                </th>
                                <th>ID</th>
                                <th>题目</th>
                                <th>选项数</th>
                                <th>正确答案</th>
                                <th>分类</th>
                                <th>难度</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for question in all_multiple_questions %}
                            {% if question not in bank.multiple_questions %}
                            <tr>
                                <td>
                                    <input type="checkbox" class="multiple-question-checkbox" value="{{ question.id }}">
                                </td>
                                <td>{{ question.id }}</td>
                                <td>
                                    <div class="question-text" style="max-width: 300px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;" title="{{ question.question }}">
                                        {{ question.question }}
                                    </div>
                                </td>
                                <td>{{ question.options|length }}</td>
                                <td>{{ question.answer|join(', ') }}</td>
                                <td>{{ question.category }}</td>
                                <td>
                                    {% for i in range(question.difficulty) %}
                                    <i class="fas fa-star text-warning"></i>
                                    {% endfor %}
                                    {% for i in range(3 - question.difficulty) %}
                                    <i class="far fa-star text-muted"></i>
                                    {% endfor %}
                                </td>
                            </tr>
                            {% endif %}
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- 批量添加多选题按钮 -->
                <div class="mt-3">
                    <button id="batchAddMultipleBtn" class="btn btn-primary">
                        <i class="fas fa-plus-circle me-1"></i>批量添加选中题目 (<span id="selectedMultipleCount">0</span>)
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 添加判断题 -->
    <div class="tab-pane fade" id="add-true-false-pane" role="tabpanel" aria-labelledby="add-true-false-tab">
        <div class="card">
            <div class="card-body">
                <form action="/admin/question-banks/{{ bank.id }}/add-true-false-question" method="post" class="mb-4">
                    <div class="row g-3 align-items-end">
                        <div class="col-md-9">
                            <label for="true_false_question_id" class="form-label">选择要添加的判断题</label>
                            <select class="form-select" id="true_false_question_id" name="question_id" required>
                                <option value="">请选择题目</option>
                                {% for question in all_truefalse_questions %}
                                {% if question not in bank.truefalse_questions %}
                                <option value="{{ question.id }}">
                                    [ID:{{ question.id }}] {{ question.question|truncate(50) }}
                                </option>
                                {% endif %}
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-plus-circle me-1"></i>添加到题库
                            </button>
                        </div>
                    </div>
                </form>
                
                <div class="table-responsive">
                    <table class="table table-hover" id="availableTrueFalseQuestionsTable">
                        <thead>
                            <tr>
                                <th width="40px">
                                    <input type="checkbox" id="selectAllTrueFalse" class="select-all-checkbox">
                                </th>
                                <th>ID</th>
                                <th>题目</th>
                                <th>正确答案</th>
                                <th>分类</th>
                                <th>难度</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for question in all_truefalse_questions %}
                            {% if question not in bank.truefalse_questions %}
                            <tr>
                                <td>
                                    <input type="checkbox" class="truefalse-question-checkbox" value="{{ question.id }}">
                                </td>
                                <td>{{ question.id }}</td>
                                <td>
                                    <div class="question-text" style="max-width: 300px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;" title="{{ question.question }}">
                                        {{ question.question }}
                                    </div>
                                </td>
                                <td>{{ "正确" if question.answer else "错误" }}</td>
                                <td>{{ question.category }}</td>
                                <td>
                                    {% for i in range(question.difficulty) %}
                                    <i class="fas fa-star text-warning"></i>
                                    {% endfor %}
                                    {% for i in range(3 - question.difficulty) %}
                                    <i class="far fa-star text-muted"></i>
                                    {% endfor %}
                                </td>
                                <td>
                                    <form action="/admin/question-banks/{{ bank.id }}/add-true-false-question" method="post" class="d-inline">
                                        <input type="hidden" name="question_id" value="{{ question.id }}">
                                        <button type="submit" class="btn btn-sm btn-outline-success">
                                            <i class="fas fa-plus"></i> 添加
                                        </button>
                                    </form>
                                </td>
                            </tr>
                            {% endif %}
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- 批量添加判断题按钮 -->
                <div class="mt-3">
                    <button id="batchAddTrueFalseBtn" class="btn btn-primary" disabled>
                        <i class="fas fa-plus-circle me-1"></i>批量添加选中题目 (<span id="selectedTrueFalseCount">0</span>)
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 添加填空题 -->
    <div class="tab-pane fade" id="add-fill-blank-pane" role="tabpanel" aria-labelledby="add-fill-blank-tab">
        <div class="card">
            <div class="card-body">
                <form action="/admin/question-banks/{{ bank.id }}/add-fill-blank-question" method="post" class="mb-4">
                    <div class="row g-3 align-items-end">
                        <div class="col-md-9">
                            <label for="fill_blank_question_id" class="form-label">选择要添加的填空题</label>
                            <select class="form-select" id="fill_blank_question_id" name="question_id" required>
                                <option value="">请选择题目</option>
                                {% for question in all_fill_blank_questions %}
                                {% if question not in bank.fill_blank_questions %}
                                <option value="{{ question.id }}">
                                    [ID:{{ question.id }}] {{ question.question|truncate(50) }}
                                </option>
                                {% endif %}
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-plus-circle me-1"></i>添加到题库
                            </button>
                        </div>
                    </div>
                </form>
                
                <div class="table-responsive">
                    <table class="table table-hover" id="availableFillBlankQuestionsTable">
                        <thead>
                            <tr>
                                <th width="40px">
                                    <input type="checkbox" id="selectAllFillBlank" class="select-all-checkbox">
                                </th>
                                <th>ID</th>
                                <th>题目</th>
                                <th>正确答案</th>
                                <th>分类</th>
                                <th>难度</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for question in all_fill_blank_questions %}
                            {% if question not in bank.fill_blank_questions %}
                            <tr>
                                <td>
                                    <input type="checkbox" class="fillblank-question-checkbox" value="{{ question.id }}">
                                </td>
                                <td>{{ question.id }}</td>
                                <td>
                                    <div class="question-text" style="max-width: 300px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;" title="{{ question.question }}">
                                        {{ question.question }}
                                    </div>
                                </td>
                                <td>{{ question.answer|join(', ') }}</td>
                                <td>{{ question.category }}</td>
                                <td>
                                    {% for i in range(question.difficulty) %}
                                    <i class="fas fa-star text-warning"></i>
                                    {% endfor %}
                                    {% for i in range(3 - question.difficulty) %}
                                    <i class="far fa-star text-muted"></i>
                                    {% endfor %}
                                </td>
                                <td>
                                    <form action="/admin/question-banks/{{ bank.id }}/add-fill-blank-question" method="post" class="d-inline">
                                        <input type="hidden" name="question_id" value="{{ question.id }}">
                                        <button type="submit" class="btn btn-sm btn-outline-success">
                                            <i class="fas fa-plus"></i> 添加
                                        </button>
                                    </form>
                                </td>
                            </tr>
                            {% endif %}
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- 批量添加填空题按钮 -->
                <div class="mt-3">
                    <button id="batchAddFillBlankBtn" class="btn btn-primary" disabled>
                        <i class="fas fa-plus-circle me-1"></i>批量添加选中题目 (<span id="selectedFillBlankCount">0</span>)
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 底部返回按钮 -->
<div class="d-flex justify-content-center mt-4 mb-4">
    <a href="/admin/question-banks" class="btn btn-outline-primary" style="min-width: 120px;">
        <i class="fas fa-arrow-left me-2"></i>返回
    </a>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .btn-link:hover {
        color: var(--primary-color) !important;
    }
    
    /* 优化模态框样式 */
    .modal {
        transition: opacity 0.15s linear;
    }
    .modal.show {
        background-color: rgba(0, 0, 0, 0.5);
    }
    .modal-dialog {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
</style>
<!-- 添加SweetAlert2 CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
{% endblock %}

{% block scripts %}
<!-- 添加SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    $(document).ready(function() {
        $('#currentSingleQuestionsTable').DataTable({
            language: {
                "processing": "处理中...",
                "lengthMenu": "每页显示 _MENU_ 条记录",
                "zeroRecords": "没有找到匹配的记录",
                "info": "第 _PAGE_ 页 / 共 _PAGES_ 页 ( 共 _TOTAL_ 条记录 )",
                "infoEmpty": "没有记录",
                "infoFiltered": "(从 _MAX_ 条记录中过滤)",
                "search": "搜索:",
                "emptyTable": "表中没有数据",
                "paginate": {
                    "first": "首页",
                    "previous": "上一页",
                    "next": "下一页",
                    "last": "末页"
                },
                "aria": {
                    "sortAscending": ": 升序排列",
                    "sortDescending": ": 降序排列"
                },
                "autoFill": {
                    "cancel": "取消",
                    "fill": "填充所有单元格",
                    "fillHorizontal": "填充水平单元格",
                    "fillVertical": "填充垂直单元格"
                },
                "buttons": {
                    "collection": "集合",
                    "colvis": "列可见性",
                    "copy": "复制",
                    "copyTitle": "复制到剪贴板",
                    "csv": "CSV",
                    "excel": "Excel",
                    "pdf": "PDF",
                    "print": "打印"
                }
            },
            responsive: true,
            order: [[0, 'desc']],
            pageLength: 10,  // 每页显示10条记录
            lengthMenu: [
                [10, 25, 50, 100, -1],
                ['10条', '25条', '50条', '100条', '全部']
            ],
            pagingType: "full_numbers"  // 显示所有分页按钮
        });
        
        $('#currentMultipleQuestionsTable').DataTable({
            language: {
                "processing": "处理中...",
                "lengthMenu": "每页显示 _MENU_ 条记录",
                "zeroRecords": "没有找到匹配的记录",
                "info": "第 _PAGE_ 页 / 共 _PAGES_ 页 ( 共 _TOTAL_ 条记录 )",
                "infoEmpty": "没有记录",
                "infoFiltered": "(从 _MAX_ 条记录中过滤)",
                "search": "搜索:",
                "emptyTable": "表中没有数据",
                "paginate": {
                    "first": "首页",
                    "previous": "上一页",
                    "next": "下一页",
                    "last": "末页"
                },
                "aria": {
                    "sortAscending": ": 升序排列",
                    "sortDescending": ": 降序排列"
                },
                "autoFill": {
                    "cancel": "取消",
                    "fill": "填充所有单元格",
                    "fillHorizontal": "填充水平单元格",
                    "fillVertical": "填充垂直单元格"
                },
                "buttons": {
                    "collection": "集合",
                    "colvis": "列可见性",
                    "copy": "复制",
                    "copyTitle": "复制到剪贴板",
                    "csv": "CSV",
                    "excel": "Excel",
                    "pdf": "PDF",
                    "print": "打印"
                }
            },
            responsive: true,
            order: [[0, 'desc']],
            pageLength: 10,
            lengthMenu: [
                [10, 25, 50, 100, -1],
                ['10条', '25条', '50条', '100条', '全部']
            ],
            pagingType: "full_numbers"
        });
        
        $('#availableSingleQuestionsTable').DataTable({
            language: {
                "processing": "处理中...",
                "lengthMenu": "每页显示 _MENU_ 条记录",
                "zeroRecords": "没有找到匹配的记录",
                "info": "第 _PAGE_ 页 / 共 _PAGES_ 页 ( 共 _TOTAL_ 条记录 )",
                "infoEmpty": "没有记录",
                "infoFiltered": "(从 _MAX_ 条记录中过滤)",
                "search": "搜索:",
                "emptyTable": "表中没有数据",
                "paginate": {
                    "first": "首页",
                    "previous": "上一页",
                    "next": "下一页",
                    "last": "末页"
                },
                "aria": {
                    "sortAscending": ": 升序排列",
                    "sortDescending": ": 降序排列"
                },
                "autoFill": {
                    "cancel": "取消",
                    "fill": "填充所有单元格",
                    "fillHorizontal": "填充水平单元格",
                    "fillVertical": "填充垂直单元格"
                },
                "buttons": {
                    "collection": "集合",
                    "colvis": "列可见性",
                    "copy": "复制",
                    "copyTitle": "复制到剪贴板",
                    "csv": "CSV",
                    "excel": "Excel",
                    "pdf": "PDF",
                    "print": "打印"
                }
            },
            responsive: true,
            order: [[0, 'desc']],
            pageLength: 10,
            lengthMenu: [
                [10, 25, 50, 100, -1],
                ['10条', '25条', '50条', '100条', '全部']
            ],
            pagingType: "full_numbers"
        });
        
        $('#availableMultipleQuestionsTable').DataTable({
            language: {
                "processing": "处理中...",
                "lengthMenu": "每页显示 _MENU_ 条记录",
                "zeroRecords": "没有找到匹配的记录",
                "info": "第 _PAGE_ 页 / 共 _PAGES_ 页 ( 共 _TOTAL_ 条记录 )",
                "infoEmpty": "没有记录",
                "infoFiltered": "(从 _MAX_ 条记录中过滤)",
                "search": "搜索:",
                "emptyTable": "表中没有数据",
                "paginate": {
                    "first": "首页",
                    "previous": "上一页",
                    "next": "下一页",
                    "last": "末页"
                },
                "aria": {
                    "sortAscending": ": 升序排列",
                    "sortDescending": ": 降序排列"
                },
                "autoFill": {
                    "cancel": "取消",
                    "fill": "填充所有单元格",
                    "fillHorizontal": "填充水平单元格",
                    "fillVertical": "填充垂直单元格"
                },
                "buttons": {
                    "collection": "集合",
                    "colvis": "列可见性",
                    "copy": "复制",
                    "copyTitle": "复制到剪贴板",
                    "csv": "CSV",
                    "excel": "Excel",
                    "pdf": "PDF",
                    "print": "打印"
                }
            },
            responsive: true,
            order: [[0, 'desc']],
            pageLength: 10,
            lengthMenu: [
                [10, 25, 50, 100, -1],
                ['10条', '25条', '50条', '100条', '全部']
            ],
            pagingType: "full_numbers"
        });
        
        // 初始化Select2提升下拉框体验
        $('#single_question_id').select2({
            placeholder: '请选择要添加的单选题',
            allowClear: true
        });
        
        $('#multiple_question_id').select2({
            placeholder: '请选择要添加的多选题',
            allowClear: true
        });
        
        // 删除按钮点击事件
        $(document).on('click', '.delete-btn', function() {
            const id = $(this).data('id');
            const type = $(this).data('type');
            
            // 隐藏所有其他确认框
            $('.confirm-delete-container').hide();
            
            // 显示当前确认框
            if (type === 'single') {
                $('.delete-confirm-single-' + id).slideDown(200);
            } else if (type === 'multiple') {
                $('.delete-confirm-multiple-' + id).slideDown(200);
            }
        });
        
        // 取消删除按钮点击事件
        $(document).on('click', '.cancel-delete-btn', function() {
            const id = $(this).data('id');
            const type = $(this).data('type');
            
            if (type === 'single') {
                $('.delete-confirm-single-' + id).slideUp(200);
            } else if (type === 'multiple') {
                $('.delete-confirm-multiple-' + id).slideUp(200);
            }
        });
        
        // 提交删除表单时禁用按钮防止重复提交
        $('.delete-form').on('submit', function() {
            $(this).find('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>处理中...');
        });
        
        // ESC键关闭所有确认框
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape') {
                $('.confirm-delete-container').slideUp(200);
            }
        });
        
        // 全选/取消全选单选题
        $('#selectAllSingle').on('change', function() {
            const isChecked = $(this).prop('checked');
            $('.single-question-checkbox').prop('checked', isChecked);
            updateSelectedCount('single');
        });
        
        // 全选/取消全选多选题
        $('#selectAllMultiple').on('change', function() {
            const isChecked = $(this).prop('checked');
            $('.multiple-question-checkbox').prop('checked', isChecked);
            updateSelectedCount('multiple');
        });
        
        // 全选/取消全选判断题
        $('#selectAllTrueFalse').on('change', function() {
            const isChecked = $(this).prop('checked');
            $('.truefalse-question-checkbox').prop('checked', isChecked);
            updateSelectedCount('truefalse');
        });
        
        // 全选/取消全选填空题
        $('#selectAllFillBlank').on('change', function() {
            const isChecked = $(this).prop('checked');
            $('.fillblank-question-checkbox').prop('checked', isChecked);
            updateSelectedCount('fillblank');
        });
        
        // 单选题复选框变化
        $(document).on('change', '.single-question-checkbox', function() {
            updateSelectedCount('single');
            
            // 如果所有复选框都被选中，则全选也应该被选中
            const totalCheckboxes = $('.single-question-checkbox').length;
            const checkedCheckboxes = $('.single-question-checkbox:checked').length;
            $('#selectAllSingle').prop('checked', totalCheckboxes === checkedCheckboxes);
        });
        
        // 多选题复选框变化
        $(document).on('change', '.multiple-question-checkbox', function() {
            updateSelectedCount('multiple');
            
            const totalCheckboxes = $('.multiple-question-checkbox').length;
            const checkedCheckboxes = $('.multiple-question-checkbox:checked').length;
            $('#selectAllMultiple').prop('checked', totalCheckboxes === checkedCheckboxes);
        });
        
        // 判断题复选框变化
        $(document).on('change', '.truefalse-question-checkbox', function() {
            updateSelectedCount('truefalse');
            
            const totalCheckboxes = $('.truefalse-question-checkbox').length;
            const checkedCheckboxes = $('.truefalse-question-checkbox:checked').length;
            $('#selectAllTrueFalse').prop('checked', totalCheckboxes === checkedCheckboxes);
        });
        
        // 填空题复选框变化
        $(document).on('change', '.fillblank-question-checkbox', function() {
            updateSelectedCount('fillblank');
            
            const totalCheckboxes = $('.fillblank-question-checkbox').length;
            const checkedCheckboxes = $('.fillblank-question-checkbox:checked').length;
            $('#selectAllFillBlank').prop('checked', totalCheckboxes === checkedCheckboxes);
        });
        
        // 更新选中题目数量
        function updateSelectedCount(type) {
            let selector, countElement, buttonElement;
            
            if (type === 'single') {
                selector = '.single-question-checkbox:checked';
                countElement = '#selectedSingleCount';
            } else if (type === 'multiple') {
                selector = '.multiple-question-checkbox:checked';
                countElement = '#selectedMultipleCount';
            } else if (type === 'truefalse') {
                selector = '.truefalse-question-checkbox:checked';
                countElement = '#selectedTrueFalseCount';
            } else if (type === 'fillblank') {
                selector = '.fillblank-question-checkbox:checked';
                countElement = '#selectedFillBlankCount';
            }
            
            const count = $(selector).length;
            $(countElement).text(count);
        }
        
        // 批量添加单选题
        $('#batchAddSingleBtn').on('click', function() {
            batchAddQuestions('single');
        });
        
        // 批量添加多选题
        $('#batchAddMultipleBtn').on('click', function() {
            batchAddQuestions('multiple');
        });
        
        // 批量添加判断题
        $('#batchAddTrueFalseBtn').on('click', function() {
            batchAddQuestions('truefalse');
        });
        
        // 批量添加填空题
        $('#batchAddFillBlankBtn').on('click', function() {
            batchAddQuestions('fillblank');
        });
        
        // 批量添加题目函数
        function batchAddQuestions(type) {
            let selector;
            
            if (type === 'single') {
                selector = '.single-question-checkbox:checked';
            } else if (type === 'multiple') {
                selector = '.multiple-question-checkbox:checked';
            } else if (type === 'truefalse') {
                selector = '.truefalse-question-checkbox:checked';
            } else if (type === 'fillblank') {
                selector = '.fillblank-question-checkbox:checked';
            }
            
            const selectedIds = [];
            $(selector).each(function() {
                selectedIds.push(parseInt($(this).val()));
            });
            
            if (selectedIds.length === 0) {
                Swal.fire({
                    icon: 'warning',
                    title: '请选择题目',
                    text: '您需要选择至少一道题目进行导入',
                    confirmButtonColor: '#3085d6'
                });
                return;
            }
            
            // 显示确认对话框
            Swal.fire({
                title: '批量导入题目',
                text: `确定要导入 ${selectedIds.length} 道题目到当前题库吗？`,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: '确定导入',
                cancelButtonText: '取消'
            }).then((result) => {
                if (result.isConfirmed) {
                    // 发送请求
                    $.ajax({
                        url: `/admin/question-banks/{{ bank.id }}/batch-add-questions`,
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({
                            question_type: type,
                            question_ids: selectedIds
                        }),
                        success: function(response) {
                            if (response.status === 'success') {
                                Swal.fire({
                                    title: '导入成功',
                                    html: `
                                        <p>${response.message}</p>
                                        <div class="mt-3">
                                            <button id="refreshPageBtn" class="btn btn-primary me-2">刷新页面</button>
                                            <button id="continueImportBtn" class="btn btn-outline-secondary">继续导入</button>
                                        </div>
                                    `,
                                    icon: 'success',
                                    showConfirmButton: false
                                });
                                
                                // 刷新页面按钮
                                $(document).on('click', '#refreshPageBtn', function() {
                                    window.location.reload();
                                });
                                
                                // 继续导入按钮
                                $(document).on('click', '#continueImportBtn', function() {
                                    Swal.close();
                                    // 取消选中所有复选框
                                    $('.select-all-checkbox').prop('checked', false);
                                    $(selector).prop('checked', false);
                                    updateSelectedCount(type);
                                });
                            } else {
                                Swal.fire({
                                    title: '导入失败',
                                    text: response.message || '导入题目时发生错误',
                                    icon: 'error'
                                });
                            }
                        },
                        error: function() {
                            Swal.fire({
                                title: '导入失败',
                                text: '服务器错误，请稍后重试',
                                icon: 'error'
                            });
                        }
                    });
                }
            });
        }
    });
</script>
{% endblock %} 
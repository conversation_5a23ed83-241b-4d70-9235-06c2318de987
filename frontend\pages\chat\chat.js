// pages/chat/chat.js
const app = getApp()

// 格式化时间
function formatTime(timestamp) {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date

  if (diff < 60000) { // 1分钟内
    return { text: '刚刚', showOutside: true }
  } else if (diff < 3600000) { // 1小时内
    return { text: Math.floor(diff / 60000) + '分钟前', showOutside: true }
  } else if (diff < 86400000) { // 24小时内
    const hours = date.getHours()
    const minutes = date.getMinutes()
    return { text: `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`, showOutside: true }
  } else {
    const month = date.getMonth() + 1
    const day = date.getDate()
    const hours = date.getHours()
    const minutes = date.getMinutes()
    return { text: `${month}月${day}日 ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`, showOutside: true }
  }
}

Page({
  data: {
    chatUserId: null,
    chatUser: {},
    currentUserId: null,
    currentUser: {},
    sessionId: null,
    messages: [],
    inputContent: '',
    hasContent: false,
    inputFocus: false,
    showActionSheet: false,
    loading: false,
    scrollTop: 0,
    scrollIntoView: '',
    page: 1,
    hasMore: true,
    lastMessageId: 0, // 记录最后一条消息的ID，用于轮询新消息
    pollingTimer: null, // 轮询定时器
    isPolling: false // 是否正在轮询
  },

  onLoad: function(options) {
    const chatUserId = options.userId
    const nickname = decodeURIComponent(options.nickname || '用户')
    const avatar = decodeURIComponent(options.avatar || '')

    if (!chatUserId) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    // 获取当前用户信息
    const userInfo = wx.getStorageSync('userInfo')
    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    this.setData({
      chatUserId: parseInt(chatUserId),
      currentUserId: userInfo.id,
      currentUser: userInfo,
      chatUser: {
        id: parseInt(chatUserId),
        nickname: nickname,
        avatar: avatar || '/images/cat.png'
      }
    })

    // 创建或获取聊天会话
    this.createChatSession()
  },

  onShow: function() {
    // 页面显示时聚焦输入框
    this.setData({ inputFocus: true })
    // 开始轮询新消息
    this.startPolling()
  },

  onHide: function() {
    // 页面隐藏时停止轮询
    this.stopPolling()
  },

  onUnload: function() {
    // 页面卸载时停止轮询
    this.stopPolling()
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack()
  },



  // 创建或获取聊天会话
  createChatSession: function() {
    const that = this
    wx.request({
      url: `${app.globalData.baseUrl}/cat_circle/chat/sessions`,
      method: 'POST',
      data: {
        user1_id: this.data.currentUserId,
        user2_id: this.data.chatUserId
      },
      success: function(res) {
        if (res.data.code === 200) {
          that.setData({
            sessionId: res.data.data.session.id
          })
          // 获取聊天消息
          that.loadMessages()
        } else {
          wx.showToast({
            title: res.data.message || '创建会话失败',
            icon: 'none'
          })
        }
      },
      fail: function(err) {
        console.error('创建会话失败:', err)
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
      }
    })
  },

  // 加载聊天消息
  loadMessages: function() {
    if (this.data.loading || !this.data.hasMore || !this.data.sessionId) return

    this.setData({ loading: true })

    const that = this
    wx.request({
      url: `${app.globalData.baseUrl}/cat_circle/chat/sessions/${this.data.sessionId}/messages`,
      method: 'GET',
      data: {
        user_id: this.data.currentUserId,
        page: this.data.page,
        per_page: 20
      },
      success: function(res) {
        if (res.data.code === 200) {
          const newMessages = res.data.data.messages || []
          const pagination = res.data.data.pagination || {}

          // 为每条消息添加格式化的时间信息
          const formattedMessages = newMessages.map(message => {
            const timeInfo = formatTime(new Date(message.created_at))
            return {
              ...message,
              timeDisplay: timeInfo.text,
              showTimeOutside: timeInfo.showOutside
            }
          })

          const allMessages = that.data.page === 1 ? formattedMessages : [...formattedMessages, ...that.data.messages]

          // 记录最后一条消息的ID
          const lastMessageId = allMessages.length > 0 ? Math.max(...allMessages.map(msg => msg.id)) : 0

          that.setData({
            messages: allMessages,
            loading: false,
            hasMore: pagination.has_next || false,
            page: that.data.page + 1,
            lastMessageId: lastMessageId
          })

          if (that.data.page === 2) { // 首次加载
            that.scrollToBottom()
            // 首次加载完成后开始轮询
            that.startPolling()
          }
        } else {
          that.setData({ loading: false })
          wx.showToast({
            title: res.data.message || '加载消息失败',
            icon: 'none'
          })
        }
      },
      fail: function(err) {
        console.error('加载消息失败:', err)
        that.setData({ loading: false })
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
      }
    })
  },

  // 输入内容
  onInput: function(e) {
    const value = e.detail.value
    const hasContent = value.trim().length > 0
    console.log('输入内容:', value, '是否有内容:', hasContent)
    this.setData({
      inputContent: value,
      hasContent: hasContent
    })
  },

  // 发送消息
  sendMessage: function() {
    const content = this.data.inputContent.trim()
    if (!content) {
      wx.showToast({
        title: '请输入消息内容',
        icon: 'none'
      })
      return
    }

    if (!this.data.sessionId) {
      wx.showToast({
        title: '会话未建立',
        icon: 'none'
      })
      return
    }

    // 先添加到本地显示
    const now = new Date()
    const timeInfo = formatTime(now)
    const tempMessage = {
      id: Date.now(),
      sender_id: this.data.currentUserId,
      receiver_id: this.data.chatUserId,
      content: content,
      created_at: now.toISOString(),
      timeDisplay: timeInfo.text,
      showTimeOutside: timeInfo.showOutside,
      message_type: 'text',
      sender_info: this.data.currentUser
    }

    this.setData({
      messages: [...this.data.messages, tempMessage],
      inputContent: '',
      hasContent: false
    })

    this.scrollToBottom()

    // 发送到服务器
    const that = this
    wx.request({
      url: `${app.globalData.baseUrl}/cat_circle/chat/messages`,
      method: 'POST',
      data: {
        session_id: this.data.sessionId,
        sender_id: this.data.currentUserId,
        receiver_id: this.data.chatUserId,
        content: content,
        message_type: 'text'
      },
      success: function(res) {
        if (res.data.code === 200) {
          // 更新消息ID并重新格式化时间
          const messages = that.data.messages
          const lastIndex = messages.length - 1
          if (lastIndex >= 0 && messages[lastIndex].id === tempMessage.id) {
            const serverMessage = res.data.data.message
            const timeInfo = formatTime(new Date(serverMessage.created_at))
            messages[lastIndex] = {
              ...serverMessage,
              timeDisplay: timeInfo.text,
              showTimeOutside: timeInfo.showOutside
            }
            that.setData({
              messages: messages,
              lastMessageId: serverMessage.id // 更新最后一条消息ID
            })
          }
        } else {
          wx.showToast({
            title: res.data.message || '发送失败',
            icon: 'none'
          })
        }
      },
      fail: function(err) {
        console.error('发送消息失败:', err)
        wx.showToast({
          title: '发送失败',
          icon: 'none'
        })
      }
    })
  },

  // 滚动到底部
  scrollToBottom: function() {
    const lastMessageId = this.data.messages[this.data.messages.length - 1]?.id
    if (lastMessageId) {
      this.setData({
        scrollIntoView: `msg-${lastMessageId}`
      })
    }
  },

  // 加载更多消息
  loadMore: function() {
    this.loadMessages()
  },

  // 显示功能弹窗
  showActionSheet: function() {
    this.setData({
      showActionSheet: true
    })
  },

  // 隐藏功能弹窗
  hideActionSheet: function() {
    this.setData({
      showActionSheet: false
    })
  },

  // 选择图片
  chooseImage: function() {
    const that = this
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: function(res) {
        const tempFilePath = res.tempFiles[0].tempFilePath

        that.setData({
          showActionSheet: false
        })

        // 显示上传中
        wx.showLoading({
          title: '上传中...'
        })

        // 上传图片到服务器
        wx.uploadFile({
          url: `${app.globalData.baseUrl}/cat_circle/chat/upload_image`,
          filePath: tempFilePath,
          name: 'image',
          success: function(uploadRes) {
            wx.hideLoading()

            try {
              const data = JSON.parse(uploadRes.data)
              if (data.code === 200) {
                // 发送图片消息
                that.sendImageMessage(data.data.image_url)
              } else {
                wx.showToast({
                  title: data.message || '上传失败',
                  icon: 'none'
                })
              }
            } catch (e) {
              wx.showToast({
                title: '上传失败',
                icon: 'none'
              })
            }
          },
          fail: function(err) {
            wx.hideLoading()
            console.error('上传图片失败:', err)
            wx.showToast({
              title: '上传失败',
              icon: 'none'
            })
          }
        })
      },
      fail: function(err) {
        console.log('选择图片失败:', err)
      }
    })
  },

  // 发送图片消息
  sendImageMessage: function(imageUrl) {
    if (!this.data.sessionId) {
      wx.showToast({
        title: '会话未建立',
        icon: 'none'
      })
      return
    }

    // 先添加到本地显示
    const now = new Date()
    const timeInfo = formatTime(now)
    const tempMessage = {
      id: Date.now(),
      sender_id: this.data.currentUserId,
      receiver_id: this.data.chatUserId,
      content: imageUrl,
      created_at: now.toISOString(),
      timeDisplay: timeInfo.text,
      showTimeOutside: timeInfo.showOutside,
      message_type: 'image',
      sender_info: this.data.currentUser
    }

    this.setData({
      messages: [...this.data.messages, tempMessage]
    })

    this.scrollToBottom()

    // 发送到服务器
    const that = this
    wx.request({
      url: `${app.globalData.baseUrl}/cat_circle/chat/messages`,
      method: 'POST',
      data: {
        session_id: this.data.sessionId,
        sender_id: this.data.currentUserId,
        receiver_id: this.data.chatUserId,
        content: imageUrl,
        message_type: 'image'
      },
      success: function(res) {
        if (res.data.code === 200) {
          // 更新消息ID并重新格式化时间
          const messages = that.data.messages
          const lastIndex = messages.length - 1
          if (lastIndex >= 0 && messages[lastIndex].id === tempMessage.id) {
            const serverMessage = res.data.data.message
            const timeInfo = formatTime(new Date(serverMessage.created_at))
            messages[lastIndex] = {
              ...serverMessage,
              timeDisplay: timeInfo.text,
              showTimeOutside: timeInfo.showOutside
            }
            that.setData({
              messages: messages,
              lastMessageId: serverMessage.id // 更新最后一条消息ID
            })
          }
        } else {
          wx.showToast({
            title: res.data.message || '发送失败',
            icon: 'none'
          })
        }
      },
      fail: function(err) {
        console.error('发送图片消息失败:', err)
        wx.showToast({
          title: '发送失败',
          icon: 'none'
        })
      }
    })
  },

  // 选择表情包
  chooseEmoji: function() {
    this.setData({
      showActionSheet: false
    })

    // 跳转到表情包选择页面
    wx.navigateTo({
      url: `/pages/emoji/emoji?sessionId=${this.data.sessionId}&receiverId=${this.data.chatUserId}`
    })
  },

  // 选择定位
  chooseLocation: function() {
    const that = this
    this.setData({
      showActionSheet: false
    })

    wx.chooseLocation({
      success: function(res) {
        console.log('选择的位置:', res)

        // 发送定位消息
        that.sendLocationMessage({
          latitude: res.latitude,
          longitude: res.longitude,
          name: res.name,
          address: res.address
        })
      },
      fail: function(err) {
        console.error('选择位置失败:', err)
        if (err.errMsg.includes('auth deny')) {
          wx.showModal({
            title: '提示',
            content: '需要获取您的位置权限才能发送定位，请在设置中开启位置权限',
            showCancel: false
          })
        } else {
          wx.showToast({
            title: '获取位置失败',
            icon: 'none'
          })
        }
      }
    })
  },

  // 发送定位消息
  sendLocationMessage: function(locationData) {
    if (!this.data.sessionId) {
      wx.showToast({
        title: '会话未建立',
        icon: 'none'
      })
      return
    }

    const that = this
    const messageData = {
      session_id: this.data.sessionId,
      sender_id: this.data.currentUserId,
      receiver_id: this.data.chatUserId,
      latitude: locationData.latitude,
      longitude: locationData.longitude,
      location_name: locationData.name,
      address: locationData.address
    }

    console.log('发送定位消息:', messageData)

    wx.request({
      url: `${app.globalData.baseUrl}/cat_circle/chat/location/send`,
      method: 'POST',
      header: {
        'Content-Type': 'application/json'
      },
      data: messageData,
      success: function(res) {
        console.log('定位消息发送响应:', res.data)
        if (res.data.code === 200) {
          // 添加消息到本地显示
          const messageInfo = res.data.data.message
          const newMessage = {
            id: messageInfo.id,
            session_id: that.data.sessionId,
            sender_id: that.data.currentUserId,
            receiver_id: that.data.chatUserId,
            message_type: 'location',
            content: messageInfo.content,
            extra_data: messageInfo.extra_data,
            created_at: messageInfo.created_at,
            timeDisplay: '刚刚',
            showTimeOutside: false
          }

          const updatedMessages = [...that.data.messages, newMessage]
          that.setData({
            messages: updatedMessages,
            lastMessageId: newMessage.id
          })

          // 滚动到底部
          that.scrollToBottom()
        } else {
          wx.showToast({
            title: res.data.message || '发送失败',
            icon: 'none'
          })
        }
      },
      fail: function(err) {
        console.error('发送定位消息失败:', err)
        wx.showToast({
          title: '发送失败',
          icon: 'none'
        })
      }
    })
  },

  // 打开位置
  openLocation: function(e) {
    const { latitude, longitude, name, address } = e.currentTarget.dataset

    wx.openLocation({
      latitude: parseFloat(latitude),
      longitude: parseFloat(longitude),
      name: name || '位置信息',
      address: address || '详细地址',
      scale: 18
    })
  },



  // 加载更多消息
  loadMore: function() {
    this.loadMessages()
  },

  // 开始轮询新消息
  startPolling: function() {
    if (this.data.isPolling || !this.data.sessionId) {
      console.log('轮询未启动:', { isPolling: this.data.isPolling, sessionId: this.data.sessionId })
      return
    }

    console.log('开始轮询新消息, sessionId:', this.data.sessionId, 'lastMessageId:', this.data.lastMessageId)
    this.setData({ isPolling: true })

    const that = this
    this.data.pollingTimer = setInterval(() => {
      that.checkNewMessages()
    }, 2000) // 每2秒检查一次新消息
  },

  // 停止轮询
  stopPolling: function() {
    if (this.data.pollingTimer) {
      clearInterval(this.data.pollingTimer)
      this.setData({
        pollingTimer: null,
        isPolling: false
      })
    }
  },

  // 检查新消息
  checkNewMessages: function() {
    if (!this.data.sessionId || !this.data.isPolling) {
      console.log('checkNewMessages: 条件不满足', { sessionId: this.data.sessionId, isPolling: this.data.isPolling })
      return
    }

    console.log('检查新消息:', { sessionId: this.data.sessionId, lastMessageId: this.data.lastMessageId, currentUserId: this.data.currentUserId })

    const that = this
    wx.request({
      url: `${app.globalData.baseUrl}/cat_circle/chat/sessions/${this.data.sessionId}/new_messages`,
      method: 'GET',
      data: {
        user_id: this.data.currentUserId,
        last_message_id: this.data.lastMessageId
      },
      success: function(res) {
        console.log('轮询响应:', res.data)
        // 修正：后端返回的是 res.data.data.messages 数组
        if (res.data.code === 200 && res.data.data && res.data.data.messages && res.data.data.messages.length > 0) {
          const newMessages = res.data.data.messages

          console.log('收到新消息:', newMessages.length, '条')

          // 为新消息添加格式化的时间信息
          const formattedNewMessages = newMessages.map(message => {
            const timeInfo = formatTime(new Date(message.created_at))
            return {
              ...message,
              timeDisplay: timeInfo.text,
              showTimeOutside: timeInfo.showOutside
            }
          })

          // 添加新消息到现有消息列表
          const updatedMessages = [...that.data.messages, ...formattedNewMessages]
          const newLastMessageId = Math.max(...newMessages.map(msg => msg.id))

          that.setData({
            messages: updatedMessages,
            lastMessageId: newLastMessageId
          })

          // 滚动到底部显示新消息
          that.scrollToBottom()
        }


      },
      fail: function(err) {
        console.error('检查新消息失败:', err)
      }
    })
  },




})

<!--pages/profile/profile.wxml-->
<view class="container">
  <!-- 全局通知组件 -->
  <notification-toast
    show="{{showGlobalNotification}}"
    title="{{globalNotificationTitle}}"
    message="{{globalNotificationMessage}}"
    postId="{{globalNotificationPostId}}"
    bind:hide="onGlobalNotificationHide"
    bind:tap="onGlobalNotificationTap">
  </notification-toast>

  <!-- 用户信息卡片 -->
  <view class="user-card">
    <view class="avatar-container">
      <image class="avatar" src="{{userInfo.avatar || '/images/default-avatar.png'}}"></image>
    </view>
    <view class="user-info">
      <text class="username">{{userInfo.nickname || '同学'}}</text>
      <text class="user-desc">最近活跃: {{statistics.lastActive}}</text>
      <view class="user-streak">
        <text class="streak-text">连续学习 {{statistics.streak}} 天</text>
        <view class="streak-bar">
          <view class="streak-progress" style="width: {{statistics.streak * 20}}%;"></view>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 学习统计区域 -->
  <view class="stats-card">
    <view class="stats-title">学习成就</view>
    <view class="stats-grid">
      <view class="stats-item">
        <text class="stats-value">{{statistics.totalQuestions}}</text>
        <text class="stats-label">总题数</text>
      </view>
      <view class="stats-item">
        <text class="stats-value">{{statistics.correctCount}}</text>
        <text class="stats-label">答对题数</text>
      </view>
      <view class="stats-item">
        <text class="stats-value">{{statistics.wrongCount}}</text>
        <text class="stats-label">答错题数</text>
      </view>
      <view class="stats-item">
        <text class="stats-value">{{statistics.accuracy}}</text>
        <text class="stats-label">正确率</text>
      </view>
    </view>
  </view>
  
  <!-- 功能列表 -->
  <view class="function-list">
    <block wx:for="{{functionList}}" wx:key="id">
      <view class="function-item" bindtap="navigateTo" data-url="{{item.url}}">
        <view class="function-icon">
          <text class="icon-text">{{item.icon}}</text>
        </view>
        <view class="function-info">
          <text class="function-name">{{item.name}}</text>
          <text class="function-desc">{{item.description}}</text>
        </view>
        <text class="function-arrow">></text>
      </view>
    </block>
  </view>
  
  <!-- 退出登录按钮 -->
  <view class="logout-container">
    <button class="logout-button" bindtap="logout">退出登录</button>
  </view>
  
  <!-- 加载提示 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading"></view>
    <text class="loading-text">加载中...</text>
  </view>
  
  <!-- 版本信息 -->
  <view class="version-info">
    <text class="version-text">大数据答题小程序 v1.0.0</text>
  </view>
</view>
{% extends "admin/base.html" %}

{% block title %}编辑单选题 - 大数据题库管理后台{% endblock %}

{% block header %}编辑单选题{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <form method="post" action="/admin/questions/edit-single/{{ question.id }}">
                    <div class="mb-3">
                        <label for="question" class="form-label">题目文本</label>
                        <textarea class="form-control" id="question" name="question" rows="3" required>{{ question.question }}</textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">选项</label>
                        <div id="options-container">
                            {% for option in question.options %}
                            <div class="input-group mb-2">
                                <div class="input-group-text">
                                    <input class="form-check-input mt-0" type="radio" name="answer" value="{{ loop.index0 }}" {% if question.answer == loop.index0 %}checked{% endif %}>
                                </div>
                                <input type="text" class="form-control" name="options[]" placeholder="选项 {{ chr(65 + loop.index0) }}" value="{{ option }}" required>
                                <button type="button" class="btn btn-outline-danger remove-option" {% if question.options|length <= 2 %}disabled{% endif %}>删除</button>
                            </div>
                            {% endfor %}
                        </div>
                        <button type="button" class="btn btn-outline-primary" id="add-option">添加选项</button>
                    </div>
                    
                    <div class="mb-3">
                        <label for="category" class="form-label">分类</label>
                        <input type="text" class="form-control" id="category" name="category" value="{{ question.category }}">
                    </div>
                    
                    <div class="mb-3">
                        <label for="difficulty" class="form-label">难度</label>
                        <select class="form-select" id="difficulty" name="difficulty">
                            <option value="1" {% if question.difficulty == 1 %}selected{% endif %}>简单</option>
                            <option value="2" {% if question.difficulty == 2 %}selected{% endif %}>中等</option>
                            <option value="3" {% if question.difficulty == 3 %}selected{% endif %}>困难</option>
                        </select>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="classSelect" class="form-label">班级</label>
                            <select class="form-select" id="classSelect">
                                <option value="">选择班级</option>
                                {% for class_item in classes %}
                                <option value="{{ class_item.id }}" {% if selected_class_id == class_item.id %}selected{% endif %}>{{ class_item.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="courseSelect" class="form-label">课程</label>
                            <select class="form-select" id="courseSelect" name="course_id">
                                <option value="">选择课程</option>
                                {% for course in courses %}
                                <option value="{{ course.id }}" {% if selected_course_id == course.id %}selected{% endif %}>{{ course.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    
                    <div class="text-end">
                        <a href="/admin/questions" class="btn btn-secondary">取消</a>
                        <button type="submit" class="btn btn-primary">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    let optionCount = {{ question.options|length }}; // 已有选项数量
    
    // 添加选项
    document.getElementById('add-option').addEventListener('click', function() {
        const container = document.getElementById('options-container');
        const newOption = document.createElement('div');
        newOption.className = 'input-group mb-2';
        newOption.innerHTML = `
            <div class="input-group-text">
                <input class="form-check-input mt-0" type="radio" name="answer" value="${optionCount}">
            </div>
            <input type="text" class="form-control" name="options[]" placeholder="选项 ${String.fromCharCode(65 + optionCount)}" required>
            <button type="button" class="btn btn-outline-danger remove-option">删除</button>
        `;
        container.appendChild(newOption);
        optionCount++;
        
        // 启用删除按钮
        if (optionCount > 2) {
            document.querySelectorAll('.remove-option').forEach(btn => {
                btn.disabled = false;
            });
        }
    });
    
    // 删除选项
    document.addEventListener('click', function(e) {
        if (e.target && e.target.classList.contains('remove-option') && !e.target.disabled) {
            e.target.closest('.input-group').remove();
            optionCount--;
            
            // 重新排序选项和单选按钮的值
            const options = document.querySelectorAll('[name="options[]"]');
            const radios = document.querySelectorAll('[name="answer"]');
            
            options.forEach((opt, idx) => {
                opt.placeholder = `选项 ${String.fromCharCode(65 + idx)}`;
            });
            
            radios.forEach((radio, idx) => {
                radio.value = idx;
            });
            
            // 如果只剩下2个选项，禁用删除按钮
            if (optionCount <= 2) {
                document.querySelectorAll('.remove-option').forEach(btn => {
                    btn.disabled = true;
                });
            }
        }
    });
    
    // 班级选择变更时获取对应课程
    document.getElementById('classSelect').addEventListener('change', function() {
        const classId = this.value;
        const courseSelect = document.getElementById('courseSelect');
        
        // 清空课程选择框
        courseSelect.innerHTML = '<option value="">选择课程</option>';
        
        if (classId) {
            // 获取该班级下的课程
            fetch(`/admin/courses/search?class_id=${classId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.courses && data.courses.length > 0) {
                        data.courses.forEach(course => {
                            const option = document.createElement('option');
                            option.value = course.id;
                            option.textContent = course.name;
                            courseSelect.appendChild(option);
                        });
                    }
                })
                .catch(error => console.error('获取课程失败:', error));
        }
    });
</script>
{% endblock %} 
/* exam.wxss */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f7fa;
  position: relative;
}

/* 头部区域 */
.header-area {
  position: relative;
  width: 100%;
  z-index: 10;
}

.status-bar-placeholder {
  width: 100%;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.exam-header {
  position: relative;
  z-index: 10;
  width: 100%;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 30rpx;
  color: #fff;
}

.empty-space {
  width: 30px; /* 与timer宽度对应，保持标题居中 */
  height: 30px;
}

.back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
}

.back-icon {
  font-size: 40rpx;
  font-weight: bold;
  color: #fff;
}

.exam-title {
  font-size: 34rpx;
  font-weight: 500;
}

.timer {
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  padding: 5px 10px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 15px;
}

/* 答题用时样式 */
.time-used {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16rpx 0;
  margin: 20rpx 0;
  background-color: #f0f7ff;
  border-radius: 12rpx;
}

.time-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 15rpx;
}

.time-value {
  font-size: 30rpx;
  font-weight: 600;
  color: #2b85e4;
}

/* 进度条容器 */
.progress-container {
  width: 100%;
  background-color: rgba(0, 0, 0, 0.05);
}

/* 进度条 */
.progress-bar {
  display: flex;
  height: 8px;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.3);
  overflow: hidden;
  border-radius: 4px;
  margin: 10rpx 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) inset;
}

/* 各题型段落 */
.progress-segment {
  height: 100%;
  position: relative;
  overflow: hidden;
  transition: width 0.3s ease;
}

/* 进度内容 */
.progress-inner {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.7);
  transition: width 0.3s ease;
}

/* 题目进度信息 */
.question-progress {
  padding: 15rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #fff;
  font-size: 24rpx;
  background-color: rgba(0, 0, 0, 0.1);
}

/* 题型指示器容器 */
.question-types {
  display: flex;
  flex-wrap: wrap;
}

/* 题型指示器 */
.type-indicator {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
  margin-bottom: 6rpx;
}

/* 题型圆点 */
.type-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 8rpx;
}

/* 单选题圆点 */
.single-dot {
  background-color: #4e8df7;
}

/* 多选题圆点 */
.multiple-dot {
  background-color: #7e57c2;
}

/* 判断题圆点 */
.judgment-dot {
  background-color: #ff9800;
}

/* 填空题圆点 */
.fillblank-dot {
  background-color: #4caf50;
}

/* 总体进度文本 */
.overall-progress {
  font-weight: 500;
}

/* 加载中 */
.loading-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 15px;
  font-size: 14px;
  color: #666;
}

/* 题目区域 */
.question-area {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
}

.question-container {
  background-color: #fff;
  border-radius: 10px;
  padding: 15px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.question-type {
  margin-bottom: 15px;
}

.type-label {
  display: inline-block;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 14px;
  font-weight: bold;
  color: #fff;
}

.single {
  background-color: #4e8df7;
}

.multiple {
  background-color: #7e57c2;
}

.judgment {
  background-color: #ff9800;
}

.fillblank {
  background-color: #4caf50;
}

.question-content {
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 20px;
  color: #333;
}

.debug-info {
  color: #ff5252;
  font-weight: bold;
}

.debug-box {
  margin-top: 10px;
  padding: 10px;
  background-color: #fff3f3;
  border: 1px solid #ff5252;
  border-radius: 5px;
  font-size: 12px;
}

/* 选项样式 */
.options-container {
  margin-top: 15px;
}

/* 基础选项样式 */
.option {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  margin-bottom: 10px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  background-color: #f9f9f9;
  transition: all 0.3s ease;
  position: relative;
}

/* 基础前缀样式 - 未选中状态 */
.option-prefix {
  width: 28px;
  height: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background-color: #e0e0e0;
  color: #666;
  font-weight: bold;
  margin-right: 10px;
}

.option-content {
  flex: 1;
  font-size: 15px;
  color: #333;
}

/* 选中状态样式 - 简化版本 */
.option.selected {
  background-color: #e6f0ff;
  border: 2px solid #4e8df7;
  box-shadow: 0 2px 6px rgba(78, 141, 247, 0.15);
  padding: 11px 15px; /* 补偿边框增加的尺寸 */
}

/* 选中时的前缀样式 */
.option.selected .option-prefix {
  background-color: #4e8df7;
  color: #fff;
}

/* 选中标记样式 - 完全重写 */
.selected-mark {
  display: none;
}

.selected-mark-visible {
  display: block;
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #4e8df7;
  /* 勾选图标 */
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z'/%3E%3C/svg%3E");
  background-size: 12px;
  background-position: center;
  background-repeat: no-repeat;
  /* 添加阴影使其更明显 */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* 判断题样式 */
.judgment-option {
  justify-content: center;
}

/* 填空题样式 */
.fillblank-input-container {
  width: 100%;
  padding: 10px 0;
}

.fillblank-input {
  width: 100%;
  height: 40px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 0 15px;
  font-size: 15px;
  background-color: #fff;
}

/* 答题导航栏 */
.navigation-bar {
  display: flex;
  justify-content: space-between;
  padding: 15px;
  background-color: #fff;
  border-top: 1px solid #e0e0e0;
}

.nav-btn {
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 15px;
  text-align: center;
}

.prev {
  background-color: #f5f5f5;
  color: #666;
}

.next {
  background-color: #4e8df7;
  color: #fff;
}

.submit {
  background-color: #4caf50;
  color: #fff;
}

/* 题目导航 */
.question-nav {
  padding: 15px;
  background-color: #fff;
  border-top: 1px solid #eee;
}

.nav-header {
  margin-bottom: 10px;
  font-size: 14px;
  color: #666;
}

.nav-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.nav-item {
  width: 36px;
  height: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  border: 1px solid #e0e0e0;
  font-size: 14px;
  color: #666;
  background-color: #f9f9f9;
}

.nav-item.current {
  background-color: #4e8df7;
  color: #fff;
  border-color: #4e8df7;
}

.nav-item.answered {
  background-color: rgba(76, 175, 80, 0.1);
  border-color: #4caf50;
  color: #4caf50;
}

.nav-item.current.answered {
  background-color: #4caf50;
  border-color: #4caf50;
  color: #fff;
}

/* 成绩展示 */
.result-container {
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: fadeIn 0.5s ease;
}

.result-card {
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  width: 100%;
  padding: 24rpx;
  box-sizing: border-box;
}

/* 科目信息样式 */
.course-info {
  display: flex;
  align-items: center;
  padding-bottom: 24rpx;
  margin-bottom: 24rpx;
  border-bottom: 1px solid #f0f0f0;
}

.course-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.course-avatar text {
  color: white;
  font-size: 40rpx;
  font-weight: 600;
}

.course-details {
  display: flex;
  flex-direction: column;
}

.course-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.course-score {
  font-size: 26rpx;
  color: #666;
}

/* 分数显示 */
.score-display {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  margin: 30rpx 0;
}

.big-score {
  font-size: 100rpx;
  font-weight: 700;
  color: #2b85e4;
  line-height: 1;
}

.score-unit {
  font-size: 36rpx;
  color: #666;
  margin-bottom: 16rpx;
  margin-left: 8rpx;
}

/* 统计行 */
.stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  padding: 20rpx;
}

.stat-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-label {
  font-size: 24rpx;
  color: #888;
  margin-bottom: 8rpx;
}

.stat-value {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

/* 按钮 */
.action-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
}

.review-btn, .return-btn {
  min-width: 45%;
  padding: 14rpx 0;
  font-size: 28rpx;
  border-radius: 40rpx;
  border: none;
}

.review-btn {
  background-color: #ff9800;
  color: white;
}

.return-btn {
  background-color: #2b85e4;
  color: white;
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20rpx); }
  to { opacity: 1; transform: translateY(0); }
}

/* 退出确认对话框 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(2px);
}

.modal-content {
  position: relative;
  width: 75%;
  max-width: 300px;
  background-color: #fff;
  border-radius: 16px;
  padding: 20px 18px;
  z-index: 1001;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.modal-header {
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 16px;
  color: #333;
}

.modal-body {
  font-size: 15px;
  color: #555;
  text-align: center;
  margin-bottom: 20px;
  line-height: 1.5;
}

.modal-footer {
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

.modal-btn {
  flex: 1;
  padding: 3px 0;
  border-radius: 20px;
  font-size: 15px;
  font-weight: 500;
  text-align: center;
  border: none;
  transition: all 0.2s;
  max-width: 110px;
  min-width: 80px;
}

.modal-btn:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.cancel {
  background-color: #f0f0f0;
  color: #666;
  border: 1px solid #ddd;
}

.confirm {
  background-color: #ff5252;
  color: #fff;
  box-shadow: 0 4px 8px rgba(255, 82, 82, 0.25);
}

/* 多选题只使用单选题的样式类 */
.multi-header {
  font-size: 14px;
  color: #4e8df7;
  background-color: #ecf5ff;
  text-align: center;
  padding: 8px;
  border-radius: 6px;
  margin-bottom: 12px;
  font-weight: bold;
}

/* 选中样式已经在.option.selected中定义，这里不需要重复定义 */

/* 多选题的选中样式 */
.check-mark {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #4e8df7;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  font-size: 14px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.multi-check-mark {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #4e8df7;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  font-size: 14px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 2;
}

/* 多选题新样式 */
.multi-option {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  margin-bottom: 10px;
  border-radius: 8px;
  background-color: #f5f5f5;
  position: relative;
  border: 2px solid #e0e0e0;
}

.multi-letter {
  width: 28px;
  height: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background-color: #e0e0e0;
  color: #666;
  font-weight: bold;
  margin-right: 10px;
}

.multi-content {
  flex: 1;
  font-size: 15px;
  color: #333;
}

.multi-mark {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #4e8df7;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  font-size: 16px;
}

/* 多选题选中样式 */
.multi-selected {
  background-color: #e1f0ff;
  border: 2px solid #4e8df7;
}

.multi-selected .multi-letter {
  background-color: #4e8df7;
  color: white;
}

/* 题型分值说明 */
.score-rules {
  margin: 20rpx 0 30rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  padding: 16rpx 20rpx;
}

.rules-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 12rpx;
  text-align: center;
  font-weight: 500;
}

.rules-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.rule-item {
  width: 48%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8rpx 0;
}

.rule-label {
  font-size: 26rpx;
  color: #666;
}

.rule-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

/* 答题情况样式 */
.answer-status {
  display: flex;
  justify-content: space-around;
  margin: 15rpx 0 25rpx;
  padding: 15rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
}

.answer-status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10rpx 30rpx;
}

.status-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.status-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
} 
from flask import Blueprint, jsonify, request
from app import db, Class, User, token_required

# 创建蓝图
class_api = Blueprint('class_api', __name__)

# 获取所有班级列表
@class_api.route('/classes', methods=['GET'])
@token_required
def get_classes(user_id):
    """获取所有可选班级"""
    try:
        classes = Class.query.all()
        return jsonify({
            'status': 'success',
            'classes': [cls.to_dict() for cls in classes]
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# 更新用户所属班级
@class_api.route('/classes/select', methods=['POST'])
@token_required
def select_class(user_id):
    """设置用户所属班级"""
    try:
        data = request.get_json()
        
        if not data or 'class_id' not in data:
            return jsonify({'error': 'Missing class_id parameter'}), 400
        
        class_id = data.get('class_id')
        
        # 检查班级是否存在
        selected_class = Class.query.get(class_id)
        if not selected_class:
            return jsonify({'error': f'Class with ID {class_id} not found'}), 404
        
        # 获取用户并更新班级
        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        user.class_id = class_id
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': f'User {user_id} assigned to class {class_id}',
            'user': user.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# 获取用户当前班级信息
@class_api.route('/classes/current', methods=['GET'])
@token_required
def get_current_class(user_id):
    """获取用户当前班级信息"""
    try:
        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        if not user.class_id:
            return jsonify({
                'status': 'success',
                'message': 'User has no class assigned',
                'has_class': False
            })
        
        user_class = Class.query.get(user.class_id)
        if not user_class:
            return jsonify({
                'status': 'success',
                'message': 'Class not found',
                'has_class': False
            })
        
        return jsonify({
            'status': 'success',
            'has_class': True,
            'class': user_class.to_dict()
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# 管理员接口：创建测试班级
@class_api.route('/admin/create-test-classes', methods=['GET'])
def create_test_classes():
    """创建测试班级数据（仅用于测试）"""
    try:
        # 检查是否已有班级数据
        existing_classes = Class.query.all()
        if existing_classes:
            return jsonify({
                'status': 'info',
                'message': f'已存在 {len(existing_classes)} 个班级，无需创建测试数据',
                'classes': [cls.to_dict() for cls in existing_classes]
            })
        
        # 创建测试班级
        test_classes = [
            Class(name='计算机科学与技术', description='计算机科学与技术专业班级'),
            Class(name='软件工程', description='软件工程专业班级'),
            Class(name='数据科学', description='数据科学与大数据技术专业班级'),
            Class(name='人工智能', description='人工智能专业班级'),
            Class(name='网络工程', description='网络工程专业班级')
        ]
        
        for cls in test_classes:
            db.session.add(cls)
        
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': f'成功创建 {len(test_classes)} 个测试班级',
            'classes': [cls.to_dict() for cls in test_classes]
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500 
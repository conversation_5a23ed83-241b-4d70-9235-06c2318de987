// app.js
App({
  globalData: {
    userInfo: null,
    token: '',
    // baseUrl: 'https://www.qiangs.xyz/api',
    baseUrl: 'http://localhost:5000/api',
    heartbeatTimer: null,
    apiBaseUrl: 'http://localhost:5000',  // 开发模式下的本地地址
    // 根据需要在发布时将其改为生产环境地址
    // apiBaseUrl: 'https://yourserver.com',
    hasUserInfo: false,
    canIUse: wx.canIUse('button.open-type.getUserInfo'),
    questions: [],
    currentQuestionIndex: 0,
    lastActiveTime: Date.now(), // 添加最后活跃时间记录
    catVisible: true, // 控制小猫是否可见
    needRefreshCatFriends: false, // 控制从详情页返回时是否刷新猫友圈列表
    // 全局通知系统
    notificationSystem: {
      isPolling: false,
      pollingTimer: null,
      lastNotificationId: 0,
      currentPage: null,
      showNotification: false,
      notificationData: null,
      shownNotifications: null // 将在初始化时从本地存储加载
    }
  },
  
  onLaunch: function() {
    console.log('小程序启动');

    // 初始化已显示通知记录
    this.initShownNotifications();

    // 初始化云开发环境 - 暂时禁用以避免access_token错误
    // if (wx.cloud) {
    //   wx.cloud.init({
    //     env: 'cloud1-0gelsccsb08cd3a0', // 替换为您的云开发环境ID
    //     traceUser: true
    //   });
    //   console.log('云开发环境初始化成功');
    // } else {
    //   console.error('请使用 2.2.3 或以上的基础库以使用云能力');
    // }

    // 先测试API连通性
    this.testApiConnection();

    // 初始化小猫组件
    this.initCatComponent();
    
    // 清除失效的token和过期的用户信息，但保留openid
    try {
      // 获取本地存储的用户信息和令牌
      const token = wx.getStorageSync('token');
      const userInfo = wx.getStorageSync('userInfo');
      
      console.log('本地存储的token:', token);
      console.log('本地存储的userInfo:', userInfo);
      
      if (token && userInfo) {
        // 确保token是字符串
        this.globalData.token = String(token);
        this.globalData.userInfo = userInfo;
        this.globalData.hasUserInfo = true;
        console.log('已从本地存储加载token和用户信息');
        
        // 如果有登录信息，自动启动心跳包
        this.startHeartbeat();
      } else {
        console.log('本地没有保存token或用户信息');
        // 清除可能存在的不一致状态，但保留openid
        this.globalData.token = '';
        this.globalData.userInfo = null;
        wx.removeStorageSync('token');
        wx.removeStorageSync('userInfo');
      }
    } catch(e) {
      console.error('读取存储数据出错:', e);
      this.globalData.token = '';
      this.globalData.userInfo = null;
      
      // 清除登录数据但保留openid
      wx.removeStorageSync('token');
      wx.removeStorageSync('userInfo');
    }
  },
  
  onShow: function() {
    console.log('小程序进入前台');
    // 如果用户登录了，恢复在线状态
    if (this.checkLogin()) {
      // 重启心跳，会自动将用户状态设置为在线
      this.startHeartbeat();
      // 启动全局通知轮询
      this.startGlobalNotificationPolling();
    }
  },
  
  onHide: function() {
    console.log('小程序进入后台');
    // 设置离线状态
    if (this.checkLogin()) {
      this.setOffline();
    }

    // 停止心跳
    this.stopHeartbeat();
    // 停止全局通知轮询
    this.stopGlobalNotificationPolling();
  },
  
  // 当小程序被卸载时，确保用户设置为离线
  onUnload: function() {
    console.log('小程序被卸载');
    // 设置用户离线状态
    if (this.checkLogin()) {
      this.setOffline().catch(err => {
        console.error('离线状态设置失败:', err);
      });
    }
    // 停止心跳
    this.stopHeartbeat();
  },
  
  // 心跳包系统，定期向服务器发送在线状态
  startHeartbeat: function() {
    console.log('启动心跳包系统');
    // 清除可能存在的旧心跳定时器
    this.stopHeartbeat();
    
    // 创建新的心跳定时器，每2分钟发送一次
    this.heartbeatTimer = setInterval(() => {
      // 只有在登录状态下才发送心跳
      if (this.checkLogin()) {
        console.log('发送心跳包...');
        wx.request({
          url: this.globalData.baseUrl + '/heartbeat',
          method: 'POST',
          header: {
            'Authorization': this.globalData.token
          },
          success: (res) => {
            console.log('心跳成功:', res.data);
          },
          fail: (err) => {
            console.error('心跳失败:', err);
            // 如果心跳失败，可能服务器已关闭或网络问题
            // 减少失败时的发送频率，但不要完全停止
            this.stopHeartbeat();
            // 30秒后尝试重启心跳
            setTimeout(() => {
              this.startHeartbeat();
            }, 30000);
          }
        });
      } else {
        // 如果用户已经不在登录状态，停止心跳
        console.log('用户未登录，停止心跳');
        this.stopHeartbeat();
      }
    }, 120000); // 2分钟一次心跳
  },
  
  // 测试API连通性
  testApiConnection: function() {
    console.log('测试API连通性...');
    
    const header = {
      'Content-Type': 'application/json', // 强制指定JSON格式
      'Accept': 'application/json'
    };
    
    // wx.request({
    //   url: `${this.globalData.baseUrl}/test`,
    //   method: 'GET',
    //   header: header,
    //   timeout: 5000,
    //   success: (res) => {
    //     console.log('API连通性测试成功:', res.data);
    //     // 可以在这里显示一个成功提示
    //   },
    //   fail: (err) => {
    //     console.error('API连通性测试失败:', err);
    //     // 提示用户服务器连接问题
    //     wx.showModal({
    //       title: '服务器连接失败',
    //       content: '无法连接到服务器，请检查网络并联系管理员。错误信息: ' + err.errMsg,
    //       showCancel: false
    //     });
    //   }
    // });
  },
  
  // 用户登录
  login: function(userInfo) {
    const that = this;
    const nickname = userInfo.nickName;
    const avatarUrl = userInfo.avatarUrl;
    
    // 尝试从本地获取已存储的openid
    const storedOpenid = wx.getStorageSync('openid');
    
    console.log('正在使用openid进行登录:', storedOpenid);
    
    return new Promise((resolve, reject) => {
      wx.login({
        success: function(loginRes) {
          if (loginRes.code) {
            // 如果没有存储的openid，则使用新获取的code
            // 如果已有存储的openid，则使用它，确保用户身份一致性
            const codeToUse = storedOpenid || loginRes.code;
            
            // 准备发送到服务器的数据
            const requestData = {
              code: codeToUse,
              nickname: nickname,
              avatar: avatarUrl
            };
            
            console.log('登录请求数据:', requestData);
            
            // 获取到微信登录code后，发送到服务器
            wx.request({
              url: that.globalData.baseUrl + '/login',
              method: 'POST',
              header: {
                'Content-Type': 'application/json'
              },
              data: requestData,
              success: function (res) {
                console.log('登录请求成功:', res);
                
                // 检查用户是否被禁用 - 服务器可能返回403但仍会走success回调
                if (res.statusCode === 403 && res.data && res.data.banned) {
                  console.error('用户已被禁用 (app.login 403状态码)');
                  // 清除登录状态但保存用户信息
                  that.globalData.token = '';
                  that.globalData.userInfo = res.data.user || null;
                  wx.removeStorageSync('token');
                  // 保留userInfo以在禁用页面显示
                  
                  // 将错误和用户信息一起返回
                  reject({
                    banned: true,
                    statusCode: 403,
                    data: res.data,
                    message: res.data.error || '您的账户已被禁用'
                  });
                  return;
                }
                
                // 检查其它错误响应，如果返回的不是成功状态
                if (res.statusCode !== 200 || !res.data || !res.data.token) {
                  console.error('登录失败:', res);
                  
                  // 检查用户是否被禁用 - 检查消息内容
                  if (res.data && (res.data.banned || (res.data.error && res.data.error.indexOf('禁用') >= 0))) {
                    console.error('用户已被禁用 (app.login 消息内容)');
                    // 保存部分用户信息以在禁用页面显示
                    that.globalData.userInfo = {
                      nickname: nickname,
                      nickName: nickname,
                      avatar: avatarUrl,
                      avatarUrl: avatarUrl
                    };
                    
                    reject({
                      banned: true,
                      data: res.data,
                      message: res.data.error || '您的账户已被禁用'
                    });
                    return;
                  }
                  
                  reject(new Error(res.data?.error || '登录失败'));
                  return;
                }
                
                that.globalData.token = String(res.data.token);
                that.globalData.userInfo = res.data.user;
                that.globalData.hasUserInfo = true;
                
                // 检查返回的用户信息是否被禁用
                if (that.globalData.userInfo && that.globalData.userInfo.is_active === false) {
                  console.error('用户已被禁用 (app.login 用户信息)');
                  // 清除token
                  that.globalData.token = '';
                  wx.removeStorageSync('token');
                  
                  reject({
                    banned: true,
                    data: {
                      user: that.globalData.userInfo,
                      error: '您的账户已被禁用'
                    },
                    message: '您的账户已被禁用'
                  });
                  return;
                }
                
                // 保存token和用户信息到本地存储
                wx.setStorageSync('token', that.globalData.token);
                wx.setStorageSync('userInfo', that.globalData.userInfo);
                
                // 如果还没有保存openid，则保存现在的code作为openid
                // 在开发环境中，服务器将code视为openid
                if (!storedOpenid) {
                  console.log('保存新的openid:', codeToUse);
                  wx.setStorageSync('openid', codeToUse);
                }
                
                // 启动心跳，保持在线状态
                that.startHeartbeat();

                // 启动全局通知轮询
                that.startGlobalNotificationPolling();

                // 如果用户没有班级ID，设置一个默认值以便测试
                if (!that.globalData.userInfo.class_id) {
                  console.log('用户未设置班级ID，将在下一步选择');
                }
                
                // 同步头像URL以确保一致性
                that.globalData.userInfo.avatarUrl = avatarUrl;
                
                resolve(res.data);
              },
              fail: function (err) {
                console.error('登录请求失败:', err);
                reject(err);
              }
            });
          } else {
            console.error('获取微信登录code失败', loginRes);
            reject(new Error('获取微信登录code失败'));
          }
        },
        fail: function(err) {
          console.error('微信登录失败:', err);
          reject(err);
        }
      });
    });
  },
  
  // API请求方法，自动带上token
  request: function(options) {
    return new Promise((resolve, reject) => {
      // 检查登录状态，如果没有token则先触发登录
      if (!this.globalData.token) {
        console.error('未找到token，需要登录');
        reject(new Error('未授权，请先登录'));
        wx.navigateTo({
          url: '/pages/login/login'
        });
        return;
      }

      // 构建请求header - 使用完整的标准HTTP头
      const header = {
        'content-type': 'application/json',
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      };
      
      // 添加授权头
      if (this.globalData.token) {
        header['Authorization'] = this.globalData.token;
      }

      console.log(`发送${options.method || 'GET'}请求到: ${options.url}`);
      console.log('请求头:', header);
      
      wx.request({
        url: `${this.globalData.baseUrl}${options.url}`,
        method: options.method || 'GET',
        data: options.data,
        header: header,
        timeout: 10000,
        success: (res) => {
          console.log(`请求成功: ${options.url}`, res);
          if (res.statusCode === 401) {
            console.error('401未授权错误, 需要重新登录');
            
            // Token无效，清除本地存储的登录信息
            this.globalData.token = '';
            this.globalData.userInfo = null;
            wx.removeStorageSync('token');
            wx.removeStorageSync('userInfo');
            
            // 使用navigateTo替代reLaunch，避免无限循环
            wx.navigateTo({
              url: '/pages/login/login'
            });
            reject(new Error('未授权，请重新登录'));
          } else if (res.statusCode === 403 && res.data && res.data.banned) {
            console.error('用户已被禁用');
            
            // 用户被禁用，清除登录状态
            this.globalData.token = '';
            this.globalData.userInfo = null;
            wx.removeStorageSync('token');
            // 但不要删除userInfo，因为需要在禁用页面显示用户信息
            
            // 停止心跳
            this.stopHeartbeat();
            
            // 显示禁用信息对话框
            wx.showModal({
              title: '账号已被禁用',
              content: '您的账号已被管理员禁用，如需帮助请联系客服邮箱：<EMAIL>',
              showCancel: false,
              confirmText: '确定',
              success: () => {
                // 跳转到禁用账户页面
                wx.redirectTo({
                  url: '/pages/banned/banned'
                });
              }
            });
            
            reject(new Error('账户已被禁用'));
          } else if (res.statusCode !== 200) {
            console.error(`请求失败，状态码: ${res.statusCode}`, res);
            reject(new Error(`请求失败，状态码: ${res.statusCode}`));
          } else {
            resolve(res.data);
          }
        },
        fail: (err) => {
          console.error('请求失败:', err);
          
          // 连接重置问题特殊处理
          if (err.errMsg && err.errMsg.includes('fail')) {
            console.error('连接失败，可能需要在微信小程序管理后台配置域名白名单');
            wx.showModal({
              title: '连接失败',
              content: '无法连接到服务器，请确认网络连接并联系管理员',
              showCancel: false
            });
          }
          
          reject(err);
        }
      });
    });
  },
  
  // 添加重试请求功能
  retryRequest: function(fn, maxRetries = 3) {
    return new Promise((resolve, reject) => {
      let attempts = 0;
      
      const attempt = () => {
        attempts++;
        fn()
          .then(resolve)
          .catch(err => {
            console.log(`尝试 ${attempts}/${maxRetries} 失败:`, err);
            if (attempts < maxRetries) {
              setTimeout(() => attempt(), 1000); // 1秒后重试
            } else {
              reject(err);
            }
          });
      };
      
      attempt();
    });
  },
  
  // 检查是否已登录
  checkLogin: function() {
    return !!this.globalData.token && this.validateToken(this.globalData.token);
  },
  
  // 简单验证token格式
  validateToken: function(token) {
    if (!token || typeof token !== 'string') {
      return false;
    }
    // 最基本的格式检查 - token应该至少有一定长度
    return token.length > 10;
  },
  
  // 登出方法
  logout: function() {
    // 先设置离线状态
    if (this.checkLogin()) {
      this.setOffline();
    }
    
    // 停止心跳
    this.stopHeartbeat();
    
    // 清除点赞和收藏的缓存数据
    this.clearPostsCache();
    
    // 保留openid，但清除其他登录信息
    this.globalData.token = '';
    this.globalData.userInfo = null;
    wx.removeStorageSync('token');
    wx.removeStorageSync('userInfo');
    // 注意：不删除openid，保留以便重新登录时识别相同用户
    
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },
  
  // 清除帖子相关缓存，确保切换用户后不会显示上一个用户的点赞/收藏状态
  clearPostsCache: function() {
    console.log('清除帖子缓存数据');
    // 清除可能存在的帖子缓存
    wx.removeStorageSync('posts_cache');
    wx.removeStorageSync('liked_posts');
    wx.removeStorageSync('collected_posts');
  },
  
  stopHeartbeat: function() {
    if (this.heartbeatTimer) {
      console.log('停止心跳包系统');
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  },
  
  // 设置离线状态
  setOffline: function() {
    console.log('设置离线状态');
    if (!this.checkLogin()) {
      console.log('用户未登录，无需设置离线状态');
      return Promise.resolve();
    }
    
    return new Promise((resolve, reject) => {
      wx.request({
        url: this.globalData.baseUrl + '/logout',
        method: 'POST',
        header: {
          'Authorization': this.globalData.token
        },
        success: (res) => {
          console.log('设置离线状态成功:', res.data);
          resolve(res.data);
        },
        fail: (err) => {
          console.error('设置离线状态失败:', err);
          reject(err);
        }
      });
    });
  },
  
  // 小程序错误监控
  onError: function(error) {
    console.error('小程序发生错误:', error);
  },
  
  // 小程序页面不存在监控
  onPageNotFound: function(res) {
    console.error('页面不存在:', res);
    wx.switchTab({
      url: '/pages/index/index'
    });
  },
  
  // 游客登录
  visitorLogin: function() {
    if (this.data.isLogging) return;
    
    this.setData({ 
      isLogging: true,
      loginFailed: false,
      errorMsg: ''
    });
    
    console.log('开始游客登录');
    
    // 创建模拟用户信息，使用网络图片避免本地图片加载问题
    const userInfo = {
      nickName: '游客' + Math.floor(Math.random() * 10000), // 添加随机数以区分不同游客
      avatarUrl: 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0'
    };
    
    console.log('游客信息:', userInfo);
    this.handleLogin(userInfo);
  },
  
  // 检查用户是否有班级
  checkUserHasClass: function() {
    return new Promise((resolve, reject) => {
      if (!this.checkLogin()) {
        resolve(false);
        return;
      }
      
      wx.request({
        url: this.globalData.baseUrl + '/classes/current',
        method: 'GET',
        header: {
          'Authorization': this.globalData.token
        },
        success: (res) => {
          console.log('检查用户班级:', res.data);
          if (res.data && res.data.status === 'success') {
            resolve(res.data.has_class);
          } else {
            resolve(false);
          }
        },
        fail: (err) => {
          console.error('检查班级状态失败:', err);
          resolve(false);
        }
      });
    });
  },
  
  // 创建测试班级
  createTestClasses: function() {
    return new Promise((resolve, reject) => {
      wx.request({
        url: this.globalData.baseUrl + '/admin/create-test-classes',
        method: 'GET',
        success: (res) => {
          console.log('创建测试班级结果:', res.data);
          resolve(res.data);
        },
        fail: (err) => {
          console.error('创建测试班级失败:', err);
          reject(err);
        }
      });
    });
  },
  
  // 初始化小猫组件 - 确保所有页面都能访问
  initCatComponent: function() {
    // 在页面页显示后，确保小猫组件在每个页面都可见
    // 已通过全局组件注册实现
    console.log('小猫组件正在初始化');
    
    // 强制更新小猫组件可见状态
    this.globalData.catVisible = true;
    
    // 尝试获取当前页面实例 
    const pages = getCurrentPages();
    if (pages && pages.length > 0) {
      const currentPage = pages[pages.length - 1];
      if (currentPage && currentPage.setData) {
        console.log('找到当前页面，尝试更新组件状态');
        // 尝试在当前页面中更新组件状态
        currentPage.setData({
          catComponentVisible: true
        });
      }
    }
  },

  // ==================== 全局通知系统 ====================

  // 初始化已显示通知记录
  initShownNotifications: function() {
    try {
      const shownNotifications = wx.getStorageSync('shownNotifications') || []
      this.globalData.notificationSystem.shownNotifications = new Set(shownNotifications)
      console.log('已加载已显示通知记录:', shownNotifications.length, '条')
    } catch (e) {
      console.error('加载已显示通知记录失败:', e)
      this.globalData.notificationSystem.shownNotifications = new Set()
    }
  },

  // 保存已显示通知记录到本地存储
  saveShownNotifications: function() {
    try {
      const shownNotifications = Array.from(this.globalData.notificationSystem.shownNotifications)
      wx.setStorageSync('shownNotifications', shownNotifications)
      console.log('已保存已显示通知记录:', shownNotifications.length, '条')
    } catch (e) {
      console.error('保存已显示通知记录失败:', e)
    }
  },

  // 启动全局通知轮询
  startGlobalNotificationPolling: function() {
    if (this.globalData.notificationSystem.isPolling) {
      return // 已经在轮询中
    }

    if (!this.checkLogin()) {
      return // 未登录不启动轮询
    }

    console.log('启动全局评论通知轮询')
    this.globalData.notificationSystem.isPolling = true

    // 立即检查一次
    this.checkGlobalNotifications()

    // 每5秒检查一次新通知
    let cleanupCounter = 0
    this.globalData.notificationSystem.pollingTimer = setInterval(() => {
      this.checkGlobalNotifications()
      // 每12次轮询（1分钟）清理一次过期的通知记录
      cleanupCounter++
      if (cleanupCounter >= 12) {
        this.cleanupShownNotifications()
        cleanupCounter = 0
      }
    }, 5000)
  },

  // 停止全局通知轮询
  stopGlobalNotificationPolling: function() {
    console.log('停止全局评论通知轮询')
    this.globalData.notificationSystem.isPolling = false

    if (this.globalData.notificationSystem.pollingTimer) {
      clearInterval(this.globalData.notificationSystem.pollingTimer)
      this.globalData.notificationSystem.pollingTimer = null
    }
  },

  // 检查全局通知
  checkGlobalNotifications: function() {
    if (!this.checkLogin()) return

    const userInfo = this.globalData.userInfo
    if (!userInfo || !userInfo.id) return

    wx.request({
      url: `${this.globalData.baseUrl}/cat_circle/notifications/comments`,
      method: 'GET',
      data: {
        user_id: userInfo.id,
        page: 1,
        per_page: 1,
        unread_only: true
      },
      success: (res) => {
        if (res.data.code === 200) {
          const notifications = res.data.data.notifications || []

          if (notifications.length > 0) {
            const latestNotification = notifications[0]

            // 检查是否是新通知且未显示过
            if (latestNotification.id > this.globalData.notificationSystem.lastNotificationId &&
                !this.globalData.notificationSystem.shownNotifications.has(latestNotification.id)) {
              this.globalData.notificationSystem.lastNotificationId = latestNotification.id
              this.showGlobalNotification(latestNotification)
            }
          }
        }
      },
      fail: (err) => {
        console.error('检查全局通知失败:', err)
      }
    })
  },

  // 显示全局通知
  showGlobalNotification: function(notification) {
    // 不在评论详情页面时才显示通知
    const currentPages = getCurrentPages()
    const currentPage = currentPages[currentPages.length - 1]
    const currentRoute = currentPage ? currentPage.route : ''

    // 如果当前在评论详情页面，不显示全局通知
    if (currentRoute === 'pages/post-detail/post-detail') {
      return
    }

    console.log('显示全局通知:', notification)

    const commenterName = notification.commenter_info?.nickname || '匿名用户'
    const content = notification.comment_content
    const shortContent = content.length > 30 ? content.substring(0, 30) + '...' : content

    // 根据通知类型生成不同的标题
    let title = ''
    switch (notification.notification_type) {
      case 'reply':
        title = `${commenterName} 回复了你`
        break
      case 'comment':
        title = `${commenterName} 评论了你的动态`
        break
      default:
        title = `${commenterName} 回复了你`
        break
    }

    // 记录已显示的通知ID
    this.globalData.notificationSystem.shownNotifications.add(notification.id)
    // 保存到本地存储
    this.saveShownNotifications()

    this.globalData.notificationSystem.showNotification = true
    this.globalData.notificationSystem.notificationData = {
      title: title,
      message: shortContent,
      postId: notification.post_id,
      notificationId: notification.id
    }

    // 通知所有页面显示通知
    this.notifyAllPagesShowNotification()

    // 通知猫友圈页面更新未读评论数量
    this.notifyUnreadCountUpdate(notification.post_id)
  },

  // 通知所有页面显示通知
  notifyAllPagesShowNotification: function() {
    const pages = getCurrentPages()
    pages.forEach(page => {
      if (page.updateGlobalNotification && typeof page.updateGlobalNotification === 'function') {
        page.updateGlobalNotification()
      }
    })
  },

  // 通知猫友圈页面更新未读评论数量
  notifyUnreadCountUpdate: function(postId) {
    const pages = getCurrentPages()
    pages.forEach(page => {
      // 检查是否是猫友圈页面
      if (page.route === 'pages/cat-friends/cat-friends' &&
          page.updateUnreadCommentCount &&
          typeof page.updateUnreadCommentCount === 'function') {
        page.updateUnreadCommentCount(postId)
      }
    })
  },

  // 隐藏全局通知
  hideGlobalNotification: function() {
    this.globalData.notificationSystem.showNotification = false
    this.globalData.notificationSystem.notificationData = null

    // 通知所有页面隐藏通知
    const pages = getCurrentPages()
    pages.forEach(page => {
      if (page.hideGlobalNotification && typeof page.hideGlobalNotification === 'function') {
        page.hideGlobalNotification()
      }
    })
  },

  // 处理通知点击
  handleNotificationTap: function(postId, notificationId) {
    // 隐藏通知
    this.hideGlobalNotification()

    // 标记通知为已读
    if (notificationId) {
      this.markNotificationAsRead(notificationId)
    }

    // 跳转到评论详情页面
    wx.navigateTo({
      url: `/pages/post-detail/post-detail?id=${postId}`
    })
  },

  // 标记通知为已读
  markNotificationAsRead: function(notificationId) {
    if (!this.checkLogin()) return

    const userInfo = this.globalData.userInfo
    if (!userInfo || !userInfo.id) return

    wx.request({
      url: `${this.globalData.baseUrl}/cat_circle/notifications/comments/${notificationId}/read`,
      method: 'POST',
      data: { user_id: userInfo.id },
      success: (res) => {
        if (res.data.code === 200) {
          console.log('通知已标记为已读')
        }
      },
      fail: (err) => {
        console.error('标记通知已读失败:', err)
      }
    })
  },

  // 清理过期的已显示通知记录
  cleanupShownNotifications: function() {
    const shownNotifications = this.globalData.notificationSystem.shownNotifications

    // 如果记录超过100条，只保留最新的50条
    if (shownNotifications.size > 100) {
      const notificationArray = Array.from(shownNotifications)
      // 按ID排序，保留最大的50个ID（最新的通知）
      const sortedNotifications = notificationArray.sort((a, b) => b - a)
      const keepNotifications = sortedNotifications.slice(0, 50)

      // 重新创建Set
      this.globalData.notificationSystem.shownNotifications = new Set(keepNotifications)
      // 更新本地存储
      this.saveShownNotifications()
      console.log(`清理通知记录，保留${keepNotifications.length}条最新记录`)
    }
  }
})

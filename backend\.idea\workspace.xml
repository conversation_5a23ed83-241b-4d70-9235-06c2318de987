<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="98ffd213-5265-4772-bd9c-d702e3bf8b82" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2w9fvOY9OPK95oIlC4BhaBvyJ1p" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Python.app.executor&quot;: &quot;Run&quot;,
    &quot;Python.backend.executor&quot;: &quot;Run&quot;,
    &quot;Python.fix_font_awesome.executor&quot;: &quot;Run&quot;,
    &quot;Python.update_templates.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected="Python.app">
    <configuration name="app" type="PythonConfigurationType" factoryName="Python">
      <module name="backend" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="SDK_NAME" value="D:\anaconda (5)" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <EXTENSION ID="net.ashald.envfile">
        <option name="IS_ENABLED" value="false" />
        <option name="IS_SUBST" value="false" />
        <option name="IS_PATH_MACRO_SUPPORTED" value="false" />
        <option name="IS_IGNORE_MISSING_FILES" value="false" />
        <option name="IS_ENABLE_EXPERIMENTAL_INTEGRATIONS" value="false" />
        <ENTRIES>
          <ENTRY IS_ENABLED="true" PARSER="runconfig" IS_EXECUTABLE="false" />
        </ENTRIES>
      </EXTENSION>
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/app.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="fix_font_awesome" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="backend" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/static" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/static/fix_font_awesome.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="update_templates" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="backend" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/static" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/static/update_templates.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.fix_font_awesome" />
        <item itemvalue="Python.update_templates" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-PY-241.19072.16" />
        <option value="bundled-python-sdk-8336bb23522e-2767605e8bc2-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-241.19072.16" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="98ffd213-5265-4772-bd9c-d702e3bf8b82" name="Changes" comment="" />
      <created>1745458941620</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1745458941620</updated>
      <workItem from="1745458943079" duration="2373000" />
      <workItem from="1745513044032" duration="2915000" />
      <workItem from="1745551166472" duration="638000" />
      <workItem from="1745553665313" duration="2610000" />
      <workItem from="1745671721122" duration="3769000" />
      <workItem from="1745677665948" duration="194000" />
      <workItem from="1745677897760" duration="3824000" />
      <workItem from="1745739086141" duration="785000" />
      <workItem from="1745747504925" duration="2072000" />
      <workItem from="1745757274941" duration="2254000" />
      <workItem from="1745809333895" duration="940000" />
      <workItem from="1745813203080" duration="3331000" />
      <workItem from="1745820673641" duration="1270000" />
      <workItem from="1745923535004" duration="2952000" />
      <workItem from="1746370097450" duration="7741000" />
      <workItem from="1746418647065" duration="4645000" />
      <workItem from="1746435849206" duration="15711000" />
      <workItem from="1746496568332" duration="3681000" />
      <workItem from="1746504083257" duration="4717000" />
      <workItem from="1746516699202" duration="6000" />
      <workItem from="1746519716560" duration="10433000" />
      <workItem from="1746552182521" duration="3671000" />
      <workItem from="1746588591802" duration="702000" />
      <workItem from="1746711765797" duration="648000" />
      <workItem from="1746721451044" duration="5487000" />
      <workItem from="1746765257958" duration="1560000" />
      <workItem from="1746770572330" duration="643000" />
      <workItem from="1746777680461" duration="1215000" />
      <workItem from="1746797797431" duration="8210000" />
      <workItem from="1746812595791" duration="1417000" />
      <workItem from="1746846694656" duration="961000" />
      <workItem from="1746863191185" duration="12894000" />
      <workItem from="1748411238683" duration="5687000" />
      <workItem from="1748443358712" duration="811000" />
      <workItem from="1748445721203" duration="374000" />
      <workItem from="1748617382389" duration="7786000" />
      <workItem from="1748928106980" duration="1282000" />
      <workItem from="1748963490960" duration="16000" />
      <workItem from="1749113750362" duration="685000" />
      <workItem from="1749115772442" duration="6555000" />
      <workItem from="1749136645412" duration="845000" />
      <workItem from="1749201894840" duration="68000" />
      <workItem from="1749541925065" duration="3609000" />
      <workItem from="1749654444475" duration="2456000" />
      <workItem from="1749908264053" duration="1767000" />
      <workItem from="1749981918042" duration="582000" />
      <workItem from="1749982786961" duration="420000" />
      <workItem from="1749986087199" duration="48000" />
      <workItem from="1749997179029" duration="23000" />
      <workItem from="1750397725663" duration="15282000" />
      <workItem from="1750489184537" duration="11496000" />
      <workItem from="1750574806750" duration="6771000" />
      <workItem from="1750643552775" duration="5110000" />
      <workItem from="1750670961504" duration="1397000" />
      <workItem from="1750739299500" duration="783000" />
      <workItem from="1750841323209" duration="701000" />
      <workItem from="1750875235854" duration="139000" />
      <workItem from="1751345547199" duration="308000" />
      <workItem from="1751356607121" duration="861000" />
      <workItem from="1751958318411" duration="941000" />
      <workItem from="1751960408028" duration="3812000" />
      <workItem from="1752054618228" duration="756000" />
      <workItem from="1752056514461" duration="1699000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/app.py</url>
          <line>1735</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/backend$fix_font_awesome.coverage" NAME="fix_font_awesome Coverage Results" MODIFIED="1745514782274" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/static" />
    <SUITE FILE_PATH="coverage/backend$app.coverage" NAME="app Coverage Results" MODIFIED="1752058217052" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="" />
    <SUITE FILE_PATH="coverage/backend$backend.coverage" NAME="app Coverage Results" MODIFIED="1745463027470" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="" />
    <SUITE FILE_PATH="coverage/backend$update_templates.coverage" NAME="update_templates Coverage Results" MODIFIED="1745513858777" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/static" />
  </component>
</project>
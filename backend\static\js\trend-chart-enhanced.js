// 用户答题数据趋势 - 霓虹灯科技风格增强版
document.addEventListener('DOMContentLoaded', function() {
  // 查找图表容器
  const trendChartCanvas = document.getElementById('trendChart');
  if (!trendChartCanvas) {
    console.error('找不到用户答题数据趋势图表元素');
    return;
  }

  console.log('正在初始化科技感霓虹灯趋势图表...');
  
  try {
    // 获取图表容器尺寸
    const container = trendChartCanvas.parentElement;
    const width = container.clientWidth;
    const height = container.clientHeight || 400;
    
    // 设置画布尺寸
    trendChartCanvas.width = width;
    trendChartCanvas.height = height;
    
    // 设置容器样式
    container.style.position = 'relative';
    container.style.overflow = 'hidden';
    container.style.borderRadius = '12px';
    container.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.15)';
    
    // 创建暗色背景
    const darkBackground = document.createElement('div');
    darkBackground.style.position = 'absolute';
    darkBackground.style.top = '0';
    darkBackground.style.left = '0';
    darkBackground.style.width = '100%';
    darkBackground.style.height = '100%';
    darkBackground.style.background = 'linear-gradient(135deg, #0f172a 0%, #1e293b 100%)';
    darkBackground.style.zIndex = '-2';
    container.appendChild(darkBackground);
    
    // 创建背景网格
    const grid = document.createElement('div');
    grid.style.position = 'absolute';
    grid.style.top = '0';
    grid.style.left = '0';
    grid.style.width = '100%';
    grid.style.height = '100%';
    grid.style.backgroundImage = 'linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px)';
    grid.style.backgroundSize = '20px 20px';
    grid.style.opacity = '0.4';
    grid.style.zIndex = '-1';
    container.appendChild(grid);
    
    // 创建发光粒子
    for (let i = 0; i < 20; i++) {
      const particle = document.createElement('div');
      const size = Math.random() * 6 + 2;
      const posX = Math.random() * 100;
      const posY = Math.random() * 100;
      const hue = Math.random() * 60 + 220; // 蓝色调范围
      
      particle.style.position = 'absolute';
      particle.style.width = `${size}px`;
      particle.style.height = `${size}px`;
      particle.style.borderRadius = '50%';
      particle.style.backgroundColor = `hsla(${hue}, 100%, 70%, 0.8)`;
      particle.style.boxShadow = `0 0 ${size*2}px ${size}px hsla(${hue}, 100%, 60%, 0.4)`;
      particle.style.left = `${posX}%`;
      particle.style.top = `${posY}%`;
      particle.style.opacity = Math.random() * 0.5 + 0.3;
      particle.style.zIndex = '-1';
      
      // 创建浮动动画
      const animName = `float-trend-${i}`;
      const keyframes = `
        @keyframes ${animName} {
          0% { transform: translate(0, 0) scale(1); opacity: ${Math.random() * 0.3 + 0.3}; }
          50% { transform: translate(${Math.random() * 30 - 15}px, ${Math.random() * 30 - 15}px) scale(${Math.random() * 0.5 + 0.8}); opacity: ${Math.random() * 0.5 + 0.5}; }
          100% { transform: translate(0, 0) scale(1); opacity: ${Math.random() * 0.3 + 0.3}; }
        }
      `;
      
      const style = document.createElement('style');
      style.textContent = keyframes;
      document.head.appendChild(style);
      
      particle.style.animation = `${animName} ${Math.random() * 5 + 8}s ease-in-out infinite`;
      container.appendChild(particle);
    }
    
    // 获取canvas上下文
    const ctx = trendChartCanvas.getContext('2d');
    
    // 从后端获取数据
    fetch('/api/statistics/trend-data')
      .then(response => response.json())
      .then(data => {
        // 从API响应中提取数据
        const chartData = {
          labels: data.labels,
          answerCounts: data.answer_counts,
          correctRates: data.correct_rates
        };
        
        // 初始化图表
        initChart(chartData);
      })
      .catch(error => {
        console.error('获取数据失败:', error);
        // 使用默认数据作为备用
        // 这里使用固定的模拟数据，避免与模板变量冲突
        const fallbackData = {
          labels: ['4-29', '4-30', '5-1', '5-2', '5-3', '5-4', '5-5'],
          answerCounts: [9, 4, -3, 1, -6, -1, 2],
          correctRates: [85, 70, 60, 75, 50, 65, 80]
        };
        
        // 初始化图表
        initChart(fallbackData);
      });
    
    function initChart(chartData) {
      // 创建渐变填充
      const gradientFill = ctx.createLinearGradient(0, 0, 0, height);
      gradientFill.addColorStop(0, 'rgba(78, 115, 223, 0.4)');
      gradientFill.addColorStop(0.5, 'rgba(78, 115, 223, 0.1)');
      gradientFill.addColorStop(1, 'rgba(78, 115, 223, 0.02)');
      
      // 创建柱状图渐变
      const barGradient = ctx.createLinearGradient(0, 0, 0, height);
      barGradient.addColorStop(0, 'rgba(99, 102, 241, 0.9)');
      barGradient.addColorStop(1, 'rgba(99, 102, 241, 0.5)');
      
      // 创建图表实例
      const chart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: chartData.labels,
          datasets: [
            {
              label: '答题数量',
              data: chartData.answerCounts,
              backgroundColor: barGradient,
              borderColor: 'rgba(99, 102, 241, 1)',
              borderWidth: 2,
              pointBackgroundColor: '#fff',
              pointBorderColor: 'rgba(99, 102, 241, 1)',
              pointBorderWidth: 2,
              pointHoverBackgroundColor: 'rgba(99, 102, 241, 1)',
              pointHoverBorderColor: '#fff',
              pointHoverBorderWidth: 2,
              pointRadius: 5,
              pointHoverRadius: 7,
              order: 2,
              yAxisID: 'y',
              type: 'bar',
              barPercentage: 0.6,
              categoryPercentage: 0.7
            },
            {
              label: '正确率 (%)',
              data: chartData.correctRates,
              backgroundColor: gradientFill,
              borderColor: ctx => {
                // 创建荧光边框渐变
                const gradient = ctx.createLinearGradient(0, 0, width, 0);
                gradient.addColorStop(0, 'rgba(56, 189, 248, 0.8)');
                gradient.addColorStop(0.5, 'rgba(6, 182, 212, 0.8)');
                gradient.addColorStop(1, 'rgba(14, 165, 233, 0.8)');
                return gradient;
              },
              borderWidth: 3,
              pointBackgroundColor: 'rgb(219, 234, 254)',
              pointBorderColor: 'rgb(37, 99, 235)',
              pointBorderWidth: 2,
              pointHoverBackgroundColor: 'rgb(255, 255, 255)',
              pointHoverBorderColor: 'rgb(37, 99, 235)',
              pointHoverBorderWidth: 2,
              pointRadius: 5,
              pointHoverRadius: 7,
              fill: true,
              tension: 0.4,
              yAxisID: 'y1',
              order: 1
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          layout: {
            padding: {
              top: 20,
              right: 25,
              bottom: 10,
              left: 15
            }
          },
          interaction: {
            mode: 'index',
            intersect: false,
          },
          plugins: {
            legend: {
              position: 'top',
              align: 'end',
              labels: {
                usePointStyle: true,
                padding: 20,
                boxWidth: 8,
                boxHeight: 8,
                color: 'rgb(226, 232, 240)',
                font: {
                  size: 12,
                  weight: '600',
                  family: "'Inter', 'Helvetica', 'Arial', sans-serif"
                }
              }
            },
            tooltip: {
              enabled: true,
              backgroundColor: 'rgba(15, 23, 42, 0.9)',
              titleColor: 'rgb(226, 232, 240)',
              bodyColor: 'rgb(226, 232, 240)',
              titleFont: {
                size: 14,
                weight: 'bold',
                family: "'Inter', 'Helvetica', 'Arial', sans-serif"
              },
              bodyFont: {
                size: 13,
                family: "'Inter', 'Helvetica', 'Arial', sans-serif"
              },
              borderColor: 'rgba(71, 85, 105, 0.5)',
              borderWidth: 1,
              padding: 12,
              cornerRadius: 8,
              usePointStyle: true,
              boxWidth: 8,
              boxHeight: 8,
              callbacks: {
                title: function(tooltipItems) {
                  return tooltipItems[0].label + ' 数据';
                },
                label: function(context) {
                  let label = context.dataset.label || '';
                  if (label) {
                    label += ': ';
                  }
                  if (context.parsed.y !== null) {
                    label += context.dataset.label.includes('%') ? 
                      context.parsed.y + '%' : 
                      context.parsed.y + ' 题';
                  }
                  return label;
                }
              }
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              position: 'left',
              title: {
                display: true,
                text: '答题数量',
                color: 'rgba(99, 102, 241, 1)',
                font: {
                  size: 12,
                  weight: 'bold',
                  family: "'Inter', 'Helvetica', 'Arial', sans-serif"
                }
              },
              grid: {
                drawBorder: false,
                color: 'rgba(71, 85, 105, 0.1)'
              },
              ticks: {
                padding: 10,
                font: {
                  size: 11,
                  family: "'Inter', 'Helvetica', 'Arial', sans-serif"
                },
                color: 'rgba(148, 163, 184, 1)'
              }
            },
            y1: {
              beginAtZero: true,
              position: 'right',
              title: {
                display: true,
                text: '正确率 (%)',
                color: 'rgba(56, 189, 248, 1)',
                font: {
                  size: 12,
                  weight: 'bold',
                  family: "'Inter', 'Helvetica', 'Arial', sans-serif"
                }
              },
              min: 0,
              max: 100,
              grid: {
                drawOnChartArea: false,
              },
              ticks: {
                padding: 10,
                font: {
                  size: 11,
                  family: "'Inter', 'Helvetica', 'Arial', sans-serif"
                },
                color: 'rgba(148, 163, 184, 1)'
              }
            },
            x: {
              grid: {
                drawBorder: false,
                color: 'rgba(71, 85, 105, 0.1)'
              },
              ticks: {
                padding: 10,
                font: {
                  size: 11,
                  family: "'Inter', 'Helvetica', 'Arial', sans-serif"
                },
                color: 'rgba(148, 163, 184, 1)'
              }
            }
          },
          animations: {
            tension: {
              duration: 1000,
              easing: 'linear'
            },
            x: {
              type: 'number',
              easing: 'easeOutQuart',
              duration: 1000,
              from: NaN,
              delay(ctx) {
                return ctx.dataIndex * 100;
              }
            },
            y: {
              type: 'number',
              easing: 'easeOutQuart',
              duration: 1000,
              from: NaN,
              delay(ctx) {
                return ctx.dataIndex * 100 + 500;
              }
            }
          }
        }
      });
      
      // 切换视图按钮绑定事件
      document.getElementById('daily-btn').addEventListener('click', function() {
        // 设置日视图的数据
        updateChartWithAnimation(chart, chartData.labels, chartData.answerCounts, chartData.correctRates);
        setActiveButton(this);
      });
      
      // 模拟周、月数据 - 实际项目中可从API获取
      document.getElementById('weekly-btn').addEventListener('click', function() {
        // 聚合为周数据
        const weekLabels = [];
        const weekAnswerCounts = [];
        const weekCorrectRates = [];
        
        // 简单聚合：每7天为一周
        for (let i = 0; i < chartData.labels.length; i += 7) {
          const weekEnd = Math.min(i + 6, chartData.labels.length - 1);
          weekLabels.push(`第${Math.floor(i/7)+1}周`);
          
          // 计算这一周的平均值
          let totalAnswers = 0;
          let totalCorrectRate = 0;
          let count = 0;
          
          for (let j = i; j <= weekEnd && j < chartData.labels.length; j++) {
            totalAnswers += chartData.answerCounts[j];
            totalCorrectRate += chartData.correctRates[j];
            count++;
          }
          
          weekAnswerCounts.push(Math.round(totalAnswers / count));
          weekCorrectRates.push(parseFloat((totalCorrectRate / count).toFixed(1)));
        }
        
        updateChartWithAnimation(chart, weekLabels, weekAnswerCounts, weekCorrectRates);
        setActiveButton(this);
      });
      
      document.getElementById('monthly-btn').addEventListener('click', function() {
        // 聚合为月数据
        const monthLabels = [];
        const monthAnswerCounts = [];
        const monthCorrectRates = [];
        
        // 从日期中提取月份信息
        const monthMap = {};
        
        chartData.labels.forEach((label, index) => {
          // 假设日期格式为 YYYY-MM-DD
          let month = "";
          // 尝试提取月份
          try {
            month = label.substring(0, 7); // 获取 YYYY-MM
          } catch(e) {
            // 如果失败，使用索引作为月份
            month = "Month-" + Math.floor(index / 30);
          }
          
          if (!monthMap[month]) {
            monthMap[month] = {
              answerSum: 0,
              correctRateSum: 0,
              count: 0
            };
          }
          
          monthMap[month].answerSum += chartData.answerCounts[index];
          monthMap[month].correctRateSum += chartData.correctRates[index];
          monthMap[month].count++;
        });
        
        // 将聚合数据转换为数组
        Object.keys(monthMap).sort().forEach(month => {
          // 尝试提取月份数字，如果失败则使用整个字符串
          let monthLabel = month;
          try {
            const monthNumber = parseInt(month.split('-')[1]);
            if (!isNaN(monthNumber)) {
              monthLabel = `${monthNumber}月`;
            }
          } catch(e) {
            // 保持原始标签
          }
          
          monthLabels.push(monthLabel);
          monthAnswerCounts.push(Math.round(monthMap[month].answerSum / monthMap[month].count));
          monthCorrectRates.push(parseFloat((monthMap[month].correctRateSum / monthMap[month].count).toFixed(1)));
        });
        
        updateChartWithAnimation(chart, monthLabels, monthAnswerCounts, monthCorrectRates);
        setActiveButton(this);
      });
    }
    
    // 带动画效果的图表更新
    function updateChartWithAnimation(chart, labels, answerData, correctData) {
      // 先隐藏数据点
      chart.data.datasets[0].pointRadius = 0;
      chart.data.datasets[1].pointRadius = 0;
      
      // 更新标签和数据
      chart.data.labels = labels;
      chart.data.datasets[0].data = answerData;
      chart.data.datasets[1].data = correctData;
      
      // 应用更新
      chart.update('none'); // 先不带动画更新
      
      // 延迟后显示数据点并带动画
      setTimeout(() => {
        chart.data.datasets[0].pointRadius = 5;
        chart.data.datasets[1].pointRadius = 5;
        chart.update({
          duration: 800,
          easing: 'easeOutQuart'
        });
      }, 50);
    }
    
    function setActiveButton(activeButton) {
      document.querySelectorAll('.chart-actions .btn').forEach(btn => {
        btn.classList.remove('active');
      });
      activeButton.classList.add('active');
    }
    
  } catch (error) {
    console.error('初始化图表时出错:', error);
  }
}); 
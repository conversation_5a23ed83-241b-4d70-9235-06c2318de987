<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>猫友圈管理 - 颜色方案展示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 自定义紫色徽章样式 */
        .badge-purple {
            background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
            color: white;
            border: none;
        }
        
        /* 自定义进度条紫色样式 */
        .progress-bar-purple {
            background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
        }
        
        /* 自定义文字紫色样式 */
        .text-purple {
            color: #8b5cf6 !important;
        }
        
        body {
            background-color: #f8f9fa;
        }
        
        .demo-section {
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-5">猫友圈管理 - 新颜色方案</h1>
        
        <!-- 动态类型徽章展示 -->
        <div class="demo-section">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-palette"></i> 动态类型徽章颜色方案</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>修改前（使用红色/黑色）：</h6>
                            <div class="mb-2">
                                <span class="badge bg-danger">❌ 猫友 (红色 - 不符合UI美观)</span>
                            </div>
                            <div class="mb-2">
                                <span class="badge bg-dark">❌ 猫友 (黑色 - 不符合UI美观)</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>修改后（使用紫色渐变）：</h6>
                            <div class="mb-2">
                                <span class="badge badge-purple">✅ 猫友 (紫色渐变 - 美观协调)</span>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <h6>完整的类型徽章展示：</h6>
                    <div class="d-flex flex-wrap gap-2">
                        <span class="badge bg-primary">动态</span>
                        <span class="badge bg-success">二手交易</span>
                        <span class="badge bg-info">求助</span>
                        <span class="badge bg-warning">失物招领</span>
                        <span class="badge badge-purple">猫友</span>
                        <span class="badge bg-secondary">校园跑</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 统计图标展示 -->
        <div class="demo-section">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar"></i> 统计图标颜色方案</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>修改前：</h6>
                            <div class="mb-2">
                                <i class="fas fa-heart text-danger"></i> ❌ 点赞 (红色)
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>修改后：</h6>
                            <div class="mb-2">
                                <i class="fas fa-heart text-purple"></i> ✅ 点赞 (紫色)
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <h6>完整的统计图标展示：</h6>
                    <div class="row text-center">
                        <div class="col-3">
                            <div class="border rounded p-3">
                                <i class="fas fa-eye text-info fa-2x"></i>
                                <div class="mt-2">
                                    <strong>100</strong>
                                    <div class="small text-muted">浏览</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="border rounded p-3">
                                <i class="fas fa-heart text-purple fa-2x"></i>
                                <div class="mt-2">
                                    <strong>50</strong>
                                    <div class="small text-muted">点赞</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="border rounded p-3">
                                <i class="fas fa-comment text-primary fa-2x"></i>
                                <div class="mt-2">
                                    <strong>20</strong>
                                    <div class="small text-muted">评论</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="border rounded p-3">
                                <i class="fas fa-star text-warning fa-2x"></i>
                                <div class="mt-2">
                                    <strong>10</strong>
                                    <div class="small text-muted">收藏</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 进度条展示 -->
        <div class="demo-section">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-line"></i> 进度条颜色方案</h5>
                </div>
                <div class="card-body">
                    <h6>猫友类型统计进度条：</h6>
                    <div class="mb-3">
                        <label class="form-label">猫友 (cat_friends)</label>
                        <div class="progress">
                            <div class="progress-bar progress-bar-purple" style="width: 25%">
                                15
                            </div>
                        </div>
                    </div>
                    
                    <h6>其他类型进度条对比：</h6>
                    <div class="mb-2">
                        <label class="form-label">动态</label>
                        <div class="progress">
                            <div class="progress-bar bg-primary" style="width: 60%">30</div>
                        </div>
                    </div>
                    <div class="mb-2">
                        <label class="form-label">二手交易</label>
                        <div class="progress">
                            <div class="progress-bar bg-success" style="width: 40%">20</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 敏感词统计展示 -->
        <div class="demo-section">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-filter"></i> 敏感词统计颜色</h5>
                </div>
                <div class="card-body text-center">
                    <h3 class="text-purple">156</h3>
                    <p class="text-muted">敏感词总数</p>
                    <p class="small">使用紫色替代原来的红色/黑色，更加美观协调</p>
                </div>
            </div>
        </div>
        
        <!-- 颜色方案说明 -->
        <div class="demo-section">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle"></i> 颜色方案说明</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>修改内容：</h6>
                            <ul>
                                <li>✅ 猫友类型徽章：红色/黑色 → 紫色渐变</li>
                                <li>✅ 点赞图标：红色 → 紫色</li>
                                <li>✅ 进度条：黑色 → 紫色渐变</li>
                                <li>✅ 敏感词统计：红色/黑色 → 紫色</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>设计理念：</h6>
                            <ul>
                                <li>🎨 避免使用红色和黑色</li>
                                <li>🌈 采用紫色渐变，更加现代美观</li>
                                <li>🎯 保持整体UI的协调性</li>
                                <li>💫 使用渐变效果增加视觉层次</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="alert alert-success mt-3">
                        <h6><i class="fas fa-check-circle"></i> 修改完成</h6>
                        <p class="mb-0">所有红色和黑色元素已替换为美观的紫色方案，符合整体UI设计美学。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

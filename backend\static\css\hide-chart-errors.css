/* 隐藏图表错误信息的CSS */

/* 隐藏图表容器中的错误文本 */
.chart-container > :not(canvas),
div:has(> canvas[id$="Chart"]) > :not(canvas) {
    display: none !important;
}

/* 确保canvas正确显示 */
canvas[id$="Chart"] {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    width: 100% !important;
    height: 100% !important;
    position: relative;
    z-index: 2;
}

/* 隐藏图表旁的错误文本 */
canvas[id$="Chart"] + * {
    display: none !important;
}

/* 为图表容器设置样式 */
.chart-container {
    position: relative;
    min-height: 250px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
} 
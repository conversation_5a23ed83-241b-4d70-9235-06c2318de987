import os

class Config:
    # 基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY', 'quiz_app_secret_key')
    
    # 数据库配置
    DB_USER = os.environ.get('DB_USER', 'root')
    # DB_USER = os.environ.get('DB_USER', 'quiz_app')
    DB_PASSWORD = os.environ.get('DB_PASSWORD', '123456')
    DB_HOST = os.environ.get('DB_HOST', 'localhost')
    DB_NAME = os.environ.get('DB_NAME', 'quiz_app')
    
    SQLALCHEMY_DATABASE_URI = f'mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}/{DB_NAME}'
    SQLALCHEMY_TRACK_MODIFICATIONS = False

class DevelopmentConfig(Config):
    DEBUG = True

class ProductionConfig(Config):
    DEBUG = False

# 根据环境变量选择配置
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}

def get_config():
    config_name = os.environ.get('FLASK_ENV', 'default')
    return config.get(config_name, config['default']) 
<!-- 用户状态指示器模板 -->
<!-- 使用方法: 在需要显示用户状态的地方引入此模板 -->
{% macro user_status_indicator(user_id, is_online=false, show_text=false, extra_class='') %}
    <div class="user-status-container {{ extra_class }}">
        <span class="user-status-indicator {{ 'user-online' if is_online else 'user-offline' }}" 
              data-user-id="{{ user_id }}" 
              title="{{ '在线' if is_online else '离线' }}"></span>
        {% if show_text %}
        <span class="status-label {{ 'online' if is_online else 'offline' }}">
            {{ '在线' if is_online else '离线' }}
        </span>
        {% endif %}
    </div>
{% endmacro %}

<!-- 带有状态指示器的用户头像 -->
{% macro user_avatar_with_status(user, extra_class='') %}
    <div class="user-avatar-with-status {{ extra_class }}">
        {% if user.avatar %}
            <img src="{{ user.avatar }}" alt="头像" class="user-avatar">
        {% else %}
            <div class="user-avatar">
                <i class="fas fa-user"></i>
            </div>
        {% endif %}
        <span class="user-status-indicator {{ 'user-online' if user.is_online else 'user-offline' }}" 
              data-user-id="{{ user.id }}" 
              title="{{ '在线' if user.is_online else '离线' }}"></span>
    </div>
{% endmacro %} 
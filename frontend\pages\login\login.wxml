<!-- login.wxml -->
<view class="container" style="padding-top: {{navigationBarHeight}}px;">
  <!-- 自定义导航栏 -->
  <view class="custom-navigation-bar" style="height: {{navigationBarHeight}}px;">
    <view class="navigation-bar-title" style="padding-top: {{statusBarHeight}}px;">大数据答题小程序</view>
  </view>
  
  <!-- 顶部Logo区域 -->
  <view class="logo-container">
    <view class="logo-circle">
      <view class="logo-content">
        <view class="logo-graphic">
          <view class="bar bar1"></view>
          <view class="bar bar2"></view>
          <view class="bar bar3"></view>
          <view class="data-dot dot1"></view>
          <view class="data-dot dot2"></view>
          <view class="data-dot dot3"></view>
        </view>
      </view>
    </view>
    <text class="app-name">大数据答题小程序</text>
    <text class="app-desc">让学习变得更有趣</text>
  </view>
  
  <!-- 主要内容卡片 -->
  <view class="main-card">
    <form bindsubmit="onSubmitLogin">
      <view class="profile-form">
        <view class="avatar-section">
          <text class="section-title">选择头像</text>
          <view class="avatar-container">
            <button class="avatar-wrapper" open-type="chooseAvatar" bindchooseavatar="onChooseAvatar">
              <image class="avatar" src="{{avatarUrl}}" mode="aspectFill"></image>
              <view class="avatar-overlay">
                <text class="avatar-icon">📷</text>
              </view>
            </button>
          </view>
          <text class="avatar-tip">点击更换头像</text>
        </view>
        
        <view class="nickname-section">
          <text class="section-title">设置昵称</text>
          <view class="input-container">
            <view class="input-prefix">
              <text class="input-prefix-icon">👤</text>
            </view>
            <input type="nickname" name="nickname" placeholder="请输入昵称" value="{{nickName}}" class="nickname-input" bindinput="onNicknameInput" />
            <text class="input-icon" wx:if="{{nickName}}">✓</text>
          </view>
          <text class="nickname-tip">用于小程序中显示</text>
        </view>
        
        <button class="login-btn wxlogin-btn" form-type="submit" loading="{{isLogging}}" hover-class="button-hover">
          <text class="btn-icon">🔑</text> 登录
        </button>
      </view>
    </form>
    
    <!-- 游客登录按钮 -->
    <!-- <button class="login-btn visitor-btn" bindtap="visitorLogin" loading="{{isLogging}}" hover-class="button-hover">
      <text class="btn-icon">👤</text> 游客登录
    </button> -->
    
    <!-- 用户协议 -->
    <view class="agreement-section">
      <text class="agreement-text">登录时建议用自己</text>
      <text class="agreement-link">班级</text>
      <text class="agreement-text">和</text>
      <text class="agreement-link">姓名</text>
    </view>
  </view>
  
  <!-- 波浪背景效果 -->
  <view class="wave-container">
    <view class="wave wave1"></view>
    <view class="wave wave2"></view>
  </view>
</view> 
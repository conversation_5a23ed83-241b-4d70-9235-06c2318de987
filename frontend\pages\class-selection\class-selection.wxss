.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f6f6f6;
  box-sizing: border-box;
  padding: 0;
}

.custom-header {
  background-color: #4e8df7;
  padding: 66rpx 40rpx 30rpx;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
}

.header-content {
  display: flex;
  flex-direction: column;
}

.page-title {
  font-size: 36rpx;
  font-weight: 500;
  margin-bottom: 6rpx;
}

.subtitle {
  font-size: 24rpx;
  opacity: 0.85;
}

.header-tools {
  display: flex;
  align-items: center;
}

.tool-item {
  margin-left: 20rpx;
  height: 60rpx;
  width: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 30rpx;
}

.tool-icon {
  width: 40rpx;
  height: 40rpx;
  position: relative;
}

.dot-menu::before {
  content: "";
  position: absolute;
  width: 5rpx;
  height: 5rpx;
  background-color: white;
  border-radius: 50%;
  top: 18rpx;
  left: 18rpx;
  box-shadow: 0 -10rpx 0 white, 0 10rpx 0 white;
}

.circle {
  border: 2rpx solid white;
  border-radius: 50%;
}

.class-list {
  flex: 1;
  overflow-y: auto;
  padding: 20rpx 30rpx;
}

.loading, .error, .empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
  color: #888;
  font-size: 28rpx;
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #e0e0e0;
  border-top: 6rpx solid #4e8df7;
  border-radius: 50%;
  margin-bottom: 20rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.retry-btn {
  margin-top: 20rpx;
  font-size: 28rpx;
  background: #4e8df7;
  color: white;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
}

.class-cards {
  display: flex;
  flex-direction: column;
}

.class-card {
  background: white;
  border-radius: 8rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 1rpx 6rpx rgba(0,0,0,0.03);
  position: relative;
  border: 1rpx solid transparent;
}

.class-card.selected {
  background: #edf3ff;
  border: 2rpx solid #4e8df7;
}

.class-card-content {
  display: flex;
  align-items: center;
}

.class-icon {
  margin-right: 24rpx;
  flex-shrink: 0;
}

.icon-circle {
  width: 80rpx;
  height: 80rpx;
  background: #4e8df7;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  font-weight: 500;
}

.class-info {
  flex: 1;
}

.class-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.class-desc {
  font-size: 22rpx;
  color: #999;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.footer {
  padding: 10rpx 30rpx 50rpx;
  background-color: white;
  border-top: 1rpx solid #f0f0f0;
}

.confirm-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  background: #f5f5f5;
  color: #999;
  font-size: 30rpx;
  border-radius: 8rpx;
  text-align: center;
  border: none;
  padding: 0;
}

.confirm-btn.disabled {
  background: #f5f5f5;
  color: #999;
}

.confirm-btn.active {
  background: #4e8df7;
  color: white;
}
{% extends "admin/base.html" %}

{% block title %}
{% if action == 'add' %}
添加题库 - 题库管理系统
{% else %}
编辑题库 - 题库管理系统
{% endif %}
{% endblock %}

{% block header %}
<div class="d-flex align-items-center">
    <a href="/admin/question-banks" class="btn btn-link text-decoration-none text-dark p-0 me-3">
        <i class="fas fa-arrow-left"></i> 返回题库列表
    </a>
    <span>{% if action == 'add' %}添加题库{% else %}编辑题库{% endif %}</span>
</div>
{% endblock %}

{% block header_buttons %}{% endblock %}

{% block content %}
<div class="card shadow-sm">
    <div class="card-header bg-white">
        <div class="d-flex align-items-center">
            <i class="fas fa-edit me-2 text-primary"></i>
            <span class="fw-bold">{% if action == 'add' %}创建新题库{% else %}修改题库信息{% endif %}</span>
        </div>
    </div>
    <div class="card-body">
        <form method="post" action="{% if action == 'add' %}/admin/question-banks/add{% else %}/admin/question-banks/edit/{{ bank.id }}{% endif %}">
            <div class="mb-3">
                <label for="name" class="form-label">题库名称 <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="name" name="name" required 
                       value="{% if action == 'edit' %}{{ bank.name }}{% endif %}" placeholder="请输入题库名称">
                <div class="form-text text-muted">例如：Python基础知识、数据库原理期末题库等</div>
            </div>
            
            <div class="mb-3">
                <label for="course_id" class="form-label">所属课程 <span class="text-danger">*</span></label>
                <select class="form-select" id="course_id" name="course_id" required>
                    <option value="">请选择课程</option>
                    {% for course in courses %}
                    <option value="{{ course.id }}" {% if action == 'edit' and bank.course_id == course.id %}selected{% endif %}>
                        {{ course.name }} 
                        {% if course.class_obj is defined and course.class_obj %}
                            ({{ course.class_obj.name }})
                        {% elif course.classes is defined and course.classes %}
                            {% if course.classes|length > 0 %}
                                ({{ course.classes[0].name }})
                            {% else %}
                                (无班级)
                            {% endif %}
                        {% else %}
                            (无班级)
                        {% endif %}
                    </option>
                    {% endfor %}
                </select>
                {% if courses|length == 0 %}
                <div class="alert alert-warning mt-2">
                    <div class="d-flex">
                        <i class="fas fa-exclamation-triangle me-3 fa-2x"></i>
                        <div>
                            <strong>注意!</strong>
                            <p class="mb-0">没有可用的课程，请先 <a href="/admin/courses/add">添加课程</a>。</p>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
            
            <div class="mb-3">
                <label for="description" class="form-label">题库描述</label>
                <textarea class="form-control" id="description" name="description" rows="4" placeholder="请输入题库描述信息">{% if action == 'edit' %}{{ bank.description }}{% endif %}</textarea>
                <div class="form-text text-muted">可选：对题库的详细描述，包括题库用途、覆盖范围等</div>
            </div>
            
            <div class="form-check form-switch mb-4">
                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" {% if action == 'edit' and bank.is_active %}checked{% endif %}>
                <label class="form-check-label" for="is_active">立即上架</label>
                <div class="form-text text-muted">开启后，该题库将立即可用于学生答题</div>
            </div>
            
            <div class="d-flex justify-content-between align-items-center">
                <a href="/admin/question-banks" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>取消
                </a>
                <button type="submit" class="btn btn-primary" {% if courses|length == 0 %}disabled{% endif %}>
                    {% if action == 'add' %}
                    <i class="fas fa-plus me-1"></i>添加题库
                    {% else %}
                    <i class="fas fa-save me-1"></i>保存修改
                    {% endif %}
                </button>
            </div>
        </form>
    </div>
</div>

<!-- 底部返回按钮 -->
<div class="d-flex justify-content-center mt-4 mb-4">
    <a href="/admin/question-banks" class="btn btn-outline-primary" style="min-width: 120px;">
        <i class="fas fa-arrow-left me-2"></i>返回
    </a>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .btn-link:hover {
        color: var(--primary-color) !important;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #4c51bf;
        box-shadow: 0 0 0 0.25rem rgba(76, 81, 191, 0.25);
    }
    
    .form-check-input:checked {
        background-color: #4c51bf;
        border-color: #4c51bf;
    }
</style>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // 使用Select2增强下拉框体验
        $('#course_id').select2({
            placeholder: '请选择所属课程',
            allowClear: true
        });
    });
</script>
{% endblock %} 
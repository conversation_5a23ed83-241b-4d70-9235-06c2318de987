# 在宝塔面板部署大数据题库应用

本文档提供在宝塔面板上部署大数据题库应用的详细步骤。

## 准备工作

1. 一台安装了宝塔面板的Linux服务器（推荐CentOS 7+）
2. 已绑定域名 `www.qiangs.xyz` 到服务器IP
3. 宝塔面板已安装以下软件：
   - Nginx 1.18+
   - Python 3.8+
   - MySQL 5.7+

## 部署步骤

### 1. 创建网站

1. 登录宝塔面板
2. 点击 "网站" -> "添加站点"
3. 填写以下信息：
   - 域名：`www.qiangs.xyz`（可以添加多个域名，如需要添加不带www的`qiangs.xyz`）
   - 网站目录：`/www/wwwroot/www.qiangs.xyz`
   - 数据库：选择MySQL，创建新数据库（也可以选择SQLite）
   - PHP版本：纯静态
   - 其他选项保持默认

### 2. 上传应用文件

1. 将本地项目文件打包（backend目录）
2. 在宝塔面板中点击 "文件" 进入 `/www/wwwroot/www.qiangs.xyz`
3. 上传并解压应用文件
4. 确保所有文件都在 `/www/wwwroot/www.qiangs.xyz` 目录中

### 3. 安装Python依赖

在宝塔面板中打开终端，执行以下命令：

```bash
cd /www/wwwroot/www.qiangs.xyz
pip3 install -r requirements.txt
```

### 4. 配置uWSGI

1. 确保uwsgi.ini文件存在于网站根目录
2. 在终端执行：

```bash
cd /www/wwwroot/www.qiangs.xyz
uwsgi --ini uwsgi.ini
```

### 5. 配置Nginx

1. 在宝塔面板中点击 "网站" -> 找到 `www.qiangs.xyz` -> "设置"
2. 点击 "配置文件"
3. 将本项目中的 `nginx_config.conf` 文件内容复制替换现有配置
4. 保存配置并重启Nginx：点击"保存"或在终端执行 `service nginx restart`

### 6. 设置SSL证书（可选但推荐）

1. 在宝塔面板中点击 "网站" -> 找到 `www.qiangs.xyz` -> "设置" -> "SSL"
2. 选择 "Let's Encrypt" 免费证书
3. 勾选你的域名并点击申请
4. 证书申请成功后，点击"强制HTTPS"

### 7. 测试应用

1. 在浏览器中访问 `https://www.qiangs.xyz/admin/login`
2. 使用默认凭据登录：
   - 用户名：admin
   - 密码：admin123
3. 如果能够成功登录到管理界面，则部署成功

### 8. 配置小程序前端

1. 确保在小程序前端的 `app.js` 中，将 `baseUrl` 更新为 `'https://www.qiangs.xyz/api'`
2. 重新编译小程序并测试连接

## 故障排除

### 问题：无法访问应用
- 检查Nginx配置是否正确
- 检查uWSGI是否正在运行：`ps aux | grep uwsgi`
- 查看Nginx错误日志：`/www/wwwlogs/www.qiangs.xyz.error.log`
- 查看uWSGI日志：`/www/wwwroot/www.qiangs.xyz/uwsgi.log`

### 问题：数据库连接错误
- 检查数据库配置和凭据
- 确保数据库用户有正确的访问权限

### 问题：文件权限错误
- 执行以下命令设置正确的权限：
  ```bash
  chown -R www:www /www/wwwroot/www.qiangs.xyz
  chmod -R 755 /www/wwwroot/www.qiangs.xyz
  ```

## 维护

### 更新应用
使用deploy.sh脚本进行应用更新：
```bash
cd /www/wwwroot/www.qiangs.xyz
bash deploy.sh
```

### 重启应用
```bash
# 停止uWSGI
uwsgi --stop /www/wwwroot/www.qiangs.xyz/uwsgi.pid

# 启动uWSGI
cd /www/wwwroot/www.qiangs.xyz
uwsgi --ini uwsgi.ini
```

### 查看日志
```bash
# Nginx访问日志
tail -f /www/wwwlogs/www.qiangs.xyz.access.log

# Nginx错误日志
tail -f /www/wwwlogs/www.qiangs.xyz.error.log

# uWSGI日志
tail -f /www/wwwroot/www.qiangs.xyz/uwsgi.log
``` 
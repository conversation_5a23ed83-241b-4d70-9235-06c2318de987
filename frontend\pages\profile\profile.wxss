/* pages/profile/profile.wxss */
.container {
  min-height: 100vh;
  padding: 30rpx;
  padding-bottom: 140rpx; /* 为底部tabBar留出足够空间 */
  background-color: #f5f7fa;
}

/* 用户信息卡片 */
.user-card {
  display: flex;
  align-items: center;
  background-color: #4C84FF;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  color: white;
  box-shadow: 0 4rpx 20rpx rgba(76, 132, 255, 0.3);
}

.avatar-container {
  margin-right: 30rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.5);
}

.user-info {
  flex: 1;
}

.username {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  display: block;
}

.user-desc {
  font-size: 24rpx;
  opacity: 0.8;
  margin-bottom: 20rpx;
  display: block;
}

.user-streak {
  margin-top: 15rpx;
}

.streak-text {
  font-size: 24rpx;
  margin-bottom: 10rpx;
  display: block;
}

.streak-bar {
  width: 100%;
  height: 12rpx;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 6rpx;
  overflow: hidden;
}

.streak-progress {
  height: 100%;
  background-color: white;
  border-radius: 6rpx;
}

/* 统计卡片 */
.stats-card {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

.stats-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.stats-grid {
  display: flex;
  flex-wrap: wrap;
}

.stats-item {
  width: 50%;
  text-align: center;
  margin-bottom: 20rpx;
}

.stats-value {
  font-size: 40rpx;
  color: #4C84FF;
  font-weight: bold;
  display: block;
  margin-bottom: 5rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #999;
}

/* 功能列表 */
.function-list {
  background-color: white;
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

.function-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.function-item:last-child {
  border-bottom: none;
}

.function-icon {
  width: 80rpx;
  height: 80rpx;
  background-color: #eef5ff;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.icon-text {
  font-size: 36rpx;
}

.function-info {
  flex: 1;
}

.function-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 5rpx;
}

.function-desc {
  font-size: 24rpx;
  color: #999;
}

.function-arrow {
  color: #ccc;
  font-size: 36rpx;
}

/* 退出登录按钮 */
.logout-container {
  margin: 40rpx 0;
}

.logout-button {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: white;
  color: #ff4d4f;
  border: none;
  border-radius: 45rpx;
  font-size: 32rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 加载提示 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
}

.loading {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #4C84FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 版本信息 */
.version-info {
  text-align: center;
  margin: 20rpx 0 40rpx;
}

.version-text {
  font-size: 24rpx;
  color: #999;
} 
{"singleChoice": [{"id": 1, "question": "第一次信息化浪潮主要解决什么问题？", "options": ["信息传输", "信息处理", "信息爆炸", "信息转换"], "answer": 1}, {"id": 2, "question": "下面哪个选项属于大数据技术的数据存储和管理技术层面的功能？", "options": ["利用分布式文件系统、数据仓库、关系数据库等实现对结构化、半结构化和非结构化海量数据的存储和管理。", "利用分布式并行编程模型和计算框架，结合机器学习和数据挖掘算法，实现对海量数据的处理和分析", "构建隐私数据保护体系和数据安全体系，有效保护个人隐私和数据安全", "把实时采集的数据作为流计算系统的输入，进行实时处理分析"], "answer": 0}, {"id": 3, "question": "在大数据的计算模式中，流计算解决的是什么问题？", "options": ["针对大规模数据的批量处理", "针对大规模图结构数据的处理", "大规模数据的存储管理和查询分析", "针对流数据的实时计算"], "answer": 3}, {"id": 4, "question": "大数据产业指什么？", "options": ["一切与支撑大数据组织管理和价值发现相关的企业经济活动的集合", "提供智能交通、智慧医疗、智能物流、智能电网等行业应用的企业", "提供数据分享平台、数据分析平台、数据租售平台等服务的企业", "提供分布式计算、数据挖掘、统计分析等服务的各类企业"], "answer": 0}, {"id": 5, "question": "下列哪一个不属于大数据产业的产业链环节？", "options": ["数据循环层", "数据源层", "数据分析层", "数据应用层"], "answer": 0}, {"id": 6, "question": "下列哪一个不属于第三次信息化浪潮中新兴的技术？", "options": ["互联网", "云计算", "大数据", "物联网"], "answer": 0}, {"id": 7, "question": "云计算平台层（PaaS）指的是什么？", "options": ["操作系统和围绕特定应用的必需的服务", "将基础设施(计算资源和存储)作为服务出租", "从一个集中的系统部署软件，使之在一台本地计算机上(或从云中远程地)运行的一个模型", "提供硬件、软件、网络等基础设施以及提供咨询、规划和系统集成服务"], "answer": 0}, {"id": 8, "question": "下面关于云计算数据中心的描述正确的是：", "options": ["数据中心是云计算的重要载体，为各种平台和应用提供运行支撑环境", "数据中心就是放在企业内部的一台中心服务器", "每个企业都需要建设一个云计算数据中心", "数据中心不需要网络带宽的支撑"], "answer": 0}, {"id": 9, "question": "下列哪个不属于物联网的应用？", "options": ["智能物流", "智能安防", "环保监测", "数据清洗"], "answer": 3}, {"id": 10, "question": "下列哪项不属于大数据的发展历程？", "options": ["成熟期", "萌芽期", "大规模应用期", "迷茫期"], "answer": 3}, {"id": 11, "question": "下列哪个不属于Hadoop的特性？", "options": ["成本高", "高可靠性", "高容错性", "运行在 Linux 平台上"], "answer": 0}, {"id": 12, "question": "Hadoop框架中最核心的设计是什么？", "options": ["为海量数据提供存储的HDFS和对数据进行计算的MapReduce", "提供整个HDFS文件系统的NameSpace(命名空间)管理、块管理等所有服务", "Hadoop不仅可以运行在企业内部的集群中，也可以运行在云计算环境中", "Hadoop被视为事实上的大数据处理标准"], "answer": 0}, {"id": 13, "question": "在一个基本的Hadoop集群中，DataNode主要负责什么？", "options": ["负责执行由JobTracker指派的任务", "协调数据计算任务", "负责协调集群中的数据存储", "存储被拆分的数据块"], "answer": 3}, {"id": 14, "question": "Hadoop最初是由谁创建的？", "options": ["Lucene", "<PERSON>", "Apache", "MapReduce"], "answer": 1}, {"id": 15, "question": "下列哪一个不属于Hadoop的大数据层的功能？", "options": ["数据挖掘", "离线分析", "实时计算", "BI分析"], "answer": 2}, {"id": 16, "question": "在一个基本的Hadoop集群中，SecondaryNameNode主要负责什么？", "options": ["帮助 NameNode 收集文件系统运行的状态信息", "负责执行由 JobTracker 指派的任务", "协调数据计算任务", "负责协调集群中的数据存储"], "answer": 0}, {"id": 17, "question": "下面哪一项不是Hadoop的特性？", "options": ["可扩展性高", "只支持少数几种编程语言", "成本低", "能在linux上运行"], "answer": 1}, {"id": 18, "question": "在Hadoop项目结构中，HDFS指的是什么？", "options": ["分布式文件系统", "分布式并行编程模型", "资源管理和调度器", "<PERSON><PERSON>上的数据仓库"], "answer": 0}, {"id": 19, "question": "在Hadoop项目结构中，MapReduce指的是什么？", "options": ["分布式并行编程模型", "流计算框架", "Hadoop上的工作流管理系统", "提供分布式协调一致性服务"], "answer": 0}, {"id": 20, "question": "下面哪个不是Hadoop1.0的组件：", "options": ["HDFS", "MapReduce", "YARN", "NameNode 和 DataNode"], "answer": 2}, {"id": 21, "question": "分布式文件系统指的是什么？", "options": ["把文件分布存储到多个计算机节点上，成千上万的计算机节点构成计算机集群", "用于在 Hadoop 与传统数据库之间进行数据传递", "一个高可用的，高可靠的，分布式的海量日志采集、聚合和传输的系统", "一种高吞吐量的分布式发布订阅消息系统，可以处理消费者规模的网站中的所有动作流数据"], "answer": 0}, {"id": 22, "question": "下面哪一项不属于计算机集群中的节点？", "options": ["主节点(Master Node)", "源节点（SourceNode）", "名称结点(NameNode)", "从节点（Slave Node）"], "answer": 1}, {"id": 23, "question": "在HDFS中，默认一个块多大？", "options": ["64MB", "32KB", "128KB", "16KB"], "answer": 0}, {"id": 24, "question": "下列哪一项不属于HDFS采用抽象的块概念带来的好处？", "options": ["简化系统设计", "支持大规模文件存储", "强大的跨平台兼容性", "适合数据备份"], "answer": 2}, {"id": 25, "question": "在HDFS中，NameNode的主要功能是什么？", "options": ["维护了block id 到datanode本地文件的映射关系", "存储文件内容", "文件内存保存在磁盘中", "存储元数据"], "answer": 3}, {"id": 26, "question": "下面对FsImage的描述，哪个是错误的？", "options": ["FsImage文件没有记录每个块存储在哪个数据节点", "FsImage文件包含文件系统中所有目录和文件inode的序列化形式", "FsImage用于维护文件系统树以及文件树中所有的文件和文件夹的元数据", "FsImage文件记录了每个块具体被存储在哪个数据节点"], "answer": 3}, {"id": 27, "question": "下面对SecondaryNameNode第二名称节点的描述，哪个是错误的？", "options": ["SecondaryNameNode一般是并行运行在多台机器上", "它是用来保存名称节点中对HDFS元数据信息的备份，并减少名称节点重启的时间", "SecondaryNameNode通过HTTPGET方式从NameNode上获取到FsImage和EditLog文件，并下载到本地的相应目录下", "SecondaryNameNode是HDFS架构中的一个组成部分"], "answer": 0}, {"id": 28, "question": "HDFS采用了什么模型？", "options": ["分层模型", "主从结构模型", "管道-过滤器模型", "点对点模型"], "answer": 1}, {"id": 29, "question": "下列关于HDFS的描述，哪个不正确？", "options": ["HDFS还采用了相应的数据存放、数据读取和数据复制策略，来提升系统整体读写响应性能", "HDFS采用了主从（Master/Slave）结构模型", "HDFS采用了冗余数据存储，增强了数据可靠性", "HDFS采用块的概念，使得系统的设计变得更加复杂"], "answer": 3}, {"id": 30, "question": "下列关于BigTable的描述，哪个是错误的？", "options": ["爬虫持续不断地抓取新页面，这些页面每隔一段时间地存储到BigTable里", "BigTable是一个分布式存储系统", "BigTable起初用于解决典型的互联网搜索问题", "网络搜索应用查询建立好的索引，从BigTable得到网页"], "answer": 0}, {"id": 31, "question": "下列选项中，关于HBase和BigTable的底层技术对应关系，哪个是错误的？", "options": ["GFS与HDFS相对应", "GFS与Zookeeper相对应", "MapReduce与Hadoop MapReduce相对应", "Chubby与Zookeeper相对应"], "answer": 1}, {"id": 32, "question": "在HBase中，关于数据操作的描述，下列哪一项是错误的？", "options": ["HBase采用了更加简单的数据模型，它把数据存储为未经解释的字符串", "HBase操作不存在复杂的表与表之间的关系", "HBase不支持修改操作", "HBase在设计上就避免了复杂的表和表之间的关系"], "answer": 2}, {"id": 33, "question": "在HBase访问接口中，Pig主要用在哪个场合？", "options": ["适合Hadoop MapReduce作业并行批处理HBase表数据", "适合HBase管理使用", "适合其他异构系统在线访问HBase表数据", "适合做数据统计"], "answer": 3}, {"id": 34, "question": "HBase中需要根据某些因素来确定一个单元格，这些因素可以视为一个\"四维坐标\"，下面哪个不属于\"四维坐标\"？", "options": ["行键", "关键字", "列族", "时间戳"], "answer": 1}, {"id": 35, "question": "关于HBase的三层结构中各层次的名称和作用的说法，哪个是错误的？", "options": ["Zookeeper文件记录了用户数据表的Region位置信息", "-ROOT-表记录了.META.表的Region位置信息", ".META.表保存了HBase中所有用户数据表的Region位置信息", "Zookeeper文件记录了-ROOT-表的位置信息"], "answer": 0}, {"id": 36, "question": "下面关于主服务器Master主要负责表和Region的管理工作的描述，哪个是错误的？", "options": ["在Region分裂或合并后，负责重新调整Region的分布", "对发生故障失效的Region服务器上的Region进行迁移", "管理用户对表的增加、删除、修改、查询等操作", "不支持不同Region服务器之间的负载均衡"], "answer": 3}, {"id": 37, "question": "HBase只有一个针对行健的索引，如果要访问HBase表中的行，下面哪种方式是不可行的？", "options": ["通过单个行健访问", "通过时间戳访问", "通过一个行健的区间来访问", "全表扫描"], "answer": 1}, {"id": 38, "question": "下面关于Region的说法，哪个是错误的？", "options": ["同一个Region不会被分拆到多个Region服务器", "为了加快访问速度，.META.表的全部Region都会被保存在内存中", "一个-ROOT-表可以有多个Region", "为了加速寻址，客户端会缓存位置信息，同时，需要解决缓存失效问题"], "answer": 2}, {"id": 39, "question": "下列哪个不属于NoSQL数据库的特点？", "options": ["灵活的可扩展性", "灵活的数据模型", "与云计算紧密融合", "数据存储规模有限"], "answer": 3}, {"id": 40, "question": "下面关于NoSQL和关系数据库的简单比较，哪个是错误的？", "options": ["RDBMS有关系代数理论作为基础，NoSQL没有统一的理论基础", "NoSQL很难实现横向扩展，RDBMS可以很容易通过添加更多设备来支持更大规模的数据", "RDBMS需要定义数据库模式，严格遵守数据定义，NoSQL一般不存在数据库模式，可以自由灵活定义并存储各种不同类型的数据", "RDBMS借助于索引机制可以实现快速查询，很多NoSQL数据库没有面向复杂查询的索引"], "answer": 1}, {"id": 41, "question": "下列哪一项不属于NoSQL的四大类型？", "options": ["文档数据库", "图数据库", "列族数据库", "时间戳数据库"], "answer": 3}, {"id": 42, "question": "下列关于键值数据库的描述，哪一项是错误的？", "options": ["扩展性好，灵活性好", "大量写操作时性能高", "无法存储结构化信息", "条件查询效率高"], "answer": 3}, {"id": 43, "question": "下列关于列族数据库的描述，哪一项是错误的？", "options": ["查找速度慢，可扩展性差", "功能较少，大都不支持强事务一致性", "容易进行分布式扩展", "复杂性低"], "answer": 0}, {"id": 44, "question": "下列哪一项不属于数据库事务具有ACID四性？", "options": ["间断性", "原子性", "一致性", "持久性"], "answer": 0}, {"id": 45, "question": "下面关于MongoDB说法，哪一项是正确的？", "options": ["具有较差的水平可扩展性", "设置个别属性的索引来实现更快的排序", "提供了一个面向文档存储，操作复杂", "可以实现替换完成的文档（数据）或者一些指定的数据字段"], "answer": 3}, {"id": 46, "question": "下列关于NoSQL与关系数据库的比较，哪个说法是错误的？", "options": ["在一致性方面，RDBMS强于NoSQL", "在数据完整性方面，RDBMS容易实现", "在扩展性方面，NoSQL 比较好", "在可用性方面，NoSQL优于RDBMS"], "answer": 3}, {"id": 47, "question": "关于文档数据库的说法，下列哪一项是错误的？", "options": ["数据是规则的", "性能好（高并发）", "缺乏统一的查询语法", "复杂性低"], "answer": 0}, {"id": 48, "question": "下列哪个不属于云计算的优势？", "options": ["按需服务", "随时服务", "通用性", "价格不菲"], "answer": 3}, {"id": 49, "question": "下列关于云数据库的描述，哪个是错误的？", "options": ["云数据库是部署和虚拟化在云计算环境中的数据库", "云数据库是在云计算的大背景下发展起来的一种新兴的共享基础架构的方法", "云数据库价格不菲，维护费用极其昂贵", "云数据库具有高可扩展性、高可用性、采用多租形式和支持资源有效分发等特点"], "answer": 2}, {"id": 50, "question": "下列哪一个不属于云数据库产品？", "options": ["本地安装MySQL", "阿里云RDS", "Oracle Cloud", "百度云数据库"], "answer": 0}], "multipleChoice": [{"id": 1, "question": "第三次信息化浪潮的标志是哪些技术的兴起？", "options": ["个人计算机", "物联网", "云计算", "大数据"], "answer": [1, 2, 3]}, {"id": 2, "question": "信息科技为大数据时代提供哪些技术支撑？", "options": ["存储设备容量不断增加", "网络带宽不断增加", "CPU 处理能力大幅提升", "数据量不断增大"], "answer": [0, 1, 2]}, {"id": 3, "question": "大数据具有哪些特点？", "options": ["数据的大量化", "数据的快速化", "数据的多样化", "数据的价值密度比较低"], "answer": [0, 1, 2, 3]}, {"id": 4, "question": "下面哪个属于大数据的应用领域？", "options": ["智能医疗研发", "监控身体情况", "实时掌握交通状况", "金融交易"], "answer": [0, 1, 2, 3]}, {"id": 5, "question": "大数据的两个核心技术是什么？", "options": ["分布式存储", "分布式应用", "分布式处理", "集中式存储"], "answer": [0, 2]}, {"id": 6, "question": "云计算关键技术包括什么？", "options": ["分布式存储", "虚拟化", "分布式计算", "多租户"], "answer": [0, 1, 2, 3]}, {"id": 7, "question": "云计算的服务模式和类型主要包括哪三类？", "options": ["软件即服务（SaaS）", "平台即服务（PaaS）", "基础设施即服务（IaaS）", "数据采集即服务（DaaS）"], "answer": [0, 1, 2]}, {"id": 8, "question": "物联网主要由下列哪些部分组成的？", "options": ["应用层", "处理层", "感知层", "网络层"], "answer": [0, 1, 2, 3]}, {"id": 9, "question": "物联网的关键技术包括哪些？", "options": ["识别和感知技术", "网络与通信技术", "数据挖掘与融合技术", "信息处理一体化技术"], "answer": [0, 1, 2]}, {"id": 10, "question": "大数据对社会发展的影响有哪些？", "options": ["大数据成为一种新的决策方式", "大数据应用促进信息技术与各行业的深度融合", "大数据开发推动新技术和新应用的不断涌现", "大数据对社会发展没有产生积极影响"], "answer": [0, 1, 2]}, {"id": 11, "question": "Hadoop的特性包括哪些？", "options": ["高可扩展性", "支持多种编程语言", "成本低", "运行在Linux平台上"], "answer": [0, 1, 2, 3]}, {"id": 12, "question": "下面哪个是Hadoop2.0的组件？", "options": ["ResourceManager", "JobTracker", "TaskTracker", "NodeManager"], "answer": [0, 3]}, {"id": 13, "question": "一个基本的Hadoop集群中的节点主要包括什么？", "options": ["DataNode：存储被拆分的数据块", "JobTracker：协调数据计算任务", "TaskTracker：负责执行由JobTracker指派的任务", "SecondaryNameNode：帮助NameNode收集文件系统运行的状态信息"], "answer": [0, 1, 2, 3]}, {"id": 14, "question": "下列关于Hadoop的描述，哪些是正确的？", "options": ["为用户提供了系统底层细节透明的分布式基础架构", "具有很好的跨平台特性", "可以部署在廉价的计算机集群中", "曾经被公认为行业大数据标准开源软件"], "answer": [0, 1, 2, 3]}, {"id": 15, "question": "Hadoop集群的整体性能主要受到什么因素影响？", "options": ["CPU性能", "内存", "网络", "存储容量"], "answer": [0, 1, 2, 3]}, {"id": 16, "question": "HDFS要实现以下哪几个目标？", "options": ["兼容廉价的硬件设备", "流数据读写", "大数据集", "复杂的文件模型"], "answer": [0, 1, 2]}, {"id": 17, "question": "HDFS特殊的设计，在实现优良特性的同时，也使得自身具有一些应用局限性，主要包括以下哪几个方面？", "options": ["较差的跨平台兼容性", "无法高效存储大量小文件", "不支持多用户写入及任意修改文件", "不适合低延迟数据访问"], "answer": [1, 2, 3]}, {"id": 18, "question": "HDFS采用抽象的块概念可以带来以下哪几个明显的好处？", "options": ["支持大规模文件存储", "支持小规模文件存储", "适合数据备份", "简化系统设计"], "answer": [0, 2, 3]}, {"id": 19, "question": "在HDFS中，名称节点（NameNode）主要保存了哪些核心的数据结构？", "options": ["FsImage", "DN8", "Block", "EditLog"], "answer": [0, 3]}, {"id": 20, "question": "数据节点（DataNode）的主要功能包括哪些？", "options": ["负责数据的存储和读取", "根据客户端或者是名称节点的调度来进行数据的存储和检索", "向名称节点定期发送自己所存储的块的列表", "用来保存名称节点中对HDFS元数据信息的备份，并减少名称节点重启的时间"], "answer": [0, 1, 2]}, {"id": 21, "question": "HDFS的命名空间包含什么？", "options": ["磁盘", "文件", "块", "目录"], "answer": [1, 2, 3]}, {"id": 22, "question": "下列对于客户端的描述，哪些是正确的？", "options": ["客户端是用户操作HDFS最常用的方式，HDFS在部署时都提供了客户端", "HDFS客户端是一个库，暴露了HDFS文件系统接口", "严格来说，客户端并不算是HDFS的一部分", "客户端可以支持打开、读取、写入等常见的操作"], "answer": [0, 1, 2, 3]}, {"id": 23, "question": "HDFS只设置唯一一个名称节点，这样做虽然大大简化了系统设计，但也带来了哪些明显的局限性？", "options": ["命名空间的限制", "性能的瓶颈", "隔离问题", "集群的可用性"], "answer": [0, 1, 2, 3]}, {"id": 24, "question": "HDFS数据块多副本存储具备以下哪些优点？", "options": ["加快数据传输速度", "容易检查数据错误", "保证数据可靠性", "适合多平台上运行"], "answer": [0, 1, 2]}, {"id": 25, "question": "HDFS具有较高的容错性，设计了哪些相应的机制检测数据错误和进行自动恢复？", "options": ["数据源太大", "数据节点出错", "数据出错", "名称节点出错"], "answer": [1, 2, 3]}, {"id": 26, "question": "关系数据库已经无法满足Web2.0的需求，主要表现在以下几个方面？", "options": ["无法满足海量数据的管理需求", "无法满足数据高并发的需求", "无法满足高可扩展性和高可用性的需求", "使用难度高"], "answer": [0, 1, 2]}, {"id": 27, "question": "下列关于MySQL集群的描述，哪些是正确的？", "options": ["复杂性：部署、管理、配置很复杂", "数据库复制：MySQL主备之间一般采用复制方式，很多时候是异步复制", "扩容问题：如果系统压力过大需要增加新的机器，这个过程涉及数据重新划分", "动态数据迁移问题：如果某个数据库组压力过大，需要将其中部分数据迁移出去"], "answer": [0, 1, 2, 3]}, {"id": 28, "question": "关系数据库引以为傲的两个关键特性（完善的事务机制和高效的查询机制），到了Web2.0时代却成了鸡肋，主要表现在以下哪几个方面？", "options": ["Web2.0 网站系统通常不要求严格的数据库事务", "Web2.0 网站系统基本上不用关系数据库来存储数据", "Web2.0 并不要求严格的读写实时性", "Web2.0 通常不包含大量复杂的SQL 查询"], "answer": [0, 2, 3]}, {"id": 29, "question": "下面关于NoSQL与关系数据库的比较，哪些是正确的？", "options": ["关系数据库以完善的关系代数理论作为基础，有严格的标准", "关系数据库可扩展性较差，无法较好支持海量数据存储", "NoSQL可以支持超大规模数据存储", "NoSQL 数据库缺乏数学理论基础，复杂查询性能不高"], "answer": [0, 1, 2, 3]}, {"id": 30, "question": "下列关于文档数据库的描述，哪些是正确的？", "options": ["性能好（高并发），灵活性高", "具备统一的查询语法", "文档数据库支持文档间的事务", "复杂性低，数据结构灵活"], "answer": [0, 3]}, {"id": 31, "question": "下列关于图数据库的描述，哪些是正确的？", "options": ["专门用于处理具有高度相互关联关系的数据", "比较适合于社交网络、模式识别、依赖分析、推荐系统以及路径寻找等问题", "灵活性高，支持复杂的图算法", "复杂性高，只能支持一定的数据规模"], "answer": [0, 1, 2, 3]}, {"id": 32, "question": "NoSQL的三大基石？", "options": ["CAP", "最终一致性", "BASE", "DN8"], "answer": [0, 1, 2]}, {"id": 33, "question": "关于NoSQL的三大基石之一的CAP，下列哪些说法是正确的？", "options": ["一致性，是指任何一个读操作总是能够读到之前完成的写操作的结果", "一个分布式系统可以同时满足一致性、可用性和分区容忍性这三个需求", "可用性，是指快速获取数据", "分区容忍性，是指当出现网络分区的情况时（即系统中的一部分节点无法和其他节点进行通信），分离的系统也能够正常运行"], "answer": [0, 2, 3]}, {"id": 34, "question": "当处理CAP的问题时，可以有哪几个明显的选择？", "options": ["CA：也就是强调一致性（C）和可用性（A），放弃分区容忍性（P）", "CP：也就是强调一致性（C）和分区容忍性（P），放弃可用性（A）", "AP：也就是强调可用性（A）和分区容忍性（P），放弃一致性（C）", "CAP：也就是同时兼顾可用性（A）、分区容忍性（P）和一致性（C），当时系统性能会下降很多"], "answer": [0, 1, 2]}, {"id": 35, "question": "数据库事务具有ACID四性，下面哪几项属于四性？", "options": ["原子性", "持久性", "间断性", "一致性"], "answer": [0, 1, 3]}, {"id": 36, "question": "关于HBase与BigTable的底层技术的描述，哪些是正确的？", "options": ["关系数据库已经流行很多年，并且Hadoop已经有了HDFS和MapReduce，为什么需要HBase？", "Hadoop可以很好地解决大规模数据的离线批量处理问题，但是，受限于Hadoop MapReduce编程框架的高延迟数据处理机制，使得Hadoop无法满足大规模数据实时处理应用的需求上", "HDFS面向批量访问模式，不是随机访问模式", "传统的通用关系型数据库无法应对在数据规模剧增时导致的系统扩展性和性能问题"], "answer": [1, 2, 3]}, {"id": 37, "question": "HBase与传统的关系数据库的区别主要体现在以下哪几个方面？", "options": ["数据类型", "数据操作", "存储模式", "数据维护"], "answer": [0, 1, 2, 3]}, {"id": 38, "question": "HBase访问接口类型包括哪些？", "options": ["Native Java API", "HBase Shell", "Thrift Gateway", "REST Gateway"], "answer": [0, 1, 2, 3]}, {"id": 39, "question": "下列关于数据模型的描述，哪些是正确的？", "options": ["HBase采用表来组织数据，表由行和列组成，列划分为若干个列族", "每个HBase表都由若干行组成，每个行由行键（row key）来标识", "列族里的数据通过列限定符（或列）来定位", "每个单元格都保存着同一份数据的多个版本，这些版本采用时间戳进行索引"], "answer": [0, 1, 2, 3]}, {"id": 40, "question": "HBase的实现包括哪三个主要的功能组件？", "options": ["库函数：链接到每个客户端", "一个Master主服务器", "许多个Region服务器", "廉价的计算机集群"], "answer": [0, 1, 2]}, {"id": 41, "question": "HBase的三层结构中，三层指的是哪三层？", "options": ["Zookeeper文件", "-ROOT-表", ".META.表", "数据类型"], "answer": [0, 1, 2]}, {"id": 42, "question": "以下哪些软件可以对HBase进行性能监视？", "options": ["Master-status(自带)", "Ganglia", "OpenTSDB", "Ambari"], "answer": [0, 1, 2, 3]}, {"id": 43, "question": "Zookeeper是一个很好的集群管理工具，被大量用于分布式计算，它主要提供什么服务？", "options": ["配置维护", "域名服务", "分布式同步", "负载均衡服务"], "answer": [0, 1, 2]}, {"id": 44, "question": "下列关于Region服务器工作原理的描述，哪些是正确的？", "options": ["每个Region服务器都有一个自己的HLog 文件", "每次刷写都生成一个新的StoreFile，数量太多，影响查找速度", "合并操作比较耗费资源，只有数量达到一个阈值才启动合并", "Store是Region服务器的核心"], "answer": [0, 1, 2, 3]}, {"id": 45, "question": "下列关于HLog工作原理的描述，哪些是正确的？", "options": ["分布式环境必须要考虑系统出错。HBase采用HLog保证", "HBase系统为每个Region服务器配置了一个HLog文件", "Zookeeper会实时监测每个Region服务器的状态", "Master首先会处理该故障Region服务器上面遗留的HLog文件"], "answer": [0, 1, 2, 3]}, {"id": 46, "question": "云数据库具有以下哪些特性？", "options": ["动态可扩展", "高可用性", "免维护", "安全"], "answer": [0, 1, 2, 3]}, {"id": 47, "question": "下列关于云数据库的描述，哪些是正确的？", "options": ["Amazon是云数据库市场的先行者", "Google Cloud SQL是谷歌公司推出的基于MySQL的云数据库", "从数据模型的角度来说，云数据库并非一种全新的数据库技术", "云数据库并没有专属于自己的数据模型"], "answer": [0, 1, 2, 3]}, {"id": 48, "question": "UMP系统架构设计遵循了以下哪些原则？", "options": ["保持单一的系统对外入口，并且为系统内部维护单一的资源池", "消除单点故障，保证服务的高可用性", "保证系统具有良好的可伸缩，能够动态地增加、删减计算与存储节点", "保证分配给用户的资源也是弹性可伸缩的"], "answer": [0, 1, 2, 3]}, {"id": 49, "question": "UMP系统架构依赖的哪些开源组件？", "options": ["Mnesia", "LVS", "RabbitMQ", "<PERSON><PERSON><PERSON><PERSON>"], "answer": [0, 1, 2, 3]}, {"id": 50, "question": "下列关于UMP系统架构的描述，哪些是正确的？", "options": ["信息统计服务器定期将采集到的用户的连接数", "Web控制台无法向用户提供系统管理界面", "LVS(Linux Virtual Server)即Linux虚拟服务器", "UMP系统借助于LVS来实现集群内部的负载均衡"], "answer": [0, 2, 3]}, {"id": 51, "question": "为什么说云数据库是个性化数据存储需求的理想选择？", "options": ["云数据库可以满足大企业的海量数据存储需求", "云数据库可以满足中小企业的低成本数据存储需求", "云数据库可以满足企业动态变化的数据存储需求", "前期零投入、后期免维护的数据库服务，可以很好满足它们的需求"], "answer": [0, 1, 2, 3]}, {"id": 52, "question": "下列关于云数据库与其他数据库的关系，哪些是正确的？", "options": ["从数据模型的角度来说，云数据库并非一种全新的数据库技术", "云数据库并没有专属于自己的数据模型，云数据库所采用的数据模型可以是关系数据库所使用的关系模型", "同一个公司只能提供采用不同数据模型的单个云数据库服务", "许多公司在开发云数据库时，后端数据库都是直接使用现有的各种关系数据库或NoSQL数据库产品"], "answer": [0, 1, 3]}, {"id": 53, "question": "以下哪些是Amazon的云数据库产品？", "options": ["Amazon RDS：云中的关系数据库", "Amazon SimpleDB：云中的键值数据库", "Amazon DynamoDB：云中的数据仓库", "Amazon ElastiCache：云中的分布式内存缓存"], "answer": [0, 1, 3]}, {"id": 54, "question": "Microsoft的云数据库产品SQL Azure具有以下哪些特性？", "options": ["属于关系型数据库：支持使用TSQL来管理、创建和操作云数据库", "支持存储过程：它的数据类型、存储过程和传统的SQL Server具有很大的相似性", "支持大量数据类型", "支持云中的事务：支持局部事务，但是不支持分布式事务"], "answer": [0, 1, 2, 3]}, {"id": 55, "question": "MapReduce相较于传统的并行计算框架有什么优势？", "options": ["非共享式，容错性好", "普通PC机，便宜，扩展性好", "编程简单，只要告诉MapReduce做什么即可", "批处理、非实时、数据密集型"], "answer": [0, 1, 2, 3]}, {"id": 56, "question": "MapReduce体系结构主要由以下哪几个部分构成？", "options": ["Client", "JobTracker", "TaskTracker", "Task"], "answer": [0, 1, 2, 3]}, {"id": 57, "question": "下列关于MapReduce的体系结构的描述，说法正确的有？", "options": ["用户编写的MapReduce程序通过Client提交到JobTracker端", "JobTracker负责资源监控和作业调度", "TaskTracker监控所有TaskTracker与Job的健康状况", "TaskTracker 使用\"slot\"等量划分本节点上的资源量（CPU、内存等）"], "answer": [0, 1, 3]}, {"id": 58, "question": "MapReduce的作业主要包括什么？", "options": ["从磁盘或从网络读取数据，即IO密集工作", "计算数据，即CPU密集工作", "针对不同的工作节点选择合适硬件类型", "负责协调集群中的数据存储"], "answer": [0, 1]}, {"id": 59, "question": "对于MapReduce 而言，其处理单位是split。split 是一个逻辑概念，它包含哪些元数据信息？", "options": ["数据起始位置", "数据长度", "数据所在节点", "数据大小"], "answer": [0, 1, 2]}, {"id": 60, "question": "下列关于Map 端的Shuffle的描述，哪些是正确的？", "options": ["MapReduce默认为每个Map任务分配1000MB缓存", "多个溢写文件归并成一个或多个大文件，文件中的键值对是排序的", "当数据很少时，不需要溢写到磁盘，直接在缓存中归并，然后输出给Reduce", "每个Map任务分配多个缓存，使得任务运行更有效率"], "answer": [1, 2]}, {"id": 61, "question": "MapReduce的具体应用包括哪些？", "options": ["关系代数运算（选择、投影、并、交、差、连接）", "分组与聚合运算", "矩阵-向量乘法", "矩阵乘法"], "answer": [0, 1, 2, 3]}, {"id": 62, "question": "MapReduce执行的全过程包括以下哪几个主要阶段？", "options": ["从分布式文件系统读入数据", "执行Map任务输出中间结果", "通过 Shuffle阶段把中间结果分区排序整理后发送给Reduce任务", "执行Reduce任务得到最终结果并写入分布式文件系统"], "answer": [0, 1, 2, 3]}, {"id": 63, "question": "下列说法正确的是？", "options": ["MapReduce体系结构主要由四个部分组成，分别是：Client、JobTracker、TaskTracker以及Task", "Task 分为Map Task 和Reduce Task 两种，均由TaskTracker 启动", "在MapReduce工作流程中，所有的数据交换都是通过MapReduce框架自身去实现的", "在MapReduce工作流程中，用户不能显式地从一台机器向另一台机器发送消息"], "answer": [0, 1, 2, 3]}, {"id": 64, "question": "下列选项中，哪些属于Hadoop1.0的核心组件的不足之处？", "options": ["实时性差（适合批处理，不支持实时交互式）", "资源浪费（Map和Reduce分两阶段执行）", "执行迭代操作效率低", "难以看到程序整体逻辑"], "answer": [0, 1, 2, 3]}, {"id": 65, "question": "Hadoop的优化与发展主要体现在哪几个方面？", "options": ["Hadoop自身核心组件MapReduce的架构设计改进", "Hadoop自身核心组件HDFS的架构设计改进", "Hadoop生态系统其它组件的不断丰富", "Hadoop生态系统减少不必要的组件，整合系统"], "answer": [0, 1, 2]}, {"id": 66, "question": "下列哪些属于Hadoop2.0相对于Hadoop1.0的改进？", "options": ["设计了HDFS HA", "提供名称节点热备机制", "设计了HDFS Federation，管理多个命名空间", "设计了新的资源管理框架YARN"], "answer": [0, 1, 2, 3]}, {"id": 67, "question": "下面哪个属于不断完善的Hadoop生态系统中的组件？", "options": ["Pig", "Tez", "Kafka", "DN8"], "answer": [0, 1, 2]}, {"id": 68, "question": "HDFS1.0 主要存在哪些问题？", "options": ["单点故障问题", "不可以水平扩展", "单个名称节点难以提供不同程序之间的隔离性", "系统整体性能受限于单个名称节点的吞吐量"], "answer": [0, 2, 3]}, {"id": 69, "question": "HDFS Federation 相对于HDFS1.0 的优势主要体现在哪里？", "options": ["能够解决单点故障问题", "HDFS 集群扩展性", "性能更高效", "良好的隔离性"], "answer": [1, 2, 3]}, {"id": 70, "question": "JobTracker主要包括哪三大功能？", "options": ["资源管理", "任务调度", "任务监控", "数据即服务"], "answer": [0, 1, 2]}]}
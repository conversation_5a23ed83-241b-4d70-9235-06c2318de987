{% extends 'admin/base.html' %}

{% block content %}
<div class="card">
    <div class="card-header d-flex align-items-center">
        <i class="fas fa-history me-2"></i>
        <span>管理员登录记录</span>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped align-middle">
                <thead class="table-primary">
                    <tr>
                        <th>登录时间</th>
                        <th>用户名</th>
                        <th>IP地址</th>
                        <th>设备信息</th>
                        <th>地理位置</th>
                    </tr>
                </thead>
                <tbody>
                {% if records and records|length > 0 %}
                    {% for r in records %}
                    <tr>
                        <td>{{ r.login_time.strftime('%Y-%m-%d %H:%M:%S') if r.login_time else '' }}</td>
                        <td>{{ r.username }}</td>
                        <td>{{ r.ip }}</td>
                        <td style="max-width:300px;word-break:break-all;">{{ r.user_agent }}</td>
                        <td>{{ r.location or '-' }}</td>
                    </tr>
                    {% endfor %}
                {% else %}
                    <tr>
                        <td colspan="5" class="text-center text-muted py-4">
                            <i class="fas fa-info-circle me-1"></i> 暂无登录记录
                        </td>
                    </tr>
                {% endif %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %} 
<!--pages/chat/chat.wxml-->
<view class="page-wrapper">
  <!-- 状态栏占位 -->
  <view class="status-bar-placeholder"></view>
  
  <view class="container">
    <!-- 导航栏 -->
    <view class="nav-bar">
      <view class="nav-left" bindtap="goBack">
        <text class="nav-icon back-icon">‹</text>
      </view>
      <view class="nav-title">{{chatUser.nickname || '聊天'}}</view>
      <view class="nav-right">
        <!-- 移除三点菜单图标 -->
      </view>
    </view>

    <!-- 聊天消息区域 -->
    <scroll-view 
      class="chat-messages" 
      scroll-y="true" 
      scroll-top="{{scrollTop}}"
      scroll-into-view="{{scrollIntoView}}"
      enhanced="true"
      show-scrollbar="false">
      
      <view class="message-list">
        <view 
          class="message-item {{item.sender_id === currentUserId ? 'sent' : 'received'}}" 
          wx:for="{{messages}}" 
          wx:key="id"
          id="msg-{{item.id}}">
          
          <!-- 接收的消息 -->
          <view class="message-content received-message" wx:if="{{item.sender_id !== currentUserId}}">
            <image
              class="message-avatar"
              src="{{chatUser.avatar || '/images/cat.png'}}"
              mode="aspectFill">
            </image>
            <view class="message-wrapper">
              <!-- 文本消息（有气泡） -->
              <view class="message-bubble" wx:if="{{item.message_type === 'text' || item.type === 'text'}}">
                <text class="message-text">{{item.content}}</text>
                <!-- 气泡内时间（5分钟内） -->
                <text class="message-time-inside" wx:if="{{!item.showTimeOutside}}">{{item.timeDisplay || item.created_at}}</text>
              </view>
              <!-- 图片消息（无气泡） -->
              <view class="message-media" wx:if="{{item.message_type === 'image' || item.type === 'image'}}">
                <image class="message-image" src="{{item.content}}" mode="aspectFill"></image>
                <!-- 时间显示在图片下方 -->
                <text class="message-time-media" wx:if="{{!item.showTimeOutside}}">{{item.timeDisplay || item.created_at}}</text>
              </view>
              <!-- 表情包消息（无气泡） -->
              <view class="message-media" wx:if="{{item.message_type === 'emoji'}}">
                <view class="message-emoji">
                  <text class="emoji-icon">{{item.extra_data.emoji_url || item.content}}</text>
                </view>
                <!-- 时间显示在表情包下方 -->
                <text class="message-time-media" wx:if="{{!item.showTimeOutside}}">{{item.timeDisplay || item.created_at}}</text>
              </view>
              <!-- 定位消息（无气泡） -->
              <view class="message-media" wx:if="{{item.message_type === 'location'}}">
                <view class="message-location" bindtap="openLocation" data-latitude="{{item.extra_data.latitude}}" data-longitude="{{item.extra_data.longitude}}" data-name="{{item.extra_data.name}}" data-address="{{item.extra_data.address}}">
                  <view class="location-icon">📍</view>
                  <view class="location-info">
                    <text class="location-name">{{item.extra_data.name || '位置信息'}}</text>
                    <text class="location-address">{{item.extra_data.address || '详细地址'}}</text>
                  </view>
                </view>
                <!-- 时间显示在定位下方 -->
                <text class="message-time-media" wx:if="{{!item.showTimeOutside}}">{{item.timeDisplay || item.created_at}}</text>
              </view>
              <!-- 气泡外时间（超过5分钟） -->
              <text class="message-time-outside" wx:if="{{item.showTimeOutside}}">{{item.timeDisplay || item.created_at}}</text>
            </view>
          </view>

          <!-- 发送的消息 -->
          <view class="message-content sent-message" wx:if="{{item.sender_id === currentUserId}}">
            <view class="message-wrapper">
              <!-- 文本消息（有气泡） -->
              <view class="message-bubble" wx:if="{{item.message_type === 'text' || item.type === 'text'}}">
                <text class="message-text">{{item.content}}</text>
                <!-- 气泡内时间（5分钟内） -->
                <text class="message-time-inside" wx:if="{{!item.showTimeOutside}}">{{item.timeDisplay || item.created_at}}</text>
              </view>
              <!-- 图片消息（无气泡） -->
              <view class="message-media" wx:if="{{item.message_type === 'image' || item.type === 'image'}}">
                <image class="message-image" src="{{item.content}}" mode="aspectFill"></image>
                <!-- 时间显示在图片下方 -->
                <text class="message-time-media" wx:if="{{!item.showTimeOutside}}">{{item.timeDisplay || item.created_at}}</text>
              </view>
              <!-- 表情包消息（无气泡） -->
              <view class="message-media" wx:if="{{item.message_type === 'emoji'}}">
                <view class="message-emoji">
                  <text class="emoji-icon">{{item.extra_data.emoji_url || item.content}}</text>
                </view>
                <!-- 时间显示在表情包下方 -->
                <text class="message-time-media" wx:if="{{!item.showTimeOutside}}">{{item.timeDisplay || item.created_at}}</text>
              </view>
              <!-- 定位消息（无气泡） -->
              <view class="message-media" wx:if="{{item.message_type === 'location'}}">
                <view class="message-location" bindtap="openLocation" data-latitude="{{item.extra_data.latitude}}" data-longitude="{{item.extra_data.longitude}}" data-name="{{item.extra_data.name}}" data-address="{{item.extra_data.address}}">
                  <view class="location-icon">📍</view>
                  <view class="location-info">
                    <text class="location-name">{{item.extra_data.name || '位置信息'}}</text>
                    <text class="location-address">{{item.extra_data.address || '详细地址'}}</text>
                  </view>
                </view>
                <!-- 时间显示在定位下方 -->
                <text class="message-time-media" wx:if="{{!item.showTimeOutside}}">{{item.timeDisplay || item.created_at}}</text>
              </view>
              <!-- 气泡外时间（超过5分钟） -->
              <text class="message-time-outside" wx:if="{{item.showTimeOutside}}">{{item.timeDisplay || item.created_at}}</text>
            </view>
            <image
              class="message-avatar"
              src="{{currentUser.avatar || '/images/cat.png'}}"
              mode="aspectFill">
            </image>
          </view>
        </view>

        <!-- 空状态 -->
        <view class="empty-messages" wx:if="{{messages.length === 0 && !loading}}">
          <text class="empty-text">开始聊天吧！</text>
        </view>
      </view>
    </scroll-view>

    <!-- 输入区域 -->
    <view class="chat-input-area">
      <view class="input-wrapper">
        <!-- 加号按钮 -->
        <view class="plus-btn" bindtap="showActionSheet">
          <text class="plus-icon">+</text>
        </view>

        <input
          class="chat-input"
          placeholder="输入消息..."
          value="{{inputContent}}"
          bindinput="onInput"
          confirm-type="send"
          bindconfirm="sendMessage"
          focus="{{inputFocus}}"
        />
        <button
          class="send-btn {{hasContent ? 'active' : ''}}"
          bindtap="sendMessage">
          发送
        </button>
      </view>
    </view>
  </view>

  <!-- 功能弹窗 -->
  <view class="action-sheet-mask" wx:if="{{showActionSheet}}" bindtap="hideActionSheet"></view>
  <view class="action-sheet" wx:if="{{showActionSheet}}">
    <view class="action-sheet-header">
      <text class="action-sheet-title">选择功能</text>
    </view>
    <view class="action-sheet-content">
      <view class="action-item" bindtap="chooseImage">
        <view class="action-icon photo-icon">📷</view>
        <text class="action-text">相册</text>
      </view>
      <view class="action-item" bindtap="chooseEmoji">
        <view class="action-icon emoji-icon">😊</view>
        <text class="action-text">表情包</text>
      </view>
      <view class="action-item" bindtap="chooseLocation">
        <view class="action-icon location-icon">📍</view>
        <text class="action-text">定位</text>
      </view>
    </view>
    <view class="action-sheet-cancel" bindtap="hideActionSheet">
      <text>取消</text>
    </view>
  </view>

  <!-- 加载中 -->
  <view class="loading-overlay" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>


</view>

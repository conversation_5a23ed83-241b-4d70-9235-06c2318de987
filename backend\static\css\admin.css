/* 下拉菜单样式修复 */
.form-select {
    position: relative;
    z-index: 100;
    cursor: pointer;
    width: 100%;
}

/* 确保下拉选项可见 */
.form-select option {
    display: block !important;
    background-color: white;
    color: #333;
    padding: 5px;
}

/* 增加下拉菜单列表的z-index，确保在其他元素之上 */
select.form-select {
    position: relative;
    z-index: 9999 !important;
}

/* 确保下拉菜单容器不被遮挡 */
.col-md-5 {
    position: relative;
    z-index: 900;
}

/* 为下拉菜单添加悬停效果 */
.form-select:hover {
    border-color: #007bff;
}

/* 确保下拉菜单在点击时保持可见 */
.form-select:focus {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    border-color: #86b7fe;
    z-index: 9999 !important;
}

/* 调整下拉菜单的外观 */
.form-select option:hover,
.form-select option:focus,
.form-select option:active,
.form-select option:checked {
    background-color: #f0f7ff !important;
    color: #000 !important;
}

/* 确保弹出菜单内容可见 */
.dropdown-menu {
    z-index: 10000 !important;
    display: block;
    visibility: visible;
    opacity: 1;
}

/* 课程选择加载指示器 */
.form-select.loading {
    background-image: url('data:image/svg+xml;charset=utf8,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"%3E%3Cpath fill="%23adb5bd" d="M460.115 373.846l-6.941-4.008c-5.546-3.202-7.564-10.177-4.661-15.886 32.971-64.838 31.167-142.731-5.415-205.954-36.504-63.356-103.118-103.876-175.8-107.701C260.952 39.963 256 34.676 256 28.321v-8.012c0-6.904 5.808-12.337 12.703-11.982 83.552 4.306 160.157 50.861 202.106 123.67 42.069 72.703 44.083 162.322 6.034 236.838-3.14 6.149-10.75 8.462-16.728 5.011z"%3E%3C/path%3E%3C/svg%3E');
    background-position: right 0.75rem center;
    background-size: 16px 16px;
    background-repeat: no-repeat;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
} 
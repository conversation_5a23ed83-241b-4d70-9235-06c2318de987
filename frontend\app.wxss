/**app.wxss**/

/* 全局样式 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: #333;
  font-size: 28rpx;
  line-height: 1.6;
  background-color: #f5f7fa;
  padding-bottom: 0;
  box-sizing: border-box;
  min-height: 100vh;
  position: relative;
}

/* 小猫组件全局样式 */
cat-component {
  position: fixed !important;
  z-index: 10000 !important;
  pointer-events: none !important; /* 组件整体不接收事件，只有猫图片接收 */
  touch-action: none !important;
}

/* cat-component内部的元素可以有自己的pointer-events */
cat-component .cat-image,
cat-component .chat-container {
  pointer-events: auto !important; /* 确保猫和聊天窗口可以接收点击 */
}

/* 中文美化字体 */
@font-face {
  font-family: 'ChineseElegant';
  src: local('STXingkai'), local('华文行楷'), local('STKaiti'), local('楷体');
  font-weight: normal;
  font-style: normal;
}

/* 图标字体 */
@font-face {
  font-family: 'iconfont';
  src: url('data:application/x-font-woff2;charset=utf-8;base64,d09GMgABAAAAAAP4AAsAAAAACEwAAAOrAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHFQGYACDFAqECINQATYCJAMQCwoABCAFhGcHQxudB8gOJUHBwGBgAGBgPPy7X7vvzez/WRCqRJV0eqeTJqqNRIhnE51EPJIgJEL2/xvadwM0aE+sSU/U7IjEomSP7knmSeaJ7sn8ePrfOtObY3koyzGG5XhcL2AB5YDG2KKyK5CAvGHsJg7iMASIU4wGUr5i1QY4GOwTgLRv1rQRuOIwmIkSOAU3D3YtMgNHTGpeA6aBX5ffpJpoIA4Kth5124rNKfky9svCF7p/6Hoh4H08CzC3gQUaAAYkpW6oFw2MNbBY/P+/JTcNyFKUTP6L5MhfJgMoYP0HDxDIgIkjZKvBAGQQfJnBkYEvC0cVviwsVaSJSIlDEdIL8JMggOPgcVAJSpvmtNq+/dBjx44d3tMgc0QvxalXpuI30Sfj1/XT1y3E/L0yLzz/6fzZSy+XVY+VH+J0RwqY6bviRe+Lp2ee3xWeQcnzU86eW/lLB+OM3Ts3T+4KCQXdXU0dV05evDD14oUpl67UUKPcDTx+c/fpR7Xwi3Dj5yeFsE4sGn8+Vy9ezkKFkopCbE0eZlwYSRUuXl5fN1PWFR58OvJ45nD9YHn/5a37n5+8cEr6bm3ptx+eEPK/XjPvs8uULR8vj+YekAduZAerR/iOChKMOXNm5vn7w9LHTp3avUWP7xjfHzx/XuS5ZbF6v4mQbI0fNH5RsMdz+aJrj3uPHk9Wzwzh+8SL9MTsHbsnX7t2bcxYv55i2jP5p01FXpNvHlb36rWD6+P48Z8mLk2q9c6SkPd2WLz4MCXvyJO3e7/Zg3t79pjL9sNr1x7eL+5OXAYfOny4esXSBw+WVq9ADTj8duHChw9fLnx7OP3X1G8H7xpUVFwb1LSnY25f2+w+1P05tGnfvrpbOm3X1o+B9wYAtR/5/+8xoBbLnxGboFDgmyB8BM7s35QGAJgLVKDJ/XFEtcFHRNIEHwIEcBQiqDyUZHjAZ5JCNGb6DH7NRN8P68qV6gHrFHCaFpCt1A8QOKqDYKU+5FQY4GPh8+DBSjPEqc+uJIsYFtKogBYD0k9Qe3SQKdkUv64J2mMVFf1+iYFGUSXGxMglsm4inaA5Q55LouEwUC2uIFe9hnGnQ1nHHWZWFDJkmDu1B9+ZVgZVUGQAtxjIDAX0JEDag6S99DC5/P4m0NysCgW7yxKYqLDFRjGE1Z1QbTcVPFE88r6Qy4WECoMDmk14BdGqHobx7ZtClZ13MGZJQtxjMOo29zq1rK9qvO0ewDHu5nCEJIyk4Lrc5qoR7WaJQBsZEcJEjEXWCJuOAAA=');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 40rpx;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 金榜题名样式 */
.golden-character {
  font-family: 'ChineseElegant', 'STXingkai', '华文行楷', 'STKaiti', '楷体', serif;
  background: linear-gradient(to bottom, #f6e681 0%, #D4AF37 50%, #AA771C 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  text-shadow: 0 1px 2px rgba(173, 137, 25, 0.3);
}

/* 活动列表样式优化 - 减少底部空白区域 */
.activity-list-container {
  padding-bottom: 30rpx;
  margin-bottom: 0;
}

.activity-list-item {
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  flex-direction: column;
}

.activity-list {
  margin-bottom: 10rpx;
}

button.view-more {
  width: 60%;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  background-color: #f7f7f7;
  color: #666;
  border-radius: 40rpx;
  margin: 20rpx auto 30rpx;
  padding: 0;
  border: none;
}

/* 修复布局和容器样式 */
.main-container {
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 200rpx);
  position: relative;
  padding-bottom: 0;
}

/* 确保查看更多按钮不会有太多底部空白 */
.view-more-wrapper {
  margin: 10rpx auto 20rpx;
  text-align: center;
}

.container {
  min-height: auto !important;
  padding-bottom: 40rpx;
} 
/**
 * 用户数据管理工具，包含获取用户头像和昵称的功能
 * 适用于微信小程序3.8以上版本
 */

// 默认头像
const defaultAvatarUrl = 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0';

/**
 * 用户信息管理模块
 */
const userManager = {
  /**
   * 保存用户信息到本地存储
   * @param {Object} userInfo 用户信息对象
   */
  saveUserInfo(userInfo) {
    if (!userInfo) return;
    
    // 确保用户信息同时包含两种格式的属性
    this.normalizeUserInfo(userInfo);
    
    try {
      wx.setStorageSync('userInfo', userInfo);
      console.log('用户信息已保存到本地存储');
    } catch (e) {
      console.error('保存用户信息失败:', e);
    }
  },

  /**
   * 获取本地存储的用户信息
   * @returns {Object|null} 用户信息对象，如果不存在则返回null
   */
  getLocalUserInfo() {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo) {
        // 确保用户信息同时包含两种格式的属性
        this.normalizeUserInfo(userInfo);
        return userInfo;
      }
      return null;
    } catch (e) {
      console.error('获取本地用户信息失败:', e);
      return null;
    }
  },

  /**
   * 统一用户信息格式，确保同时兼容 avatar/nickname 和 avatarUrl/nickName 两种格式
   * @param {Object} userInfo 用户信息对象
   * @returns {Object} 标准化后的用户信息对象
   */
  normalizeUserInfo(userInfo) {
    if (!userInfo) return userInfo;
    
    // 确保两种格式的属性都存在
    if (userInfo.avatarUrl && !userInfo.avatar) {
      userInfo.avatar = userInfo.avatarUrl;
    } else if (userInfo.avatar && !userInfo.avatarUrl) {
      userInfo.avatarUrl = userInfo.avatar;
    }
    
    if (userInfo.nickName && !userInfo.nickname) {
      userInfo.nickname = userInfo.nickName;
    } else if (userInfo.nickname && !userInfo.nickName) {
      userInfo.nickName = userInfo.nickname;
    }
    
    return userInfo;
  },

  /**
   * 处理用户选择头像事件
   * @param {Object} e 事件对象，包含所选头像的临时文件路径
   * @param {Function} callback 处理完成后的回调函数，会传入头像URL
   */
  handleChooseAvatar(e, callback) {
    const { avatarUrl } = e.detail;
    if (!avatarUrl) {
      console.warn('未获取到头像URL');
      return;
    }

    // 更新本地用户信息中的头像
    const userInfo = this.getLocalUserInfo() || {};
    userInfo.avatarUrl = avatarUrl;
    userInfo.avatar = avatarUrl; // 同时更新两种属性名
    this.saveUserInfo(userInfo);
    
    // 同步更新到app全局数据
    const app = getApp();
    if (app && app.globalData && app.globalData.userInfo) {
      app.globalData.userInfo.avatarUrl = avatarUrl;
      app.globalData.userInfo.avatar = avatarUrl; // 同时更新两种属性名
      console.log('已更新全局用户头像:', avatarUrl);
      
      // 可选：同步到服务器
      if (app.globalData.token) {
        wx.request({
          url: app.globalData.baseUrl + '/update_profile',
          method: 'POST',
          header: {
            'Authorization': app.globalData.token
          },
          data: {
            avatar: avatarUrl
          },
          success: (res) => {
            console.log('用户头像已同步到服务器:', res.data);
          },
          fail: (err) => {
            console.error('用户头像同步到服务器失败:', err);
          }
        });
      }
    }
    
    // 如果提供了回调函数，则调用它
    if (typeof callback === 'function') {
      callback(avatarUrl);
    }
  },

  /**
   * 处理用户输入昵称事件
   * @param {Object} e 事件对象，包含输入的昵称
   * @param {Function} callback 处理完成后的回调函数，会传入昵称
   */
  handleNicknameInput(e, callback) {
    const nickName = e.detail.value.trim();
    if (!nickName) {
      console.warn('昵称不能为空');
      return;
    }

    // 更新本地用户信息中的昵称
    const userInfo = this.getLocalUserInfo() || {};
    userInfo.nickName = nickName;
    userInfo.nickname = nickName; // 同时更新两种属性名
    this.saveUserInfo(userInfo);
    
    // 如果提供了回调函数，则调用它
    if (typeof callback === 'function') {
      callback(nickName);
    }
  },

  /**
   * 处理表单提交事件，可以一次性获取表单中的昵称等信息
   * @param {Object} e 事件对象，包含表单数据
   * @param {Function} callback 处理完成后的回调函数，会传入用户信息对象
   */
  handleFormSubmit(e, callback) {
    const formData = e.detail.value;
    const userInfo = this.getLocalUserInfo() || {};
    
    // 更新用户信息
    if (formData.nickname) {
      const nickName = formData.nickname.trim();
      userInfo.nickName = nickName;
      userInfo.nickname = nickName; // 同时更新两种属性名
    }
    
    this.saveUserInfo(userInfo);
    
    // 同步更新到app全局数据
    const app = getApp();
    if (app && app.globalData) {
      app.globalData.userInfo = {...app.globalData.userInfo, ...userInfo};
      // 确保全局数据包含两种属性名格式
      this.normalizeUserInfo(app.globalData.userInfo);
      console.log('已更新全局用户信息:', app.globalData.userInfo);
      
      // 可选：同步到服务器
      if (app.globalData.token) {
        wx.request({
          url: app.globalData.baseUrl + '/update_profile',
          method: 'POST',
          header: {
            'Authorization': app.globalData.token
          },
          data: {
            nickname: userInfo.nickName,
            avatar: userInfo.avatarUrl
          },
          success: (res) => {
            console.log('用户资料已同步到服务器:', res.data);
          },
          fail: (err) => {
            console.error('用户资料同步到服务器失败:', err);
          }
        });
      }
    }
    
    // 如果提供了回调函数，则调用它
    if (typeof callback === 'function') {
      callback(userInfo);
    }
  },

  /**
   * 获取默认头像URL
   * @returns {string} 默认头像URL
   */
  getDefaultAvatarUrl() {
    return defaultAvatarUrl;
  }
};

/**
 * 获取用户信息的组件使用示例
 * @example
 * // wxml
 * <form bindsubmit="onSubmit">
 *   <button open-type="chooseAvatar" bindchooseavatar="onChooseAvatar">
 *     <image class="avatar" src="{{avatarUrl}}"></image>
 *   </button>
 *   <input type="nickname" name="nickname" placeholder="请输入昵称"/>
 *   <button form-type="submit">保存</button>
 * </form>
 * 
 * // js
 * Page({
 *   data: {
 *     avatarUrl: userManager.getDefaultAvatarUrl()
 *   },
 *   onLoad() {
 *     const userInfo = userManager.getLocalUserInfo();
 *     if (userInfo && userInfo.avatarUrl) {
 *       this.setData({ avatarUrl: userInfo.avatarUrl });
 *     }
 *   },
 *   onChooseAvatar(e) {
 *     userManager.handleChooseAvatar(e, (avatarUrl) => {
 *       this.setData({ avatarUrl });
 *     });
 *   },
 *   onSubmit(e) {
 *     userManager.handleFormSubmit(e, (userInfo) => {
 *       wx.showToast({ title: '保存成功' });
 *     });
 *   }
 * })
 */

module.exports = userManager; 
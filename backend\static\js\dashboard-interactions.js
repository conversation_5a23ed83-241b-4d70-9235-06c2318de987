// Dashboard Enhanced Interactions
document.addEventListener('DOMContentLoaded', function() {
    // Apply subtle hover effects to cards
    const allCards = document.querySelectorAll('.card, .top-stat-card');
    
    allCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            // Add subtle shadow glow based on card type
            if (this.querySelector('.icon-container')) {
                const iconContainer = this.querySelector('.icon-container');
                const computedStyle = window.getComputedStyle(iconContainer);
                const bgColor = computedStyle.backgroundColor;
                
                // Create a subtle glow effect with the same color
                this.style.boxShadow = `0 10px 25px -5px ${bgColor.replace(')', ', 0.15)')}`;
            }
        });
        
        card.addEventListener('mouseleave', function() {
            // Reset to original shadow
            this.style.boxShadow = '';
        });
    });
    
    // Add pulsing effect to important metrics that changed
    function pulseEffect(element) {
        // Create and apply the pulse animation
        const keyframes = [
            { transform: 'scale(1)', opacity: '1' },
            { transform: 'scale(1.05)', opacity: '0.9' },
            { transform: 'scale(1)', opacity: '1' }
        ];
        
        const options = {
            duration: 1000,
            iterations: 1,
            easing: 'ease-in-out'
        };
        
        element.animate(keyframes, options);
    }
    
    // Apply pulse to metrics that change
    const refreshBtn = document.querySelector('.welcome-actions .btn:first-child');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            // Simulate data refresh with visual feedback
            const statValues = document.querySelectorAll('.welcome-stat-value, .card-value');
            
            // Pulse each value with slight delay
            statValues.forEach((el, index) => {
                setTimeout(() => {
                    pulseEffect(el);
                }, index * 150);
            });
        });
    }
    
    // Enhanced chart interaction
    const chartContainers = document.querySelectorAll('.chart-container');
    chartContainers.forEach(container => {
        const canvas = container.querySelector('canvas');
        if (canvas) {
            // Add subtle zoom effect on hover
            container.addEventListener('mouseenter', function() {
                canvas.style.transform = 'scale(1.02)';
                canvas.style.transition = 'transform 0.3s ease';
            });
            
            container.addEventListener('mouseleave', function() {
                canvas.style.transform = 'scale(1)';
            });
        }
    });
    
    // Add parallax effect to welcome panel
    const welcomePanel = document.querySelector('.dashboard-welcome');
    if (welcomePanel) {
        welcomePanel.addEventListener('mousemove', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left; // x position within the element
            const y = e.clientY - rect.top;  // y position within the element
            
            // Calculate percentage
            const xPercent = Math.floor((x / rect.width) * 100);
            const yPercent = Math.floor((y / rect.height) * 100);
            
            // Move the background pattern slightly
            this.style.backgroundPosition = `${51 + (xPercent - 50) * 0.1}% ${51 + (yPercent - 50) * 0.1}%`;
            
            // Subtle shadow change
            this.style.boxShadow = `0 15px 35px rgba(0,0,0,0.1), ${(xPercent - 50) * 0.03}px ${(yPercent - 50) * 0.01}px 20px rgba(0,0,0,0.1)`;
        });
        
        welcomePanel.addEventListener('mouseleave', function() {
            this.style.backgroundPosition = '50% 50%';
            this.style.boxShadow = '0 15px 35px rgba(0,0,0,0.1)';
        });
    }
    
    // Add enhanced dropdown behavior
    const dropdowns = document.querySelectorAll('.dropdown');
    dropdowns.forEach(dropdown => {
        const toggle = dropdown.querySelector('.dropdown-toggle');
        const menu = dropdown.querySelector('.dropdown-menu');
        
        if (toggle && menu) {
            // Add subtle animation for dropdown
            toggle.addEventListener('click', function() {
                // Toggle a class to handle the animation
                if (menu.classList.contains('show')) {
                    animateDropdownClose(menu);
                } else {
                    animateDropdownOpen(menu);
                }
            });
        }
    });
    
    function animateDropdownOpen(menu) {
        menu.style.opacity = '0';
        menu.style.transform = 'translateY(-10px)';
        menu.style.transition = 'opacity 0.2s ease, transform 0.2s ease';
        
        // Force reflow
        menu.offsetHeight;
        
        menu.style.opacity = '1';
        menu.style.transform = 'translateY(0)';
    }
    
    function animateDropdownClose(menu) {
        menu.style.opacity = '0';
        menu.style.transform = 'translateY(-10px)';
    }
    
    // Enhance table hover effects
    const tableRows = document.querySelectorAll('.table-hover tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            const cells = this.querySelectorAll('td');
            cells.forEach((cell, index) => {
                // Add staggered subtle highlight
                setTimeout(() => {
                    cell.style.transition = 'background-color 0.3s ease';
                    cell.style.backgroundColor = 'rgba(0,0,0,0.01)';
                }, index * 30);
            });
        });
        
        row.addEventListener('mouseleave', function() {
            const cells = this.querySelectorAll('td');
            cells.forEach(cell => {
                cell.style.backgroundColor = '';
            });
        });
    });
    
    // Add water ripple effect to buttons
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Create ripple element
            const ripple = document.createElement('span');
            ripple.classList.add('ripple');
            
            // Set ripple size and position
            const size = Math.max(this.offsetWidth, this.offsetHeight);
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = `${size}px`;
            ripple.style.left = `${x}px`;
            ripple.style.top = `${y}px`;
            
            this.appendChild(ripple);
            
            // Remove ripple after animation
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
    
    // Add CSS for ripple effect
    const style = document.createElement('style');
    style.textContent = `
        .btn {
            position: relative;
            overflow: hidden;
        }
        
        .ripple {
            position: absolute;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        }
        
        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
    
    // Enhanced progress bar animation
    const progressBars = document.querySelectorAll('.progress-bar');
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // Add animation when in view
                const bar = entry.target;
                const width = bar.style.width;
                
                // Reset first
                bar.style.width = '0%';
                
                // Animate to target width
                setTimeout(() => {
                    bar.style.transition = 'width 1s ease-in-out';
                    bar.style.width = width;
                }, 200);
                
                // Unobserve after animation
                observer.unobserve(bar);
            }
        });
    }, { threshold: 0.1 });
    
    progressBars.forEach(bar => {
        observer.observe(bar);
    });
}); 
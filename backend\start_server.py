#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def start_server():
    """启动服务器"""
    try:
        print("正在启动猫友圈服务器...")
        
        # 确保实例目录存在
        instance_dir = os.path.join(os.path.dirname(__file__), 'instance')
        if not os.path.exists(instance_dir):
            os.makedirs(instance_dir)
            print(f"创建实例目录: {instance_dir}")
        
        # 导入并运行应用
        from app import app, db
        
        # 创建应用上下文
        with app.app_context():
            # 创建数据库表
            db.create_all()
            print("数据库表已创建/更新")
            
            # 初始化猫友圈数据
            try:
                from cat_circle_api import SensitiveWord
                if SensitiveWord.query.count() == 0:
                    sensitive_words = ['垃圾', '骗子', '傻逼', '白痴', '死']
                    for word in sensitive_words:
                        sw = SensitiveWord(word=word, type='normal')
                        db.session.add(sw)
                    db.session.commit()
                    print(f"已添加 {len(sensitive_words)} 个敏感词")
            except Exception as e:
                print(f"敏感词初始化失败: {e}")
        
        print("服务器启动成功!")
        print("访问地址: http://localhost:5000")
        print("API文档: http://localhost:5000/api")
        
        # 启动服务器
        app.run(debug=True, host='0.0.0.0', port=5000)
        
    except Exception as e:
        print(f"服务器启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    start_server()

/* course.wxss */

.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 30rpx;
  padding-top: 0;
}

/* 顶部区域（状态栏+头部） */
.header-area {
  position: relative;
  margin-bottom: 40rpx;
}

/* 状态栏安全区域 */
.status-bar-placeholder {
  width: 100%;
  /* 背景颜色通过内联样式动态设置 */
}

/* 头部背景 */
.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 320rpx;
  z-index: 0;
}

/* 课程头部内容 */
.course-header {
  color: white;
  padding: 20rpx 24rpx 120rpx;
  position: relative;
  z-index: 1;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.back-btn {
  padding: 12rpx;
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transition: all 0.3s;
}

.back-btn:active {
  transform: scale(0.9);
  background-color: rgba(255, 255, 255, 0.3);
}

.back-icon {
  font-size: 34rpx;
  font-weight: bold;
}

.course-title {
  font-size: 40rpx;
  font-weight: bold;
  flex: 1;
  text-align: center;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.placeholder {
  width: 64rpx;
}

/* 顺序/随机切换按钮 */
.toggle-button {
  display: flex;
  align-items: center;
  background-color: rgba(78, 141, 247, 0.1);
  border-radius: 40rpx;
  padding: 8rpx 20rpx;
  transition: all 0.3s;
}

.toggle-button:active {
  transform: scale(0.95);
}

.toggle-icon {
  margin-right: 10rpx;
  font-size: 30rpx;
}

.toggle-text {
  font-size: 24rpx;
  color: #333;
}

/* 统计卡片 - 浮动效果 */
.stats-card {
  margin: 0 30rpx;
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
  position: relative;
  margin-top: -100rpx;
  z-index: 2;
}

.course-stats {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.stat-item:not(:last-child)::after {
  content: "";
  position: absolute;
  right: -30rpx;
  top: 50%;
  height: 60%;
  width: 2rpx;
  background-color: rgba(0, 0, 0, 0.1);
  transform: translateY(-50%);
}

.stat-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #4e8df7;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 26rpx;
  color: #666;
}

/* 卡片容器 */
.card-section {
  margin: 0 30rpx 30rpx;
  border-radius: 20rpx;
  background-color: white;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.06);
  padding: 30rpx;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  position: relative;
  padding-left: 24rpx;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 36rpx;
  background-color: #4e8df7;
  border-radius: 4rpx;
}

/* 题型卡片 */
.question-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background-color: #f9fafe;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.question-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05) inset;
}

.question-card:last-child {
  margin-bottom: 0;
}

.question-card::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 8rpx;
  background-color: #4e8df7;
  border-radius: 4rpx 0 0 4rpx;
}

.multiple-card::before {
  background-color: #5e66ff;
}

.judgment-card::before {
  background-color: #9c27b0;
}

.fillblank-card::before {
  background-color: #009688;
}

.all-card::before {
  background-color: #ff9800;
}

.review-card::before {
  background-color: #FF5722;
}

.question-card-left {
  display: flex;
  align-items: center;
}

.question-icon {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 24rpx;
  position: relative;
  overflow: hidden;
}

.question-icon::after {
  content: "";
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

.single-icon {
  background: linear-gradient(135deg, #4e8df7, #3c78e0);
  box-shadow: 0 4rpx 10rpx rgba(78, 141, 247, 0.3);
}

.multiple-icon {
  background: linear-gradient(135deg, #5e66ff, #4a52e0);
  box-shadow: 0 4rpx 10rpx rgba(94, 102, 255, 0.3);
}

.judgment-icon {
  background: linear-gradient(135deg, #9c27b0, #7b1fa2);
  box-shadow: 0 4rpx 10rpx rgba(156, 39, 176, 0.3);
}

.fillblank-icon {
  background: linear-gradient(135deg, #009688, #00796b);
  box-shadow: 0 4rpx 10rpx rgba(0, 150, 136, 0.3);
}

.all-icon {
  background-color: #ff9800;
}

.exam-icon {
  background: linear-gradient(135deg, #e91e63, #c2185b);
  box-shadow: 0 4rpx 10rpx rgba(233, 30, 99, 0.3);
}

.review-icon {
  background: linear-gradient(135deg, #FF5722, #E64A19);
  box-shadow: 0 4rpx 10rpx rgba(255, 87, 34, 0.3);
}

.icon-text {
  font-size: 36rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.question-info {
  display: flex;
  flex-direction: column;
}

.question-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}

.question-count {
  font-size: 26rpx;
  color: #666;
  display: flex;
  align-items: center;
}

.question-action {
  display: flex;
  align-items: center;
  background-color: rgba(78, 141, 247, 0.1);
  padding: 14rpx 28rpx;
  border-radius: 30rpx;
  transition: all 0.3s;
}

.question-action:active {
  background-color: rgba(78, 141, 247, 0.2);
  transform: scale(0.95);
}

.multiple-card .question-action {
  background-color: rgba(94, 102, 255, 0.1);
}

.all-card .question-action {
  background-color: rgba(255, 152, 0, 0.1);
}

.review-card .question-action {
  background-color: rgba(255, 87, 34, 0.1);
}

.action-text {
  font-size: 30rpx;
  color: #4e8df7;
  margin-right: 10rpx;
  font-weight: 500;
}

.multiple-card .action-text {
  color: #5e66ff;
}

.all-card .action-text {
  color: #ff9800;
}

.review-card .action-text {
  color: #FF5722;
}

.arrow {
  font-size: 28rpx;
  color: #4e8df7;
  font-weight: bold;
}

.multiple-card .arrow {
  color: #5e66ff;
}

.all-card .arrow {
  color: #ff9800;
}

.review-card .arrow {
  color: #FF5722;
}

/* 工具网格 */
.tools-grid {
  display: flex;
  justify-content: space-between;
  margin-top: 10rpx;
}

.tool-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 48%;
  padding: 40rpx 30rpx;
  background-color: #f9fafe;
  border-radius: 16rpx;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.tool-card:active {
  transform: scale(0.97);
  background-color: #f2f4fc;
}

.tool-icon {
  font-size: 60rpx;
  margin-bottom: 20rpx;
  background-color: white;
  width: 120rpx;
  height: 120rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
}

.tool-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}

.tool-desc {
  font-size: 24rpx;
  color: #666;
  background-color: rgba(0, 0, 0, 0.05);
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
}

/* 加载提示 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 999;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.loading {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid rgba(78, 141, 247, 0.1);
  border-top: 6rpx solid #4e8df7;
  border-radius: 50%;
  margin-bottom: 20rpx;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
} 
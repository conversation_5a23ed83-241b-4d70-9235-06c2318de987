// 全局消息通知组件
Component({
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    title: {
      type: String,
      value: '新消息'
    },
    message: {
      type: String,
      value: ''
    },
    postId: {
      type: Number,
      value: null
    },
    duration: {
      type: Number,
      value: 4000 // 默认显示4秒
    }
  },

  data: {
    timer: null
  },

  observers: {
    'show': function(show) {
      if (show) {
        this.startTimer()
      } else {
        this.clearTimer()
      }
    }
  },

  methods: {
    // 开始计时器
    startTimer() {
      this.clearTimer()
      this.data.timer = setTimeout(() => {
        this.hide()
      }, this.properties.duration)
    },

    // 清除计时器
    clearTimer() {
      if (this.data.timer) {
        clearTimeout(this.data.timer)
        this.data.timer = null
      }
    },

    // 隐藏通知
    hide() {
      this.clearTimer()
      this.triggerEvent('hide')
    },

    // 点击通知
    onToastTap() {
      this.hide()
      this.triggerEvent('tap', {
        postId: this.properties.postId
      })
    },

    // 点击关闭按钮
    onClose(e) {
      e.stopPropagation()
      this.hide()
    }
  },

  detached() {
    this.clearTimer()
  }
})

import pymysql
import os
import json
from sqlalchemy import create_engine, text, inspect
from flask_app.models import User, Class

# 数据库连接配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '123456',
    'charset': 'utf8mb4'
}

def init_database():
    """初始化数据库和表结构"""
    # 创建数据库连接
    conn = pymysql.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    try:
        # 创建数据库
        cursor.execute("CREATE DATABASE IF NOT EXISTS quiz_app CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        print("Database 'quiz_app' created or already exists")
        
        # 切换到新创建的数据库
        cursor.execute("USE quiz_app")
        
        # 创建用户表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS user (
            id INT AUTO_INCREMENT PRIMARY KEY,
            openid VARCHAR(50) UNIQUE NOT NULL,
            nickname VA<PERSON>HA<PERSON>(50),
            avatar VARCHAR(200),
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT 1
        )
        """)
        print("Table 'user' created or already exists")
        
        # 创建单选题表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS single_choice_question (
            id INT AUTO_INCREMENT PRIMARY KEY,
            question VARCHAR(500) NOT NULL,
            options JSON NOT NULL,
            answer INT NOT NULL,
            category VARCHAR(50),
            difficulty INT DEFAULT 1
        )
        """)
        print("Table 'single_choice_question' created or already exists")
        
        # 创建多选题表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS multiple_choice_question (
            id INT AUTO_INCREMENT PRIMARY KEY,
            question VARCHAR(500) NOT NULL,
            options JSON NOT NULL,
            answer JSON NOT NULL,
            category VARCHAR(50),
            difficulty INT DEFAULT 1
        )
        """)
        print("Table 'multiple_choice_question' created or already exists")
        
        # 创建答题记录表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS quiz_record (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            question_type VARCHAR(20) NOT NULL,
            question_id INT NOT NULL,
            user_answer JSON NOT NULL,
            is_correct BOOLEAN NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES user(id)
        )
        """)
        print("Table 'quiz_record' created or already exists")
        
        # 创建错题本表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS wrong_question (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            question_type VARCHAR(20) NOT NULL,
            question_id INT NOT NULL,
            times_wrong INT DEFAULT 1,
            last_wrong_time DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES user(id)
        )
        """)
        print("Table 'wrong_question' created or already exists")
        
        conn.commit()
        print("Database initialization completed successfully!")
        
    except Exception as e:
        conn.rollback()
        print(f"Error initializing database: {e}")
    finally:
        cursor.close()
        conn.close()

def load_questions():
    """从JSON文件加载题目数据到数据库"""
    try:
        # 检查questions.json文件是否存在
        if not os.path.exists('questions.json'):
            # 尝试从上级目录复制
            if os.path.exists('../questions.json'):
                import shutil
                shutil.copy('../questions.json', './questions.json')
                print("Copied questions.json from parent directory")
            else:
                print("Error: questions.json file not found!")
                return
        
        # 读取题目数据
        with open('questions.json', 'r', encoding='utf-8') as f:
            questions_data = json.load(f)
        
        # 创建数据库连接
        conn = pymysql.connect(**DB_CONFIG)
        conn.select_db('quiz_app')
        cursor = conn.cursor()
        
        try:
            # 加载单选题
            for q in questions_data.get('singleChoice', []):
                cursor.execute(
                    "SELECT COUNT(*) FROM single_choice_question WHERE id = %s",
                    (q['id'],)
                )
                
                if cursor.fetchone()[0] == 0:
                    cursor.execute(
                        """
                        INSERT INTO single_choice_question 
                        (id, question, options, answer, category, difficulty)
                        VALUES (%s, %s, %s, %s, %s, %s)
                        """,
                        (q['id'], q['question'], json.dumps(q['options']), 
                         q['answer'], '大数据', 1)
                    )
            
            # 加载多选题
            for q in questions_data.get('multipleChoice', []):
                cursor.execute(
                    "SELECT COUNT(*) FROM multiple_choice_question WHERE id = %s",
                    (q['id'],)
                )
                
                if cursor.fetchone()[0] == 0:
                    cursor.execute(
                        """
                        INSERT INTO multiple_choice_question 
                        (id, question, options, answer, category, difficulty)
                        VALUES (%s, %s, %s, %s, %s, %s)
                        """,
                        (q['id'], q['question'], json.dumps(q['options']), 
                         json.dumps(q['answer']), '大数据', 1)
                    )
            
            conn.commit()
            print("Questions loaded successfully!")
            
        except Exception as e:
            conn.rollback()
            print(f"Error loading questions: {e}")
        finally:
            cursor.close()
            conn.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    init_database()
    load_questions()

    # 检查并创建必要的数据库表
    with app.app_context():
        # 先检查每个表是否存在，如果不存在则创建
        if not engine.dialect.has_table(engine, 'user'):
            User.__table__.create(engine)
        else:
            # 检查是否需要添加is_active列
            inspector = inspect.from_engine(engine)
            columns = [column['name'] for column in inspector.get_columns('user')]
            if 'is_active' not in columns:
                # 添加is_active列，默认值为1（True）
                with engine.connect() as con:
                    con.execute(text("ALTER TABLE `user` ADD COLUMN `is_active` BOOLEAN DEFAULT 1"))
                    print("已为用户表添加is_active字段")
        
        if not engine.dialect.has_table(engine, 'class'):
            Class.__table__.create(engine) 
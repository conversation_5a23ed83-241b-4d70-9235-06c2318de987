server {
    listen 80;
    server_name www.qiangs.xyz qiangs.xyz;
    root /www/wwwroot/www.qiangs.xyz;
    
    # 访问日志
    access_log /www/wwwlogs/www.qiangs.xyz.access.log;
    error_log /www/wwwlogs/www.qiangs.xyz.error.log;
    
    location / {
        include uwsgi_params;
        uwsgi_pass 127.0.0.1:8080;
        
        # 解决中文路径问题
        uwsgi_param UWSGI_CHDIR /www/wwwroot/www.qiangs.xyz;
        uwsgi_param UWSGI_SCRIPT app:app;
        
        # 设置超时时间
        uwsgi_read_timeout 600;
        uwsgi_connect_timeout 600;
        uwsgi_send_timeout 600;
        
        # 缓冲区设置
        uwsgi_buffer_size 128k;
        uwsgi_buffers 4 256k;
        uwsgi_busy_buffers_size 256k;
    }
    
    # 静态文件处理
    location /static {
        alias /www/wwwroot/www.qiangs.xyz/static;
        expires 30d;
    }
    
    # 避免favicon.ico的404错误
    location = /favicon.ico {
        log_not_found off;
        access_log off;
    }
    
    # robots.txt处理
    location = /robots.txt {
        log_not_found off;
        access_log off;
    }
}

# HTTPS配置 (如果有SSL证书)
server {
    listen 443 ssl http2;
    server_name www.qiangs.xyz qiangs.xyz;
    root /www/wwwroot/www.qiangs.xyz;
    
    # SSL证书配置（您需要在宝塔面板中申请或上传证书）
    # ssl_certificate /www/server/panel/vhost/cert/www.qiangs.xyz/fullchain.pem;
    # ssl_certificate_key /www/server/panel/vhost/cert/www.qiangs.xyz/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers EECDH+AESGCM:EDH+AESGCM;
    ssl_ecdh_curve secp384r1;
    ssl_session_timeout 10m;
    ssl_session_cache shared:SSL:10m;
    ssl_session_tickets off;
    ssl_stapling on;
    ssl_stapling_verify on;
    
    # HTTP 重定向到 HTTPS
    if ($server_port !~ 443){
        rewrite ^(/.*)$ https://$host$1 permanent;
    }
    
    # 访问日志
    access_log /www/wwwlogs/www.qiangs.xyz.ssl.access.log;
    error_log /www/wwwlogs/www.qiangs.xyz.ssl.error.log;
    
    location / {
        include uwsgi_params;
        uwsgi_pass 127.0.0.1:8080;
        
        # 解决中文路径问题
        uwsgi_param UWSGI_CHDIR /www/wwwroot/www.qiangs.xyz;
        uwsgi_param UWSGI_SCRIPT app:app;
        
        # 设置超时时间
        uwsgi_read_timeout 600;
        uwsgi_connect_timeout 600;
        uwsgi_send_timeout 600;
        
        # 缓冲区设置
        uwsgi_buffer_size 128k;
        uwsgi_buffers 4 256k;
        uwsgi_busy_buffers_size 256k;
    }
    
    # 静态文件处理
    location /static {
        alias /www/wwwroot/www.qiangs.xyz/static;
        expires 30d;
    }
    
    # 避免favicon.ico的404错误
    location = /favicon.ico {
        log_not_found off;
        access_log off;
    }
    
    # robots.txt处理
    location = /robots.txt {
        log_not_found off;
        access_log off;
    }
} 
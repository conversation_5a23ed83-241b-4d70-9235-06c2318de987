<!-- pages/quiz/multiple/multiple.wxml -->
<view class="container">
  <!-- 顶部进度条 -->
  <view class="progress-bar">
    <view class="progress-inner" style="width: {{progress}}%"></view>
  </view>
  
  <text class="question-counter">{{currentQuestionIndex + 1}} / {{questions.length}}</text>
  
  <!-- 课程筛选选项卡 - 在错题本模式和单个题目练习模式下不显示 -->
  <view class="course-filter" wx:if="{{coursesList && coursesList.length > 0 && mode !== 'wrong' && mode !== 'practice'}}">
    <scroll-view scroll-x="true" class="course-scroll" enhanced show-scrollbar="{{false}}">
      <view class="course-tabs">
        <view class="course-tab {{activeCourse === 0 ? 'active' : ''}}" bindtap="switchCourse" data-id="0">
          <text>全部课程</text>
        </view>
        <block wx:for="{{coursesList}}" wx:key="id">
          <view class="course-tab {{activeCourse === item.id ? 'active' : ''}}" bindtap="switchCourse" data-id="{{item.id}}">
            <text>{{item.name}}</text>
            <text class="course-count">{{item.question_count}}</text>
          </view>
        </block>
      </view>
    </scroll-view>
  </view>
  
  <!-- 题目内容 -->
  <view class="question-container" wx:if="{{!loading && questions.length > 0}}">
    <text class="question">{{questions[currentQuestionIndex].question}}</text>
    <text class="question-hint">(多选题，可选择多个正确答案)</text>
    
    <!-- 选项列表 -->
    <view class="options-list">
      <block wx:for="{{questions[currentQuestionIndex].options}}" wx:key="index">
        <view 
          bindtap="selectOption"
          data-index="{{index}}"
          class="option {{!submitted && selectedOptions[index] ? 'selected' : ''}} {{submitted ? optionColors[index] : ''}}"
        >
          <text class="option-letter">{{index === 0 ? 'A' : index === 1 ? 'B' : index === 2 ? 'C' : 'D'}}</text>
          <text class="option-text">{{item.text}}</text>
          <!-- 显示正确/错误答案标记 -->
          <view class="answer-mark" wx:if="{{submitted}}">
            <text wx:if="{{optionColors[index] === 'correct'}}" class="correct-mark">✓</text>
            <text wx:if="{{optionColors[index] === 'wrong'}}" class="wrong-mark">✗</text>
          </view>
        </view>
      </block>
    </view>
    
    <!-- 正确答案提示区域 -->
    <view class="correct-answers" wx:if="{{submitted}}">
      <view class="correct-answers-header">正确答案:</view>
      <view class="correct-answers-content">
        <view wx:for="{{questions[currentQuestionIndex].correctAnswers}}" wx:key="*this" class="correct-answer-circle">
          {{item === 0 ? 'A' : item === 1 ? 'B' : item === 2 ? 'C' : 'D'}}
        </view>
      </view>
    </view>
    
    <!-- 答案结果提示 -->
    <view class="result-tip" wx:if="{{submitted}}">
      <text class="{{isCorrect ? 'correct-tip' : 'wrong-tip'}}">
        {{isCorrect ? '恭喜你，回答正确！' : '很遗憾，回答错误！'}}
      </text>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button 
        class="action-btn submit-btn" 
        bindtap="submitAnswer" 
        wx:if="{{!submitted}}"
        disabled="{{!hasSelection}}"
      >
        提交答案
      </button>
      
      <button 
        class="action-btn next-btn" 
        bindtap="nextQuestion" 
        wx:if="{{submitted}}"
      >
        {{currentQuestionIndex + 1 >= questions.length ? '完成答题' : '下一题'}}
      </button>
    </view>
  </view>
  
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading"></view>
    <text class="loading-text">加载题目中...</text>
  </view>
  
  <!-- 完成确认对话框 -->
  <view class="modal-container" wx:if="{{completed}}">
    <view class="modal">
      <view class="modal-header">
        <text class="modal-title">完成答题</text>
      </view>
      <view class="modal-content">
        <text>您已完成所有多选题，共{{questions.length}}题，答对{{results.correct}}题。</text>
        <text>查看详细答题结果？</text>
      </view>
      <view class="modal-footer">
        <button class="modal-btn cancel-btn" bindtap="continuePractice">继续</button>
        <button class="modal-btn confirm-btn" bindtap="viewResults">查看结果</button>
      </view>
    </view>
  </view>
</view> 
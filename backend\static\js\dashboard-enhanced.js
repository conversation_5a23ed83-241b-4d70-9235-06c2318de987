// Enhanced Dashboard Charts Configuration
document.addEventListener('DOMContentLoaded', function() {
    // Add essential styles for chart loading and error states
    const chartStyles = document.createElement('style');
    chartStyles.textContent = `
        .chart-loading {
            opacity: 0.6;
            filter: blur(1px);
            transition: all 0.3s ease;
        }
        .chart-loading-indicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
        }
        .chart-error-message {
            background-color: #fee2e2;
            color: #ef4444;
            padding: 8px 12px;
            border-radius: 4px;
            text-align: center;
            margin: 10px auto;
            font-size: 14px;
            max-width: 80%;
        }
        .chart-container {
            position: relative;
            transition: all 0.3s ease;
            height: 100%;
            width: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .chart-initialized canvas {
            animation: chart-fade-in 0.5s ease-in-out;
        }
        @keyframes chart-fade-in {
            from { opacity: 0; transform: scale(0.97); }
            to { opacity: 1; transform: scale(1); }
        }
        .chart-card {
            height: 100%;
            transition: all 0.3s ease;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        .chart-card:hover {
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        .chart-canvas {
            width: 100% !important;
            height: 100% !important;
        }
        
        /* Enhanced pie chart styling */
        #questionTypeChart {
            max-height: 300px !important;
            margin: 0 auto;
        }
        
        /* Center align canvas within container */
        .col-lg-4 .chart-container {
            padding: 20px 10px 30px 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        /* Specifically style the legend to appear at the bottom */
        #questionTypeChart + .chartjs-legend-bottom {
            margin-top: 10px;
            width: 100%;
            display: flex;
            justify-content: center;
        }
        
        /* Make the legend items display horizontally */
        .chartjs-bottom-legend ul {
            display: flex;
            flex-direction: row;
            justify-content: center;
            gap: 20px;
            padding: 0;
            margin: 10px 0 0 0;
            list-style: none;
        }
        
        /* Style for tooltips */
        #custom-chart-tooltip {
            position: absolute;
            background: rgba(17, 24, 39, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            pointer-events: none;
            z-index: 100;
            opacity: 0;
            transition: opacity 0.3s;
        }
        
        /* Override Chart.js legend position to ensure it's at the bottom */
        .chart-js-bottom-legend {
            width: 100%;
            text-align: center;
            margin-top: 10px;
        }
    `;
    document.head.appendChild(chartStyles);
    
    // Check if Chart.js is loaded
    if (typeof Chart === 'undefined') {
        console.error('Chart.js library not loaded');
        showStaticCharts();
        return;
    }
    
    console.log('Chart.js loaded, initializing charts');
    try {
        initializeEnhancedCharts();
    } catch (error) {
        console.error('Failed to initialize charts:', error);
        showStaticCharts();
    }
});

// Main function to initialize enhanced charts with error handling
function initializeEnhancedCharts() {
    try {
        // Define color palette for consistent styling
        window.colorPalette = {
            primary: { main: '#4f46e5', light: 'rgba(79, 70, 229, 0.2)' },
            secondary: { main: '#64748b', light: 'rgba(100, 116, 139, 0.2)' },
            success: { main: '#10b981', light: 'rgba(16, 185, 129, 0.2)' },
            danger: { main: '#ef4444', light: 'rgba(239, 68, 68, 0.2)' },
            warning: { main: '#f59e0b', light: 'rgba(245, 158, 11, 0.2)' },
            info: { main: '#3b82f6', light: 'rgba(59, 130, 246, 0.2)' }
        };

        // Initialize all charts
        initializeAllCharts();
        
        // Show success notification if available
        if (typeof showToastNotification === 'function') {
            showToastNotification('仪表盘图表初始化成功', 'success');
        }
    } catch (error) {
        console.error('Error initializing enhanced charts:', error);
        showStaticCharts();
    }
}

// Initialize all charts in the dashboard
function initializeAllCharts() {
    console.log('Initializing all dashboard charts...');
    
    // Initialize activity chart
    const activityChartCanvas = document.getElementById('activityChart');
    if (activityChartCanvas) {
        initializeActivityChart();
    }
    
    // Initialize question type distribution chart
    const questionTypeCanvas = document.getElementById('questionTypeChart');
    if (questionTypeCanvas) {
        initializeQuestionTypeChart();
    }
    
    // Initialize period distribution chart
    const periodCanvas = document.getElementById('periodChart');
    if (periodCanvas) {
        // Add custom initialization for this chart (to be implemented)
    }
    
    // Initialize user activity analysis chart
    const activityAnalysisCanvas = document.getElementById('activityAnalysisChart');
    if (activityAnalysisCanvas) {
        // Add custom initialization for this chart (to be implemented)
    }
    
    // Initialize user activity chart (用户活跃度分析)
    const userActivityCanvas = document.getElementById('userActivityChart');
    if (userActivityCanvas) {
        initializeUserActivityChart(userActivityCanvas);
    }
    
    // Initialize user activity periods chart (用户活跃时段)
    const userActivityPeriodsCanvas = document.getElementById('userActivityPeriodsChart');
    if (userActivityPeriodsCanvas) {
        initializeUserActivityPeriodsChart(userActivityPeriodsCanvas);
    }
    
    // Initialize user answer trends chart (用户答题趋势)
    const userAnswerTrendsCanvas = document.getElementById('userAnswerTrendsChart');
    if (userAnswerTrendsCanvas) {
        initializeUserAnswerTrendsChart(userAnswerTrendsCanvas);
    }
    
    // Initialize difficulty distribution chart (难度分布)
    const difficultyCanvas = document.getElementById('difficultyChart');
    if (difficultyCanvas) {
        initializeDifficultyChart(difficultyCanvas);
    }
    
    // Initialize active time chart (用户活跃时段分布)
    const activeTimeCanvas = document.getElementById('activeTimeChart');
    if (activeTimeCanvas) {
        initializeActiveTimeChart(activeTimeCanvas);
    }
    
    // Add resize handler for responsive charts
    let resizeTimer;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(function() {
            // Resize all Chart instances when window is resized
            if (typeof Chart !== 'undefined') {
                Chart.instances.forEach(chart => {
                    chart.resize();
                    chart.update();
                });
            }
        }, 200);
    });
    
    // Mark all chart containers as initialized
    document.querySelectorAll('.chart-container').forEach(container => {
        container.classList.add('chart-initialized');
    });
}

// Initialize activity chart with real data from API
function initializeActivityChart(canvas) {
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    const chartContainer = canvas.closest('.chart-container');
    if (!chartContainer) return;
    
    // Create loading indicator
    chartContainer.classList.add('chart-loading');
    const loadingIndicator = document.createElement('div');
    loadingIndicator.className = 'chart-loading-indicator';
    loadingIndicator.innerHTML = '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>';
    chartContainer.appendChild(loadingIndicator);
    
    // Create enhanced gradient background for better visuals
    const createGradient = (ctx) => {
        const gradient = ctx.createLinearGradient(0, 0, 0, 300);
        gradient.addColorStop(0, 'rgba(79, 70, 229, 0.6)');
        gradient.addColorStop(1, 'rgba(79, 70, 229, 0.0)');
        return gradient;
    };
    
    const getPointBackgroundColor = (value, previousValue) => {
        // Change point color based on trend (improve visualization)
        if (previousValue === null) return '#4f46e5';
        if (value > previousValue) return '#059669'; // increasing (green)
        if (value < previousValue) return '#dc2626'; // decreasing (red)
        return '#4f46e5'; // unchanging (purple)
    };
    
    // Enhanced chart options for better responsiveness and appearance
    const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
            mode: 'index',
            intersect: false,
        },
        plugins: {
            legend: {
                display: false,
            },
            tooltip: {
                enabled: true,
                backgroundColor: 'rgba(255, 255, 255, 0.9)',
                titleColor: '#1f2937',
                bodyColor: '#4b5563',
                borderColor: '#e5e7eb',
                borderWidth: 1,
                padding: 10,
                cornerRadius: 6,
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                callbacks: {
                    title: function(tooltipItems) {
                        return tooltipItems[0].label;
                    },
                    label: function(context) {
                        return `Total Answers: ${context.raw}`;
                    },
                    afterLabel: function(context) {
                        const dataset = context.dataset;
                        const currentValue = dataset.data[context.dataIndex];
                        const prevIndex = context.dataIndex > 0 ? context.dataIndex - 1 : null;
                        
                        if (prevIndex !== null) {
                            const prevValue = dataset.data[prevIndex];
                            const change = currentValue - prevValue;
                            const percentage = prevValue !== 0 ? (change / prevValue * 100).toFixed(1) : 0;
                            const symbol = change >= 0 ? '↑' : '↓';
                            return `Change: ${symbol} ${Math.abs(change)} (${Math.abs(percentage)}%)`;
                        }
                        return '';
                    }
                }
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: 'rgba(226, 232, 240, 0.6)',
                    drawBorder: false
                },
                ticks: {
                    padding: 10,
                    color: '#64748b',
                    font: {
                        size: 11,
                        weight: '500'
                    }
                }
            },
            x: {
                grid: {
                    display: false
                },
                ticks: {
                    padding: 10,
                    color: '#64748b',
                    font: {
                        size: 11,
                        weight: '500'
                    },
                    maxRotation: 30,
                    minRotation: 0
                }
            }
        },
        elements: {
            line: {
                tension: 0.4,
                borderWidth: 2,
                borderColor: '#4f46e5',
                fill: true,
                backgroundColor: function(context) {
                    return createGradient(ctx);
                }
            },
            point: {
                radius: 4,
                hitRadius: 8,
                hoverRadius: 6,
                hoverBorderWidth: 2
            }
        },
        animation: {
            duration: 1000,
            easing: 'easeOutQuart'
        }
    };
    
    // Function to fetch data from API with retry
    const fetchDataWithRetry = async (url, retries = 3, delay = 500) => {
        let lastError;
        
        for (let i = 0; i < retries; i++) {
            try {
                const response = await fetch(url);
                if (!response.ok) throw new Error(`HTTP error: ${response.status}`);
                return await response.json();
            } catch (error) {
                console.error(`Attempt ${i + 1} failed:`, error);
                lastError = error;
                
                if (i < retries - 1) {
                    await new Promise(resolve => setTimeout(resolve, delay));
                    delay *= 1.5; // Exponential backoff
                }
            }
        }
        
        throw lastError;
    };
    
    // Default fallback data in case API fails
    const fallbackData = {
        dates: [...Array(14)].map((_, i) => {
            const date = new Date();
            date.setDate(date.getDate() - (13 - i));
            return date.toISOString().split('T')[0];
        }),
        totals: [...Array(14)].map(() => Math.floor(Math.random() * 30 + 10)),
        correct_rates: [...Array(14)].map(() => (Math.random() * 0.5 + 0.3).toFixed(2))
    };
    
    // Fetch real data from the API
    fetchDataWithRetry('/api/statistics/trend-data')
        .then(data => {
            // Remove loading indicator
            chartContainer.classList.remove('chart-loading');
            loadingIndicator.remove();
            
            // Process data for chart
            const dates = data.dates || fallbackData.dates;
            const totals = data.totals || fallbackData.totals;
            
            // Calculate point colors based on trends
            const pointBackgroundColors = totals.map((value, index) => {
                const prevValue = index > 0 ? totals[index - 1] : null;
                return getPointBackgroundColor(value, prevValue);
            });
            
            // Create chart
            const activityChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: dates,
                    datasets: [{
                        label: 'User Activity',
                        data: totals,
                        borderColor: '#4f46e5',
                        pointBackgroundColor: pointBackgroundColors,
                        pointBorderColor: '#ffffff',
                        tension: 0.4,
                        fill: true,
                        backgroundColor: createGradient(ctx)
                    }]
                },
                options: chartOptions
            });
            
            // Mark chart as initialized
            chartContainer.classList.add('chart-initialized');
            
            // Add click event for interactive data insights
            ctx.onclick = function(evt) {
                const points = activityChart.getElementsAtEventForMode(evt, 'nearest', { intersect: true }, false);
                
                if (points.length) {
                    const point = points[0];
                    const label = activityChart.data.labels[point.index];
                    const value = activityChart.data.datasets[0].data[point.index];
                    const correctRate = data.correct_rates ? data.correct_rates[point.index] : '-';
                    
                    // Show detailed information in a toast or popover
                    if (typeof bootstrap !== 'undefined') {
                        const toastEl = document.createElement('div');
                        toastEl.className = 'toast align-items-center text-white bg-primary border-0 position-fixed bottom-0 end-0 m-3';
                        toastEl.setAttribute('role', 'alert');
                        toastEl.setAttribute('aria-live', 'assertive');
                        toastEl.setAttribute('aria-atomic', 'true');
                        toastEl.innerHTML = `
                            <div class="d-flex">
                                <div class="toast-body">
                                    <strong>${label}</strong>: ${value} total answers<br>
                                    Correct rate: ${(correctRate * 100).toFixed(1)}%
                                </div>
                                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                            </div>
                        `;
                        document.body.appendChild(toastEl);
                        const toast = new bootstrap.Toast(toastEl, { delay: 3000 });
                        toast.show();
                        
                        toastEl.addEventListener('hidden.bs.toast', function () {
                            toastEl.remove();
                        });
                    } else {
                        console.log(`${label}: ${value} total answers, Correct rate: ${(correctRate * 100).toFixed(1)}%`);
                    }
                }
            };
        })
        .catch(error => {
            console.error('Error fetching activity data:', error);
            chartContainer.classList.remove('chart-loading');
            loadingIndicator.remove();
            
            // Show fallback chart with sample data
            const activityChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: fallbackData.dates,
                    datasets: [{
                        label: 'User Activity (Sample)',
                        data: fallbackData.totals,
                        borderColor: '#9ca3af',
                        backgroundColor: 'rgba(156, 163, 175, 0.2)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    ...chartOptions,
                    plugins: {
                        ...chartOptions.plugins,
                        tooltip: {
                            ...chartOptions.plugins.tooltip,
                            callbacks: {
                                ...chartOptions.plugins.tooltip.callbacks,
                                footer: function() {
                                    return 'Using sample data - Unable to load real data';
                                }
                            }
                        }
                    }
                }
            });
            
            // Add error message
            const errorMsg = document.createElement('div');
            errorMsg.className = 'chart-error-message';
            errorMsg.textContent = 'Unable to load real data. Showing sample data instead.';
            chartContainer.appendChild(errorMsg);
            
            // Mark chart as initialized
            chartContainer.classList.add('chart-initialized');
        });
}

// Add interactive features to the chart
function addChartInteractions(canvas, chartInstance) {
    // Add hover highlight effect
    canvas.addEventListener('mousemove', function(e) {
        const points = chartInstance.getElementsAtEventForMode(e, 'nearest', { intersect: true }, false);
        if (points.length) {
            const point = points[0];
            const dataset = chartInstance.data.datasets[point.datasetIndex];
            const value = dataset.data[point.index];
            
            // Highlight the current point
            if (Array.isArray(dataset.pointRadius)) {
                const originalRadius = dataset.pointRadius.slice();
                dataset.pointRadius = dataset.pointRadius.map((r, i) => i === point.index ? r * 1.5 : r);
                
                // Reset after a short delay
                setTimeout(() => {
                    dataset.pointRadius = originalRadius;
                    chartInstance.update();
                }, 300);
                
                chartInstance.update();
            }
        }
    });
    
    // Add click event to show more details
    canvas.addEventListener('click', function(e) {
        const points = chartInstance.getElementsAtEventForMode(e, 'nearest', { intersect: true }, false);
        if (points.length) {
            const point = points[0];
            const dataset = chartInstance.data.datasets[point.datasetIndex];
            const value = dataset.data[point.index];
            const label = chartInstance.data.labels[point.index];
            
            // Show detailed information in a modal or tooltip
            if (typeof showToastNotification === 'function') {
                showToastNotification(`${label}: ${dataset.label} = ${value}`, 'info');
            }
        }
    });
}

// Set up handlers for daily/weekly/monthly view buttons
function setupChartViewHandlers(chartInstance, fullData) {
    const viewButtons = document.querySelectorAll('[data-chart-view]');
    if (!viewButtons.length) return;
    
    // Store original full data
    const originalData = {
        labels: [...fullData.labels],
        answer_counts: [...fullData.answer_counts],
        correct_rates: fullData.correct_rates ? [...fullData.correct_rates] : null
    };
    
    viewButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Update active button state
            viewButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            const viewType = this.getAttribute('data-chart-view');
            updateChartDataView(chartInstance, viewType, originalData);
        });
    });
}

// Update chart data based on selected view
function updateChartDataView(chartInstance, viewType, originalData) {
    let labels = [...originalData.labels];
    let answerCounts = [...originalData.answer_counts];
    let correctRates = originalData.correct_rates ? [...originalData.correct_rates] : null;
    
    // Apply transformations based on view type
    if (viewType === 'weekly') {
        // Group data by week (assuming labels are date strings)
        const weeklyData = aggregateDataByWeek(labels, answerCounts, correctRates);
        labels = weeklyData.labels;
        answerCounts = weeklyData.answerCounts;
        correctRates = weeklyData.correctRates;
    } else if (viewType === 'monthly') {
        // Group data by month
        const monthlyData = aggregateDataByMonth(labels, answerCounts, correctRates);
        labels = monthlyData.labels;
        answerCounts = monthlyData.answerCounts;
        correctRates = monthlyData.correctRates;
    }
    
    // Update chart data
    chartInstance.data.labels = labels;
    chartInstance.data.datasets[0].data = answerCounts;
    
    if (correctRates && chartInstance.data.datasets.length > 1) {
        chartInstance.data.datasets[1].data = correctRates;
    }
    
    // Update with animation
    chartInstance.update({
        duration: 600,
        easing: 'easeOutCubic'
    });
}

// Helper function to aggregate data by week
function aggregateDataByWeek(dateLabels, answerCounts, correctRates) {
    const weeks = {};
    
    dateLabels.forEach((dateStr, index) => {
        try {
            const date = new Date(dateStr);
            const year = date.getFullYear();
            const weekNumber = getWeekNumber(date);
            const weekKey = `${year}-W${weekNumber}`;
            
            if (!weeks[weekKey]) {
                weeks[weekKey] = {
                    label: `第${weekNumber}周`,
                    totalAnswers: 0,
                    totalCorrect: 0,
                    count: 0
                };
            }
            
            weeks[weekKey].totalAnswers += answerCounts[index] || 0;
            if (correctRates) {
                // For correct rates, we need to calculate weighted average
                weeks[weekKey].totalCorrect += (correctRates[index] || 0) * (answerCounts[index] || 1);
            }
            weeks[weekKey].count++;
        } catch (e) {
            console.warn('Error processing date:', dateStr, e);
        }
    });
    
    // Convert the aggregated data back to arrays
    const result = {
        labels: [],
        answerCounts: [],
        correctRates: correctRates ? [] : null
    };
    
    Object.keys(weeks).sort().forEach(weekKey => {
        const weekData = weeks[weekKey];
        result.labels.push(weekData.label);
        result.answerCounts.push(weekData.totalAnswers);
        if (correctRates) {
            const avgCorrectRate = weekData.totalAnswers > 0 ? 
                weekData.totalCorrect / weekData.totalAnswers : 0;
            result.correctRates.push(parseFloat(avgCorrectRate.toFixed(1)));
        }
    });
    
    return result;
}

// Helper function to aggregate data by month
function aggregateDataByMonth(dateLabels, answerCounts, correctRates) {
    const months = {};
    const monthNames = ['一月', '二月', '三月', '四月', '五月', '六月', 
                        '七月', '八月', '九月', '十月', '十一月', '十二月'];
    
    dateLabels.forEach((dateStr, index) => {
        try {
            const date = new Date(dateStr);
            const year = date.getFullYear();
            const month = date.getMonth();
            const monthKey = `${year}-${month}`;
            
            if (!months[monthKey]) {
                months[monthKey] = {
                    label: monthNames[month],
                    totalAnswers: 0,
                    totalCorrect: 0,
                    count: 0
                };
            }
            
            months[monthKey].totalAnswers += answerCounts[index] || 0;
            if (correctRates) {
                months[monthKey].totalCorrect += (correctRates[index] || 0) * (answerCounts[index] || 1);
            }
            months[monthKey].count++;
        } catch (e) {
            console.warn('Error processing date:', dateStr, e);
        }
    });
    
    // Convert the aggregated data back to arrays
    const result = {
        labels: [],
        answerCounts: [],
        correctRates: correctRates ? [] : null
    };
    
    Object.keys(months).sort().forEach(monthKey => {
        const monthData = months[monthKey];
        result.labels.push(monthData.label);
        result.answerCounts.push(monthData.totalAnswers);
        if (correctRates) {
            const avgCorrectRate = monthData.totalAnswers > 0 ? 
                monthData.totalCorrect / monthData.totalAnswers : 0;
            result.correctRates.push(parseFloat(avgCorrectRate.toFixed(1)));
        }
    });
    
    return result;
}

// Helper function to get week number
function getWeekNumber(date) {
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDaysOfYear = (date - firstDayOfYear) / 86400000;
    return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
}

function initializeChart(canvas) {
    try {
        if (!canvas) {
            console.warn('Canvas element is null');
            return;
        }
        
        // Skip if already initialized
        if (canvas.chartInitialized) {
            return;
        }
        
        let chartData;
        try {
            const dataAttr = canvas.getAttribute('data-chart-values');
            if (!dataAttr) {
                console.warn(`No data found for chart ${canvas.id}`);
                return;
            }
            
            chartData = JSON.parse(dataAttr);
            if (!chartData || !chartData.datasets || !chartData.labels) {
                console.warn(`Invalid chart data for ${canvas.id}:`, chartData);
                replaceCanvasWithFallback(canvas, 'Invalid chart data');
                return;
            }
        } catch (e) {
            console.error(`Error parsing chart data for ${canvas.id}:`, e);
            replaceCanvasWithFallback(canvas, 'Error parsing chart data');
            return;
        }
        
        const chartType = determineChartType(canvas.id, chartData);
        console.log(`Creating ${chartType} chart for ${canvas.id}`);
        
        // Create the chart
        new Chart(canvas, {
            type: chartType,
            data: chartData,
            options: getOptionsForChart(chartType)
        });
        
        // Mark as initialized to avoid duplicate initialization
        canvas.chartInitialized = true;
        console.log(`Successfully initialized chart: ${canvas.id}`);
    } catch (error) {
        console.error(`Failed to initialize chart ${canvas.id}:`, error);
        replaceCanvasWithFallback(canvas, 'Chart initialization failed');
    }
}

// Determine chart type based on canvas ID and data
function determineChartType(canvasId, chartData) {
    // Default to bar chart
    let chartType = 'bar';
    
    if (canvasId.includes('Pie') || canvasId.toLowerCase().includes('type') || canvasId.toLowerCase().includes('distribution')) {
        chartType = 'pie';
    } else if (canvasId.includes('Line') || canvasId.toLowerCase().includes('trend') || canvasId.toLowerCase().includes('activity')) {
        chartType = 'line';
    } else if (canvasId.includes('Doughnut')) {
        chartType = 'doughnut';
    }
    
    // Check if data structure suggests a specific chart type
    if (chartData.datasets && chartData.datasets.length > 0) {
        const dataset = chartData.datasets[0];
        if (dataset.borderColor && dataset.fill !== undefined) {
            chartType = 'line';
        }
    }
    
    return chartType;
}

// Get chart options based on chart type
function getOptionsForChart(chartType) {
    const basicOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top',
                labels: {
                    padding: 15,
                    usePointStyle: true,
                    font: {
                        family: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
                        size: 12
                    }
                }
            },
            title: {
                display: false
            },
            tooltip: {
                backgroundColor: 'rgba(33, 33, 33, 0.9)',
                titleFont: {
                    size: 13
                },
                bodyFont: {
                    size: 12
                },
                padding: 10,
                cornerRadius: 6,
                displayColors: true
            }
        },
        layout: {
            padding: {
                top: 5,
                right: 5,
                bottom: 5,
                left: 5
            }
        },
        animation: {
            duration: 800,
            easing: 'easeOutQuart'
        }
    };
    
    switch (chartType) {
        case 'pie':
        case 'doughnut':
            return {
                ...basicOptions,
                plugins: {
                    ...basicOptions.plugins,
                    legend: {
                        position: 'bottom',
                        align: 'center',
                        labels: {
                            padding: 15,
                            usePointStyle: true,
                            boxWidth: 10,
                            boxHeight: 10,
                            font: {
                                size: 11,
                                family: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif'
                            },
                            generateLabels: function(chart) {
                                const data = chart.data;
                                if (data.labels.length && data.datasets.length) {
                                    return data.labels.map(function(label, i) {
                                        const meta = chart.getDatasetMeta(0);
                                        const style = meta.controller.getStyle(i);
                                        
                                        // Calculate percentage for the label
                                        const value = data.datasets[0].data[i];
                                        const total = data.datasets[0].data.reduce((a, b) => a + b, 0);
                                        const percentage = Math.round((value / total) * 100);
                                        
                                        return {
                                            text: `${label} (${percentage}%)`,
                                            fillStyle: style.backgroundColor,
                                            strokeStyle: style.borderColor,
                                            lineWidth: style.borderWidth,
                                            hidden: !chart.getDataVisibility(i),
                                            index: i
                                        };
                                    });
                                }
                                return [];
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                },
                cutout: chartType === 'doughnut' ? '40%' : 0,
                radius: '85%',
                elements: {
                    arc: {
                        borderWidth: 2,
                        borderColor: '#ffffff',
                        hoverBorderWidth: 3,
                        hoverBorderColor: '#ffffff'
                    }
                },
                // Add layout settings for better positioning with bottom legend
                layout: {
                    padding: {
                        top: 10,
                        bottom: 20,
                        left: 10,
                        right: 10
                    }
                },
                // Add animations
                animation: {
                    animateRotate: true,
                    animateScale: true,
                    duration: 1000,
                    easing: 'easeOutQuart',
                    delay: function(context) {
                        return context.dataIndex * 100;
                    }
                }
            };
        case 'line':
            return {
                ...basicOptions,
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(200, 200, 200, 0.1)',
                            drawBorder: false
                        },
                        ticks: {
                            font: {
                                size: 11,
                                family: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif'
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            maxRotation: 0,
                            font: {
                                size: 11,
                                family: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif'
                            }
                        }
                    }
                },
                elements: {
                    line: {
                        tension: 0.4,
                        borderWidth: 2
                    },
                    point: {
                        radius: 3,
                        backgroundColor: '#fff',
                        borderWidth: 2,
                        hoverRadius: 5,
                        hoverBorderWidth: 2
                    }
                }
            };
        case 'bar':
            return {
                ...basicOptions,
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(200, 200, 200, 0.1)',
                            drawBorder: false
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                },
                elements: {
                    bar: {
                        borderWidth: 0,
                        borderRadius: 4
                    }
                }
            };
        default:
            return basicOptions;
    }
}

// Show static content when charts fail to load
function showStaticCharts() {
    console.log('Displaying fallback static charts');
    const chartElements = document.querySelectorAll('canvas');
    chartElements.forEach(function(canvas) {
        replaceCanvasWithFallback(canvas, '图表加载失败，请刷新页面重试');
    });
}

// Replace canvas with fallback message
function replaceCanvasWithFallback(canvasElement, message) {
    try {
        // Only replace if the element exists and is a canvas
        if (!canvasElement || canvasElement.tagName !== 'CANVAS') return;
        
        // Create container for fallback content
        const fallbackContainer = document.createElement('div');
        fallbackContainer.className = 'chart-fallback-container';
        fallbackContainer.style.cssText = `
            width: 100%; 
            height: 200px; 
            display: flex; 
            align-items: center; 
            justify-content: center;
            flex-direction: column;
            background-color: #f9fafb;
            border-radius: 8px;
            padding: 20px;
            border: 1px dashed #e5e7eb;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        `;
        
        // Add icon
        const iconElement = document.createElement('div');
        iconElement.innerHTML = '<i class="fas fa-chart-bar" style="font-size: 2.5rem; color: #d1d5db; margin-bottom: 15px;"></i>';
        fallbackContainer.appendChild(iconElement);
        
        // Add message
        const messageElement = document.createElement('div');
        messageElement.textContent = message;
        messageElement.style.cssText = 'color: #6b7280; font-size: 15px; font-weight: 500; margin-bottom: 15px; text-align: center;';
        fallbackContainer.appendChild(messageElement);
        
        // Add refresh button
        const refreshButton = document.createElement('button');
        refreshButton.textContent = '刷新图表';
        refreshButton.className = 'btn btn-sm btn-primary mt-2';
        refreshButton.style.cssText = 'background-color: #4f46e5; border: none; padding: 8px 16px; border-radius: 6px; color: white; font-weight: 500; cursor: pointer;';
        refreshButton.onclick = function() {
            // Try to reload the chart or refresh the page
            try {
                if (typeof initializeEnhancedCharts === 'function') {
                    // Replace with canvas again
                    const newCanvas = document.createElement('canvas');
                    newCanvas.id = canvasElement.id;
                    newCanvas.setAttribute('data-chart-values', canvasElement.getAttribute('data-chart-values') || '');
                    fallbackContainer.parentNode.replaceChild(newCanvas, fallbackContainer);
                    initializeEnhancedCharts();
                } else {
            window.location.reload();
                }
            } catch (e) {
                console.error('Failed to reload chart:', e);
                window.location.reload();
            }
        };
        
        // Add subtle "Powered by" text
        const poweredByText = document.createElement('div');
        poweredByText.textContent = '由 Chart.js 提供支持';
        poweredByText.style.cssText = 'color: #9ca3af; font-size: 11px; margin-top: 10px;';
        
        fallbackContainer.appendChild(refreshButton);
        fallbackContainer.appendChild(poweredByText);
        
        // Replace canvas with fallback
        canvasElement.parentNode.replaceChild(fallbackContainer, canvasElement);
        
        // Log the error
        console.log(`Chart "${canvasElement.id}" replaced with fallback. Error: ${message}`);
    } catch (error) {
        console.error('Failed to create fallback for chart:', error);
    }
}

// Initialize smooth scrolling for anchor links
function initializeSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            if (targetId !== '#') {
                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            }
        });
    });
}

// Create a notification function as fallback
function showToastNotification(message, type = 'info', duration = 3000) {
    try {
        // Check if we already have a toast container
        let toastContainer = document.getElementById('toast-container');
        
        // If no container exists, create one
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            
            // Add toast container styles if they don't exist
            if (!document.getElementById('toast-styles')) {
                const toastStyles = document.createElement('style');
                toastStyles.id = 'toast-styles';
                toastStyles.textContent = `
                    #toast-container {
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        z-index: 9999;
                    }
                    .toast {
                        padding: 12px 20px;
                        border-radius: 6px;
                        margin-bottom: 10px;
                        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                        min-width: 250px;
                        opacity: 0;
                        transform: translateY(-20px);
                        transition: all 0.3s ease;
                        display: flex;
                        align-items: center;
                    }
                    .toast.show {
                        opacity: 1;
                        transform: translateY(0);
                    }
                    .toast-info {
                        background-color: #3b82f6;
                        color: white;
                    }
                    .toast-success {
                        background-color: #10b981;
                        color: white;
                    }
                    .toast-warning {
                        background-color: #f59e0b;
                        color: white;
                    }
                    .toast-error {
                        background-color: #ef4444;
                        color: white;
                    }
                    .toast-icon {
                        margin-right: 10px;
                    }
                `;
                document.head.appendChild(toastStyles);
            }
            
            document.body.appendChild(toastContainer);
        }
        
        // Create the toast element
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        
        // Set icon based on type
        let icon = '';
        switch (type) {
            case 'success': icon = '<i class="fas fa-check-circle toast-icon"></i>'; break;
            case 'warning': icon = '<i class="fas fa-exclamation-triangle toast-icon"></i>'; break;
            case 'error': icon = '<i class="fas fa-times-circle toast-icon"></i>'; break;
            default: icon = '<i class="fas fa-info-circle toast-icon"></i>';
        }
        
        toast.innerHTML = `${icon}${message}`;
        toastContainer.appendChild(toast);
        
        // Trigger reflow to enable transition
        toast.offsetHeight;
        toast.classList.add('show');
        
        // Auto-remove after duration
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentElement) {
                    toastContainer.removeChild(toast);
                }
            }, 300);
        }, duration);
    } catch (error) {
        console.error('Failed to show notification:', error);
    }
}

// Function to show notification
function showChartNotification(message, type = 'info') {
    if (typeof showNotification === 'function') {
        showNotification(message, type);
    } else {
        console.log(message);
        
        // Fallback notification if the main function isn't available
        try {
            const toast = document.createElement('div');
            toast.className = `toast align-items-center text-white bg-${type} border-0 position-fixed top-0 end-0 m-3`;
            toast.setAttribute('role', 'alert');
            toast.setAttribute('aria-live', 'assertive');
            toast.setAttribute('aria-atomic', 'true');
            
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-${type === 'success' ? 'check-circle' : 
                                        type === 'danger' ? 'exclamation-circle' : 
                                        type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            `;
            
            document.body.appendChild(toast);
            
            if (typeof bootstrap !== 'undefined' && bootstrap.Toast) {
                const bsToast = new bootstrap.Toast(toast, { delay: 3000 });
                bsToast.show();
                
                toast.addEventListener('hidden.bs.toast', function() {
                    document.body.removeChild(toast);
                });
            } else {
                // Simple fallback if Bootstrap is not available
                setTimeout(() => {
                    if (toast.parentElement) {
                        document.body.removeChild(toast);
                    }
                }, 3000);
            }
        } catch (e) {
            console.error('Failed to show notification:', e);
        }
    }
}

function initializeQuestionTypeChart() {
    const ctx = document.getElementById('questionTypeChart');
    if (!ctx) return;
    
    const chartContainer = ctx.closest('.chart-container');
    if (!chartContainer) return;
    
    // Create loading indicator
    chartContainer.classList.add('chart-loading');
    const loadingIndicator = document.createElement('div');
    loadingIndicator.className = 'chart-loading-indicator';
    loadingIndicator.innerHTML = '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>';
    chartContainer.appendChild(loadingIndicator);
    
    // Beautiful color palette for pie chart (simplified to just two colors as shown in screenshot)
    const colorPalette = [
        '#4f46e5', // Indigo for 单选题
        '#10b981'  // Green for 多选题
    ];
    
    // Enhanced options for pie chart
    const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                align: 'center',
                labels: {
                    usePointStyle: true,
                    padding: 15,
                    boxWidth: 10,
                    boxHeight: 10,
                    font: {
                        size: 12,
                        weight: '500'
                    }
                }
            },
            tooltip: {
                enabled: true,
                backgroundColor: 'rgba(17, 24, 39, 0.9)',
                titleColor: '#ffffff',
                bodyColor: '#ffffff',
                borderColor: 'rgba(17, 24, 39, 0.9)',
                borderWidth: 0,
                padding: 10,
                cornerRadius: 6,
                displayColors: false,
                callbacks: {
                    title: function() {
                        return ''; // No title in tooltip
                    },
                    label: function(context) {
                        const label = context.label || '';
                        const value = context.raw;
                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = Math.round((value / total) * 100);
                        return `${label}: ${value} (${percentage}%)`;
                    }
                }
            }
        },
        elements: {
            arc: {
                borderWidth: 0, // Remove border between segments
                hoverBorderWidth: 0
            }
        },
        interaction: {
            mode: 'nearest',
            intersect: true,
            // Show custom tooltip on hover
            onHover: function(event, elements) {
                if (elements && elements.length) {
                    event.native.target.style.cursor = 'pointer';
                } else {
                    event.native.target.style.cursor = 'default';
                }
            }
        },
        animation: {
            animateRotate: true,
            animateScale: true,
            duration: 800,
            easing: 'easeOutQuart'
        },
        cutout: 0, // Make it a pie chart, not doughnut
        layout: {
            padding: {
                top: 20,
                bottom: 45, // Add extra padding at the bottom for the legend
                left: 10,
                right: 10
            }
        }
    };
    
    // Fetch question type distribution data from an API or use fallback
    fetch('/api/statistics/question-types')
        .then(response => {
            if (!response.ok) throw new Error(`HTTP error: ${response.status}`);
            return response.json();
        })
        .then(data => {
            // Remove loading indicator
            chartContainer.classList.remove('chart-loading');
            loadingIndicator.remove();
            
            // Process data for chart (simplified to match screenshot with just two types)
            const labels = data.labels || ['单选题', '多选题'];
            const values = data.values || [50, 70]; // Example values
            
            // Create pie chart
            const questionTypeChart = new Chart(ctx, {
                type: 'pie', // Change to pie from doughnut
                data: {
                    labels: labels,
                    datasets: [{
                        data: values,
                        backgroundColor: colorPalette,
                        hoverBackgroundColor: colorPalette,
                        borderWidth: 0
                    }]
                },
                options: chartOptions
            });
            
            // Add custom hover functionality to show percentage overlay like in second screenshot
            ctx.onclick = function(evt) {
                const points = questionTypeChart.getElementsAtEventForMode(evt, 'nearest', { intersect: true }, false);
                if (points.length) {
                    const point = points[0];
                    const dataset = questionTypeChart.data.datasets[point.datasetIndex];
                    const value = dataset.data[point.index];
                    const label = questionTypeChart.data.labels[point.index];
                    const total = dataset.data.reduce((a, b) => a + b, 0);
                    const percentage = Math.round((value / total) * 100);
                    
                    // Show tooltip on click
                    const tooltipEl = document.getElementById('custom-chart-tooltip') || createTooltipElement();
                    tooltipEl.innerHTML = `${label}: ${value} (${percentage}%)`;
                    tooltipEl.style.opacity = 1;
                    tooltipEl.style.left = evt.offsetX + 'px';
                    tooltipEl.style.top = evt.offsetY + 'px';
                    
                    // Hide after 2 seconds
                    setTimeout(() => {
                        tooltipEl.style.opacity = 0;
                    }, 2000);
                }
            };
            
            // Create tooltip element if needed
            function createTooltipElement() {
                const tooltipEl = document.createElement('div');
                tooltipEl.id = 'custom-chart-tooltip';
                tooltipEl.style.cssText = `
                    position: absolute;
                    background: rgba(17, 24, 39, 0.9);
                    color: white;
                    padding: 8px 12px;
                    border-radius: 4px;
                    font-size: 12px;
                    pointer-events: none;
                    z-index: 100;
                    opacity: 0;
                    transition: opacity 0.3s;
                `;
                document.body.appendChild(tooltipEl);
                return tooltipEl;
            }
            
            // Mark chart as initialized
            chartContainer.classList.add('chart-initialized');
        })
        .catch(error => {
            console.error('Error fetching question type data:', error);
            chartContainer.classList.remove('chart-loading');
            loadingIndicator.remove();
            
            // Create fallback data matching the screenshot
            const labels = ['单选题', '多选题'];
            const values = [50, 70]; // Example values like in the screenshot
            
            // Create pie chart with fallback data
            new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: labels,
                    datasets: [{
                        data: values,
                        backgroundColor: colorPalette,
                        hoverBackgroundColor: colorPalette,
                        borderWidth: 0
                    }]
                },
                options: chartOptions
            });
            
            // Mark chart as initialized
            chartContainer.classList.add('chart-initialized');
        });
}

// Function to initialize user activity chart
function initializeUserActivityChart(canvas) {
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    const chartContainer = canvas.closest('.chart-container');
    if (!chartContainer) return;
    
    // Show loading state
    chartContainer.classList.add('chart-loading');
    const loadingIndicator = document.createElement('div');
    loadingIndicator.className = 'chart-loading-indicator';
    loadingIndicator.innerHTML = '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>';
    chartContainer.appendChild(loadingIndicator);
    
    // Parse chart data from data attribute
    let activityData = null;
    try {
        if (canvas.dataset && canvas.dataset.chartValues) {
            const rawData = JSON.parse(canvas.dataset.chartValues);
            if (rawData.labels && rawData.datasets && rawData.datasets[0] && rawData.datasets[0].data) {
                activityData = {
                    labels: rawData.labels,
                    datasets: rawData.datasets
                };
                console.log('Successfully parsed user activity data:', activityData);
            }
        }
    } catch (e) {
        console.error('Failed to parse user activity chart data:', e);
    }
    
    // Handle missing data
    if (!activityData) {
        console.error('No valid data found for user activity chart');
        chartContainer.classList.remove('chart-loading');
        loadingIndicator.remove();
        replaceCanvasWithFallback(canvas, '未能加载用户活跃度数据');
        return;
    }
    
    // Configure chart
    const chart = new Chart(ctx, {
        type: 'line',
        data: activityData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            animation: {
                duration: 1000,
                easing: 'easeOutQuart'
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)',
                        drawBorder: false
                    },
                    ticks: {
                        padding: 10,
                        font: {
                            size: 12,
                            family: "'Inter', 'Helvetica', 'Arial', sans-serif"
                        },
                        color: '#64748b'
                    }
                },
                x: {
                    grid: {
                        display: false,
                        drawBorder: false
                    },
                    ticks: {
                        padding: 10,
                        font: {
                            size: 12,
                            family: "'Inter', 'Helvetica', 'Arial', sans-serif"
                        },
                        color: '#64748b'
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: '#1e293b',
                    titleColor: '#ffffff',
                    bodyColor: '#e2e8f0',
                    padding: 12,
                    cornerRadius: 8,
                    displayColors: false,
                    callbacks: {
                        label: function(context) {
                            return `活跃用户: ${context.raw} 人`;
                        }
                    }
                }
            }
        }
    });
    
    // Cleanup loading state
    chartContainer.classList.remove('chart-loading');
    loadingIndicator.remove();
    
    // Mark chart as initialized
    chartContainer.classList.add('chart-initialized');
    
    return chart;
}

// Function to initialize user activity periods chart
function initializeUserActivityPeriodsChart(canvas) {
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    const chartContainer = canvas.closest('.chart-container');
    if (!chartContainer) return;
    
    // Show loading state
    chartContainer.classList.add('chart-loading');
    const loadingIndicator = document.createElement('div');
    loadingIndicator.className = 'chart-loading-indicator';
    loadingIndicator.innerHTML = '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>';
    chartContainer.appendChild(loadingIndicator);
    
    // Parse chart data from data attribute
    let periodsData = null;
    try {
        if (canvas.dataset && canvas.dataset.chartValues) {
            const rawData = JSON.parse(canvas.dataset.chartValues);
            if (rawData.labels && rawData.datasets && rawData.datasets[0] && rawData.datasets[0].data) {
                periodsData = {
                    labels: rawData.labels,
                    datasets: rawData.datasets
                };
                console.log('Successfully parsed user activity periods data:', periodsData);
            }
        }
    } catch (e) {
        console.error('Failed to parse user activity periods chart data:', e);
    }
    
    // Handle missing data
    if (!periodsData) {
        console.error('No valid data found for user activity periods chart');
        chartContainer.classList.remove('chart-loading');
        loadingIndicator.remove();
        replaceCanvasWithFallback(canvas, '未能加载用户活跃时段数据');
        return;
    }
    
    // Configure chart
    const chart = new Chart(ctx, {
        type: 'bar',
        data: periodsData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            animation: {
                duration: 1000,
                easing: 'easeOutQuart'
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)',
                        drawBorder: false
                    },
                    ticks: {
                        padding: 10,
                        font: {
                            size: 12,
                            family: "'Inter', 'Helvetica', 'Arial', sans-serif"
                        },
                        color: '#64748b'
                    }
                },
                x: {
                    grid: {
                        display: false,
                        drawBorder: false
                    },
                    ticks: {
                        padding: 10,
                        font: {
                            size: 12,
                            family: "'Inter', 'Helvetica', 'Arial', sans-serif"
                        },
                        color: '#64748b'
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: '#1e293b',
                    titleColor: '#ffffff',
                    bodyColor: '#e2e8f0',
                    padding: 12,
                    cornerRadius: 8,
                    displayColors: false,
                    callbacks: {
                        label: function(context) {
                            return `活跃人数: ${context.raw} 人`;
                        }
                    }
                }
            },
            barThickness: 30,
            maxBarThickness: 40,
            borderRadius: 6,
            categoryPercentage: 0.7,
            barPercentage: 0.8
        }
    });
    
    // Cleanup loading state
    chartContainer.classList.remove('chart-loading');
    loadingIndicator.remove();
    
    // Mark chart as initialized
    chartContainer.classList.add('chart-initialized');
    
    return chart;
}

// Function to initialize user answer trends chart
function initializeUserAnswerTrendsChart(canvas) {
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    const chartContainer = canvas.closest('.chart-container');
    if (!chartContainer) return;
    
    // Show loading state
    chartContainer.classList.add('chart-loading');
    const loadingIndicator = document.createElement('div');
    loadingIndicator.className = 'chart-loading-indicator';
    loadingIndicator.innerHTML = '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>';
    chartContainer.appendChild(loadingIndicator);
    
    // Parse chart data from data attribute
    let trendsData = null;
    try {
        if (canvas.dataset && canvas.dataset.chartValues) {
            const rawData = JSON.parse(canvas.dataset.chartValues);
            if (rawData.labels && rawData.datasets && rawData.datasets[0] && rawData.datasets[0].data) {
                trendsData = {
                    labels: rawData.labels,
                    datasets: rawData.datasets
                };
                console.log('Successfully parsed user answer trends data:', trendsData);
            }
        }
    } catch (e) {
        console.error('Failed to parse user answer trends chart data:', e);
    }
    
    // Handle missing data
    if (!trendsData) {
        console.error('No valid data found for user answer trends chart');
        chartContainer.classList.remove('chart-loading');
        loadingIndicator.remove();
        replaceCanvasWithFallback(canvas, '未能加载用户答题趋势数据');
        return;
    }
    
    // Create gradient background for better visuals
    const gradient = ctx.createLinearGradient(0, 0, 0, 400);
    gradient.addColorStop(0, 'rgba(79, 70, 229, 0.4)');
    gradient.addColorStop(1, 'rgba(79, 70, 229, 0.0)');
    
    // Update dataset with gradient
    trendsData.datasets[0].backgroundColor = gradient;
    
    // Configure chart
    const chart = new Chart(ctx, {
        type: 'line',
        data: trendsData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            animation: {
                duration: 1000,
                easing: 'easeOutQuart'
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)',
                        drawBorder: false
                    },
                    ticks: {
                        padding: 10,
                        font: {
                            size: 12,
                            family: "'Inter', 'Helvetica', 'Arial', sans-serif"
                        },
                        color: '#64748b'
                    }
                },
                x: {
                    grid: {
                        display: false,
                        drawBorder: false
                    },
                    ticks: {
                        padding: 10,
                        font: {
                            size: 12,
                            family: "'Inter', 'Helvetica', 'Arial', sans-serif"
                        },
                        color: '#64748b'
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: '#1e293b',
                    titleColor: '#ffffff',
                    bodyColor: '#e2e8f0',
                    padding: 12,
                    cornerRadius: 8,
                    displayColors: false,
                    callbacks: {
                        label: function(context) {
                            return `答题数: ${context.raw}`;
                        }
                    }
                }
            },
            elements: {
                line: {
                    tension: 0.4,
                    borderWidth: 2,
                    fill: true
                },
                point: {
                    radius: 4,
                    hitRadius: 10,
                    hoverRadius: 6,
                    borderWidth: 2,
                    backgroundColor: '#ffffff'
                }
            }
        }
    });
    
    // Cleanup loading state
    chartContainer.classList.remove('chart-loading');
    loadingIndicator.remove();
    
    // Mark chart as initialized
    chartContainer.classList.add('chart-initialized');
    
    return chart;
}

// Function to initialize difficulty distribution chart
function initializeDifficultyChart(canvas) {
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    const chartContainer = canvas.closest('.chart-container') || canvas.closest('.donut-chart-container');
    if (!chartContainer) return;
    
    // Show loading state
    chartContainer.classList.add('chart-loading');
    const loadingIndicator = document.createElement('div');
    loadingIndicator.className = 'chart-loading-indicator';
    loadingIndicator.innerHTML = '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>';
    chartContainer.appendChild(loadingIndicator);
    
    // Parse chart data from data attribute
    let difficultyData = null;
    try {
        if (canvas.dataset && canvas.dataset.chartValues) {
            const rawData = JSON.parse(canvas.dataset.chartValues);
            if (rawData.labels && rawData.datasets && rawData.datasets[0] && rawData.datasets[0].data) {
                difficultyData = {
                    labels: rawData.labels,
                    datasets: rawData.datasets
                };
                console.log('Successfully parsed difficulty distribution data:', difficultyData);
            }
        }
    } catch (e) {
        console.error('Failed to parse difficulty chart data:', e);
    }
    
    // Handle missing data
    if (!difficultyData) {
        console.error('No valid data found for difficulty distribution chart');
        chartContainer.classList.remove('chart-loading');
        loadingIndicator.remove();
        replaceCanvasWithFallback(canvas, '未能加载难度分布数据');
        return;
    }
    
    // Configure chart
    const chart = new Chart(ctx, {
        type: 'doughnut',
        data: difficultyData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '65%',
            animation: {
                animateRotate: true,
                animateScale: true,
                duration: 800,
                easing: 'easeOutQuart'
            },
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        usePointStyle: true,
                        padding: 15,
                        boxWidth: 10,
                        boxHeight: 10,
                        font: {
                            size: 12,
                            weight: '500'
                        }
                    }
                },
                tooltip: {
                    backgroundColor: '#1e293b',
                    titleColor: '#ffffff',
                    bodyColor: '#e2e8f0',
                    padding: 12,
                    cornerRadius: 8,
                    displayColors: false,
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.raw;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = Math.round((value / total) * 100);
                            return `${label}: ${percentage}%`;
                        }
                    }
                }
            },
            elements: {
                arc: {
                    borderWidth: 0,
                    borderRadius: 4
                }
            }
        }
    });
    
    // Cleanup loading state
    chartContainer.classList.remove('chart-loading');
    loadingIndicator.remove();
    
    // Mark chart as initialized
    chartContainer.classList.add('chart-initialized');
    
    return chart;
}

// Function to initialize active time chart
function initializeActiveTimeChart(canvas) {
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    const chartContainer = canvas.closest('.chart-container');
    if (!chartContainer) return;
    
    // Show loading state
    chartContainer.classList.add('chart-loading');
    const loadingIndicator = document.createElement('div');
    loadingIndicator.className = 'chart-loading-indicator';
    loadingIndicator.innerHTML = '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>';
    chartContainer.appendChild(loadingIndicator);
    
    // Parse chart data from data attribute
    let activeTimeData = null;
    try {
        if (canvas.dataset && canvas.dataset.chartValues) {
            const rawData = JSON.parse(canvas.dataset.chartValues);
            if (rawData.labels && rawData.datasets && rawData.datasets[0] && rawData.datasets[0].data) {
                activeTimeData = {
                    labels: rawData.labels,
                    datasets: rawData.datasets
                };
                console.log('Successfully parsed active time data:', activeTimeData);
            }
        }
    } catch (e) {
        console.error('Failed to parse active time chart data:', e);
    }
    
    // Handle missing data
    if (!activeTimeData) {
        console.error('No valid data found for active time chart');
        chartContainer.classList.remove('chart-loading');
        loadingIndicator.remove();
        replaceCanvasWithFallback(canvas, '未能加载用户活跃时段分布数据');
        return;
    }
    
    // Configure chart
    const chart = new Chart(ctx, {
        type: 'polarArea',
        data: activeTimeData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            animation: {
                duration: 1000,
                easing: 'easeOutQuart'
            },
            scales: {
                r: {
                    ticks: {
                        backdropColor: 'transparent',
                        color: '#64748b',
                        z: 100
                    },
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    },
                    pointLabels: {
                        font: {
                            size: 12,
                            family: "'Inter', 'Helvetica', 'Arial', sans-serif"
                        },
                        color: '#64748b'
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        usePointStyle: true,
                        padding: 15,
                        boxWidth: 10,
                        boxHeight: 10,
                        font: {
                            size: 12,
                            weight: '500'
                        }
                    }
                },
                tooltip: {
                    backgroundColor: '#1e293b',
                    titleColor: '#ffffff',
                    bodyColor: '#e2e8f0',
                    padding: 12,
                    cornerRadius: 8,
                    displayColors: false,
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.raw;
                            return `${label}: ${value} 人`;
                        }
                    }
                }
            }
        }
    });
    
    // Cleanup loading state
    chartContainer.classList.remove('chart-loading');
    loadingIndicator.remove();
    
    // Mark chart as initialized
    chartContainer.classList.add('chart-initialized');
    
    return chart;
} 
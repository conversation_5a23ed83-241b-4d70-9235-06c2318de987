{% extends "admin/base.html" %}

{% block title %}数据统计 - 大数据题库管理后台{% endblock %}

{% block header %}数据统计{% endblock %}

{% block head %}
<!-- 引入图表全屏样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/chart-fullsize.css') }}">
{% endblock %}

{% block content %}
<div class="row">
    <!-- 用户统计 -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                用户统计
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="stats-card">
                            <div class="number">{{ total_users }}</div>
                            <div class="label">总用户数</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="stats-card">
                            <div class="number">{{ active_users|default(0) }}</div>
                            <div class="label">活跃天数</div>
                        </div>
                    </div>
                </div>
                
                <h5>用户增长趋势</h5>
                <div style="height: 200px;">
                    <canvas id="userGrowthChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 答题统计 -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                答题统计
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="stats-card">
                            <div class="number">{{ total_records }}</div>
                            <div class="label">总答题数</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stats-card">
                            <div class="number">{{ correct_records }}</div>
                            <div class="label">正确数</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stats-card">
                            <div class="number">{{ "%.1f"|format(accuracy_rate) }}%</div>
                            <div class="label">正确率</div>
                        </div>
                    </div>
                </div>
                
                <h5>题型分布</h5>
                <div style="height: 200px;">
                    <canvas id="questionTypesChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 各题型正确率 -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                各题型正确率
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="small-stats-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="stat-title">单选题</div>
                                <div class="stat-value">{{ "%.1f"|format(single_accuracy) }}%</div>
                            </div>
                            <div class="progress mt-2">
                                <div class="progress-bar bg-primary" role="progressbar" style="width: {{ single_accuracy }}%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="small-stats-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="stat-title">多选题</div>
                                <div class="stat-value">{{ "%.1f"|format(multiple_accuracy) }}%</div>
                            </div>
                            <div class="progress mt-2">
                                <div class="progress-bar bg-success" role="progressbar" style="width: {{ multiple_accuracy }}%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="small-stats-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="stat-title">判断题</div>
                                <div class="stat-value">{{ "%.1f"|format(judgment_accuracy) }}%</div>
                            </div>
                            <div class="progress mt-2">
                                <div class="progress-bar bg-warning" role="progressbar" style="width: {{ judgment_accuracy }}%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="small-stats-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="stat-title">填空题</div>
                                <div class="stat-value">{{ "%.1f"|format(fill_blank_accuracy) }}%</div>
                            </div>
                            <div class="progress mt-2">
                                <div class="progress-bar bg-info" role="progressbar" style="width: {{ fill_blank_accuracy }}%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div style="height: 200px; margin-top: 15px;">
                    <canvas id="accuracyComparisonChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 题目难度分布 -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                题目难度与分类
            </div>
            <div class="card-body">
                <h5 class="mb-3">难度分布</h5>
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="text-center">
                            <div class="difficulty-circle easy">
                                <span>{{ "%.1f"|format(difficulty_percentages.easy) }}%</span>
                            </div>
                            <div class="mt-2">简单</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <div class="difficulty-circle medium">
                                <span>{{ "%.1f"|format(difficulty_percentages.medium) }}%</span>
                            </div>
                            <div class="mt-2">中等</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <div class="difficulty-circle hard">
                                <span>{{ "%.1f"|format(difficulty_percentages.hard) }}%</span>
                            </div>
                            <div class="mt-2">困难</div>
                        </div>
                    </div>
                </div>
                
                <h5 class="mb-3">热门分类</h5>
                <div style="height: 200px;">
                    <canvas id="topCategoriesChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 每日答题趋势 -->
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header">
                每日答题趋势
            </div>
            <div class="card-body">
                <div style="height: 250px;">
                    <canvas id="dailyRecordsChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 每日数据详情 -->
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                每日数据详情
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>日期</th>
                                <th>新增用户</th>
                                <th>答题数量</th>
                                <th>正确数</th>
                                <th>正确率</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% set user_data = {} %}
                            {% for item in user_daily_growth %}
                                {% set _ = user_data.update({item.date: item.count}) %}
                            {% endfor %}
                            
                            {% for stat in record_daily_stats|reverse %}
                            <tr>
                                <td>{{ stat.date }}</td>
                                <td>{{ user_data.get(stat.date, 0) }}</td>
                                <td>{{ stat.total }}</td>
                                <td>{{ stat.correct }}</td>
                                <td>{{ "%.1f"|format(stat.accuracy) }}%</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card shadow h-100 d-flex flex-column">
            <div class="card-header d-flex justify-content-between align-items-center bg-gradient-light">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line text-primary me-2"></i>用户答题数据趋势
                </h5>
                <div class="chart-actions">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-outline-primary active" id="daily-btn">日</button>
                        <button type="button" class="btn btn-sm btn-outline-primary" id="weekly-btn">周</button>
                        <button type="button" class="btn btn-sm btn-outline-primary" id="monthly-btn">月</button>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="chart-wrapper" style="position: relative; height: 400px; width: 100%;">
                    <canvas id="trendChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="card h-100 d-flex flex-column">
            <div class="card-header">
                <h5 class="card-title">用户答题数据趋势</h5>
            </div>
            <div class="card-body chart-container chart-container-flex">
                {% if charts and charts.system_trend_chart %}
                <img src="data:image/png;base64,{{ charts.system_trend_chart }}" alt="用户答题数据趋势" class="fullsize-chart">
                {% else %}
                <div class="text-center py-5 w-100">
                    <p>暂无足够数据生成图表</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card h-100 d-flex flex-column">
            <div class="card-header">
                <h5 class="card-title">题型分布</h5>
            </div>
            <div class="card-body chart-container chart-container-flex">
                {% if charts and charts.user_growth_chart %}
                <img src="data:image/png;base64,{{ charts.user_growth_chart }}" alt="题型分布" class="fullsize-chart">
                {% else %}
                <div class="text-center py-5 w-100">
                    <p>暂无足够数据生成图表</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="card h-100 d-flex flex-column">
            <div class="card-header">
                <h5 class="card-title">用户活跃时段</h5>
            </div>
            <div class="card-body chart-container flex-grow-1">
                <canvas id="activeTimeChart"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card h-100 d-flex flex-column">
            <div class="card-header">
                <h5 class="card-title">使用情况统计</h5>
            </div>
            <div class="card-body chart-container flex-grow-1">
                <div class="row">
                    <div class="col-md-6">
                        <div class="stats-card">
                            <div class="number">{{ active_users|default(0) }}</div>
                            <div class="label">今日活跃用户</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="stats-card">
                            <div class="number">{{ today_records|default(0) }}</div>
                            <div class="label">今日答题数</div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="stats-card">
                            <div class="number">{{ avg_time|default("0分钟") }}</div>
                            <div class="label">平均使用时长</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="stats-card">
                            <div class="number">{{ "%.1f"|format(accuracy_rate|default(0)) }}%</div>
                            <div class="label">今日正确率</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- 引入自定义增强版趋势图JS -->
<script src="{{ url_for('static', filename='js/trend-chart-enhanced.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 趋势图表
        const trendChartCtx = document.getElementById('trendChart').getContext('2d');
        
        // 创建渐变背景
        const gradientFill = trendChartCtx.createLinearGradient(0, 0, 0, 400);
        gradientFill.addColorStop(0, 'rgba(78, 115, 223, 0.4)');
        gradientFill.addColorStop(0.5, 'rgba(78, 115, 223, 0.1)');
        gradientFill.addColorStop(1, 'rgba(78, 115, 223, 0.02)');
        
        const barGradient = trendChartCtx.createLinearGradient(0, 0, 0, 400);
        barGradient.addColorStop(0, 'rgba(255, 159, 64, 0.9)');
        barGradient.addColorStop(1, 'rgba(255, 159, 64, 0.5)');
        
        const monthLabels = ['1月', '2月', '3月', '4月', '5月', '6月'];
        const weekLabels = ['第1周', '第2周', '第3周', '第4周'];
        const dayLabels = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
        
        const correctRateData = [68, 58, 68, 72, 75, 67];
        const answerCountData = [64, 58, 79, 80, 55, 54];
        
        let trendChart = new Chart(trendChartCtx, {
            type: 'line',
            data: {
                labels: monthLabels,
                datasets: [
                    {
                        label: '答题数量',
                        data: answerCountData,
                        backgroundColor: barGradient,
                        borderColor: 'rgba(255, 159, 64, 1)',
                        borderWidth: 2,
                        pointBackgroundColor: '#fff',
                        pointBorderColor: 'rgba(255, 159, 64, 1)',
                        pointBorderWidth: 2,
                        pointHoverBackgroundColor: 'rgba(255, 159, 64, 1)',
                        pointHoverBorderColor: '#fff',
                        pointHoverBorderWidth: 2,
                        pointRadius: 5,
                        pointHoverRadius: 7,
                        order: 2,
                        yAxisID: 'y',
                        type: 'bar',
                        barPercentage: 0.6,
                        categoryPercentage: 0.7
                    },
                    {
                        label: '正确率 (%)',
                        data: correctRateData,
                        backgroundColor: gradientFill,
                        borderColor: 'rgba(78, 115, 223, 1)',
                        borderWidth: 3,
                        pointBackgroundColor: '#fff',
                        pointBorderColor: 'rgba(78, 115, 223, 1)',
                        pointBorderWidth: 2,
                        pointHoverBackgroundColor: 'rgba(78, 115, 223, 1)',
                        pointHoverBorderColor: '#fff',
                        pointHoverBorderWidth: 2,
                        pointRadius: 5,
                        pointHoverRadius: 7,
                        fill: true,
                        tension: 0.4,
                        yAxisID: 'y1',
                        order: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                layout: {
                    padding: {
                        top: 20,
                        right: 25,
                        bottom: 10,
                        left: 15
                    }
                },
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                plugins: {
                    legend: {
                        position: 'top',
                        align: 'end',
                        labels: {
                            usePointStyle: true,
                            padding: 20,
                            boxWidth: 8,
                            boxHeight: 8,
                            font: {
                                size: 12,
                                weight: '600'
                            }
                        }
                    },
                    tooltip: {
                        enabled: true,
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        titleColor: '#5a5c69',
                        bodyColor: '#5a5c69',
                        titleFont: {
                            size: 14,
                            weight: 'bold'
                        },
                        bodyFont: {
                            size: 13
                        },
                        borderColor: '#ddd',
                        borderWidth: 1,
                        padding: 12,
                        cornerRadius: 8,
                        usePointStyle: true,
                        boxWidth: 8,
                        boxHeight: 8,
                        callbacks: {
                            title: function(tooltipItems) {
                                return tooltipItems[0].label + ' 数据';
                            },
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += context.dataset.label.includes('%') ? 
                                        context.parsed.y + '%' : 
                                        context.parsed.y + ' 题';
                                }
                                return label;
                            },
                            labelPointStyle: function(context) {
                                return {
                                    pointStyle: 'circle',
                                    rotation: 0
                                };
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: '答题数量',
                            color: 'rgba(255, 159, 64, 1)',
                            font: {
                                size: 12,
                                weight: 'bold'
                            }
                        },
                        grid: {
                            drawBorder: false,
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            padding: 10,
                            font: {
                                size: 11
                            },
                            color: 'rgba(255, 159, 64, 0.8)'
                        }
                    },
                    y1: {
                        beginAtZero: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: '正确率 (%)',
                            color: 'rgba(78, 115, 223, 1)',
                            font: {
                                size: 12,
                                weight: 'bold'
                            }
                        },
                        min: 0,
                        max: 100,
                        grid: {
                            drawOnChartArea: false,
                        },
                        ticks: {
                            padding: 10,
                            font: {
                                size: 11
                            },
                            color: 'rgba(78, 115, 223, 0.8)'
                        }
                    },
                    x: {
                        grid: {
                            drawBorder: false,
                            display: false
                        },
                        ticks: {
                            padding: 10,
                            font: {
                                size: 11
                            }
                        }
                    }
                },
                animations: {
                    radius: {
                        duration: 400,
                        easing: 'linear'
                    },
                    tension: {
                        duration: 1000,
                        easing: 'linear'
                    }
                }
            }
        });
        
        // 切换视图按钮绑定事件
        document.getElementById('daily-btn').addEventListener('click', function() {
            const dailyAnswerData = [65, 59, 80, 81, 75, 60, 70];
            const dailyCorrectData = [70, 60, 75, 82, 78, 65, 73];
            
            // 设置更生动的色彩
            trendChart.data.datasets[0].backgroundColor = barGradient;
            trendChart.data.datasets[1].borderColor = 'rgba(78, 115, 223, 1)';
            
            updateChartWithAnimation(dayLabels, dailyAnswerData, dailyCorrectData);
            setActiveButton(this);
        });
        
        document.getElementById('weekly-btn').addEventListener('click', function() {
            const weeklyAnswerData = [63, 75, 80, 52];
            const weeklyCorrectData = [68, 74, 77, 65];
            
            // 设置更生动的色彩
            const weekBarGradient = trendChartCtx.createLinearGradient(0, 0, 0, 400);
            weekBarGradient.addColorStop(0, 'rgba(28, 200, 138, 0.9)'); // 绿色渐变
            weekBarGradient.addColorStop(1, 'rgba(28, 200, 138, 0.5)');
            
            trendChart.data.datasets[0].backgroundColor = weekBarGradient;
            trendChart.data.datasets[1].borderColor = 'rgba(54, 185, 204, 1)'; // 蓝绿色
            
            updateChartWithAnimation(weekLabels, weeklyAnswerData, weeklyCorrectData);
            setActiveButton(this);
        });
        
        document.getElementById('monthly-btn').addEventListener('click', function() {
            // 恢复原始色彩
            trendChart.data.datasets[0].backgroundColor = barGradient;
            trendChart.data.datasets[1].borderColor = 'rgba(78, 115, 223, 1)';
            
            updateChartWithAnimation(monthLabels, answerCountData, correctRateData);
            setActiveButton(this);
        });
        
        // 带动画效果的图表更新
        function updateChartWithAnimation(labels, answerData, correctData) {
            // 先隐藏数据点
            trendChart.data.datasets[0].pointRadius = 0;
            trendChart.data.datasets[1].pointRadius = 0;
            
            // 更新标签和数据
            trendChart.data.labels = labels;
            trendChart.data.datasets[0].data = answerData;
            trendChart.data.datasets[1].data = correctData;
            
            // 应用更新
            trendChart.update('none'); // 先不带动画更新
            
            // 延迟后显示数据点并带动画
            setTimeout(() => {
                trendChart.data.datasets[0].pointRadius = 5;
                trendChart.data.datasets[1].pointRadius = 5;
                trendChart.update({
                    duration: 800,
                    easing: 'easeOutQuart'
                });
            }, 50);
        }
        
        function setActiveButton(activeButton) {
            document.querySelectorAll('.chart-actions .btn').forEach(btn => {
                btn.classList.remove('active');
            });
            activeButton.classList.add('active');
        }
        
        // 用户增长趋势图
        const userGrowthCtx = document.getElementById('userGrowthChart').getContext('2d');
        const userGrowthData = {{ user_daily_growth|tojson }};
        
        new Chart(userGrowthCtx, {
            type: 'line',
            data: {
                labels: userGrowthData.map(item => item.date),
                datasets: [{
                    label: '新增用户数',
                    data: userGrowthData.map(item => item.count),
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    borderColor: 'rgba(75, 192, 192, 1)',
                    borderWidth: 2,
                    tension: 0.3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            boxWidth: 12,
                            font: {
                                size: 11
                            }
                        }
                    },
                    title: {
                        display: true,
                        text: '每日新增用户',
                        font: {
                            size: 13
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '用户数',
                            font: {
                                size: 11
                            }
                        },
                        ticks: {
                            font: {
                                size: 10
                            }
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '日期',
                            font: {
                                size: 11
                            }
                        },
                        ticks: {
                            font: {
                                size: 10
                            }
                        }
                    }
                }
            }
        });
        
        // 题型分布饼图
        const questionTypesCtx = document.getElementById('questionTypesChart').getContext('2d');
        
        new Chart(questionTypesCtx, {
            type: 'pie',
            data: {
                labels: ['单选题', '多选题', '判断题', '填空题'],
                datasets: [{
                    data: [
                        {{ single_count }}, 
                        {{ multiple_count }}, 
                        {{ judgment_count|default(0) }}, 
                        {{ fill_blank_count|default(0) }}
                    ],
                    backgroundColor: [
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 159, 64, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(153, 102, 255, 0.7)'
                    ],
                    borderColor: [
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 159, 64, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            boxWidth: 12,
                            font: {
                                size: 11
                            }
                        }
                    },
                    title: {
                        display: true,
                        text: '题型分布',
                        font: {
                            size: 13
                        }
                    }
                }
            }
        });
        
        // 每日答题趋势
        const dailyRecordsCtx = document.getElementById('dailyRecordsChart').getContext('2d');
        const recordDailyStats = {{ record_daily_stats|tojson }};
        
        new Chart(dailyRecordsCtx, {
            type: 'line',
            data: {
                labels: recordDailyStats.map(item => item.date),
                datasets: [{
                    label: '答题数量',
                    data: recordDailyStats.map(item => item.total),
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    borderColor: 'rgba(75, 192, 192, 1)',
                    borderWidth: 2,
                    tension: 0.3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            boxWidth: 12,
                            font: {
                                size: 11
                            }
                        }
                    },
                    title: {
                        display: true,
                        text: '每日答题数量',
                        font: {
                            size: 13
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '题数',
                            font: {
                                size: 11
                            }
                        },
                        ticks: {
                            font: {
                                size: 10
                            }
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '日期',
                            font: {
                                size: 11
                            }
                        },
                        ticks: {
                            font: {
                                size: 10
                            },
                            maxRotation: 45,
                            minRotation: 45
                        }
                    }
                }
            }
        });
        
        // 用户活跃时段图
        const activeTimeCtx = document.getElementById('activeTimeChart').getContext('2d');
        
        new Chart(activeTimeCtx, {
            type: 'bar',
            data: {
                labels: ['上午', '下午', '晚上', '深夜'],
                datasets: [{
                    label: '活跃用户数',
                    data: [15, 28, 32, 8],
                    backgroundColor: [
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(153, 102, 255, 0.7)',
                        'rgba(255, 159, 64, 0.7)'
                    ],
                    borderColor: [
                        'rgba(54, 162, 235, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)',
                        'rgba(255, 159, 64, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: '用户活跃时段分布'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '用户数'
                        }
                    }
                }
            }
        });
        
        // 各题型正确率比较
        const accuracyCtx = document.getElementById('accuracyComparisonChart').getContext('2d');
        
        new Chart(accuracyCtx, {
            type: 'bar',
            data: {
                labels: ['单选题', '多选题', '判断题', '填空题'],
                datasets: [{
                    label: '正确率 (%)',
                    data: [
                        {{ single_accuracy }},
                        {{ multiple_accuracy }},
                        {{ judgment_accuracy }},
                        {{ fill_blank_accuracy }}
                    ],
                    backgroundColor: [
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 159, 64, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(153, 102, 255, 0.7)'
                    ],
                    borderColor: [
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 159, 64, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y',
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: '各题型正确率比较',
                        font: {
                            size: 13
                        }
                    }
                },
                scales: {
                    x: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: '正确率 (%)',
                            font: {
                                size: 11
                            }
                        }
                    }
                }
            }
        });
        
        // 热门分类图表
        const categoriesCtx = document.getElementById('topCategoriesChart').getContext('2d');
        const topCategories = {{ top_categories|tojson }};
        
        new Chart(categoriesCtx, {
            type: 'doughnut',
            data: {
                labels: Object.keys(topCategories),
                datasets: [{
                    data: Object.values(topCategories),
                    backgroundColor: [
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 159, 64, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(153, 102, 255, 0.7)',
                        'rgba(255, 99, 132, 0.7)'
                    ],
                    borderColor: [
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 159, 64, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)',
                        'rgba(255, 99, 132, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            boxWidth: 12,
                            font: {
                                size: 11
                            }
                        }
                    },
                    title: {
                        display: true,
                        text: '热门题目分类',
                        font: {
                            size: 13
                        }
                    }
                }
            }
        });
    });
</script>

<style>
/* 确保图表容器有足够高度 */
.chart-container {
    min-height: 300px;
    height: 100%;
}

/* 确保图表完全填充容器 */
.chart-container img {
    object-fit: contain;
    width: 100%;
    height: 100%;
    display: block;
    max-width: 100%;
}

/* 增加卡片高度 */
.card.h-100 {
    min-height: 400px;
}

/* 小型统计卡片样式 */
.small-stats-card {
    padding: 10px;
    background-color: #f8f9fc;
    border-radius: 5px;
}

.small-stats-card .stat-title {
    font-size: 14px;
    color: #555;
}

.small-stats-card .stat-value {
    font-size: 16px;
    font-weight: bold;
    color: #4e73df;
}

/* 统计卡片样式 */
.stats-card {
    background-color: #f8f9fc;
    border-radius: 5px;
    padding: 15px;
    text-align: center;
    box-shadow: 0 0.15rem 1.75rem rgba(58, 59, 69, 0.15);
}

.stats-card .number {
    font-size: 24px;
    font-weight: bold;
    color: #4e73df;
}

.stats-card .label {
    font-size: 14px;
    color: #5a5c69;
    margin-top: 5px;
}

/* 难度圆环样式 */
.difficulty-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-weight: bold;
    color: white;
    font-size: 16px;
}

.difficulty-circle.easy {
    background-color: #36b9cc;
}

.difficulty-circle.medium {
    background-color: #f6c23e;
}

.difficulty-circle.hard {
    background-color: #e74a3b;
}
</style>
{% endblock %} 
// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 云函数入口函数
exports.main = async (event, context) => {
  try {
    const { question, conversationId } = event;
    
    // 调用腾讯云提供的微信AI接口（智聆AI大模型）
    return await cloud.callFunction({
      name: 'openapi',
      data: {
        action: 'invoke',
        params: {
          service: 'chat',
          function: 'completions',
          data: {
            model: "deepseekcoder", // 可选择不同模型，如"deepseekcoder"或"moonshot"
            messages: [
              {
                role: "user",
                content: question
              }
            ],
            conversation_id: conversationId || null,
            temperature: 0.7,
            presence_penalty: 0.5,
            max_tokens: 1000
          }
        }
      }
    });
  } catch (err) {
    console.error('调用AI服务出错:', err);
    return {
      errCode: -1,
      errMsg: err.message || '无法连接AI服务',
      data: null
    };
  }
} 
# 项目整体功能文档

## 一、项目简介

本项目是一个面向高校学生的大数据课程学习与题目练习平台，包含前端微信小程序（或UniApp多端）和后端API服务。平台支持多种题型的在线答题、错题本、学习统计、课程管理、AI辅助等功能，旨在帮助学生系统性地巩固知识、提升技能。

---

## 二、系统架构

- **前端**：基于UniApp/微信小程序开发，适配多端，提供用户交互界面。
- **后端**：基于Flask开发，提供RESTful API，负责用户、题库、答题、统计等核心业务逻辑。
- **数据库**：MySQL，存储用户、题库、答题记录等数据。

---

## 三、主要功能模块

### 1. 用户系统
- 用户注册/登录（微信授权或手机号等方式）
- 个人信息管理（昵称、头像、联系方式等）
- 账号状态管理（如禁用提示页）

### 2. 课程与题库管理
- 支持多门课程（如大数据技术、习思想、管理学等）
- 题库按课程分类，便于针对性学习
- 题型支持：单选题、多选题、判断题、填空题
- 题库数据可动态更新

### 3. 在线答题与练习
- 题型练习：
  - 单选题练习
  - 多选题练习
  - 判断题练习
  - 填空题练习
- 背题模式：可查看所有题目及答案，便于记忆
- 考试模式：随机抽题，模拟真实考试
- 答题限时、进度条等交互体验

### 4. 错题本管理
- 自动记录用户答错的题目
- 错题重做功能，支持针对错题反复练习
- 错题本分课程、分题型管理

### 5. 学习统计与数据分析
- 统计用户答题数量、正确率、连续学习天数等
- 题型分布统计（单选/多选/判断/填空）
- 每日答题进度、正确率趋势图
- 题型正确率对比、错题分布分析
- 个人学习能力雷达图

### 6. 课程管理
- 课程列表与详情页
- 课程下题目汇总，便于系统性学习
- 课程能力达成度展示

### 7. AI智能辅助
- 内置AI小猫助手，支持智能问答、学习建议
- 辅助用户理解题目、查漏补缺

### 8. 其他功能
- 天气查询（通过云函数获取实时天气，辅助学习生活）
- 反馈与意见收集（可扩展）
- 多端适配（支持微信小程序、APP、H5等）

---

## 四、前端主要页面与功能

1. **首页**：课程导航、题型入口、学习状态展示
2. **登录页**：用户注册/登录
3. **个人中心**：用户信息、学习统计、功能入口
4. **题目练习页**：各类题型答题界面，支持切换题型
5. **错题本**：错题列表、重做入口
6. **统计页**：学习进度、正确率、趋势图等
7. **课程页**：课程详情、题目汇总
8. **结果页**：答题结果、正确率、能力分析
9. **关于页**：应用介绍、开发团队、联系方式
10. **禁用页**：账号被禁用时的提示

---

## 五、后端API功能说明

- **用户认证**：登录/注册、Token校验
- **题目管理**：获取各类题型题目列表、题库管理
- **答题提交**：提交答案，返回判分结果
- **错题本**：获取/管理用户错题本
- **数据统计**：获取用户答题统计、生成图表
- **课程管理**：课程信息、题目归类
- **AI助手接口**：智能问答、学习建议（可扩展）
- **天气查询API**：对接第三方天气服务

---

## 六、技术栈与部署

- **前端**：UniApp（Vue3）、微信小程序原生、云函数
- **后端**：Python 3.8+、Flask、SQLAlchemy、PyMySQL、Matplotlib、Pandas、JWT
- **数据库**：MySQL
- **云服务**：微信云开发、和风天气API等

---

## 七、特色与亮点

- 多题型支持，满足大数据课程多样化考核需求
- 错题本与统计分析，助力个性化学习
- AI智能助手，提升学习效率
- 多端适配，随时随地学习
- 数据可视化，学习效果一目了然

---

## 八、适用场景

- 高校大数据课程在线学习与练习
- 题库刷题、错题巩固、能力提升
- 教师布置作业、考试模拟
- 个人自学与能力评估

---

## 九、后续可扩展方向

- 增加更多课程与题型
- 支持教师端题库管理、作业布置
- 增强AI智能辅导能力
- 增加社交与学习小组功能
- 支持更多第三方服务接入

---

## 十、后端管理端（后台管理系统）功能说明

后端管理端是为系统管理员、教务人员等设计的Web后台管理系统，基于Flask开发，提供丰富的管理功能，便于对平台数据和用户进行维护和运营。主要功能包括：

### 1. 管理员登录与权限控制
- 管理员账号登录、登出、会话管理
- 权限校验，防止未授权访问
- 管理员信息维护（如密码、最近登录时间等）

### 2. 用户管理
- 用户列表浏览、搜索、分页
- 用户详细信息查看与编辑（昵称、头像、班级、联系方式等）
- 用户状态管理（启用/禁用账号）
- 用户删除（连带删除其答题和错题记录）

### 3. 班级管理
- 班级的新增、编辑、删除
- 班级下课程的管理
- 班级信息维护（名称、描述等）

### 4. 课程管理
- 课程的新增、编辑、删除
- 课程归属班级的设置
- 课程信息维护（名称、描述等）

### 5. 题库管理
- 题库的新增、编辑、删除
- 题库归属课程的设置
- 题库信息维护（名称、描述、是否启用等）

### 6. 题目管理
- 支持单选题、多选题、判断题、填空题的增删改查
- 题目可按课程、班级、题库筛选
- 题目内容、选项、答案、解析等信息维护

### 7. 统计分析
- 用户答题数据统计（答题量、正确率、活跃度等）
- 题库、课程、班级等多维度统计报表
- 数据可视化（图表、趋势分析等）

### 8. 其他管理功能
- 管理员操作日志（可扩展）
- 系统设置与维护（如数据库初始化、静态资源管理等）

### 9. 管理端界面导航
- 仪表盘（Dashboard）：系统概览、关键数据展示
- 用户管理、班级管理、课程管理、题库管理、题目管理、统计分析等模块化导航

---

管理端极大提升了平台的可维护性和运营效率，为大数据课程的教学管理、题库维护、用户服务等提供了有力支撑。

---

（本功能文档为后续论文撰写提供全面参考，内容如需细化可进一步补充。）

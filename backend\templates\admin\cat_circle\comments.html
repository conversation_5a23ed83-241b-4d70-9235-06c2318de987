{% extends "admin/base.html" %}

{% block title %}评论管理 - 猫友圈管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-comments" style="color: #007bff;"></i> 评论管理
                </h1>
                <p class="page-subtitle">管理用户发布的评论内容</p>
            </div>
        </div>
    </div>

    <!-- 搜索 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-2">
                            <label class="form-label">动态类型</label>
                            <select name="post_type" class="form-select">
                                <option value="">全部类型</option>
                                <option value="dynamic" {% if current_post_type == 'dynamic' %}selected{% endif %}>动态</option>
                                <option value="secondhand" {% if current_post_type == 'secondhand' %}selected{% endif %}>二手交易</option>
                                <option value="help" {% if current_post_type == 'help' %}selected{% endif %}>求助</option>
                                <option value="lost_found" {% if current_post_type == 'lost_found' %}selected{% endif %}>失物招领</option>
                                <option value="cat_friend" {% if current_post_type == 'cat_friend' %}selected{% endif %}>猫友</option>
                                <option value="cat_friends" {% if current_post_type == 'cat_friends' %}selected{% endif %}>猫友(旧)</option>
                                <option value="campus_run" {% if current_post_type == 'campus_run' %}selected{% endif %}>校园跑</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">排序方式</label>
                            <select name="sort" class="form-select">
                                <option value="created_at_desc" {% if current_sort == 'created_at_desc' %}selected{% endif %}>最新评论</option>
                                <option value="created_at_asc" {% if current_sort == 'created_at_asc' %}selected{% endif %}>最早评论</option>
                            </select>
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">每页</label>
                            <select name="per_page" class="form-select">
                                <option value="5" {% if current_per_page == 5 %}selected{% endif %}>5条</option>
                                <option value="10" {% if current_per_page == 10 %}selected{% endif %}>10条</option>
                                <option value="20" {% if current_per_page == 20 %}selected{% endif %}>20条</option>
                                <option value="50" {% if current_per_page == 50 %}selected{% endif %}>50条</option>
                                <option value="100" {% if current_per_page == 100 %}selected{% endif %}>100条</option>
                            </select>
                        </div>
                        <div class="col-md-5">
                            <label class="form-label">搜索评论内容</label>
                            <div class="input-group">
                                <input type="text" name="search" class="form-control" placeholder="搜索评论内容..." value="{{ current_search or '' }}">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                                {% if current_search %}
                                <a href="?" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i>
                                </a>
                                {% endif %}
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 评论列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">评论列表</h5>
                    <div>
                        {% if pagination %}
                            <span class="text-muted">共 {{ pagination.total }} 条记录</span>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if comments %}
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>ID</th>
                                        <th>评论用户</th>
                                        <th>评论内容</th>
                                        <th>所属动态</th>
                                        <th>动态作者</th>
                                        <th>评论时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for comment in comments %}
                                    <tr>
                                        <td>{{ comment.id }}</td>
                                        <td>
                                            {% if comment.user %}
                                                <div class="d-flex align-items-center">
                                                    {% if comment.user.avatar %}
                                                        <img src="{{ comment.user.avatar }}" class="rounded-circle me-2" width="32" height="32">
                                                    {% else %}
                                                        <div class="bg-secondary rounded-circle me-2 d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                                                            <i class="fas fa-user text-white"></i>
                                                        </div>
                                                    {% endif %}
                                                    <div>
                                                        <div class="fw-bold">{{ comment.user.nickname or '未知用户' }}</div>
                                                        <small class="text-muted">ID: {{ comment.user_id }}</small>
                                                    </div>
                                                </div>
                                            {% else %}
                                                <span class="text-muted">用户ID: {{ comment.user_id }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="content-preview" style="max-width: 250px;">
                                                {{ comment.content[:100] }}{% if comment.content|length > 100 %}...{% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            {% if comment.post %}
                                                <div class="content-preview" style="max-width: 200px;">
                                                    <div class="fw-bold text-primary">动态ID: {{ comment.post.id }}</div>
                                                    <div class="small text-muted">
                                                        {{ comment.post.content[:50] }}{% if comment.post.content|length > 50 %}...{% endif %}
                                                    </div>
                                                    {% if comment.post.type %}
                                                        <span class="badge {{ get_post_type_badge_class(comment.post.type) }}">{{ get_post_type_label(comment.post.type) }}</span>
                                                    {% endif %}
                                                </div>
                                            {% else %}
                                                <span class="text-muted">动态ID: {{ comment.post_id }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if comment.post and comment.post.user_id %}
                                                <span class="text-muted">用户ID: {{ comment.post.user_id }}</span>
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="small">
                                                {{ comment.created_at.strftime('%Y-%m-%d') if comment.created_at else '' }}<br>
                                                {{ comment.created_at.strftime('%H:%M:%S') if comment.created_at else '' }}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-outline-primary" onclick="viewComment({{ comment.id }})">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-danger" onclick="deleteComment({{ comment.id }})">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">暂无评论数据</h5>
                            <p class="text-muted">当前筛选条件下没有找到任何评论</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 高级分页导航 -->
    {% if pagination %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <div class="d-flex align-items-center">
                                <span class="text-muted me-3">
                                    显示第 {{ (pagination.page - 1) * pagination.per_page + 1 }} -
                                    {{ pagination.page * pagination.per_page if pagination.page * pagination.per_page <= pagination.total else pagination.total }} 条，
                                    共 {{ pagination.total }} 条记录
                                </span>
                            </div>
                        </div>
                        <div class="col-md-4 text-center">
                            {% if pagination.pages > 1 %}
                            <nav aria-label="评论分页">
                                <ul class="pagination pagination-sm justify-content-center mb-0">
                                    <!-- 首页 -->
                                    {% if pagination.page > 1 %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('cat_circle_admin.manage_comments', page=1, search=current_search or '', sort=current_sort or 'created_at_desc', per_page=current_per_page or 10, post_type=current_post_type or '') }}" title="首页">
                                            <i class="fas fa-angle-double-left"></i>
                                        </a>
                                    </li>
                                    {% endif %}

                                    <!-- 上一页 -->
                                    {% if pagination.has_prev %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('cat_circle_admin.manage_comments', page=pagination.prev_num, search=current_search or '', sort=current_sort or 'created_at_desc', per_page=current_per_page or 10, post_type=current_post_type or '') }}" title="上一页">
                                            <i class="fas fa-angle-left"></i>
                                        </a>
                                    </li>
                                    {% endif %}

                                    <!-- 页码 -->
                                    {% for page_num in pagination.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                                        {% if page_num %}
                                            {% if page_num != pagination.page %}
                                                <li class="page-item">
                                                    <a class="page-link" href="{{ url_for('cat_circle_admin.manage_comments', page=page_num, search=current_search or '', sort=current_sort or 'created_at_desc', per_page=current_per_page or 10, post_type=current_post_type or '') }}">{{ page_num }}</a>
                                                </li>
                                            {% else %}
                                                <li class="page-item active">
                                                    <span class="page-link">{{ page_num }}</span>
                                                </li>
                                            {% endif %}
                                        {% else %}
                                            <li class="page-item disabled">
                                                <span class="page-link">…</span>
                                            </li>
                                        {% endif %}
                                    {% endfor %}

                                    <!-- 下一页 -->
                                    {% if pagination.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('cat_circle_admin.manage_comments', page=pagination.next_num, search=current_search or '', sort=current_sort or 'created_at_desc', per_page=current_per_page or 10, post_type=current_post_type or '') }}" title="下一页">
                                            <i class="fas fa-angle-right"></i>
                                        </a>
                                    </li>
                                    {% endif %}

                                    <!-- 末页 -->
                                    {% if pagination.page < pagination.pages %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('cat_circle_admin.manage_comments', page=pagination.pages, search=current_search or '', sort=current_sort or 'created_at_desc', per_page=current_per_page or 10, post_type=current_post_type or '') }}" title="末页">
                                            <i class="fas fa-angle-double-right"></i>
                                        </a>
                                    </li>
                                    {% endif %}
                                </ul>
                            </nav>
                            {% endif %}
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex justify-content-end align-items-center">
                                <span class="text-muted me-2">跳转到:</span>
                                <div class="input-group" style="width: 120px;">
                                    <input type="number" class="form-control form-control-sm" id="jumpToPageComments" min="1" max="{{ pagination.pages }}" placeholder="页码">
                                    <button class="btn btn-outline-primary btn-sm" type="button" onclick="jumpToPageComments()">
                                        <i class="fas fa-arrow-right"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- 评论详情模态框 -->
<div class="modal fade" id="commentDetailModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">评论详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="commentDetailContent">
                <!-- 评论详情内容将通过JavaScript加载 -->
            </div>
        </div>
    </div>
</div>

<script>
function viewComment(commentId) {
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('commentDetailModal'));
    modal.show();

    // 重置内容为加载状态
    document.getElementById('commentDetailContent').innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在加载评论详情...</p>
        </div>
    `;

    // 获取评论详情
    fetch(`/admin/cat-circle/api/comments/${commentId}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayCommentDetail(data.data);
        } else {
            document.getElementById('commentDetailContent').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> 加载失败: ${data.message}
                </div>
            `;
        }
    })
    .catch(error => {
        document.getElementById('commentDetailContent').innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> 网络错误: ${error}
            </div>
        `;
    });
}

function displayCommentDetail(comment) {
    const typeLabels = {
        'dynamic': '动态',
        'secondhand': '二手交易',
        'help': '求助',
        'lost_found': '失物招领',
        'cat_friend': '猫友',
        'cat_friends': '猫友',
        'campus_run': '校园跑'
    };

    let postImagesHtml = '';
    if (comment.post && comment.post.images && comment.post.images.length > 0) {
        postImagesHtml = `
            <div class="mb-3">
                <h6>动态图片 (${comment.post.images.length}张)</h6>
                <div class="row">
                    ${comment.post.images.map(img => `
                        <div class="col-md-3 mb-2">
                            <img src="${img}" class="img-fluid rounded" style="max-height: 100px; object-fit: cover; cursor: pointer;" onclick="window.open('${img}', '_blank')">
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    const content = `
        <div class="row">
            <div class="col-md-8">
                <!-- 评论信息 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-comment"></i> 评论信息</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-borderless">
                            <tr>
                                <td width="100"><strong>评论ID:</strong></td>
                                <td>${comment.id}</td>
                            </tr>
                            <tr>
                                <td><strong>评论时间:</strong></td>
                                <td>${comment.created_at}</td>
                            </tr>
                        </table>
                        <div class="mt-3">
                            <h6>评论内容：</h6>
                            <div class="border rounded p-3 bg-light">
                                ${comment.content}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 所属动态信息 -->
                ${comment.post ? `
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-file-text"></i> 所属动态</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <span class="badge ${getBadgeClass(comment.post.type)}">${typeLabels[comment.post.type] || comment.post.type}</span>
                                <span class="badge ${comment.post.status === 'active' ? 'bg-success' : comment.post.status === 'hidden' ? 'bg-warning' : 'bg-danger'} ms-2">
                                    ${comment.post.status === 'active' ? '正常' : comment.post.status === 'hidden' ? '隐藏' : '已删除'}
                                </span>
                            </div>
                            <p class="mb-2">${comment.post.content}</p>
                            ${postImagesHtml}
                            <div class="row text-center mt-3">
                                <div class="col-3">
                                    <small class="text-muted">
                                        <i class="fas fa-eye text-info"></i> ${comment.post.view_count}
                                    </small>
                                </div>
                                <div class="col-3">
                                    <small class="text-muted">
                                        <i class="fas fa-heart text-danger"></i> ${comment.post.like_count}
                                    </small>
                                </div>
                                <div class="col-3">
                                    <small class="text-muted">
                                        <i class="fas fa-comment text-primary"></i> ${comment.post.comment_count}
                                    </small>
                                </div>
                                <div class="col-3">
                                    <small class="text-muted">
                                        <i class="fas fa-star text-warning"></i> ${comment.post.collect_count}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                ` : `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i> 所属动态信息不可用
                    </div>
                `}
            </div>

            <div class="col-md-4">
                <!-- 评论用户信息 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-user"></i> 评论用户</h6>
                    </div>
                    <div class="card-body text-center">
                        ${comment.user ? `
                            <div class="mb-3">
                                ${comment.user.avatar ?
                                    `<img src="${comment.user.avatar}" class="rounded-circle mb-2" width="64" height="64">` :
                                    `<div class="bg-secondary rounded-circle mx-auto mb-2 d-flex align-items-center justify-content-center" style="width: 64px; height: 64px;">
                                        <i class="fas fa-user text-white fa-2x"></i>
                                    </div>`
                                }
                                <h6>${comment.user.nickname}</h6>
                                <small class="text-muted">用户ID: ${comment.user.id}</small>
                            </div>
                        ` : `
                            <div class="text-muted">
                                <i class="fas fa-user-slash fa-2x mb-2"></i>
                                <p>用户信息不可用</p>
                            </div>
                        `}
                    </div>
                </div>

                <!-- 动态作者信息 -->
                ${comment.post && comment.post.user ? `
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-edit"></i> 动态作者</h6>
                        </div>
                        <div class="card-body text-center">
                            <div class="mb-3">
                                ${comment.post.user.avatar ?
                                    `<img src="${comment.post.user.avatar}" class="rounded-circle mb-2" width="48" height="48">` :
                                    `<div class="bg-secondary rounded-circle mx-auto mb-2 d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                                        <i class="fas fa-user text-white"></i>
                                    </div>`
                                }
                                <h6>${comment.post.user.nickname}</h6>
                                <small class="text-muted">用户ID: ${comment.post.user.id}</small>
                            </div>
                        </div>
                    </div>
                ` : ''}
            </div>
        </div>
    `;

    document.getElementById('commentDetailContent').innerHTML = content;
}

function getBadgeClass(type) {
    const badgeClasses = {
        'dynamic': 'bg-primary',
        'secondhand': 'bg-success',
        'help': 'bg-info',
        'lost_found': 'bg-warning',
        'cat_friend': 'badge-purple',
        'cat_friends': 'badge-purple',
        'campus_run': 'bg-secondary'
    };
    return badgeClasses[type] || 'bg-light text-dark';
}

function deleteComment(commentId) {
    if (confirm('确定要删除这条评论吗？此操作不可恢复！')) {
        fetch(`/admin/cat-circle/api/comments/${commentId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('删除失败: ' + data.message);
            }
        })
        .catch(error => {
            alert('删除失败: ' + error);
        });
    }
}

// 跳转到指定页面
function jumpToPageComments() {
    const pageInput = document.getElementById('jumpToPageComments');
    const page = parseInt(pageInput.value);

    if (page && page > 0 && page <= {{ pagination.pages if pagination else 1 }}) {
        const url = new URL(window.location);
        url.searchParams.set('page', page);
        window.location.href = url.toString();
    } else {
        alert('请输入有效的页码 (1-{{ pagination.pages if pagination else 1 }})');
        pageInput.focus();
    }
}

// 回车键跳转
document.addEventListener('DOMContentLoaded', function() {
    const pageInput = document.getElementById('jumpToPageComments');
    if (pageInput) {
        pageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                jumpToPageComments();
            }
        });
    }
});
</script>

<style>
.page-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.content-preview {
    word-break: break-word;
    line-height: 1.4;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}
</style>
{% endblock %}

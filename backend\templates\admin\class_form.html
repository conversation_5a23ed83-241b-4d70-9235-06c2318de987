{% extends "admin/base.html" %}

{% block title %}
{% if action == 'add' %}
添加班级 - 题库管理系统
{% else %}
编辑班级 - 题库管理系统
{% endif %}
{% endblock %}

{% block header %}
{% if action == 'add' %}
添加班级
{% else %}
编辑班级
{% endif %}
{% endblock %}

{% block header_buttons %}
<a href="/admin/classes" class="btn btn-outline-secondary">
    <i class="fas fa-arrow-left me-1"></i>返回班级列表
</a>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-body">
        <form method="post" action="{% if action == 'add' %}/admin/classes/add{% else %}/admin/classes/edit/{{ class_obj.id }}{% endif %}">
            <div class="mb-3">
                <label for="name" class="form-label">班级名称 <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="name" name="name" required 
                       value="{% if action == 'edit' %}{{ class_obj.name }}{% endif %}">
                <div class="form-text">例如：计算机科学2023级、软件工程2022级等</div>
            </div>
            
            <div class="mb-3">
                <label for="description" class="form-label">班级描述</label>
                <textarea class="form-control" id="description" name="description" rows="3">{% if action == 'edit' %}{{ class_obj.description }}{% endif %}</textarea>
                <div class="form-text">可选：对班级的详细描述</div>
            </div>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="/admin/classes" class="btn btn-outline-secondary me-md-2">取消</a>
                <button type="submit" class="btn btn-primary">
                    {% if action == 'add' %}
                    <i class="fas fa-plus me-1"></i>添加班级
                    {% else %}
                    <i class="fas fa-save me-1"></i>保存修改
                    {% endif %}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %} 
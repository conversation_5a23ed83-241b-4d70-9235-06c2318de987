#!/bin/bash

echo "===== 安装Python依赖 ====="

# 检查是否有虚拟环境
if [ ! -d "venv" ]; then
    echo "创建虚拟环境..."
    python3 -m venv venv
    if [ $? -ne 0 ]; then
        echo "无法创建虚拟环境，尝试使用系统Python..."
        # 继续执行，使用系统Python
    fi
fi

# 如果存在虚拟环境，激活它
if [ -d "venv" ]; then
    echo "激活虚拟环境..."
    source venv/bin/activate
fi

# 安装依赖
echo "安装依赖..."
pip install -r requirements.txt

if [ $? -eq 0 ]; then
    echo "依赖安装成功!"
    # 检查是否安装了gunicorn和flask
    if pip list | grep -q gunicorn && pip list | grep -q Flask; then
        echo "Flask和Gunicorn已安装。"
    else
        echo "警告: 某些关键依赖可能未安装，尝试单独安装..."
        pip install gunicorn Flask Flask-Cors Flask-SQLAlchemy PyJWT matplotlib numpy pandas PyMySQL
    fi
else
    echo "依赖安装失败!"
    exit 1
fi

echo "===== 依赖安装完成 =====" 
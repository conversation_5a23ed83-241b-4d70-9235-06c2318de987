/* index.wxss */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 140rpx; /* 为底部tabBar留出足够空间 */
  padding-top: 0; /* 移除顶部内边距，由header自行控制 */
}

/* 优化顶部欢迎横幅 */
.header {
  background-color: #ffffff; /* 改为白色背景 */
  color: #333; /* 文本改为深色 */
  margin: 30rpx 24rpx 24rpx; /* 顶部留出导航栏空间，四周有间距 */
  border-radius: 24rpx; /* 增加圆角 */
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.06); /* 添加阴影效果 */
  padding: 30rpx;
  position: relative;
  z-index: 1;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 增强版顶部横幅 */
.header.enhanced {
  background-color: #ffffff;
  padding: 36rpx 30rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

.header-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  background: linear-gradient(135deg, rgba(78, 141, 247, 0.05) 0%, rgba(78, 141, 247, 0) 100%);
  opacity: 0.8;
}

.header-background::before {
  content: "";
  position: absolute;
  right: -60rpx;
  top: -60rpx;
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(94, 102, 255, 0.1) 0%, rgba(94, 102, 255, 0) 70%);
}

.header-background::after {
  content: "";
  position: absolute;
  left: 40%;
  bottom: -80rpx;
  width: 240rpx;
  height: 240rpx;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(78, 141, 247, 0.08) 0%, rgba(78, 141, 247, 0) 70%);
}

.header::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 30%;
  background: linear-gradient(90deg, rgba(245, 247, 250, 0) 0%, rgba(245, 247, 250, 0.2) 100%);
  border-radius: 0 24rpx 24rpx 0;
  z-index: -1;
}

/* 调整header-content的布局 */
.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 24rpx; /* 增加左右内边距，确保内容不贴边 */
}

/* 调整欢迎文本区域 */
.welcome-text {
  display: flex;
  flex-direction: column;
  flex: 1; /* 让文本区域占据剩余空间 */
  margin-right: 20rpx; /* 增加与右侧头像区域的间距 */
}

.welcome {
  font-size: 28rpx;
  margin-bottom: 8rpx;
  color: #666; /* 灰色 */
}

.username {
  font-size: 38rpx;
  font-weight: bold;
  color: #333; /* 深色 */
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
  position: relative;
}

.user-status {
  margin-top: 8rpx;
  display: flex;
}

.status-badge {
  display: flex;
  align-items: center;
  background: rgba(78, 141, 247, 0.08);
  border-radius: 30rpx;
  padding: 6rpx 16rpx;
  border: 1rpx solid rgba(78, 141, 247, 0.2);
}

.status-icon {
  width: 14rpx;
  height: 14rpx;
  border-radius: 50%;
  background-color: #4CAF50;
  margin-right: 8rpx;
  position: relative;
  box-shadow: 0 0 6rpx rgba(76, 175, 80, 0.5);
}

.status-icon::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #4CAF50;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  70% {
    transform: scale(2);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

.status-text {
  font-size: 22rpx;
  color: #4e8df7;
  font-weight: 500;
}

.username::after {
  content: "👋";
  font-size: 32rpx;
  margin-left: 8rpx;
}

/* 头像和刷新按钮区域 */
.avatar-section {
  display: flex;
  align-items: center;
  margin-right: 16rpx; /* 确保头像不会太靠右 */
}

/* 调整刷新按钮样式 */
.refresh-button {
  position: relative;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: rgba(40, 120, 255, 0.9); /* 更亮、更饱和的蓝色 */
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 12px;
  box-shadow: 0 2px 10px rgba(40, 120, 255, 0.5), /* 增强阴影效果突出按钮 */
              0 0 15px rgba(120, 180, 255, 0.3), /* 添加光晕效果 */
              inset 0 0 8px rgba(255, 255, 255, 0.4); /* 内发光效果 */
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid rgba(255, 255, 255, 0.8); /* 更明显的白色边框 */
  overflow: hidden;
  animation: pulse-refresh 2s infinite;
}

/* 星环效果 - 添加装饰元素 */
.refresh-button::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid transparent; 
  border-radius: 50%;
  background: linear-gradient(45deg, transparent 40%, rgba(255, 255, 255, 0.8) 45%, rgba(255, 255, 255, 0.9) 50%, rgba(255, 255, 255, 0.8) 55%, transparent 60%);
  animation: spin-light 4s linear infinite;
  z-index: -1;
}

@keyframes spin-light {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.refresh-button:active {
  transform: scale(0.92);
  background-color: rgba(40, 120, 255, 1); /* 点击时更深的颜色 */
  box-shadow: 0 0 20px rgba(40, 120, 255, 0.8); /* 点击时发光效果增强 */
}

/* 统一的刷新图标 - 改进光环效果 */
.refresh-unified-icon {
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.95);
  border-radius: 50%;
  position: relative;
  box-sizing: border-box;
  transition: transform 0.3s ease;
  animation: slow-rotate 6s linear infinite; /* 添加缓慢旋转动画 */
  box-shadow: 0 0 5px rgba(255, 255, 255, 0.6), /* 外发光 */
              inset 0 0 3px rgba(255, 255, 255, 0.4); /* 内发光 */
}

/* 创建箭头部分 - 提高可见度 */
.refresh-unified-icon:before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-width: 0 0 6px 9px; /* 增大箭头 */
  border-style: solid;
  border-color: transparent transparent rgba(255, 255, 255, 0.95) rgba(255, 255, 255, 0.95);
  top: 2px;
  right: 1px;
  transform: rotate(130deg); /* 旋转箭头指向圆内 */
  transition: transform 0.3s ease;
  filter: drop-shadow(0 0 1px rgba(255, 255, 255, 0.6)); /* 箭头发光效果 */
}

/* 添加更明显的断口效果 */
.refresh-unified-icon:after {
  content: '';
  position: absolute;
  width: 10px;
  height: 10px;
  background: radial-gradient(circle, rgba(40, 120, 255, 0.95) 30%, rgba(40, 120, 255, 0.9) 70%, rgba(40, 120, 255, 0.7) 100%); /* 渐变背景 */
  border-radius: 50%;
  top: 0px;
  right: 0px;
  transition: transform 0.3s ease;
  box-shadow: inset 0 0 2px rgba(255, 255, 255, 0.4); /* 内发光 */
}

/* 创建星点特效 */
.refresh-button::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0) 2%),
    radial-gradient(circle at 75% 35%, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0) 2%),
    radial-gradient(circle at 40% 80%, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0) 2%),
    radial-gradient(circle at 85% 75%, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0) 2%);
  opacity: 0.8;
  z-index: 1;
  pointer-events: none; /* 确保不影响点击 */
}

/* 添加脉动动画提升可见度 */
@keyframes pulse-refresh {
  0% { box-shadow: 0 0 0 0 rgba(40, 120, 255, 0.4), 0 0 15px rgba(120, 180, 255, 0.3), inset 0 0 8px rgba(255, 255, 255, 0.4); }
  70% { box-shadow: 0 0 0 6px rgba(40, 120, 255, 0), 0 0 20px rgba(120, 180, 255, 0.5), inset 0 0 8px rgba(255, 255, 255, 0.4); }
  100% { box-shadow: 0 0 0 0 rgba(40, 120, 255, 0), 0 0 15px rgba(120, 180, 255, 0.3), inset 0 0 8px rgba(255, 255, 255, 0.4); }
}

/* 添加轻微脉动效果吸引注意 */
.refresh-button {
  animation: pulse-refresh 2s infinite;
}

.refresh-icon {
  width: 20px;
  height: 20px;
  position: relative;
}

/* 缓慢的顺时针旋转动画 */
@keyframes slow-rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 点击旋转动画效果 - 覆盖默认旋转 */
.refresh-button:active .refresh-unified-icon {
  animation: none; /* 取消缓慢旋转 */
  transform: rotate(-180deg); /* 点击时立即旋转 */
}

/* 刷新中的旋转动画 - 覆盖默认旋转 */
@keyframes refresh-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.refreshing .refresh-unified-icon {
  animation: refresh-spin 1s linear infinite !important; /* 强制覆盖缓慢旋转 */
}

/* 进一步突出刷新状态 */
.refreshing {
  background-color: rgba(40, 120, 255, 1) !important; /* 刷新时颜色更深 */
  box-shadow: 0 2px 15px rgba(40, 120, 255, 0.7) !important; /* 刷新时阴影更强 */
}

/* 修改头像容器样式 */
.avatar-container {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  overflow: visible;
  position: relative;
}

.avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4rpx solid rgba(78, 141, 247, 0.1);
  background-color: #f0f0f0;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.avatar-badge {
  position: absolute;
  bottom: 0;
  right: -2rpx;
  width: 30rpx;
  height: 30rpx;
  background: #4e8df7;
  border-radius: 50%;
  border: 3rpx solid #fff;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.avatar-badge::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 12rpx;
  height: 12rpx;
  background: #fff;
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

/* 功能卡片区域 */
.card-section {
  margin: 0 24rpx 24rpx;
  border-radius: 24rpx;
  background-color: white;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.06);
  padding: 30rpx 26rpx;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
}

.courses-section {
  margin-bottom: 24rpx;
  position: relative;
  background: linear-gradient(to bottom, rgba(245, 247, 250, 0.5), white);
  border-top: 2rpx solid rgba(78, 141, 247, 0.1);
  padding-top: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding: 0 10rpx;
}

.section-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  position: relative;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: -10rpx;
  left: 0;
  width: 48rpx;
  height: 4rpx;
  background-color: #4e8df7;
  border-radius: 4rpx;
}

.section-more {
  font-size: 24rpx;
  color: #666;
  background-color: rgba(78, 141, 247, 0.08);
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  transition: all 0.3s ease;
}

.section-more:active {
  background-color: rgba(78, 141, 247, 0.16);
}

/* 更多按钮样式 */
.more-button {
  display: flex;
  align-items: center;
  background-color: #4e8df7;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  transition: all 0.3s ease;
  border: 1rpx solid rgba(78, 141, 247, 0.2);
  box-shadow: 0 4rpx 12rpx rgba(78, 141, 247, 0.2);
}

.more-button:active {
  transform: scale(0.96);
  background-color: #4279da;
}

.section-more-text {
  font-size: 24rpx;
  color: white;
  font-weight: 500;
  margin-right: 6rpx;
}

.section-more-arrow {
  font-size: 24rpx;
  color: white;
  font-weight: bold;
}

/* 滑动提示 */
.swipe-tip {
  margin: 6rpx 10rpx 20rpx;
  padding: 12rpx 20rpx;
  background-color: rgba(78, 141, 247, 0.08);
  border-radius: 12rpx;
  display: flex;
  justify-content: center;
  border: 1rpx dashed rgba(78, 141, 247, 0.3);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.swipe-tip-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.swipe-tip-text {
  font-size: 24rpx;
  color: #4e8df7;
  margin-right: 10rpx;
  font-weight: 500;
}

.swipe-tip-arrows {
  display: flex;
  align-items: center;
}

.swipe-tip-arrow {
  font-size: 28rpx;
  color: #4e8df7;
  font-weight: bold;
  animation: floatRight 1.2s infinite ease-in-out;
}

@keyframes floatRight {
  0% {
    transform: translateX(0);
    opacity: 0.5;
  }
  50% {
    transform: translateX(10rpx);
    opacity: 1;
  }
  100% {
    transform: translateX(0);
    opacity: 0.5;
  }
}

/* 课程滚动区域 */
.course-scroll-container {
  width: 100%;
  position: relative;
}

.course-scroll {
  width: 100%;
  white-space: nowrap;
}

.course-scroll-content {
  display: inline-flex;
  padding: 10rpx 30rpx 20rpx;
}

/* 课程卡片样式 */
.course-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #ffffff;
  border-radius: 20rpx;
  margin-right: 20rpx;
  padding: 24rpx 16rpx;
  width: 160rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.course-card:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.08);
}

.course-card::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4rpx;
  background: linear-gradient(90deg, #4e8df7, #6097ff);
}

.course-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16rpx;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
}

.course-icon::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 40%;
  height: 40%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
  border-radius: 0 0 0 16rpx;
}

.course-icon-text {
  color: white;
  font-size: 36rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.course-info {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.course-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  text-align: center;
  white-space: normal;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  width: 100%;
}

.course-question-count {
  font-size: 22rpx;
  color: #999;
  background-color: rgba(78, 141, 247, 0.08);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

/* 答题卡片 */
.quiz-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 28rpx 28rpx;
  background-color: #f8f9fd;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  transition: all 0.3s;
  border-left: 6rpx solid #4e8df7;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);
}

.quiz-card:active {
  transform: scale(0.98);
  opacity: 0.9;
  background-color: #f5f7ff;
}

.quiz-card:last-child {
  margin-bottom: 0;
}

.multiple-card {
  background-color: #f5f7ff;
  border-left-color: #5e66ff;
}

.judgment-card {
  background-color: #f5fff7;
  border-left-color: #4caf50;
}

.fillblank-card {
  background-color: #fff5f2;
  border-left-color: #ff7043;
}

.quiz-card-left {
  display: flex;
  align-items: center;
}

.quiz-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #4e8df7;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 24rpx;
  box-shadow: 0 4rpx 10rpx rgba(78, 141, 247, 0.3);
  position: relative;
  overflow: hidden;
}

.quiz-icon::after {
  content: "";
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

.multiple-icon {
  background-color: #5e66ff;
  box-shadow: 0 4rpx 10rpx rgba(94, 102, 255, 0.3);
}

.judgment-icon {
  background-color: #4caf50;
  box-shadow: 0 4rpx 10rpx rgba(76, 175, 80, 0.3);
}

.fillblank-icon {
  background-color: #ff7043;
  box-shadow: 0 4rpx 10rpx rgba(255, 112, 67, 0.3);
}

.quiz-icon-text {
  font-size: 34rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.quiz-info {
  display: flex;
  flex-direction: column;
}

.quiz-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.quiz-description {
  font-size: 24rpx;
  color: #888;
  display: flex;
  align-items: center;
}

.quiz-description::before {
  content: "•";
  margin-right: 6rpx;
  color: #4e8df7;
  font-size: 28rpx;
}

.multiple-card .quiz-description::before {
  color: #5e66ff;
}

.judgment-card .quiz-description::before {
  color: #4caf50;
}

.fillblank-card .quiz-description::before {
  color: #ff7043;
}

.quiz-action {
  display: flex;
  align-items: center;
  background-color: rgba(78, 141, 247, 0.08);
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  transition: all 0.3s;
}

.quiz-action:active {
  background-color: rgba(78, 141, 247, 0.16);
  transform: scale(0.95);
}

.multiple-card .quiz-action {
  background-color: rgba(94, 102, 255, 0.08);
}

.judgment-card .quiz-action {
  background-color: rgba(76, 175, 80, 0.08);
}

.fillblank-card .quiz-action {
  background-color: rgba(255, 112, 67, 0.08);
}

.multiple-card .quiz-action:active {
  background-color: rgba(94, 102, 255, 0.16);
}

.judgment-card .quiz-action:active {
  background-color: rgba(76, 175, 80, 0.16);
}

.fillblank-card .quiz-action:active {
  background-color: rgba(255, 112, 67, 0.16);
}

.action-text {
  font-size: 28rpx;
  color: #4e8df7;
  margin-right: 10rpx;
  font-weight: 500;
}

.multiple-card .action-text {
  color: #5e66ff;
}

.judgment-card .action-text {
  color: #4caf50;
}

.fillblank-card .action-text {
  color: #ff7043;
}

.arrow {
  font-size: 26rpx;
  color: #4e8df7;
  font-weight: bold;
}

.multiple-card .arrow {
  color: #5e66ff;
}

.judgment-card .arrow {
  color: #4caf50;
}

.fillblank-card .arrow {
  color: #ff7043;
}

/* 工具网格 */
.tools-grid {
  display: flex;
  justify-content: space-around;
  margin-top: 10rpx;
}

.tool-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 45%;
  padding: 30rpx 24rpx;
  background-color: #f8f9fd;
  border-radius: 20rpx;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);
}

.tool-card:active {
  transform: scale(0.95);
  background-color: #f5f7ff;
}

.tool-card::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 6rpx;
  background: linear-gradient(90deg, #4e8df7, #5e66ff);
  border-radius: 6rpx;
  opacity: 0.8;
}

.tool-icon {
  font-size: 56rpx;
  margin-bottom: 16rpx;
  background-color: rgba(78, 141, 247, 0.1);
  width: 110rpx;
  height: 110rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  margin-bottom: 20rpx;
  position: relative;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08) inset;
}

.tool-icon::after {
  content: "";
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  width: 30rpx;
  height: 30rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
}

.tool-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.tool-desc {
  font-size: 22rpx;
  color: #888;
  background-color: rgba(78, 141, 247, 0.08);
  padding: 4rpx 16rpx;
  border-radius: 30rpx;
}

/* 加载提示 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 999;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.loading {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid rgba(78, 141, 247, 0.1);
  border-top: 6rpx solid #4e8df7;
  border-radius: 50%;
  margin-bottom: 20rpx;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 滑动指示器 */
.scroll-indicator {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 160rpx;
  margin-right: 24rpx;
  background-color: rgba(78, 141, 247, 0.06);
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  border: 2rpx dashed rgba(78, 141, 247, 0.2);
  box-sizing: border-box;
  height: 210rpx;
  vertical-align: middle;
}

.indicator-text {
  font-size: 24rpx;
  color: #4e8df7;
  font-weight: 500;
  margin-bottom: 16rpx;
  white-space: normal;
  text-align: center;
  line-height: 1.3;
}

.indicator-arrows {
  display: flex;
  align-items: center;
}

.indicator-arrow {
  font-size: 26rpx;
  color: #4e8df7;
  font-weight: bold;
  margin: 0 2rpx;
  animation: slideRight 1.5s infinite;
}

.indicator-arrow:nth-child(2) {
  animation-delay: 0.5s;
}

@keyframes slideRight {
  0% {
    transform: translateX(-6rpx);
    opacity: 0.3;
  }
  50% {
    transform: translateX(6rpx);
    opacity: 1;
  }
  100% {
    transform: translateX(-6rpx);
    opacity: 0.3;
  }
}

/* 滑动提示点 */
.scroll-dots {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20rpx;
  height: 24rpx;
  margin-bottom: 10rpx;
}

.scroll-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #e0e0e0;
  margin: 0 10rpx;
  transition: all 0.25s ease;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.scroll-dot.active {
  width: 40rpx;
  height: 16rpx;
  background-color: #4e8df7;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 6rpx rgba(78, 141, 247, 0.3);
}

/* 刷新按钮容器 - 用于定位提示气泡 */
.refresh-button-container {
  position: relative;
  display: flex;
  align-items: center;
}

/* 提示气泡样式 */
.refresh-tooltip {
  position: absolute;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(40, 120, 255, 0.9);
  color: white;
  padding: 6px 10px;
  border-radius: 15px;
  font-size: 12px;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  opacity: 0;
  z-index: 100;
  animation: tooltip-pulse 10s ease-in-out infinite;
  pointer-events: none; /* 确保不影响点击 */
  border: 1px solid rgba(255, 255, 255, 0.6);
}

/* 气泡尾巴 */
.refresh-tooltip::after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid rgba(40, 120, 255, 0.9);
}

/* 气泡淡入淡出动画 */
@keyframes tooltip-pulse {
  0%, 100% {
    opacity: 0;
    transform: translateX(-50%) translateY(5px);
  }
  15%, 25% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
  40% {
    opacity: 0;
    transform: translateX(-50%) translateY(-5px);
  }
} 
// pages/wrong-questions/wrong-questions.js
const app = getApp();
const globalNotificationMixin = require('../../utils/global-notification-mixin')

Page(Object.assign({}, globalNotificationMixin, {

  /**
   * 页面的初始数据
   */
  data: Object.assign({}, globalNotificationMixin.data, {
    wrongQuestions: [],
    loading: true,
    isEmpty: false,
    activeTab: 'all', // 'all', 'single', 'multiple'
    activeCourse: 0,  // 0表示全部课程，其他值为课程ID
    coursesList: [],  // 课程列表
    filteredQuestions: [] // 经过筛选后的问题
  }),

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.fetchWrongQuestions();
    this.fetchCourseWrongQuestions();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 更新自定义tabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 1
      });
    }

    // 每次显示页面时刷新数据
    this.fetchWrongQuestions();
    this.fetchCourseWrongQuestions();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.fetchWrongQuestions(() => {
      this.fetchCourseWrongQuestions(() => {
        wx.stopPullDownRefresh();
      });
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 获取错题列表
  fetchWrongQuestions(callback) {
    this.setData({ loading: true });

    app.request({
      url: '/wrong-questions',
      method: 'GET'
    })
    .then(res => {
      console.log('获取错题本数据:', res);

      const isEmpty = !res || res.length === 0;
      this.setData({
        wrongQuestions: res || [],
        loading: false,
        isEmpty: isEmpty
      });

      this.filterQuestions(this.data.activeTab, this.data.activeCourse);

      if (callback) callback();
    })
    .catch(err => {
      console.error('获取错题本失败:', err);
      this.setData({ 
        loading: false,
        isEmpty: true
      });

      wx.showToast({
        title: '获取错题失败',
        icon: 'none'
      });

      if (callback) callback();
    });
  },
  
  // 获取按课程分类的错题列表
  fetchCourseWrongQuestions(callback) {
    app.request({
      url: '/wrong-questions/by-course',
      method: 'GET'
    })
    .then(res => {
      console.log('获取课程错题本数据:', res);
      
      if (res.status === 'success' && res.courses) {
        // 处理从API获取的课程数据
        // 为每个课程计算总题目数量
        const processedCourses = res.courses.map(course => {
          // 计算每个课程中的题目总数
          const totalCount = course.questions ? course.questions.length : 0;
          return {
            id: course.id,
            name: course.name,
            question_count: totalCount
          };
        });
        
        this.setData({
          coursesList: processedCourses || []
        });
        
        // 处理每个错题，添加课程名称
        const wrongQuestions = [...this.data.wrongQuestions];
        const courseMap = {};
        
        // 创建课程ID到课程名称的映射
        res.courses.forEach(course => {
          courseMap[course.id] = course.name;
        });
        
        // 为每个错题添加课程名称
        wrongQuestions.forEach(question => {
          if (question.course_id && courseMap[question.course_id]) {
            question.course_name = courseMap[question.course_id];
          }
        });
        
        this.setData({
          wrongQuestions: wrongQuestions
        });
        
        // 重新筛选问题
        this.filterQuestions(this.data.activeTab, this.data.activeCourse);
      }
      
      if (callback) callback();
    })
    .catch(err => {
      console.error('获取课程错题本失败:', err);
      if (callback) callback();
    });
  },

  // 切换选项卡 - 保留课程筛选
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ activeTab: tab });
    // 切换tab时仍然保持当前的课程筛选
    this.filterQuestions(tab, this.data.activeCourse);
  },
  
  // 切换课程筛选 - 在所有选项卡中可用
  switchCourse(e) {
    const courseId = parseInt(e.currentTarget.dataset.id) || 0;
    this.setData({ activeCourse: courseId });
    // 切换课程时仍然保持当前的选项卡
    this.filterQuestions(this.data.activeTab, courseId);
  },

  // 根据选项卡和课程过滤题目
  filterQuestions(tab, courseId) {
    let filtered = [...this.data.wrongQuestions];
    
    // 先按课程筛选 (如果指定了课程)
    if (courseId !== 0) {
      filtered = filtered.filter(q => q.course_id === courseId);
    }
    
    // 再按题型筛选 (如果不是"全部"选项卡)
    if (tab !== 'all') {
      filtered = filtered.filter(q => q.type === tab);
    }
    
    this.setData({
      filteredQuestions: filtered,
      isEmpty: filtered.length === 0
    });
  },

  // 跳转到题目练习
  practiceQuestion(e) {
    const index = e.currentTarget.dataset.index;
    const question = this.data.filteredQuestions[index];
    
    console.log('准备练习单个错题:', question);
    
    // 根据题目类型决定跳转的页面
    let url = '/pages/quiz/';
    
    if (question.type === 'single') {
      url += 'single/single';
    } else if (question.type === 'multiple') {
      url += 'multiple/multiple';
    } else if (question.type === 'judgment') {
      url += 'judgment/judgment';
    } else if (question.type === 'fill_blank') {
      url += 'fillblank/fillblank';
    } else {
      console.error('未知的题目类型:', question.type);
      return;
    }
    
    // 标记单个题目练习模式，并确保questionId参数有效
    url += `?mode=practice&questionId=${question.id}`;
    
    // 如果题目有关联的课程，也传递课程信息
    if (question.course_id) {
      url += `&courseId=${question.course_id}`;
      if (question.course_name) {
        url += `&courseTitle=${encodeURIComponent(question.course_name)}`;
      }
    }
    
    console.log('跳转URL:', url);
    
    // 将单个题目保存到缓存，确保页面加载正确的题目
    try {
      // 清除之前的缓存
      wx.removeStorageSync('currentQuestions');
      wx.removeStorageSync('practiceQuestion');
      
      // 准备完整的题目数据
      const questionData = {
        ...question,
        question_type: question.type // 确保有正确的类型字段
      };
      
      // 添加一些日志来检查题目数据的完整性
      console.log('缓存题目数据:', {
        id: questionData.id,
        question: questionData.question,
        options: questionData.options ? questionData.options.length + '个选项' : '无选项',
        type: questionData.type,
        answer: questionData.answer
      });
      
      // 缓存当前选中题目
      wx.setStorageSync('practiceQuestion', questionData);
      
      // 同时也缓存到currentQuestions中作为备份
      wx.setStorageSync('currentQuestions', [questionData]);
    } catch (e) {
      console.error('缓存题目失败:', e);
    }
    
    wx.navigateTo({ url });
  },

  // 移除错题
  removeQuestion(e) {
    const index = e.currentTarget.dataset.index;
    const question = this.data.filteredQuestions[index];
    
    wx.showModal({
      title: '确认移除',
      content: '确定要从错题本中移除这道题目吗？',
      success: (res) => {
        if (res.confirm) {
          app.request({
            url: '/wrong-questions/remove',
            method: 'POST',
            data: {
              questionId: question.id,
              questionType: question.type
            }
          })
          .then(() => {
            wx.showToast({
              title: '已移除错题',
              icon: 'success'
            });
            
            // 刷新错题本
            this.fetchWrongQuestions();
            this.fetchCourseWrongQuestions();
          })
          .catch(err => {
            console.error('移除错题失败:', err);
            wx.showToast({
              title: '移除失败',
              icon: 'none'
            });
          });
        }
      }
    });
  },

  // 开始练习全部错题
  practiceAll() {
    // 考虑当前筛选状态
    const currentTab = this.data.activeTab;
    const currentCourseId = this.data.activeCourse;
    
    // 基于选择的标签直接导航到对应题型页面
    if (currentTab === 'multiple') {
      wx.navigateTo({
        url: `/pages/quiz/multiple/multiple?mode=wrong${currentCourseId ? '&courseId=' + currentCourseId : ''}`
      });
      return;
    } else if (currentTab === 'single') {
      wx.navigateTo({
        url: `/pages/quiz/single/single?mode=wrong${currentCourseId ? '&courseId=' + currentCourseId : ''}`
      });
      return;
    } else if (currentTab === 'judgment') {
      wx.navigateTo({
        url: `/pages/quiz/judgment/judgment?mode=wrong${currentCourseId ? '&courseId=' + currentCourseId : ''}`
      });
      return;
    } else if (currentTab === 'fill_blank') {
      wx.navigateTo({
        url: `/pages/quiz/fillblank/fillblank?mode=wrong${currentCourseId ? '&courseId=' + currentCourseId : ''}`
      });
      return;
    }
    
    // 对于"全部"模式，根据是否有题目来决定
    if (this.data.filteredQuestions.length === 0) {
      // 如果没有错题但用户仍然要练习，就导航到普通的练习模式
      wx.showModal({
        title: '提示',
        content: '错题本中没有题目，是否前往普通练习模式？',
        success: (res) => {
          if (res.confirm) {
            wx.switchTab({
              url: '/pages/index/index'
            });
          }
        }
      });
      return;
    }

    // 分离各种题型
    const singleChoiceQuestions = this.data.filteredQuestions.filter(q => q.type === 'single');
    const multipleChoiceQuestions = this.data.filteredQuestions.filter(q => q.type === 'multiple');
    const judgmentQuestions = this.data.filteredQuestions.filter(q => q.type === 'judgment');
    const fillBlankQuestions = this.data.filteredQuestions.filter(q => q.type === 'fill_blank');
    
    // 计算有多少种题型有错题
    const questionTypes = [];
    if (singleChoiceQuestions.length > 0) questionTypes.push('单选题');
    if (multipleChoiceQuestions.length > 0) questionTypes.push('多选题');
    if (judgmentQuestions.length > 0) questionTypes.push('判断题');
    if (fillBlankQuestions.length > 0) questionTypes.push('填空题');
    
    if (questionTypes.length > 1) {
      // 如果有多种题型，让用户选择先练习哪种
      wx.showActionSheet({
        itemList: questionTypes.map(type => `先练习${type}`),
        success: (res) => {
          const selectedType = questionTypes[res.tapIndex];
          let url = '';
          
          if (selectedType === '单选题') {
            url = `/pages/quiz/single/single?mode=wrong${currentCourseId ? '&courseId=' + currentCourseId : ''}`;
          } else if (selectedType === '多选题') {
            url = `/pages/quiz/multiple/multiple?mode=wrong${currentCourseId ? '&courseId=' + currentCourseId : ''}`;
          } else if (selectedType === '判断题') {
            url = `/pages/quiz/judgment/judgment?mode=wrong${currentCourseId ? '&courseId=' + currentCourseId : ''}`;
          } else if (selectedType === '填空题') {
            url = `/pages/quiz/fillblank/fillblank?mode=wrong${currentCourseId ? '&courseId=' + currentCourseId : ''}`;
          }
          
          if (url) {
            wx.navigateTo({ url });
          }
        }
      });
    } else if (singleChoiceQuestions.length > 0) {
      wx.navigateTo({
        url: `/pages/quiz/single/single?mode=wrong${currentCourseId ? '&courseId=' + currentCourseId : ''}`
      });
    } else if (multipleChoiceQuestions.length > 0) {
      wx.navigateTo({
        url: `/pages/quiz/multiple/multiple?mode=wrong${currentCourseId ? '&courseId=' + currentCourseId : ''}`
      });
    } else if (judgmentQuestions.length > 0) {
      wx.navigateTo({
        url: `/pages/quiz/judgment/judgment?mode=wrong${currentCourseId ? '&courseId=' + currentCourseId : ''}`
      });
    } else if (fillBlankQuestions.length > 0) {
      wx.navigateTo({
        url: `/pages/quiz/fillblank/fillblank?mode=wrong${currentCourseId ? '&courseId=' + currentCourseId : ''}`
      });
    }
  }
}))
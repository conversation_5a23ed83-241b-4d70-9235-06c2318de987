{% extends "admin/base.html" %}

{% block title %}动态管理 - 猫友圈管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-edit" style="color: #28a745;"></i> 动态管理
                </h1>
                <p class="page-subtitle">管理用户发布的动态内容</p>
            </div>
        </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-2">
                            <label class="form-label">动态类型</label>
                            <select name="type" class="form-select">
                                <option value="">全部类型</option>
                                <option value="dynamic" {% if current_type == 'dynamic' %}selected{% endif %}>动态</option>
                                <option value="secondhand" {% if current_type == 'secondhand' %}selected{% endif %}>二手交易</option>
                                <option value="help" {% if current_type == 'help' %}selected{% endif %}>求助</option>
                                <option value="lost_found" {% if current_type == 'lost_found' %}selected{% endif %}>失物招领</option>
                                <option value="cat_friend" {% if current_type == 'cat_friend' %}selected{% endif %}>猫友</option>
                                <option value="cat_friends" {% if current_type == 'cat_friends' %}selected{% endif %}>猫友(旧)</option>
                                <option value="campus_run" {% if current_type == 'campus_run' %}selected{% endif %}>校园跑</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">状态</label>
                            <select name="status" class="form-select">
                                <option value="">全部状态</option>
                                <option value="active" {% if current_status == 'active' %}selected{% endif %}>正常</option>
                                <option value="hidden" {% if current_status == 'hidden' %}selected{% endif %}>隐藏</option>
                                <option value="deleted" {% if current_status == 'deleted' %}selected{% endif %}>已删除</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">排序方式</label>
                            <select name="sort" class="form-select">
                                <option value="created_at_desc" {% if current_sort == 'created_at_desc' %}selected{% endif %}>最新发布</option>
                                <option value="created_at_asc" {% if current_sort == 'created_at_asc' %}selected{% endif %}>最早发布</option>
                                <option value="like_count_desc" {% if current_sort == 'like_count_desc' %}selected{% endif %}>点赞最多</option>
                                <option value="like_count_asc" {% if current_sort == 'like_count_asc' %}selected{% endif %}>点赞最少</option>
                                <option value="comment_count_desc" {% if current_sort == 'comment_count_desc' %}selected{% endif %}>评论最多</option>
                                <option value="comment_count_asc" {% if current_sort == 'comment_count_asc' %}selected{% endif %}>评论最少</option>
                                <option value="view_count_desc" {% if current_sort == 'view_count_desc' %}selected{% endif %}>浏览最多</option>
                                <option value="view_count_asc" {% if current_sort == 'view_count_asc' %}selected{% endif %}>浏览最少</option>
                            </select>
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">每页</label>
                            <select name="per_page" class="form-select">
                                <option value="5" {% if current_per_page == 5 %}selected{% endif %}>5条</option>
                                <option value="10" {% if current_per_page == 10 %}selected{% endif %}>10条</option>
                                <option value="20" {% if current_per_page == 20 %}selected{% endif %}>20条</option>
                                <option value="50" {% if current_per_page == 50 %}selected{% endif %}>50条</option>
                                <option value="100" {% if current_per_page == 100 %}selected{% endif %}>100条</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">搜索内容</label>
                            <div class="input-group">
                                <input type="text" name="search" class="form-control" placeholder="搜索动态内容..." value="{{ current_search or '' }}">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                                {% if current_search %}
                                <a href="?" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i>
                                </a>
                                {% endif %}
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 动态列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">动态列表</h5>
                    <div>
                        {% if pagination %}
                            <span class="text-muted">共 {{ pagination.total }} 条记录</span>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if posts %}
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>ID</th>
                                        <th>用户</th>
                                        <th>类型</th>
                                        <th>内容</th>
                                        <th>图片</th>
                                        <th>互动数据</th>
                                        <th>状态</th>
                                        <th>发布时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for post in posts %}
                                    <tr>
                                        <td>{{ post.id }}</td>
                                        <td>
                                            {% if post.user %}
                                                <div class="d-flex align-items-center">
                                                    {% if post.user.avatar %}
                                                        <img src="{{ post.user.avatar }}" class="rounded-circle me-2" width="32" height="32">
                                                    {% else %}
                                                        <div class="bg-secondary rounded-circle me-2 d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                                                            <i class="fas fa-user text-white"></i>
                                                        </div>
                                                    {% endif %}
                                                    <div>
                                                        <div class="fw-bold">{{ post.user.nickname or '未知用户' }}</div>
                                                        <small class="text-muted">ID: {{ post.user_id }}</small>
                                                    </div>
                                                </div>
                                            {% else %}
                                                <span class="text-muted">用户ID: {{ post.user_id }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge {{ get_post_type_badge_class(post.type) }}">{{ get_post_type_label(post.type) }}</span>
                                        </td>
                                        <td>
                                            <div class="content-preview" style="max-width: 200px;">
                                                {{ post.content[:50] }}{% if post.content|length > 50 %}...{% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            {% if post.images %}
                                                {% set images = post.images if post.images is iterable else [] %}
                                                {% if images|length > 0 %}
                                                    <span class="badge bg-info">{{ images|length }} 张图片</span>
                                                {% else %}
                                                    <span class="text-muted">无图片</span>
                                                {% endif %}
                                            {% else %}
                                                <span class="text-muted">无图片</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="small">
                                                <div><i class="fas fa-eye text-info"></i> {{ post.view_count }}</div>
                                                <div><i class="fas fa-heart text-danger"></i> {{ post.like_count }}</div>
                                                <div><i class="fas fa-comment text-primary"></i> {{ post.comment_count }}</div>
                                                <div><i class="fas fa-star text-warning"></i> {{ post.collect_count }}</div>
                                            </div>
                                        </td>
                                        <td>
                                            {% if post.status == 'active' %}
                                                <span class="badge bg-success">正常</span>
                                            {% elif post.status == 'hidden' %}
                                                <span class="badge bg-warning">隐藏</span>
                                            {% elif post.status == 'deleted' %}
                                                <span class="badge bg-danger">已删除</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ post.status }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="small">
                                                {{ post.created_at.strftime('%Y-%m-%d') if post.created_at else '' }}<br>
                                                {{ post.created_at.strftime('%H:%M:%S') if post.created_at else '' }}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-outline-primary" onclick="viewPost({{ post.id }})">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                {% if post.status == 'active' %}
                                                    <button type="button" class="btn btn-outline-warning" onclick="updatePostStatus({{ post.id }}, 'hidden')">
                                                        <i class="fas fa-eye-slash"></i>
                                                    </button>
                                                {% elif post.status == 'hidden' %}
                                                    <button type="button" class="btn btn-outline-success" onclick="updatePostStatus({{ post.id }}, 'active')">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                {% endif %}
                                                <button type="button" class="btn btn-outline-danger" onclick="deletePost({{ post.id }})">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">暂无动态数据</h5>
                            <p class="text-muted">当前筛选条件下没有找到任何动态</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 高级分页导航 -->
    {% if pagination %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <div class="d-flex align-items-center">
                                <span class="text-muted me-3">
                                    显示第 {{ (pagination.page - 1) * pagination.per_page + 1 }} -
                                    {{ pagination.page * pagination.per_page if pagination.page * pagination.per_page <= pagination.total else pagination.total }} 条，
                                    共 {{ pagination.total }} 条记录
                                </span>
                            </div>
                        </div>
                        <div class="col-md-4 text-center">
                            {% if pagination.pages > 1 %}
                            <nav aria-label="动态分页">
                                <ul class="pagination pagination-sm justify-content-center mb-0">
                                    <!-- 首页 -->
                                    {% if pagination.page > 1 %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('cat_circle_admin.manage_posts', page=1, type=current_type or '', status=current_status or '', search=current_search or '', sort=current_sort or 'created_at_desc', per_page=current_per_page or 10) }}" title="首页">
                                            <i class="fas fa-angle-double-left"></i>
                                        </a>
                                    </li>
                                    {% endif %}

                                    <!-- 上一页 -->
                                    {% if pagination.has_prev %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('cat_circle_admin.manage_posts', page=pagination.prev_num, type=current_type or '', status=current_status or '', search=current_search or '', sort=current_sort or 'created_at_desc', per_page=current_per_page or 10) }}" title="上一页">
                                            <i class="fas fa-angle-left"></i>
                                        </a>
                                    </li>
                                    {% endif %}

                                    <!-- 页码 -->
                                    {% for page_num in pagination.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                                        {% if page_num %}
                                            {% if page_num != pagination.page %}
                                                <li class="page-item">
                                                    <a class="page-link" href="{{ url_for('cat_circle_admin.manage_posts', page=page_num, type=current_type or '', status=current_status or '', search=current_search or '', sort=current_sort or 'created_at_desc', per_page=current_per_page or 10) }}">{{ page_num }}</a>
                                                </li>
                                            {% else %}
                                                <li class="page-item active">
                                                    <span class="page-link">{{ page_num }}</span>
                                                </li>
                                            {% endif %}
                                        {% else %}
                                            <li class="page-item disabled">
                                                <span class="page-link">…</span>
                                            </li>
                                        {% endif %}
                                    {% endfor %}

                                    <!-- 下一页 -->
                                    {% if pagination.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('cat_circle_admin.manage_posts', page=pagination.next_num, type=current_type or '', status=current_status or '', search=current_search or '', sort=current_sort or 'created_at_desc', per_page=current_per_page or 10) }}" title="下一页">
                                            <i class="fas fa-angle-right"></i>
                                        </a>
                                    </li>
                                    {% endif %}

                                    <!-- 末页 -->
                                    {% if pagination.page < pagination.pages %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('cat_circle_admin.manage_posts', page=pagination.pages, type=current_type or '', status=current_status or '', search=current_search or '', sort=current_sort or 'created_at_desc', per_page=current_per_page or 10) }}" title="末页">
                                            <i class="fas fa-angle-double-right"></i>
                                        </a>
                                    </li>
                                    {% endif %}
                                </ul>
                            </nav>
                            {% endif %}
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex justify-content-end align-items-center">
                                <span class="text-muted me-2">跳转到:</span>
                                <div class="input-group" style="width: 120px;">
                                    <input type="number" class="form-control form-control-sm" id="jumpToPage" min="1" max="{{ pagination.pages }}" placeholder="页码">
                                    <button class="btn btn-outline-primary btn-sm" type="button" onclick="jumpToPage()">
                                        <i class="fas fa-arrow-right"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- 动态详情模态框 -->
<div class="modal fade" id="postDetailModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">动态详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="postDetailContent">
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载动态详情...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function viewPost(postId) {
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('postDetailModal'));
    modal.show();

    // 重置内容为加载状态
    document.getElementById('postDetailContent').innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在加载动态详情...</p>
        </div>
    `;

    // 获取动态详情
    fetch(`/admin/cat-circle/api/posts/${postId}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayPostDetail(data.data);
        } else {
            document.getElementById('postDetailContent').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> 加载失败: ${data.message}
                </div>
            `;
        }
    })
    .catch(error => {
        document.getElementById('postDetailContent').innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> 网络错误: ${error}
            </div>
        `;
    });
}

function displayPostDetail(post) {
    const typeLabels = {
        'dynamic': '动态',
        'secondhand': '二手交易',
        'help': '求助',
        'lost_found': '失物招领',
        'cat_friend': '猫友',
        'cat_friends': '猫友',
        'campus_run': '校园跑'
    };

    const statusLabels = {
        'active': '<span class="badge bg-success">正常</span>',
        'hidden': '<span class="badge bg-warning">隐藏</span>',
        'deleted': '<span class="badge bg-danger">已删除</span>'
    };

    let imagesHtml = '';
    if (post.images && post.images.length > 0) {
        imagesHtml = `
            <div class="mb-3">
                <h6>图片 (${post.images.length}张)</h6>
                <div class="row">
                    ${post.images.map(img => `
                        <div class="col-md-3 mb-2">
                            <img src="${img}" class="img-fluid rounded" style="max-height: 150px; object-fit: cover; cursor: pointer;" onclick="window.open('${img}', '_blank')">
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    let commentsHtml = '';
    if (post.comments && post.comments.length > 0) {
        commentsHtml = `
            <div class="mb-3">
                <h6>最新评论 (共${post.comment_count}条，显示最新${post.comments.length}条)</h6>
                <div class="list-group">
                    ${post.comments.map(comment => `
                        <div class="list-group-item">
                            <div class="d-flex align-items-start">
                                <div class="me-3">
                                    ${comment.user.avatar ?
                                        `<img src="${comment.user.avatar}" class="rounded-circle" width="32" height="32">` :
                                        `<div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                                            <i class="fas fa-user text-white"></i>
                                        </div>`
                                    }
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex justify-content-between">
                                        <h6 class="mb-1">${comment.user.nickname}</h6>
                                        <small class="text-muted">${comment.created_at}</small>
                                    </div>
                                    <p class="mb-1">${comment.content}</p>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    const content = `
        <div class="row">
            <div class="col-md-8">
                <!-- 基本信息 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info-circle"></i> 基本信息</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-borderless">
                            <tr>
                                <td width="100"><strong>动态ID:</strong></td>
                                <td>${post.id}</td>
                            </tr>
                            <tr>
                                <td><strong>类型:</strong></td>
                                <td><span class="badge bg-primary">${typeLabels[post.type] || post.type}</span></td>
                            </tr>
                            <tr>
                                <td><strong>状态:</strong></td>
                                <td>${statusLabels[post.status] || post.status}</td>
                            </tr>
                            <tr>
                                <td><strong>发布时间:</strong></td>
                                <td>${post.created_at}</td>
                            </tr>
                            <tr>
                                <td><strong>更新时间:</strong></td>
                                <td>${post.updated_at}</td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- 内容 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-file-text"></i> 动态内容</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-0">${post.content}</p>
                    </div>
                </div>

                ${imagesHtml}
                ${commentsHtml}
            </div>

            <div class="col-md-4">
                <!-- 用户信息 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-user"></i> 发布用户</h6>
                    </div>
                    <div class="card-body text-center">
                        ${post.user ? `
                            <div class="mb-3">
                                ${post.user.avatar ?
                                    `<img src="${post.user.avatar}" class="rounded-circle mb-2" width="64" height="64">` :
                                    `<div class="bg-secondary rounded-circle mx-auto mb-2 d-flex align-items-center justify-content-center" style="width: 64px; height: 64px;">
                                        <i class="fas fa-user text-white fa-2x"></i>
                                    </div>`
                                }
                                <h6>${post.user.nickname}</h6>
                                <small class="text-muted">用户ID: ${post.user.id}</small>
                            </div>
                        ` : `
                            <div class="text-muted">
                                <i class="fas fa-user-slash fa-2x mb-2"></i>
                                <p>用户信息不可用</p>
                            </div>
                        `}
                    </div>
                </div>

                <!-- 统计信息 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-chart-bar"></i> 统计数据</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="border rounded p-2">
                                    <i class="fas fa-eye text-info fa-2x"></i>
                                    <div class="mt-1">
                                        <strong>${post.view_count}</strong>
                                        <div class="small text-muted">浏览</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="border rounded p-2">
                                    <i class="fas fa-heart text-danger fa-2x"></i>
                                    <div class="mt-1">
                                        <strong>${post.like_count}</strong>
                                        <div class="small text-muted">点赞</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="border rounded p-2">
                                    <i class="fas fa-comment text-primary fa-2x"></i>
                                    <div class="mt-1">
                                        <strong>${post.comment_count}</strong>
                                        <div class="small text-muted">评论</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="border rounded p-2">
                                    <i class="fas fa-star text-warning fa-2x"></i>
                                    <div class="mt-1">
                                        <strong>${post.collect_count}</strong>
                                        <div class="small text-muted">收藏</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                ${post.contact_info ? `
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-phone"></i> 联系方式</h6>
                        </div>
                        <div class="card-body">
                            <p class="mb-0">${post.contact_info}</p>
                        </div>
                    </div>
                ` : ''}

                ${post.gender ? `
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-venus-mars"></i> 性别</h6>
                        </div>
                        <div class="card-body">
                            <p class="mb-0">${post.gender}</p>
                        </div>
                    </div>
                ` : ''}
            </div>
        </div>
    `;

    document.getElementById('postDetailContent').innerHTML = content;
}

function updatePostStatus(postId, status) {
    if (confirm('确定要更改动态状态吗？')) {
        fetch(`/admin/cat-circle/api/posts/${postId}/status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ status: status })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('操作失败: ' + data.message);
            }
        })
        .catch(error => {
            alert('操作失败: ' + error);
        });
    }
}

// 跳转到指定页面
function jumpToPage() {
    const pageInput = document.getElementById('jumpToPage');
    const page = parseInt(pageInput.value);

    if (page && page > 0 && page <= {{ pagination.pages if pagination else 1 }}) {
        const url = new URL(window.location);
        url.searchParams.set('page', page);
        window.location.href = url.toString();
    } else {
        alert('请输入有效的页码 (1-{{ pagination.pages if pagination else 1 }})');
        pageInput.focus();
    }
}

// 回车键跳转
document.addEventListener('DOMContentLoaded', function() {
    const pageInput = document.getElementById('jumpToPage');
    if (pageInput) {
        pageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                jumpToPage();
            }
        });
    }
});

function deletePost(postId) {
    if (confirm('确定要删除这条动态吗？此操作不可恢复！')) {
        fetch(`/admin/cat-circle/api/posts/${postId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('删除失败: ' + data.message);
            }
        })
        .catch(error => {
            alert('删除失败: ' + error);
        });
    }
}
</script>

<style>
.page-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.content-preview {
    word-break: break-word;
    line-height: 1.4;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}
</style>
{% endblock %}

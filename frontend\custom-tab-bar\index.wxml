<view class="tab-bar">
  <view 
    wx:for="{{list}}" 
    wx:key="index" 
    class="tab-bar-item {{item.isSpecial ? 'special-item' : ''}}" 
    data-path="{{item.pagePath}}" 
    data-index="{{index}}" 
    bindtap="switchTab"
  >
    <!-- 特殊图标（猫友圈）的显示 -->
    <view wx:if="{{item.isSpecial}}" class="special-icon-container">
      <view class="special-icon-bg {{selected === index ? 'active' : ''}}">
        <image 
          src="{{selected === index ? item.selectedIconPath : item.iconPath}}" 
          class="special-icon"
        />
      </view>
    </view>
    
    <!-- 普通图标的显示 -->
    <view wx:else class="normal-icon-container">
      <image 
        src="{{selected === index ? item.selectedIconPath : item.iconPath}}" 
        class="normal-icon"
      />
    </view>
    
    <!-- 文字标签 -->
    <view class="tab-bar-text {{selected === index ? 'active' : ''}}" style="color: {{selected === index ? selectedColor : color}}">
      {{item.text}}
    </view>
  </view>
</view>

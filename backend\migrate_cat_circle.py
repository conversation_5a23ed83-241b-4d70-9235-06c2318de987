#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pymysql
from flask import Flask
from flask_sqlalchemy import SQLAlchemy

# 创建Flask应用
app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql+pymysql://root:123456@localhost/quiz_app'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# 初始化数据库
db = SQLAlchemy(app)

def migrate_cat_circle_table():
    """迁移猫友圈表，添加新字段"""
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='quiz_app',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        print("开始迁移猫友圈表...")
        
        # 检查表是否存在
        cursor.execute("SHOW TABLES LIKE 'cat_circle_posts'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("表不存在，创建新表...")
            create_table_sql = """
            CREATE TABLE cat_circle_posts (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                content TEXT NOT NULL,
                images TEXT,
                type VARCHAR(20) NOT NULL DEFAULT 'dynamic',
                contact_info VARCHAR(200),
                gender VARCHAR(10),
                price VARCHAR(50),
                location VARCHAR(200),
                time VARCHAR(100),
                status VARCHAR(20) DEFAULT 'active',
                view_count INT DEFAULT 0,
                like_count INT DEFAULT 0,
                comment_count INT DEFAULT 0,
                collect_count INT DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """
            cursor.execute(create_table_sql)
            print("✅ 表创建成功!")
        else:
            print("表已存在，检查并添加缺失的字段...")
            
            # 检查字段是否存在
            cursor.execute("DESCRIBE cat_circle_posts")
            existing_columns = [row[0] for row in cursor.fetchall()]
            
            # 需要添加的字段
            new_columns = {
                'price': "ALTER TABLE cat_circle_posts ADD COLUMN price VARCHAR(50)",
                'location': "ALTER TABLE cat_circle_posts ADD COLUMN location VARCHAR(200)",
                'time': "ALTER TABLE cat_circle_posts ADD COLUMN time VARCHAR(100)"
            }
            
            for column_name, sql in new_columns.items():
                if column_name not in existing_columns:
                    print(f"添加字段: {column_name}")
                    cursor.execute(sql)
                else:
                    print(f"字段 {column_name} 已存在")
        
        # 检查敏感词表
        cursor.execute("SHOW TABLES LIKE 'sensitive_words'")
        sensitive_table_exists = cursor.fetchone()
        
        if not sensitive_table_exists:
            print("创建敏感词表...")
            create_sensitive_table_sql = """
            CREATE TABLE sensitive_words (
                id INT AUTO_INCREMENT PRIMARY KEY,
                word VARCHAR(100) NOT NULL,
                type VARCHAR(20) DEFAULT 'normal',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """
            cursor.execute(create_sensitive_table_sql)
            
            # 添加一些基础敏感词
            sensitive_words = ['垃圾', '骗子', '傻逼', '白痴', '死']
            for word in sensitive_words:
                cursor.execute("INSERT INTO sensitive_words (word, type) VALUES (%s, %s)", (word, 'normal'))
            
            print(f"✅ 敏感词表创建成功，添加了 {len(sensitive_words)} 个敏感词")
        
        # 提交更改
        connection.commit()
        print("✅ 数据库迁移完成!")
        
        # 验证表结构
        cursor.execute("DESCRIBE cat_circle_posts")
        columns = cursor.fetchall()
        print("\n当前表结构:")
        for column in columns:
            print(f"  {column[0]} - {column[1]}")
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    migrate_cat_circle_table()

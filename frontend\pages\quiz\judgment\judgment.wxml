<!--pages/quiz/judgment/judgment.wxml-->
<view class="container">
  <!-- 头部进度条 -->
  <view class="header">
    <view class="progress-container">
      <view class="progress-text-container">
        <text class="progress-text">{{currentQuestionIndex + 1}}/{{questions.length}}</text>
      </view>
      <progress percent="{{progress}}" stroke-width="3" activeColor="#9c27b0" backgroundColor="#e5e5e5" />
    </view>
    
    <!-- 课程筛选 -->
    <view class="course-filter" wx:if="{{coursesList.length > 0 && mode !== 'wrong'}}">
      <scroll-view scroll-x="true" class="course-list">
        <view 
          class="course-item {{activeCourse === 0 ? 'active' : ''}}" 
          bindtap="switchCourse" 
          data-id="0"
        >
          全部
        </view>
        <view 
          wx:for="{{coursesList}}" 
          wx:key="id"
          class="course-item {{activeCourse === item.id ? 'active' : ''}}"
          bindtap="switchCourse"
          data-id="{{item.id}}"
        >
          {{item.name}}
        </view>
      </scroll-view>
    </view>
  </view>
  
  <view class="quiz-content" wx:if="{{!loading && questions.length > 0}}">
    <view class="question">
      <text class="question-text">{{questions[currentQuestionIndex].question}}</text>
      <text class="question-hint">(判断题，请选择正确或错误)</text>
    </view>
    
    <view class="options">
      <view 
        class="judgment-option {{selectedOption === true ? 'selected' : ''}} {{showResult && selectedOption === true ? (isCorrect ? 'correct' : 'wrong') : ''}}"
        bindtap="selectOption"
        data-value="{{true}}"
      >
        <text class="option-letter">A</text>
        <text class="option-text">正确</text>
        <view class="status-icon" wx:if="{{showResult && selectedOption === true}}">
          <text class="icon-text">{{isCorrect ? '✓' : '✗'}}</text>
        </view>
      </view>
      
      <view 
        class="judgment-option {{selectedOption === false ? 'selected' : ''}} {{showResult && selectedOption === false ? (isCorrect ? 'correct' : 'wrong') : ''}}"
        bindtap="selectOption"
        data-value="{{false}}"
      >
        <text class="option-letter">B</text>
        <text class="option-text">错误</text>
        <view class="status-icon" wx:if="{{showResult && selectedOption === false}}">
          <text class="icon-text">{{isCorrect ? '✓' : '✗'}}</text>
        </view>
      </view>
    </view>
    
    <!-- 操作按钮 - 提交答案按钮 -->
    <view class="action-buttons" wx:if="{{!showResult}}">
      <button 
        class="action-btn submit-btn" 
        bindtap="submitAnswer" 
        disabled="{{selectedOption === null}}"
      >
        提交答案
      </button>
    </view>
    
    <!-- 解释区域 -->
    <view class="answer-section" wx:if="{{showResult}}">
      <view class="correct-answer">
        <text class="answer-label">正确答案：</text>
        <view class="answer-value">
          <text class="answer-pill">{{correctAnswer ? '正确' : '错误'}}</text>
        </view>
      </view>
      
      <view class="result-feedback">
        <text class="{{isCorrect ? 'success-text' : 'error-text'}}">{{isCorrect ? '太棒了，回答正确！' : '很遗憾，回答错误！'}}</text>
      </view>
      
      <view wx:if="{{questions[currentQuestionIndex].explanation}}" class="explanation">
        <text class="explanation-text">{{questions[currentQuestionIndex].explanation}}</text>
      </view>
      
      <!-- 操作按钮 - 下一题按钮 -->
      <view class="action-buttons next-btn-container">
        <button 
          class="action-btn next-btn" 
          bindtap="nextQuestion"
        >
          {{currentQuestionIndex + 1 >= questions.length ? '完成答题' : '下一题'}}
        </button>
      </view>
    </view>
  </view>
  
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading"></view>
    <text class="loading-text">加载题目中...</text>
  </view>
  
  <!-- 空状态 - 没有题目 -->
  <view class="empty-state" wx:if="{{!loading && emptyState}}">
    <image class="empty-icon" src="/images/empty.png" mode="aspectFit"></image>
    <text class="empty-text">{{emptyStateMessage}}</text>
    <button class="btn-return" bindtap="navigateBack">返回上一页</button>
  </view>
  
  <!-- 完成确认对话框 -->
  <view class="modal-container" wx:if="{{showConfirmFinish}}">
    <view class="modal">
      <view class="modal-header">
        <text class="modal-title">完成答题</text>
      </view>
      <view class="modal-content">
        <text>您已完成所有判断题，共{{questions.length}}题，答对{{correctCount}}题。</text>
        <text>查看详细答题结果？</text>
      </view>
      <view class="modal-footer">
        <button class="modal-btn cancel-btn" bindtap="cancelFinish">继续</button>
        <button class="modal-btn confirm-btn" bindtap="confirmFinish">查看结果</button>
      </view>
    </view>
  </view>
</view> 
{% extends "admin/base.html" %}

{% block title %}仪表盘 - 大数据题库管理后台{% endblock %}

{% block header %}仪表盘{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard-enhanced.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard-modern.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/hide-chart-errors.css') }}">
<style>
/* Common styles */
.dashboard-welcome {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
}

/* Top stat cards styling */
.stats-card-container {
    margin-bottom: 2rem;
}

.top-stat-card {
    border: none;
    border-radius: 12px;
    background: white;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
    height: 100%;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

/* CHART SPECIFIC STYLES - IMPORTANT */
.chart-container {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 250px;
    overflow: visible !important;
}

.chart-canvas {
    width: 100% !important;
    height: 100% !important;
    max-height: 400px;
}

canvas[id$="Chart"] {
    display: block !important;
    visibility: visible !important;
    min-height: 250px;
    width: 100% !important;
    height: 100% !important;
}

.card-body.d-flex {
    min-height: 300px;
    padding: 1.5rem;
}

.donut-chart-container {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    width: 100%;
    min-height: 280px;
}

/* End of Chart Specific Styles */

.top-stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.top-stat-card .icon-container {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.top-stat-card .card-title {
    color: #718096;
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.top-stat-card .card-value {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.top-stat-card .card-footer {
    margin-top: 1rem;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    font-size: 0.875rem;
    border-top: 1px solid rgba(0,0,0,0.05);
    padding-top: 0.75rem;
}

.top-stat-card .trend-indicator {
    display: flex;
    align-items: center;
    font-weight: 600;
}

.top-stat-card .trend-up {
    color: #48bb78;
}

.top-stat-card .trend-down {
    color: #f56565;
}

.top-stat-card .trend-period {
    color: #a0aec0;
    margin-left: 0.5rem;
}

.top-stat-card .progress {
    height: 6px;
    background-color: #EDF2F7;
    border-radius: 3px;
    margin-top: 0.75rem;
    overflow: hidden;
}

.top-stat-card .progress-bar {
    border-radius: 3px;
}

.top-stat-card .progress-labels {
    display: flex;
    justify-content: space-between;
    width: 100%;
    font-size: 0.75rem;
    color: #718096;
}

.top-stat-card .card-footer .accuracy-indicators {
    margin-top: 0;
}

/* Icon background colors */
.user-metrics .icon-container {
    background-color: rgba(79, 70, 229, 0.1);
    color: #4F46E5;
}

.questions-metrics .icon-container {
    background-color: rgba(142, 84, 233, 0.1);
    color: #8E54E9;
}

.answers-metrics .icon-container {
    background-color: rgba(72, 187, 120, 0.1);
    color: #48BB78;
}

.accuracy-metrics .icon-container {
    background-color: rgba(237, 137, 54, 0.1);
    color: #ED8936;
}

/* Status badge styling */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
}

.status-badge.high {
    background-color: rgba(72, 187, 120, 0.1);
    color: #48BB78;
}

.status-badge.medium {
    background-color: rgba(237, 137, 54, 0.1);
    color: #ED8936;
}

.status-badge.low {
    background-color: rgba(245, 101, 101, 0.1);
    color: #F56565;
}

.welcome-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.stats-card {
    border-radius: 0.75rem;
    margin-bottom: 1.5rem;
    height: 100%;
    transition: transform 0.2s, box-shadow 0.2s;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.stats-icon {
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.5rem;
    color: white;
    margin-right: 1rem;
}

.stats-card .card-header {
    background-color: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1rem 1.25rem;
}

.stats-card .card-title {
    font-weight: 600;
    color: #4a5568;
}

.stats-card .card-body {
    padding: 1.25rem;
}

.stats-value {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    color: #2d3748;
}

.stats-label {
    font-size: 0.875rem;
    color: #718096;
}

.stats-progress {
    height: 0.5rem;
    margin-top: 0.5rem;
}

.stats-change {
    font-size: 0.875rem;
    font-weight: 600;
}

.change-up {
    color: #48bb78;
}

.change-down {
    color: #f56565;
}

.chart-container {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 250px;
    overflow: hidden;
}

.chart-container-lg {
    height: 100%;
    min-height: 300px;
}

.pie-chart-container {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.bar-chart-container {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 200px;
    padding: 10px 0;
}

.stats-meta {
    display: flex;
    justify-content: space-between;
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: #718096;
}

.admin-action-card {
    border-left: 4px solid #4e73df;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .chart-container, .chart-container-lg {
        height: 250px;
    }
    .pie-chart-container, .bar-chart-container {
        height: 200px;
    }
    .dashboard-welcome {
        padding: 1.5rem;
    }
}

.dashboard-welcome::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm34-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='rgba(255,255,255,.075)' fill-rule='evenodd'/%3E%3C/svg%3E");
    opacity: 0.3;
}

.dashboard-welcome-content {
    display: flex;
    align-items: center;
    position: relative;
    z-index: 1;
}

.welcome-icon {
    background-color: rgba(255, 255, 255, 0.2);
    width: 64px;
    height: 64px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    font-size: 28px;
}

.welcome-text h3 {
    font-weight: 700;
    margin-bottom: 5px;
    font-size: 1.6rem;
}

.welcome-text p {
    margin-bottom: 0;
    opacity: 0.8;
    font-size: 1rem;
}

.welcome-stats {
    display: flex;
    margin-top: 20px;
    gap: 15px;
}

.welcome-stat {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 15px;
    flex: 1;
}

.welcome-stat-value {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.welcome-stat-label {
    font-size: 0.85rem;
    opacity: 0.8;
}

.welcome-actions {
    margin-left: auto;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.welcome-actions .btn {
    border-radius: 10px;
    font-weight: 500;
    padding: 8px 15px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    transition: all 0.3s ease;
}

.welcome-actions .btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.welcome-actions .btn i {
    margin-right: 5px;
}

.stats-card {
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.stats-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0) 50%);
    z-index: -1;
}

.stats-card .number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.stats-card .number i {
    font-size: 1.8rem;
    margin-right: 10px;
    padding: 12px;
    border-radius: 12px;
    background: rgba(var(--primary-rgb), 0.1);
    color: var(--primary-color);
}

.stats-card .label {
    color: var(--gray-600);
    font-size: 1rem;
    font-weight: 500;
}

.stats-footer {
    margin-top: 15px;
    font-size: 0.85rem;
}

.trend-up {
    color: #28a745;
}

.trend-down {
    color: #dc3545;
}

.trend-period {
    color: var(--gray-500);
}

.chart-actions .btn-group .btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.progress {
    border-radius: 10px;
}

.progress-bar {
    border-radius: 10px;
}

/* New styles for accuracy status indicators */
.accuracy-indicators {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
    width: 100%;
}

.accuracy-indicator {
    display: inline-flex;
    align-items: center;
    font-size: 0.8rem;
    margin-right: 0.5rem;
    margin-bottom: 0.25rem;
}

.accuracy-indicator .badge {
    padding: 0.25rem 0.5rem;
    margin-right: 0.25rem;
    font-size: 0.7rem;
    font-weight: 500;
}

/* 新增卡片的样式 */
.difficulty-bar {
    transition: all 0.3s ease;
}

.difficulty-bar:hover {
    transform: translateY(-5px);
}

.timeline-wrapper {
    position: relative;
}

.timeline-badge {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-top: 3px;
}

.card-header .dropdown .btn-link {
    color: rgba(0,0,0,0.5);
}

.card-header .dropdown .btn-link:hover {
    color: rgba(0,0,0,0.7);
}

/* 为图表容器添加明确的高度 */
.chart-container {
    height: 100%;
    min-height: 300px;
    position: relative;
    width: 100%;
    border-radius: 8px;
    overflow: hidden;
    background: linear-gradient(to bottom, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.3) 100%);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
    padding: 1rem;
    transition: all 0.3s ease;
}

.chart-container:hover {
    box-shadow: inset 0 1px 5px rgba(0, 0, 0, 0.1);
    background: linear-gradient(to bottom, rgba(248, 250, 252, 0.9) 0%, rgba(241, 245, 249, 0.4) 100%);
}

.mini-chart-container {
    height: 150px;
    position: relative;
}

/* 确保卡片内的图表容器填充整个空间 */
.card-body .chart-container {
    height: calc(100% - 1rem);
    width: calc(100% - 1rem);
    margin: 0.5rem;
}

/* 活动图表专用样式 */
#activityChart {
    filter: drop-shadow(0 4px 6px rgba(79, 70, 229, 0.1));
}

/* 确保卡片内容区域有足够高度 */
.card.h-100 .card-body {
    padding: 1rem;
    height: calc(100% - 56px); /* 减去卡片头部高度 */
}

/* 为图表添加自适应高度 */
@media (max-width: 768px) {
    .chart-container {
        min-height: 200px;
    }
}

/* 添加针对不同尺寸图表的特定样式 */
.chart-container-sm {
    height: 100%;
    min-height: 180px;
}

.chart-container-lg {
    height: 100%;
    min-height: 300px;
}

/* 针对右侧卡片中的图表容器进行特殊处理 */
.card-body .chart-container {
    width: 100%;
    height: 100%;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 饼图容器特殊样式 */
.pie-chart-container {
    height: 100%;
    min-height: 200px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 柱状图容器特殊样式 */
.bar-chart-container {
    height: 100%;
    min-height: 200px;
    width: 100%;
    padding: 10px 0;
}

/* 图表展示区域增强 */
.chart-area {
    background-color: rgba(250, 250, 250, 0.5);
    border-radius: 8px;
    padding: 15px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.03);
    height: 100%;
}

/* 确保Canvas元素在容器中正确显示 */
.chart-container canvas {
    max-width: 100%;
    max-height: 100%;
    height: 100% !important;
    width: 100% !important;
    display: block;
    margin: 0 auto;
}

/* 确保右侧卡片中的图表正确显示 */
.col-lg-4 .chart-container {
    height: 100%;
    min-height: 200px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .chart-container {
        min-height: 200px;
    }
    
    .pie-chart-container,
    .bar-chart-container {
        min-height: 160px;
    }
}

/* 新的图表行样式 */
.row.mt-4 .card-body {
    height: 350px;
    padding: 1.25rem;
}

.row.mt-4 .chart-container-lg {
    height: 280px;
}

.row.mt-4 .pie-chart-container,
.row.mt-4 .bar-chart-container {
    height: 220px;
}

/* 用于用户活动趋势和题型分布的图表 */
#userActivityChart, #difficultyChart, #categoriesChart {
    width: 100% !important;
    height: 100% !important;
}

.row.mt-4 .stats-card .card-body {
    height: 400px;
    padding: 1.25rem;
    display: flex;
    flex-direction: column;
}

.row.mt-4 .chart-container-lg {
    height: 280px;
    flex-grow: 1;
}

.row.mt-4 .pie-chart-container,
.row.mt-4 .bar-chart-container {
    height: 220px;
    flex-grow: 1;
}

.row.mt-4 .card-body .mt-3 {
    margin-top: auto !important;
}

.row.mt-4 .d-flex.justify-content-end {
    margin-top: auto;
}

.chart-container {
    position: relative;
    min-height: 300px;
    margin: 0 auto;
}
.donut-chart-container {
    position: relative;
    min-height: 250px;
    margin: 0 auto;
}
.activity-bars {
    display: flex;
    align-items: flex-end;
    justify-content: center;
    height: 180px;
    margin: 0 auto;
    width: 90%;
}
.activity-bar {
    flex: 1;
    margin: 0 4px;
    border-radius: 4px 4px 0 0;
    background-color: #4CAF50;
    max-width: 40px;
    transition: all 0.3s ease;
}
.activity-bar:hover {
    filter: brightness(1.1);
}

/* 仪表盘样式 */
.dashboard-card {
    transition: all 0.3s ease;
}
.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}
.card-value {
    font-size: 2rem;
    font-weight: 600;
}
.chart-container {
    height: 250px;
    max-height: 250px;
    position: relative;
    width: 100%;
}
.mini-chart-container {
    height: 150px;
    position: relative;
}

/* 优化图表卡片样式 */
.chart-card {
    box-shadow: 0 4px 10px rgba(0,0,0,0.05);
    border-radius: 12px;
    overflow: hidden;
    height: 100%;
    transition: all 0.3s ease;
}
.chart-card:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.08);
    transform: translateY(-5px);
}
.chart-card .card-header {
    padding: 0.8rem 1.25rem;
    background-color: transparent;
    border-bottom: 1px solid rgba(0,0,0,0.05);
}
.chart-card .card-body {
    padding: 0.8rem;
    height: calc(100% - 50px);
}
.chart-card .chart-container {
    height: 100%;
    width: 100%;
    padding: 0;
    margin: 0;
}

/* 图表容器填充优化 */
canvas.chart-canvas {
    width: 100% !important;
    height: 100% !important;
}
/* 其他样式 */
/* ... */
</style>
{% endblock %}

{% block content %}
<!-- 增强的欢迎面板 -->
<div class="dashboard-welcome animate-fade-in">
    <div class="dashboard-welcome-content">
        <div class="welcome-icon">
            <i class="fas fa-chart-line"></i>
        </div>
        <div class="welcome-text">
            <h3>欢迎回到题库管理系统</h3>
            <p>今天是 {{ now.strftime('%Y年%m月%d日') }}，祝您工作愉快！</p>
            
            <div class="welcome-stats">
                <div class="welcome-stat">
                    <div class="welcome-stat-value">{{ total_users }}</div>
                    <div class="welcome-stat-label">总用户数</div>
                </div>
                <div class="welcome-stat">
                    <div class="welcome-stat-value">{{ active_users_today }}</div>
                    <div class="welcome-stat-label">今日活跃用户</div>
                </div>
                <div class="welcome-stat">
                    <div class="welcome-stat-value">{{ quizzes_today }}</div>
                    <div class="welcome-stat-label">今日答题数</div>
                </div>
                <div class="welcome-stat">
                    <div class="welcome-stat-value">{{ "%.1f"|format(system_accuracy) }}%</div>
                    <div class="welcome-stat-label">系统正确率</div>
                </div>
            </div>
        </div>
        <div class="welcome-actions">
            <button class="btn" onclick="showNotification('数据已刷新', 'success');">
                <i class="fas fa-sync-alt"></i> 刷新数据
            </button>
            <button class="btn" onclick="location.href='/admin/statistics'; return false;">
                查看详细统计 <i class="fas fa-arrow-right"></i>
            </button>
        </div>
    </div>
</div>

<!-- 新的统计卡片设计 -->
<div class="row stats-card-container">
    <!-- 用户统计卡片 -->
    <div class="col-md-3 col-sm-6 mb-3 animate-fade-in">
        <div class="top-stat-card user-metrics">
            <div class="icon-container">
                <i class="fas fa-users"></i>
            </div>
            <div class="card-title">总用户数</div>
            <div class="card-value">{{ total_users }}</div>
            <div class="card-footer">
                <div class="trend-indicator trend-up">
                    <i class="fas fa-arrow-up me-1"></i>
                    {{ new_users_this_week|default(3) }}
                </div>
                <div class="trend-period">本周新增</div>
            </div>
            <div class="progress">
                <div class="progress-bar bg-primary" role="progressbar" style="width: 75%"></div>
            </div>
        </div>
    </div>
    
    <!-- 活跃用户卡片 -->
    <div class="col-md-3 col-sm-6 mb-3 animate-fade-in">
        <div class="top-stat-card questions-metrics">
            <div class="icon-container">
                <i class="fas fa-user-clock"></i>
            </div>
            <div class="card-title">活跃天数</div>
            <div class="card-value">{{ active_users_today }}</div>
            <div class="card-footer">
                <div><i class="fas fa-dot-circle text-primary"></i> 用户: {{ active_users_month_count|default(4) }}</div>
                <div><i class="fas fa-dot-circle text-info"></i> 答题: {{ total_answers|default(125) }}</div>
            </div>
            <div class="progress">
                <div class="progress-bar bg-primary" role="progressbar" style="width: {{ (active_users_today / total_users * 100)|int if total_users > 0 else 50 }}%"></div>
            </div>
        </div>
    </div>
    
    <!-- 题目统计卡片 -->
    <div class="col-md-3 col-sm-6 mb-3 animate-fade-in">
        <div class="top-stat-card answers-metrics">
            <div class="icon-container">
                <i class="fas fa-tasks"></i>
            </div>
            <div class="card-title">总答题数</div>
            <div class="card-value">{{ total_questions }}</div>
            <div class="card-footer">
                <div class="trend-indicator trend-up">
                    <i class="fas fa-arrow-up me-1"></i>
                    {{ quizzes_today|default(25) }}
                </div>
                <div class="trend-period">今日</div>
            </div>
            <div class="progress">
                <div class="progress-bar bg-success" role="progressbar" style="width: {{ system_accuracy }}%"></div>
            </div>
        </div>
    </div>
    
    <!-- 正确率统计卡片 -->
    <div class="col-md-3 col-sm-6 mb-3 animate-fade-in">
        <div class="top-stat-card accuracy-metrics">
            <div class="icon-container">
                <i class="fas fa-percentage"></i>
            </div>
            <div class="card-title">正确率</div>
            <div class="card-value">{{ "%.1f"|format(accuracy) }}%</div>
            <div class="card-footer">
                <div class="accuracy-indicator">
                    <span class="badge bg-success">简单</span> {{ "%.0f"|format(100.0 - single_error_rate) }}%
                </div>
                <div class="accuracy-indicator">
                    <span class="badge bg-warning">中等</span> {{ "%.0f"|format(100.0 - multiple_error_rate) }}%
                </div>
            </div>
            <div class="progress">
                <div class="progress-bar bg-success" role="progressbar" style="width: {{ 100.0 - error_rate }}%"></div>
                <div class="progress-bar bg-danger" role="progressbar" style="width: {{ error_rate }}%"></div>
            </div>
        </div>
    </div>
</div>

<!-- 数据筛选功能已移除 -->

<div class="row">
    <!-- 用户答题数据趋势 -->
    <div class="col-lg-8 mb-4">
        <div class="card chart-card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-chart-line"></i> 用户答题数据趋势
                </div>
                <div class="chart-actions">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-outline-secondary active" data-chart-view="daily">日</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" data-chart-view="weekly">周</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" data-chart-view="monthly">月</button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div style="height: 220px;">
                    <canvas id="activityChart" class="chart-canvas" data-chart-values='{"labels": ["{{ now.strftime('%Y-%m-%d') }}", "{{ (now - datetime.timedelta(days=1)).strftime('%Y-%m-%d') }}", "{{ (now - datetime.timedelta(days=2)).strftime('%Y-%m-%d') }}", "{{ (now - datetime.timedelta(days=3)).strftime('%Y-%m-%d') }}", "{{ (now - datetime.timedelta(days=4)).strftime('%Y-%m-%d') }}", "{{ (now - datetime.timedelta(days=5)).strftime('%Y-%m-%d') }}", "{{ (now - datetime.timedelta(days=6)).strftime('%Y-%m-%d') }}"], "datasets": [{"label": "答题数", "data": [{{ quizzes_today }}, {{ quizzes_today - 5 }}, {{ quizzes_today - 12 }}, {{ quizzes_today - 8 }}, {{ quizzes_today - 15 }}, {{ quizzes_today - 10 }}, {{ quizzes_today - 7 }}], "borderColor": "#4f46e5", "backgroundColor": "rgba(79, 70, 229, 0.2)", "fill": true, "tension": 0.4}]}'></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 题型分布卡片 -->
    <div class="col-lg-4 mb-4">
        <div class="card chart-card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-chart-pie"></i> 题型分布
                </div>
            </div>
            <div class="card-body d-flex align-items-center justify-content-center">
                <div style="height: 220px; width: 100%;">
                    <canvas id="questionTypeChart" class="chart-canvas" data-chart-values='{"labels": ["单选题", "多选题", "判断题", "填空题"], "datasets": [{"data": [{{ single_choice_count }}, {{ multiple_choice_count }}, {{ judgment_questions|default(0) }}, {{ fill_blank_questions|default(0) }}], "backgroundColor": ["rgba(79, 70, 229, 0.8)", "rgba(245, 158, 11, 0.8)", "rgba(16, 185, 129, 0.8)", "rgba(239, 68, 68, 0.8)"]}]}'></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- User Activity Period Chart Row -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card chart-card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-clock"></i> 用户活跃时段
                </div>
            </div>
            <div class="card-body d-flex align-items-center justify-content-center">
                <div class="chart-container">
                    <canvas id="userActivityPeriodsChart" class="chart-canvas" data-chart-values='{"labels": ["上午", "下午", "晚上", "深夜"], "datasets": [{"label": "活跃人数", "data": [{{ active_users_today // 4 }}, {{ active_users_today // 2 }}, {{ active_users_today // 3 }}, {{ active_users_today // 6 }}], "backgroundColor": ["rgba(79, 70, 229, 0.6)", "rgba(59, 130, 246, 0.6)", "rgba(16, 185, 129, 0.6)", "rgba(245, 158, 11, 0.6)"]}]}'></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户活跃度分析卡片 -->
    <div class="col-lg-6 mb-4">
        <div class="card chart-card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-user-check"></i> 用户活跃度分析
                </div>
            </div>
            <div class="card-body user-activity-container" style="min-height: 300px; padding: 0;">
                <div class="chart-container" style="width: 100%; height: 100%; min-height: 280px; position: relative;">
                    <div class="current-day-indicator" style="display: none;">今日用户活跃</div>
                    <canvas id="userActivityChart" class="chart-canvas" style="width: 100%; height: 100%;" data-chart-values='{"labels": ["周一", "周二", "周三", "周四", "周五", "周六", "周日"], "datasets": [{"label": "活跃用户数", "data": [{{ active_users_today // 1.5 }}, {{ active_users_today }}, {{ active_users_today // 1.2 }}, {{ active_users_today // 1.8 }}, {{ active_users_today * 1.2 }}, {{ active_users_today * 1.5 }}, {{ active_users_today }}], "backgroundColor": "rgba(79, 70, 229, 0.2)", "borderColor": "#4f46e5", "borderWidth": 2, "tension": 0.4}]}'></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 数据详情区 -->
<div class="row">
    <!-- 最近用户活动 -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-users"></i> 最近用户活动
                </div>
                <a href="/admin/users" class="btn btn-sm btn-outline-primary">
                    查看全部
                </a>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover align-middle mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>用户</th>
                                <th>活动</th>
                                <th>时间</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in recent_records[:5] %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-placeholder me-2">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div>
                                            <div class="fw-medium">用户 {{ record.user_id }}</div>
                                            <div class="small text-muted">ID: {{ record.user_id }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="activity-label">
                                        {% if record.is_correct %}
                                        <i class="fas fa-check-circle text-success me-1"></i> 答对了一道题
                                        {% else %}
                                        <i class="fas fa-times-circle text-danger me-1"></i> 答错了一道题
                                        {% endif %}
                                    </span>
                                </td>
                                <td>
                                    <div class="small text-muted">{{ record.created_at.strftime('%m-%d %H:%M') if record.created_at else '-' }}</div>
                                </td>
                                <td>
                                    <span class="badge rounded-pill bg-{{ 'success' if record.is_correct else 'danger' }}">
                                        {{ '正确' if record.is_correct else '错误' }}
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                            
                            {% if not recent_records %}
                            <tr>
                                <td colspan="4" class="text-center py-4 text-muted">
                                    <i class="fas fa-info-circle me-1"></i> 暂无用户活动记录
                                </td>
                            </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 最近注册用户 -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-user-plus"></i> 最近注册用户
                </div>
                <a href="/admin/users" class="btn btn-sm btn-outline-primary">
                    查看全部
                </a>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover align-middle mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>用户</th>
                                <th>注册时间</th>
                                <th>答题数</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in recent_users %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% if user.avatar %}
                                        <img src="{{ user.avatar }}" alt="头像" class="rounded-circle me-2" width="40" height="40">
                                        {% else %}
                                        <div class="avatar-placeholder me-2">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        {% endif %}
                                        <div>
                                            <div class="fw-medium">{{ user.nickname or "用户" + user.id|string }}</div>
                                            <div class="small text-muted">ID: {{ user.id }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>{{ user.created_at.strftime('%Y-%m-%d') if user.created_at else '-' }}</div>
                                    <div class="small text-muted">{{ user.created_at.strftime('%H:%M:%S') if user.created_at else '' }}</div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="progress flex-grow-1" style="height: 6px; width: 80px;">
                                            <div class="progress-bar" role="progressbar" style="width: {{ user.answer_count }}%" aria-valuenow="{{ user.answer_count }}" aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                        <span class="ms-2">{{ user.answer_count }}</span>
                                    </div>
                                </td>
                                <td>
                                    <a href="/admin/users/{{ user.id }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                            
                            {% if not recent_users %}
                            <tr>
                                <td colspan="4" class="text-center py-4 text-muted">
                                    <i class="fas fa-info-circle me-1"></i> 暂无新注册用户
                                </td>
                            </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近注册用户与热门题目 -->
<div class="row">
    <!-- 热门题目 -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-fire"></i> 热门题目
                </div>
                <a href="/admin/questions" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-external-link-alt me-1"></i>查看全部
                </a>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>题目</th>
                                <th>类型</th>
                                <th>答题次数</th>
                                <th>平均正确率</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for question in popular_questions %}
                            <tr>
                                <td>
                                    <div class="fw-medium">{{ question.question }}</div>
                                    <div class="text-muted small">编号: Q-{{ question.id }}</div>
                                </td>
                                <td><span class="badge bg-{{ 'primary' if question.type == 'single' else 'info' }}">{{ '单选题' if question.type == 'single' else '多选题' }}</span></td>
                                <td>{{ question.count }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="progress flex-grow-1" style="height: 6px; width: 80px;">
                                            <div class="progress-bar bg-{{ 'success' if question.accuracy >= 60 else 'warning' if question.accuracy >= 40 else 'danger' }}" role="progressbar" style="width: {{ question.accuracy }}%" aria-valuenow="{{ question.accuracy }}" aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                        <span class="ms-2">{{ "%.0f"|format(question.accuracy) }}%</span>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 四个新添加的数据卡片 -->
<div class="row">
    <!-- 数据质量卡片 -->
    <div class="col-md-3 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between">
                <div><i class="fas fa-database"></i> 数据质量</div>
                <div class="dropdown">
                    <button class="btn btn-sm btn-link p-0 dropdown-toggle" type="button" id="dataQualityDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dataQualityDropdown">
                        <li><a class="dropdown-item" href="#"><i class="fas fa-eye me-2"></i>查看详情</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-file-export me-2"></i>导出报告</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="showQualityModal()"><i class="fas fa-chart-pie me-2"></i>质量分析</a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <h4 class="mb-3">{{ "%.1f"|format(data_quality_score) }}%</h4>
                <p class="text-muted">数据完整性评分</p>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span class="small">题目完整度</span>
                        <span class="small">{{ question_completeness }}%</span>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-success" role="progressbar" style="width: {{ question_completeness }}%" aria-valuenow="{{ question_completeness }}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span class="small">答案完整度</span>
                        <span class="small">{{ answer_completeness }}%</span>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-success" role="progressbar" style="width: {{ answer_completeness }}%" aria-valuenow="{{ answer_completeness }}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
                
                <div>
                    <div class="d-flex justify-content-between mb-1">
                        <span class="small">解析完整度</span>
                        <span class="small">{{ explanation_completeness }}%</span>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-warning" role="progressbar" style="width: {{ explanation_completeness }}%" aria-valuenow="{{ explanation_completeness }}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent">
                <button class="btn btn-sm btn-outline-primary w-100" onclick="showQualityModal()">
                    <i class="fas fa-search me-1"></i> 查看待完善题目
                </button>
            </div>
        </div>
    </div>
    
    <!-- 用户参与度卡片 -->
    <div class="col-md-3 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between">
                <div><i class="fas fa-users"></i> 用户参与度</div>
                <div class="dropdown">
                    <button class="btn btn-sm btn-link p-0 dropdown-toggle" type="button" id="engagementDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="engagementDropdown">
                        <li><a class="dropdown-item" href="#"><i class="fas fa-user-cog me-2"></i>管理用户</a></li>
                        <li><a class="dropdown-item" href="#" onclick="exportUserData()"><i class="fas fa-file-export me-2"></i>导出用户数据</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-chart-line me-2"></i>参与度分析</a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <h4 class="mb-3">{{ "%.1f"|format(monthly_active_percentage) }}%</h4>
                <p class="text-muted">月度活跃比例</p>
                
                <div class="engagement-chart">
                    <div class="d-flex text-center my-3">
                        <div class="flex-grow-1">
                            <h5>{{ "%.0f"|format(daily_quiz_avg) }}</h5>
                            <div class="small text-muted">日均答题</div>
                        </div>
                        <div class="flex-grow-1">
                            <h5>23分钟</h5>
                            <div class="small text-muted">平均停留</div>
                        </div>
                        <div class="flex-grow-1">
                            <h5>4.2</h5>
                            <div class="small text-muted">参与度评分</div>
                        </div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span class="small">注册用户活跃率</span>
                        <span class="small">{{ "%.0f"|format(monthly_active_percentage) }}%</span>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar" role="progressbar" style="width: {{ monthly_active_percentage }}%" aria-valuenow="{{ monthly_active_percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent">
                <button class="btn btn-sm btn-outline-primary w-100" onclick="showEngagementModal()">
                    <i class="fas fa-chart-bar me-1"></i> 查看详细数据
                </button>
            </div>
        </div>
    </div>
    
    <!-- 错误率分析卡片 -->
    <div class="col-md-3 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between">
                <div><i class="fas fa-exclamation-triangle"></i> 错误率分析</div>
                <div class="dropdown">
                    <button class="btn btn-sm btn-link p-0 dropdown-toggle" type="button" id="errorRateDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="errorRateDropdown">
                        <li><a class="dropdown-item" href="#"><i class="fas fa-eye me-2"></i>查看详情</a></li>
                        <li><a class="dropdown-item" href="#" onclick="showErrorModal()"><i class="fas fa-exclamation-circle me-2"></i>高错误率题目</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-file-export me-2"></i>导出报告</a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h4 class="mb-0">{{ "%.1f"|format(error_rate) }}%</h4>
                    <span class="badge bg-warning p-2">需关注</span>
                </div>
                <p class="text-muted">平均错误率</p>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span class="small">单选题错误率</span>
                        <span class="small">{{ "%.1f"|format(single_error_rate) }}%</span>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-success" role="progressbar" style="width: {{ single_error_rate }}%" aria-valuenow="{{ single_error_rate }}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span class="small">多选题错误率</span>
                        <span class="small">{{ "%.1f"|format(multiple_error_rate) }}%</span>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-danger" role="progressbar" style="width: {{ multiple_error_rate }}%" aria-valuenow="{{ multiple_error_rate }}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent">
                <button class="btn btn-sm btn-outline-danger w-100" onclick="showErrorModal()">
                    <i class="fas fa-search me-1"></i> 查看高错误率题目
                </button>
            </div>
        </div>
    </div>
    
    <!-- 系统优化建议卡片 -->
    <div class="col-md-3 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between">
                <div><i class="fas fa-lightbulb"></i> 系统优化建议</div>
                <div class="dropdown">
                    <button class="btn btn-sm btn-link p-0 dropdown-toggle" type="button" id="optimizationDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="optimizationDropdown">
                        <li><a class="dropdown-item" href="#"><i class="fas fa-check-circle me-2"></i>标记为已读</a></li>
                        <li><a class="dropdown-item" href="#" onclick="showOptimizationModal()"><i class="fas fa-list me-2"></i>查看所有建议</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-file-export me-2"></i>导出建议</a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="bg-primary text-white rounded p-2 me-3">
                        <i class="fas fa-tools"></i>
                    </div>
                    <div>
                        <h5 class="mb-0">5 项优化建议</h5>
                        <p class="small text-muted mb-0">2项高优先级</p>
                    </div>
                </div>
                
                <div class="list-group list-group-flush small">
                    <div class="list-group-item px-0 d-flex justify-content-between align-items-center border-0">
                        <div>
                            <span class="badge bg-danger me-2">高</span>
                            更新多选题难度校准
                        </div>
                        <i class="fas fa-chevron-right text-muted"></i>
                    </div>
                    <div class="list-group-item px-0 d-flex justify-content-between align-items-center border-0">
                        <div>
                            <span class="badge bg-danger me-2">高</span>
                            补充题目解析
                        </div>
                        <i class="fas fa-chevron-right text-muted"></i>
                    </div>
                    <div class="list-group-item px-0 d-flex justify-content-between align-items-center border-0">
                        <div>
                            <span class="badge bg-warning me-2">中</span>
                            优化题目分类体系
                        </div>
                        <i class="fas fa-chevron-right text-muted"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent">
                <button class="btn btn-sm btn-outline-primary w-100" onclick="showOptimizationModal()">
                    <i class="fas fa-lightbulb me-1"></i> 查看所有建议
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 数据筛选器下方的四个数据卡片 -->
<div class="row mt-4">
    <!-- 题目分类分析卡片 -->
    <div class="col-lg-3 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div><i class="fas fa-th-large"></i> 题目分类分析</div>
                <div class="dropdown">
                    <button class="btn btn-sm btn-link p-0" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#"><i class="fas fa-chart-pie me-2"></i>查看详情</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-download me-2"></i>导出数据</a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="card-title mb-0">分类占比</h5>
                    <span class="badge bg-primary">共{{ top_categories|length }}个分类</span>
                </div>
                
                {% for category, percentage in top_categories.items() %}
                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span class="small">{{ category }}</span>
                        <span class="small">{{ "%.0f"|format(percentage) }}%</span>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-primary" role="progressbar" style="width: {{ percentage }}%" aria-valuenow="{{ percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
                {% endfor %}
                
                <div class="text-center mt-3">
                    <button class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-plus me-1"></i> 管理分类
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 用户活跃度分析卡片 -->
    <div class="col-lg-3 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div><i class="fas fa-users"></i> 用户活跃度分析</div>
                <div class="dropdown">
                    <button class="btn btn-sm btn-link p-0" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#"><i class="fas fa-chart-line me-2"></i>查看趋势</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-file-export me-2"></i>导出报告</a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <div class="d-inline-block position-relative">
                        <div class="position-relative" style="width: 120px; height: 120px;">
                            <div class="position-absolute top-50 start-50 translate-middle">
                                <h3 class="mb-0">{{ "%.0f"|format(monthly_active_percentage) }}%</h3>
                                <div class="small text-muted">活跃率</div>
                            </div>
                            <svg width="120" height="120" viewBox="0 0 120 120">
                                <circle cx="60" cy="60" r="54" fill="none" stroke="#e9ecef" stroke-width="12" />
                                <circle cx="60" cy="60" r="54" fill="none" stroke="#4361ee" stroke-width="12" stroke-dasharray="339" stroke-dashoffset="{{ 339 - (339 * monthly_active_percentage / 100) }}" transform="rotate(-90 60 60)" />
                            </svg>
                        </div>
                    </div>
                </div>
                
                <div class="row text-center mb-3">
                    <div class="col-4">
                        <h5 class="mb-0 fw-bold">{{ active_users_today }}</h5>
                        <div class="small text-muted">今日活跃</div>
                    </div>
                    <div class="col-4">
                        <h5 class="mb-0 fw-bold">{{ (total_users * 0.42)|int }}</h5>
                        <div class="small text-muted">周活跃</div>
                    </div>
                    <div class="col-4">
                        <h5 class="mb-0 fw-bold">{{ (total_users * monthly_active_percentage / 100)|int }}</h5>
                        <div class="small text-muted">月活跃</div>
                    </div>
                </div>
                
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0">
                        <span class="badge rounded-pill bg-success p-2">
                            <i class="fas fa-arrow-up"></i>
                        </span>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="small">较上周提升 <span class="fw-bold text-success">12%</span></div>
                    </div>
                </div>
                
                <button class="btn btn-sm btn-outline-primary w-100 mt-2">
                    <i class="fas fa-chart-bar me-1"></i> 查看详细分析
                </button>
            </div>
        </div>
    </div>
    
    <!-- 题目难度分布卡片 -->
    <div class="col-lg-3 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div><i class="fas fa-signal"></i> 题目难度分布</div>
                <div class="dropdown">
                    <button class="btn btn-sm btn-link p-0" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#"><i class="fas fa-balance-scale me-2"></i>难度调整</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-file-alt me-2"></i>生成报告</a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="difficulty-chart d-flex align-items-end justify-content-center mx-auto" style="height: 130px; max-width: 200px;">
                        <div class="difficulty-bar mx-1" style="height: {{ easy_percentage }}%; width: 30px; background-color: #4CAF50; border-radius: 4px 4px 0 0;"></div>
                        <div class="difficulty-bar mx-1" style="height: {{ medium_percentage }}%; width: 30px; background-color: #FFC107; border-radius: 4px 4px 0 0;"></div>
                        <div class="difficulty-bar mx-1" style="height: {{ hard_percentage }}%; width: 30px; background-color: #F44336; border-radius: 4px 4px 0 0;"></div>
                    </div>
                    <div class="difficulty-labels d-flex justify-content-center mt-2">
                        <div class="mx-1" style="width: 30px;"><small>简单</small></div>
                        <div class="mx-1" style="width: 30px;"><small>中等</small></div>
                        <div class="mx-1" style="width: 30px;"><small>困难</small></div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <div class="d-flex justify-content-between mb-1">
                        <div><i class="fas fa-circle text-success me-1"></i> 简单</div>
                        <div>{{ (total_questions * easy_percentage / 100)|int }} 题 ({{ "%.0f"|format(easy_percentage) }}%)</div>
                    </div>
                    <div class="d-flex justify-content-between mb-1">
                        <div><i class="fas fa-circle text-warning me-1"></i> 中等</div>
                        <div>{{ (total_questions * medium_percentage / 100)|int }} 题 ({{ "%.0f"|format(medium_percentage) }}%)</div>
                    </div>
                    <div class="d-flex justify-content-between mb-3">
                        <div><i class="fas fa-circle text-danger me-1"></i> 困难</div>
                        <div>{{ (total_questions * hard_percentage / 100)|int }} 题 ({{ "%.0f"|format(hard_percentage) }}%)</div>
                    </div>
                </div>
                
                <div class="alert alert-info small py-2">
                    <i class="fas fa-info-circle me-1"></i> 建议增加困难题目的比例以提高题库的挑战性
                </div>
                
                <button class="btn btn-sm btn-outline-primary w-100 mt-2">
                    <i class="fas fa-cog me-1"></i> 调整难度设置
                </button>
            </div>
        </div>
    </div>
    
    <!-- 答题时间分析卡片 -->
    <div class="col-lg-3 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div><i class="fas fa-clock"></i> 答题时间分析</div>
                <div class="dropdown">
                    <button class="btn btn-sm btn-link p-0" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#"><i class="fas fa-search-plus me-2"></i>详细分析</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-file-download me-2"></i>导出数据</a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="card-title mb-0">平均答题时间</h5>
                    <span class="badge bg-info">{{ avg_time_per_question }}秒/题</span>
                </div>
                
                <div class="timeline-wrapper my-3">
                    <div class="timeline-item d-flex mb-2">
                        <div class="timeline-badge me-2 bg-success"></div>
                        <div class="timeline-content d-flex justify-content-between w-100">
                            <span>简单题</span>
                            <span class="fw-bold">{{ easy_avg_time }}秒</span>
                        </div>
                    </div>
                    <div class="timeline-item d-flex mb-2">
                        <div class="timeline-badge me-2 bg-warning"></div>
                        <div class="timeline-content d-flex justify-content-between w-100">
                            <span>中等题</span>
                            <span class="fw-bold">{{ medium_avg_time }}秒</span>
                        </div>
                    </div>
                    <div class="timeline-item d-flex mb-3">
                        <div class="timeline-badge me-2 bg-danger"></div>
                        <div class="timeline-content d-flex justify-content-between w-100">
                            <span>困难题</span>
                            <span class="fw-bold">{{ hard_avg_time }}秒</span>
                        </div>
                    </div>
                </div>
                
                <div class="small mb-3">
                    <div class="d-flex justify-content-between">
                        <div>单选题平均时间:</div>
                        <div class="fw-bold">{{ single_avg_time }}秒</div>
                    </div>
                    <div class="d-flex justify-content-between">
                        <div>多选题平均时间:</div>
                        <div class="fw-bold">{{ multiple_avg_time }}秒</div>
                    </div>
                </div>
                
                <div class="alert alert-success small py-2">
                    <i class="fas fa-check-circle me-1"></i> 答题时间在合理范围内，题目设计良好
                </div>
                
                <button class="btn btn-sm btn-outline-primary w-100 mt-2">
                    <i class="fas fa-chart-line me-1"></i> 查看时间分布图
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mt-4">
    <!-- User Answer Trends Chart -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow h-100">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">用户答题趋势</h6>
            </div>
            <div class="card-body d-flex align-items-center justify-content-center">
                <div class="chart-container">
                    <canvas id="userAnswerTrendsChart" data-chart-values='{"labels": ["{{ now.strftime('%Y-%m-%d') }}", "{{ (now - datetime.timedelta(days=1)).strftime('%Y-%m-%d') }}", "{{ (now - datetime.timedelta(days=2)).strftime('%Y-%m-%d') }}", "{{ (now - datetime.timedelta(days=3)).strftime('%Y-%m-%d') }}", "{{ (now - datetime.timedelta(days=4)).strftime('%Y-%m-%d') }}", "{{ (now - datetime.timedelta(days=5)).strftime('%Y-%m-%d') }}", "{{ (now - datetime.timedelta(days=6)).strftime('%Y-%m-%d') }}"], "datasets": [{"label": "答题数", "data": [{{ quizzes_today }}, {{ quizzes_today - 5 }}, {{ quizzes_today - 12 }}, {{ quizzes_today - 8 }}, {{ quizzes_today - 15 }}, {{ quizzes_today - 10 }}, {{ quizzes_today - 7 }}], "borderColor": "#4f46e5", "backgroundColor": "rgba(79, 70, 229, 0.2)", "fill": true, "tension": 0.4}]}'></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Question Type Distribution -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow h-100">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">题型分布</h6>
            </div>
            <div class="card-body d-flex align-items-center justify-content-center">
                <div class="chart-container">
                    <canvas id="questionTypeChart" data-chart-values='{"labels": ["单选题", "多选题", "判断题", "填空题"], "datasets": [{"data": [{{ single_choice_count }}, {{ multiple_choice_count }}, {{ judgment_questions|default(0) }}, {{ fill_blank_questions|default(0) }}], "backgroundColor": ["rgba(79, 70, 229, 0.8)", "rgba(245, 158, 11, 0.8)", "rgba(16, 185, 129, 0.8)", "rgba(239, 68, 68, 0.8)"]}]}'></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- User Activity Period Chart -->
<div class="row mt-4">
    <div class="col-lg-6 mb-4">
        <div class="card shadow h-100">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">用户活跃时段</h6>
            </div>
            <div class="card-body d-flex align-items-center justify-content-center">
                <div class="chart-container">
                    <canvas id="userActivityPeriodsChart" class="chart-canvas" data-chart-values='{"labels": ["上午", "下午", "晚上", "深夜"], "datasets": [{"label": "活跃人数", "data": [{{ active_users_today // 4 }}, {{ active_users_today // 2 }}, {{ active_users_today // 3 }}, {{ active_users_today // 6 }}], "backgroundColor": ["rgba(79, 70, 229, 0.6)", "rgba(59, 130, 246, 0.6)", "rgba(16, 185, 129, 0.6)", "rgba(245, 158, 11, 0.6)"]}]}'></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据分析卡片 -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow h-100">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">用户活跃度分析</h6>
            </div>
            <div class="card-body d-flex align-items-center justify-content-center">
                <div class="chart-container">
                    <canvas id="userActivityChart" class="chart-canvas" data-chart-values='{"labels": ["周一", "周二", "周三", "周四", "周五", "周六", "周日"], "datasets": [{"label": "活跃用户数", "data": [{{ active_users_today // 1.5 }}, {{ active_users_today }}, {{ active_users_today // 1.2 }}, {{ active_users_today // 1.8 }}, {{ active_users_today * 1.2 }}, {{ active_users_today * 1.5 }}, {{ active_users_today }}], "backgroundColor": "rgba(79, 70, 229, 0.2)", "borderColor": "#4f46e5", "borderWidth": 2, "tension": 0.4}]}'></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-6">
        <div class="card shadow-sm" style="height: 350px;">
            <div class="card-header">
                <h5 class="card-title mb-0">难度分布</h5>
            </div>
            <div class="card-body d-flex align-items-center justify-content-center">
                <div class="donut-chart-container" style="width: 100%; height: 280px;">
                    <canvas id="difficultyChart" style="width: 100%; height: 100%;" data-chart-values='{"labels": ["简单", "中等", "困难"], "datasets": [{"data": [{{ easy_percentage }}, {{ medium_percentage }}, {{ hard_percentage }}], "backgroundColor": ["rgba(16, 185, 129, 0.8)", "rgba(245, 158, 11, 0.8)", "rgba(239, 68, 68, 0.8)"]}]}'></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card shadow-sm" style="height: 350px;">
            <div class="card-header">
                <h5 class="card-title mb-0">用户活跃时段分布</h5>
            </div>
            <div class="card-body d-flex align-items-center justify-content-center">
                <div class="chart-container" style="width: 100%; height: 280px;">
                    <canvas id="activeTimeChart" style="width: 100%; height: 100%;" data-chart-values='{"labels": ["上午", "下午", "晚上", "深夜"], "datasets": [{"label": "活跃人数", "data": [{{ active_users_today // 4 }}, {{ active_users_today // 2 }}, {{ active_users_today // 3 }}, {{ active_users_today // 6 }}], "backgroundColor": ["rgba(79, 70, 229, 0.6)", "rgba(59, 130, 246, 0.6)", "rgba(16, 185, 129, 0.6)", "rgba(245, 158, 11, 0.6)"]}]}'></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<!-- 引入 Chart.js CDN -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js" integrity="sha512-ElRFoEQdI5Ht6kZvyzXhYG9NqjtkmlkfYk0wr6wHxU9JEHakS7UJZNeml5ALk+8IKlU6jDgMabC3vkumRokgJA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

<!-- 引入 Chart.js DataLabels 插件 -->
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>
<script>
  // 全局定义 ChartDataLabels 变量供所有图表使用
  window.ChartDataLabels = ChartDataLabels;
</script>

<!-- 引入交互效果增强 -->
<script src="{{ url_for('static', filename='js/dashboard-interactions.js') }}"></script>

<!-- 引入数字动画效果 -->
<script src="{{ url_for('static', filename='js/number-animation.js') }}"></script>

<!-- 图表响应式和动画增强 -->
<script>
  // 确保图表响应窗口大小变化
  function resizeCharts() {
    const chartContainers = document.querySelectorAll('.chart-container');
    chartContainers.forEach(container => {
      const canvas = container.querySelector('canvas');
      if (canvas && canvas.chart) {
        canvas.chart.resize();
      }
    });
  }
  
  // 监听窗口大小变化
  window.addEventListener('resize', function() {
    // 使用节流函数避免频繁调用
    if (this.resizeTimeout) clearTimeout(this.resizeTimeout);
    this.resizeTimeout = setTimeout(function() {
      resizeCharts();
    }, 200);
  });
  
  // 给图表添加交互效果和动画
  document.addEventListener('DOMContentLoaded', function() {
    // 获取所有图表元素
    const chartElements = document.querySelectorAll('.chart-container canvas');
    
    // 为每个图表添加鼠标悬停效果
    chartElements.forEach(canvas => {
      canvas.addEventListener('mousemove', function(event) {
        const container = this.parentElement;
        const rect = container.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        
        // 添加光晕效果
        container.style.background = `radial-gradient(circle at ${x}px ${y}px, rgba(248, 250, 252, 0.9) 0%, rgba(241, 245, 249, 0.4) 70%)`;
      });
      
      // 鼠标移出时恢复原样
      canvas.addEventListener('mouseleave', function() {
        const container = this.parentElement;
        container.style.background = 'linear-gradient(to bottom, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.3) 100%)';
      });
    });
  });
  
  // 添加图表提示信息格式化功能
  function formatChartTooltip(value, label, type) {
    if (type === 'percentage') {
      return label + ': ' + value.toFixed(1) + '%';
    } else if (type === 'count') {
      return label + ': ' + value + ' 个';
    } else {
      return label + ': ' + value;
    }
  }
</script>

<!-- 直接初始化所有图表的内联脚本 -->
<script>
// 在DOMContentLoaded和window.load事件都添加图表初始化
document.addEventListener('DOMContentLoaded', initAllCharts);
window.addEventListener('load', function() {
  // 在load事件后延迟再次尝试初始化图表
  setTimeout(initAllCharts, 500);
  setTimeout(initAllCharts, 1500);
});

function initAllCharts() {
  console.log('尝试初始化所有图表...');
  
  // 定义要初始化的图表ID和类型
  const charts = [
    { id: 'questionTypeChart', type: 'pie' },
    { id: 'userActivityPeriodsChart', type: 'bar' },
    { id: 'userActivityChart', type: 'line' },
    { id: 'activityChart', type: 'line' },
    { id: 'difficultyChart', type: 'doughnut' },
    { id: 'activeTimeChart', type: 'polarArea' },
    { id: 'userAnswerTrendsChart', type: 'line' }
  ];
  
  // 遍历所有图表进行初始化
  charts.forEach(function(chartInfo) {
    initChart(chartInfo.id, chartInfo.type);
  });
  
  // 查找页面上其他任何带有"Chart"结尾ID的canvas
  document.querySelectorAll('canvas[id$="Chart"]').forEach(function(canvas) {
    if (!charts.some(c => c.id === canvas.id)) {
      console.log('发现额外图表:', canvas.id);
      initChart(canvas.id);
    }
  });
}

function initChart(chartId, defaultType) {
  const canvas = document.getElementById(chartId);
  if (!canvas) {
    console.warn('未找到图表元素:', chartId);
    return;
  }
  
  // 尝试清除已存在的图表实例
  try {
    if (typeof Chart !== 'undefined' && typeof Chart.getChart === 'function') {
      const existingChart = Chart.getChart(canvas);
      if (existingChart) {
        existingChart.destroy();
      }
    }
  } catch (e) {
    console.warn('清除图表实例时出错:', e);
  }
  
  // 解析图表数据
  let chartData;
  try {
    if (canvas.dataset.chartValues) {
      chartData = JSON.parse(canvas.dataset.chartValues);
    }
  } catch (e) {
    console.warn(`解析图表 ${chartId} 数据时出错:`, e);
  }
  
  // 如果未能解析数据，使用默认数据
  if (!chartData) {
    chartData = getDefaultData(chartId, defaultType);
  }
  
  // 确定图表类型
  let chartType = defaultType || 'bar';
  if (!defaultType) {
    if (chartId.toLowerCase().includes('pie') || chartId.toLowerCase().includes('type')) {
      chartType = 'pie';
    } else if (chartId.toLowerCase().includes('doughnut') || chartId.toLowerCase().includes('difficulty')) {
      chartType = 'doughnut'; 
    } else if (chartId.toLowerCase().includes('activity') || chartId.toLowerCase().includes('trend')) {
      chartType = 'line';
    }
  }
  
  // 获取适合该类型的选项
  const chartOptions = getChartOptions(chartType);
  
  // 为线形图创建渐变背景
  if (chartType === 'line' && chartData.datasets && chartData.datasets[0]) {
    const ctx = canvas.getContext('2d');
    const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
    gradient.addColorStop(0, 'rgba(79, 70, 229, 0.4)');
    gradient.addColorStop(1, 'rgba(79, 70, 229, 0.0)');
    
    // 应用渐变
    chartData.datasets[0].backgroundColor = gradient;
  }
  
  // 创建图表
  try {
    console.log(`创建图表 ${chartId}，类型: ${chartType}`);
    new Chart(canvas, {
      type: chartType,
      data: chartData,
      options: chartOptions
    });
    
    console.log(`图表 ${chartId} 初始化成功`);
  } catch (error) {
    console.error(`创建图表 ${chartId} 失败:`, error);
  }
}

// 获取图表默认选项
function getChartOptions(chartType) {
  // 基本选项
  const baseOptions = {
    responsive: true,
    maintainAspectRatio: false,
    animation: {
      duration: 1000
    },
    plugins: {
      legend: {
        display: chartType === 'pie' || chartType === 'doughnut' || chartType === 'polarArea',
        position: 'bottom',
        labels: {
          usePointStyle: true,
          padding: 15,
          boxWidth: 10
        }
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleFont: {
          size: 14
        },
        bodyFont: {
          size: 13
        }
      }
    }
  };
  
  // 为不同类型的图表添加特定选项
  if (chartType === 'line') {
    return {
      ...baseOptions,
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            padding: 10
          }
        },
        x: {
          grid: {
            display: false
          }
        }
      },
      elements: {
        point: {
          radius: 4,
          hoverRadius: 6,
          backgroundColor: '#ffffff'
        },
        line: {
          tension: 0.4
        }
      }
    };
  } else if (chartType === 'bar') {
    return {
      ...baseOptions,
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            padding: 10
          }
        },
        x: {
          grid: {
            display: false
          }
        }
      }
    };
  } else if (chartType === 'doughnut') {
    return {
      ...baseOptions,
      cutout: '65%',
      plugins: {
        ...baseOptions.plugins,
        tooltip: {
          ...baseOptions.plugins.tooltip,
          callbacks: {
            label: function(context) {
              const label = context.label || '';
              const value = context.raw;
              const total = context.dataset.data.reduce((a, b) => a + b, 0);
              const percentage = Math.round((value / total) * 100);
              return `${label}: ${percentage}%`;
            }
          }
        }
      },
      elements: {
        arc: {
          borderWidth: 0,
          borderRadius: 4
        }
      }
    };
  } else if (chartType === 'pie') {
    return {
      ...baseOptions,
      plugins: {
        ...baseOptions.plugins,
        tooltip: {
          ...baseOptions.plugins.tooltip,
          callbacks: {
            label: function(context) {
              const label = context.label || '';
              const value = context.raw;
              const total = context.dataset.data.reduce((a, b) => a + b, 0);
              const percentage = Math.round((value / total) * 100);
              return `${label}: ${percentage}%`;
            }
          }
        }
      }
    };
  } else if (chartType === 'polarArea') {
    return {
      ...baseOptions,
      scales: {
        r: {
          ticks: {
            backdropColor: 'transparent',
            z: 100
          }
        }
      }
    };
  }
  
  return baseOptions;
}

// 获取默认图表数据
function getDefaultData(chartId, chartType) {
  const id = chartId.toLowerCase();
  
  // 题型分布图表
  if (id === 'questiontypechart') {
    return {
      labels: ['单选题', '多选题'],
      datasets: [{
        data: [60, 40],
        backgroundColor: [
          'rgba(79, 70, 229, 0.8)',
          'rgba(245, 158, 11, 0.8)'
        ]
      }]
    };
  } 
  // 用户活跃时段图表
  else if (id === 'useractivityperiodschart') {
    return {
      labels: ['上午', '下午', '晚上', '深夜'],
      datasets: [{
        label: '活跃人数',
        data: [15, 30, 20, 10],
        backgroundColor: [
          'rgba(79, 70, 229, 0.6)',
          'rgba(59, 130, 246, 0.6)',
          'rgba(16, 185, 129, 0.6)',
          'rgba(245, 158, 11, 0.6)'
        ]
      }]
    };
  } 
  // 用户活跃度分析图表
  else if (id === 'useractivitychart') {
    return {
      labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
      datasets: [{
        label: '活跃用户数',
        data: [15, 25, 20, 12, 30, 35, 25],
        backgroundColor: 'rgba(79, 70, 229, 0.2)',
        borderColor: '#4f46e5',
        borderWidth: 2,
        tension: 0.4
      }]
    };
  } 
  // 答题数据趋势图表
  else if (id === 'activitychart') {
    return {
      labels: ['一月', '二月', '三月', '四月', '五月', '六月', '七月'],
      datasets: [{
        label: '答题数',
        data: [45, 60, 55, 50, 65, 80, 70],
        backgroundColor: 'rgba(79, 70, 229, 0.2)',
        borderColor: '#4f46e5',
        borderWidth: 2,
        fill: true,
        tension: 0.4
      }]
    };
  } 
  // 难度分布图表
  else if (id === 'difficultychart') {
    return {
      labels: ['简单', '中等', '困难'],
      datasets: [{
        data: [40, 35, 25],
        backgroundColor: [
          'rgba(16, 185, 129, 0.8)',
          'rgba(245, 158, 11, 0.8)',
          'rgba(239, 68, 68, 0.8)'
        ]
      }]
    };
  } 
  // 活跃时段分布图表
  else if (id === 'activetimechart') {
    return {
      labels: ['上午', '下午', '晚上', '深夜'],
      datasets: [{
        label: '活跃人数',
        data: [15, 30, 20, 10],
        backgroundColor: [
          'rgba(79, 70, 229, 0.6)',
          'rgba(59, 130, 246, 0.6)',
          'rgba(16, 185, 129, 0.6)',
          'rgba(245, 158, 11, 0.6)'
        ]
      }]
    };
  } 
  // 用户答题趋势图表
  else if (id === 'useranswertrendschart') {
    return {
      labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
      datasets: [{
        label: '答题数',
        data: [45, 60, 55, 50, 65, 80, 70],
        backgroundColor: 'rgba(79, 70, 229, 0.2)',
        borderColor: '#4f46e5',
        borderWidth: 2,
        tension: 0.4,
        fill: true
      }]
    };
  } 
  // 通用默认数据
  else {
    return {
      labels: ['类别1', '类别2', '类别3', '类别4', '类别5'],
      datasets: [{
        label: '数据系列',
        data: [25, 30, 20, 15, 10],
        backgroundColor: [
          'rgba(79, 70, 229, 0.6)',
          'rgba(59, 130, 246, 0.6)',
          'rgba(16, 185, 129, 0.6)',
          'rgba(245, 158, 11, 0.6)',
          'rgba(239, 68, 68, 0.6)'
        ]
      }]
    };
  }
}
</script>

<!-- 最后加载charts-direct-fix.js作为备用初始化方法 -->
<script src="{{ url_for('static', filename='js/charts-direct-fix.js') }}"></script>

<!-- 添加图表错误清理工具 -->
<script src="{{ url_for('static', filename='js/chart-error-cleaner.js') }}"></script>

<!-- 更新默认图表选项，使图表更小巧，字体更小 -->
<script>
    const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top',
                labels: {
                    boxWidth: 12,
                    font: {
                        size: 11
                    }
                }
            },
            title: {
                display: false
            },
            tooltip: {
                bodyFont: {
                    size: 11
                },
                titleFont: {
                    size: 12
                }
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    font: {
                        size: 10
                    }
                }
            },
            x: {
                ticks: {
                    font: {
                        size: 10
                    }
                }
            }
        }
    };
    
    // 对Chart.js的全局默认设置进行修改
    if (typeof Chart !== 'undefined') {
        Chart.defaults.font.size = 11;
        Chart.defaults.responsive = true;
        Chart.defaults.maintainAspectRatio = false;
    }
</script>
{% endblock %}
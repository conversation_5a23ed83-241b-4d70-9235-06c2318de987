const app = getApp();

// requestAnimationFrame polyfill for mini programs
const requestAnimationFrame = (callback) => {
  const systemInfo = wx.getSystemInfoSync();
  const fps = systemInfo.benchmarkLevel >= 2 ? 60 : 30;
  return setTimeout(callback, 1000 / fps);
};

Page({
  data: {
    courseId: null,
    courseName: '',
    courseColor: '#4e8df7',
    courseColorDarker: '#3c78e0',
    allQuestions: {
      single: [],
      multiple: [],
      judgment: [],
      fillblank: []
    }, // 所有题目
    isLoading: true,
    statusBarHeight: 0,
    currentTab: 0, // 当前标签索引，0:单选，1:多选，2:判断，3:填空
    switchTabTimer: null,
    thumbTopPx: 0, // 滑块位置
    sliderHeightPx: 0, // 滑块容器高度
    thumbHeightPx: 0, // 滑块高度
    maxThumbTopPx: 0, // 滑块可移动最大距离
    scrollTop: 0, // 滚动位置（用于数据绑定备用）
    isDragging: false, // 添加一个状态来跟踪拖拽状态
    _lastUpdateTime: null,
    // 添加进度缓存相关数据
    scrollPositions: {
      single: 0,
      multiple: 0,
      judgment: 0,
      fillblank: 0
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    // 获取传递的课程ID和名称
    const courseId = options.id;
    const courseName = options.name || '课程';
    
    // 获取存储的课程信息，包括颜色
    const selectedCourse = wx.getStorageSync('selectedCourse') || {};
    const courseColor = selectedCourse.color || '#4e8df7';
    
    this.setData({
      courseId: courseId,
      courseName: decodeURIComponent(courseName),
      courseColor: courseColor,
      isLoading: true
    });
    
    // 设置导航栏标题和样式
    wx.setNavigationBarTitle({
      title: `${decodeURIComponent(courseName)} - 背题模式`
    });
    
    // 设置状态栏样式为白色文字
    wx.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: courseColor
    });
    
    // 加载题目数据
    this.loadAllQuestions();
    
    // 加载上次的浏览进度
    this.loadScrollPositions();
  },
  
  /**
   * 加载上次的浏览进度
   */
  loadScrollPositions: function() {
    try {
      const courseId = this.data.courseId;
      if (!courseId) return;
      
      // 构建唯一的缓存键，包含课程ID
      const cacheKey = `quiz_review_progress_${courseId}`;
      const savedPositions = wx.getStorageSync(cacheKey);
      
      if (savedPositions) {
        this.setData({
          scrollPositions: savedPositions
        });
        
        // 等待题目加载完成后，滚动到上次的位置
        this.pendingScrollRestore = true;
      }
    } catch (error) {
      console.error('加载进度缓存失败:', error);
    }
  },
  
  /**
   * 保存当前浏览进度
   */
  saveScrollPosition: function() {
    try {
      const courseId = this.data.courseId;
      if (!courseId) return;
      
      // 获取当前标签页的滚动位置
      wx.createSelectorQuery()
        .select('#questionScrollView')
        .scrollOffset(res => {
          if (res && res.scrollTop !== undefined) {
            // 更新当前标签对应的滚动位置
            const tabNames = ['single', 'multiple', 'judgment', 'fillblank'];
            const currentTabName = tabNames[this.data.currentTab];
            
            const newScrollPositions = { ...this.data.scrollPositions };
            newScrollPositions[currentTabName] = res.scrollTop;
            
            // 更新数据
            this.setData({
              scrollPositions: newScrollPositions
            });
            
            // 保存到本地存储
            const cacheKey = `quiz_review_progress_${courseId}`;
            wx.setStorageSync(cacheKey, newScrollPositions);
          }
        })
        .exec();
    } catch (error) {
      console.error('保存进度缓存失败:', error);
    }
  },
  
  /**
   * 页面显示时刷新数据（用户从其他页面回来）
   */
  onShow: function() {
    // 页面显示时，尝试恢复滚动位置
    if (this.pendingScrollRestore && !this.data.isLoading) {
      this.restoreScrollPosition();
    }
  },
  
  /**
   * 页面隐藏时释放资源（用户离开页面）
   */
  onHide: function() {
    // 释放不需要的资源，减少内存占用
    if (this.switchTabTimer) {
      clearTimeout(this.switchTabTimer);
    }
    
    // 保存当前的浏览进度
    this.saveScrollPosition();
  },
  
  /**
   * 页面卸载时清理
   */
  onUnload: function() {
    // 清理定时器和大型数据
    if (this.switchTabTimer) {
      clearTimeout(this.switchTabTimer);
    }
    
    // 保存当前的浏览进度
    this.saveScrollPosition();
  },
  
  // 生成颜色的深色版本
  getDarkerColor: function(hexColor) {
    // 如果没有提供颜色或格式不正确，返回默认深色
    if (!hexColor || hexColor.length !== 7 || hexColor[0] !== '#') {
      return '#3c78e0';
    }
    
    // 将hex转换为RGB
    let r = parseInt(hexColor.substring(1, 3), 16);
    let g = parseInt(hexColor.substring(3, 5), 16);
    let b = parseInt(hexColor.substring(5, 7), 16);
    
    // 将每个颜色通道减暗约20%
    r = Math.max(0, Math.floor(r * 0.8));
    g = Math.max(0, Math.floor(g * 0.8));
    b = Math.max(0, Math.floor(b * 0.8));
    
    // 转回hex格式
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  },
  
  // 加载所有题目
  loadAllQuestions: function() {
    this.setData({ isLoading: true });
    
    // 从缓存中获取所有题目数据
    const singleQuestions = wx.getStorageSync('currentSingleQuestions') || [];
    const multipleQuestions = wx.getStorageSync('currentMultipleQuestions') || [];
    const judgmentQuestions = wx.getStorageSync('currentJudgmentQuestions') || [];
    const fillBlankQuestions = wx.getStorageSync('currentFillBlankQuestions') || [];
    
    // 为每种题目添加类型标识，并加上correctText
    const formattedSingle = singleQuestions.map(q => {
      let idx = q.answer;
      if (idx === undefined && q.correct_answer !== undefined) {
        idx = q.correct_answer;
      }
      if (typeof idx === 'string' && !isNaN(Number(idx))) {
        idx = Number(idx);
      }
      let correctLetter = '';
      if (typeof idx === 'number' && idx >= 0 && idx < 26) {
        correctLetter = String.fromCharCode(65 + idx); // 65是A
      }
      let correctText = '无答案';
      if (typeof idx === 'number' && Array.isArray(q.options) && q.options[idx]) {
        correctText = q.options[idx].replace(/^[A-Z][\.、\s]+/, '');
      }
      return {
        ...q,
        questionType: 'single',
        correctLetter,
        correctText,
        answer: idx // 确保answer是数字
      };
    });
    
    // 处理多选题的答案
    const formattedMultiple = multipleQuestions.map(q => {
      // 提取题目文本
      const question = q.question || '';
      
      // 提取选项数组
      const options = q.options || [];
      
      // 获取正确答案索引 - 可能是数组、字符串或者是nested在correct_answers里
      let correctIndexes = [];
      
      if (q.correct_answers && Array.isArray(q.correct_answers)) {
        // 从correct_answers获取答案
        correctIndexes = q.correct_answers.map(ans => {
          // 如果答案是数字字符串，转换为数字
          if (typeof ans === 'string' && !isNaN(Number(ans))) {
            return Number(ans);
          }
          return ans;
        });
      } else if (q.answer !== undefined) {
        // 从answer字段获取答案
        if (Array.isArray(q.answer)) {
          correctIndexes = q.answer;
        } else {
          // 如果answer不是数组，则转换为数组
          correctIndexes = [q.answer];
        }
      }
      
      // 过滤任何非有效索引值
      correctIndexes = correctIndexes.filter(idx => 
        typeof idx === 'number' && !isNaN(idx) && idx >= 0 && idx < options.length
      );
      
      // 生成正确答案的字母表示，如"A, B, C"
      let correctLetters = correctIndexes.map(idx => 
        String.fromCharCode(65 + idx)
      ).join('');  // 移除了分隔符，直接拼接字母
      
      // 生成正确答案的文本表示
      let correctText = correctIndexes.map(idx => {
        if (options[idx]) {
          return options[idx].replace(/^[A-Z][\.、\s]+/, '');
        }
        return '';
      }).filter(Boolean).join('; ');
      
      if (!correctLetters) correctLetters = '无答案';
      if (!correctText) correctText = '无答案';
      
      // 处理选项，添加是否正确的标记
      const processedOptions = options.map((option, index) => {
        return {
          letter: String.fromCharCode(65 + index),
          text: option,
          isCorrect: correctIndexes.includes(index)
        };
      });
      
      return {
        ...q,
        questionType: 'multiple',
        question,
        processedOptions,
        correctIndexes,
        correctLetters,
        correctText
      };
    });
    
    const formattedJudgment = judgmentQuestions.map(q => {
      let ans = q.answer;
      if (ans === undefined && q.correct_answer !== undefined) ans = q.correct_answer;
      let correctText = '';
      
      // 处理各种可能的判断题答案格式
      if (ans === true || ans === 1 || ans === '1' || ans === 'true' || ans === '正确' || ans === '√') {
        correctText = '正确';
      } else if (ans === false || ans === 0 || ans === '0' || ans === 'false' || ans === '错误' || ans === '×') {
        correctText = '错误';
      } else {
        // 如果是数字，1为正确，0为错误
        const numAns = Number(ans);
        if (!isNaN(numAns)) {
          correctText = numAns === 1 ? '正确' : '错误';
        } else {
          correctText = String(ans);
        }
      }
      
      return { ...q, questionType: 'judgment', correctText };
    });
    
    const formattedFillBlank = fillBlankQuestions.map(q => {
      let ans = q.answer;
      if (ans === undefined && q.correct_answer !== undefined) ans = q.correct_answer;
      
      // 处理各种可能的填空题答案格式
      if (typeof ans === 'string') {
        // 如果是JSON字符串，尝试解析
        if (ans.startsWith('[') || ans.startsWith('{')) {
          try { ans = JSON.parse(ans); } catch (e) { /* 解析失败，保持原样 */ }
        }
      }
      
      // 如果是数组，拼接成字符串
      let correctText = '';
      if (Array.isArray(ans)) {
        correctText = ans.join('，');
      } else if (ans !== undefined && ans !== null) {
        correctText = String(ans);
      } else {
        correctText = '无答案';
      }
      
      return { ...q, questionType: 'fillblank', correctText };
    });
    
    // 彻底强制刷新视图，分批次更新数据以提高性能
    wx.showLoading({
      title: '加载中...'
    });
    
    // 先更新计数，使标签页数字正确显示
    this.setData({
      isLoading: true,
      'allQuestions.single.length': formattedSingle.length,
      'allQuestions.multiple.length': formattedMultiple.length,
      'allQuestions.judgment.length': formattedJudgment.length,
      'allQuestions.fillblank.length': formattedFillBlank.length
    }, () => {
      // 延迟一小段时间清空旧数据
      setTimeout(() => {
        this.setData({
          'allQuestions.single': [],
          'allQuestions.multiple': [],
          'allQuestions.judgment': [],
          'allQuestions.fillblank': []
        }, () => {
          // 分批次设置数据，避免一次性渲染太多导致卡顿
          setTimeout(() => {
            // 只加载当前标签页的数据，其他标签页的数据延迟加载
            const currentTabDataName = ['single', 'multiple', 'judgment', 'fillblank'][this.data.currentTab];
            const currentTabData = [formattedSingle, formattedMultiple, formattedJudgment, formattedFillBlank][this.data.currentTab];
            
            const updateData = {};
            updateData[`allQuestions.${currentTabDataName}`] = currentTabData;
            
            // 先更新当前标签页数据
            this.setData(updateData, () => {
              // 加载完当前标签页后再加载其他标签页，避免一次性加载过多数据
              setTimeout(() => {
                this.setData({
                  'allQuestions.single': this.data.currentTab !== 0 ? formattedSingle : this.data.allQuestions.single,
                  'allQuestions.multiple': this.data.currentTab !== 1 ? formattedMultiple : this.data.allQuestions.multiple,
                  'allQuestions.judgment': this.data.currentTab !== 2 ? formattedJudgment : this.data.allQuestions.judgment,
                  'allQuestions.fillblank': this.data.currentTab !== 3 ? formattedFillBlank : this.data.allQuestions.fillblank,
                  isLoading: false
                });
                wx.hideLoading();
                
                // 数据加载完成，恢复滚动位置
                if (this.pendingScrollRestore) {
                  this.restoreScrollPosition();
                }
              }, 100);
            });
          }, 50);
        });
      }, 50);
    });
  },
  
  // 切换题型标签
  switchTab: function(e) {
    const index = Number(e.currentTarget.dataset.index);
    if (this.data.currentTab === index) return; // 避免重复切换
    
    // 保存当前标签的滚动位置
    this.saveScrollPosition();
    
    // 使用节流技术，阻止连续快速的切换
    if (this.switchTabTimer) {
      clearTimeout(this.switchTabTimer);
    }
    
    // 先只更新当前active tab的索引，让用户看到tab切换了
    this.setData({
      currentTab: index
    });
    
    // 延迟一点点时间再处理数据，优先确保UI响应
    this.switchTabTimer = setTimeout(() => {
      // 预加载当前标签页的数据
      const tabNames = ['single', 'multiple', 'judgment', 'fillblank'];
      const currentTabName = tabNames[index];
      
      // 如果该标签页的数据为空且不是正在加载中，则加载该标签页的数据
      if (this.data.allQuestions[currentTabName].length === 0 && !this.data.isLoading) {
        // 重新加载或触发缓存加载
        this.ensureTabDataLoaded(index);
      }
      
      // 恢复该标签页的滚动位置
      const scrollTop = this.data.scrollPositions[currentTabName] || 0;
      this.setData({
        scrollTop: scrollTop
      });
    }, 50);
  },
  
  // 确保当前标签页的数据已加载
  ensureTabDataLoaded: function(tabIndex) {
    // 数据应该在loadAllQuestions中已经准备好，如果某个tab的数据为空，可以在这里特殊处理
    const tabNames = ['single', 'multiple', 'judgment', 'fillblank'];
    const currentTabName = tabNames[tabIndex];
    
    // 如果当前标签页的数据为空，尝试从缓存重新加载
    if (this.data.allQuestions[currentTabName].length === 0) {
      // 这里可以添加针对特定标签页的数据加载逻辑
      // 例如，如果多选题的数据为空，可以单独重新处理多选题
      if (tabIndex === 1) { // 多选题
        const multipleQuestions = wx.getStorageSync('currentMultipleQuestions') || [];
        if (multipleQuestions.length > 0) {
          // 处理多选题数据...
          // 这里可以复用loadAllQuestions中的逻辑
        }
      }
      // 可以为其他类型添加类似处理
    }
  },
  
  // 返回上一页
  navigateBack: function() {
    wx.navigateBack();
  },
  
  // 获取填空题正确答案文本
  getFillBlankAnswer: function(question) {
    if (!question || !question.answer) {
      return '无答案';
    }
    
    try {
      // 尝试将答案解析为JSON（如果是JSON字符串）
      let answer = question.answer;
      if (typeof answer === 'string' && (answer.startsWith('[') || answer.startsWith('{'))) {
        try {
          answer = JSON.parse(answer);
        } catch (e) {
          // 如果解析失败，保持原样
        }
      }
      
      // 如果答案是数组，将其转换为字符串
      if (Array.isArray(answer)) {
        return answer.join('，');
      }
      
      return String(answer);
    } catch (e) {
      console.error('解析填空题答案出错:', e);
      return '答案格式错误';
    }
  },
  
  // 获取多选题正确答案文本
  getMultipleAnswer: function(question) {
    if (!question || !question.answer) {
      return '无答案';
    }
    
    try {
      // 解析多选题答案
      let answer = question.answer;
      if (typeof answer === 'string') {
        try {
          // 尝试解析字符串为 JSON
          answer = JSON.parse(answer);
        } catch (e) {
          // 可能是逗号分隔的字符串
          if (answer.includes(',')) {
            answer = answer.split(',').map(a => parseInt(a.trim())).filter(a => !isNaN(a));
          } else {
            // 可能是单个数字
            answer = [parseInt(answer)];
          }
        }
      }
      
      // 如果答案是数组，格式化为选项文本
      if (Array.isArray(answer) && question.options) {
        return answer.map(a => {
          const optionIndex = typeof a === 'number' ? a : parseInt(a);
          if (!isNaN(optionIndex) && question.options[optionIndex]) {
            // 获取选项文本，去除可能的A、B、C、D前缀
            const optionText = question.options[optionIndex].replace(/^[A-Z][\.、\s]+/, '');
            return optionText;
          }
          return `选项${a}`;
        }).join('，');
      }
      
      return String(answer);
    } catch (e) {
      console.error('解析多选题答案出错:', e);
      return '答案格式错误';
    }
  },
  
  // 获取单选题正确答案文本
  getSingleAnswer: function(question) {
    if (!question) return '无答案';
    // 优先用 answer 字段，没有就用 correct_answer
    let idx = question.answer;
    if (idx === undefined && question.correct_answer !== undefined) {
      idx = question.correct_answer;
    }
    // 如果是字符串数字，转为数字
    if (typeof idx === 'string' && !isNaN(Number(idx))) {
      idx = Number(idx);
    }
    // 如果是数字且 options 存在
    if (typeof idx === 'number' && Array.isArray(question.options) && question.options[idx]) {
      // 去除选项前缀A. B.等
      return question.options[idx].replace(/^[A-Z][\.、\s]+/, '');
    }
    // 兜底：直接返回 idx
    return String(idx !== undefined ? idx : '无答案');
  },
  
  // 获取判断题正确答案文本
  getJudgmentAnswer: function(question) {
    if (!question || question.answer === undefined) {
      return '无答案';
    }
    
    try {
      let answer = question.answer;
      // 如果答案是字符串，尝试转换
      if (typeof answer === 'string') {
        if (answer.toLowerCase() === 'true' || answer === '1' || answer === '正确' || answer === '√') {
          return '正确';
        } else if (answer.toLowerCase() === 'false' || answer === '0' || answer === '错误' || answer === '×') {
          return '错误';
        }
        // 尝试解析数字
        const num = parseInt(answer);
        if (!isNaN(num)) {
          return num === 1 ? '正确' : '错误';
        }
      } else if (typeof answer === 'boolean') {
        return answer ? '正确' : '错误';
      } else if (typeof answer === 'number') {
        return answer === 1 ? '正确' : '错误';
      }
      
      return String(answer);
    } catch (e) {
      console.error('解析判断题答案出错:', e);
      return '答案格式错误';
    }
  },
  
  // 判断选项是否为正确答案
  isCorrectAnswer: function(correctText, optionIndex) {
    const optionLetter = String.fromCharCode(65 + optionIndex); // 'A', 'B', 'C', ...
    return correctText.indexOf(optionLetter) !== -1;
  },
  
  // 检查多选题选项是否为正确答案（辅助方法）
  isCorrectMultipleOption: function(correctIndexes, optionIndex) {
    if (!correctIndexes || !Array.isArray(correctIndexes)) {
      return false;
    }
    
    for (let i = 0; i < correctIndexes.length; i++) {
      if (correctIndexes[i] == optionIndex) {
        return true;
      }
    }
    
    return false;
  },

  // 重写 onReady 函数以获取关键元素引用
  onReady: function() {
    this.lastScrollTop = 0;
    this.maxScrollTop = 0;
    this.viewHeight = 0;
    this.scrollHeight = 0;
    this.scrollView = null;
    
    // 仅获取滚动视图的尺寸，不再获取已删除的滑块元素
    const query = wx.createSelectorQuery();
    query.select('#questionScrollView').boundingClientRect();
    
    query.exec(res => {
      if (res && res[0]) {
        // 只设置滚动视图的高度
        const scrollViewRect = res[0];
        this.viewHeight = scrollViewRect.height;
      }
    });
    
    // 专门获取scroll-view node
    wx.createSelectorQuery()
      .select('#questionScrollView')
      .node()
      .exec(res => {
        if (res && res[0] && res[0].node) {
          this.scrollView = res[0].node;
        }
      });
  },

  // 优化滚动事件处理，减少计算频率
  onScroll: function(e) {
    // 使用节流控制更新频率
    if (this._scrollThrottle) {
      clearTimeout(this._scrollThrottle);
    }
    
    this._scrollThrottle = setTimeout(() => {
      const { scrollTop, scrollHeight } = e.detail;
      this.lastScrollTop = scrollTop;
      this.scrollHeight = scrollHeight;
      this.maxScrollTop = scrollHeight - this.viewHeight;
      
      // 定期保存滚动位置，但不要过于频繁
      if (this._savePositionThrottle) {
        clearTimeout(this._savePositionThrottle);
      }
      
      this._savePositionThrottle = setTimeout(() => {
        this.saveScrollPosition();
      }, 1000); // 1秒后保存，避免频繁写入存储
    }, 16); // 约60fps的更新率
  },

  // 滑块相关函数的空实现，防止可能的引用错误
  _updateThumbPosition: function() {},
  onSliderTouchStart: function() { return false; },
  onSliderTouchMove: function() { return false; },
  onSliderTouchEnd: function() {},

  // 滚动到上15题
  scrollUp: function() {
    const currentTab = this.data.currentTab;
    let questions = [];
    
    // 获取当前标签页的问题数组
    if (currentTab === 0) {
      questions = this.data.allQuestions.single;
    } else if (currentTab === 1) {
      questions = this.data.allQuestions.multiple;
    } else if (currentTab === 2) {
      questions = this.data.allQuestions.judgment;
    } else if (currentTab === 3) {
      questions = this.data.allQuestions.fillblank;
    }
    
    // 获取当前滚动位置
    const scrollView = wx.createSelectorQuery().select('#questionScrollView');
    scrollView.scrollOffset().exec((res) => {
      if (res && res[0]) {
        const currentScrollTop = res[0].scrollTop;
        
        // 调整题目高度估算值，基于用户反馈实际跳转7题而不是15题
        const questionHeight = 450; // 增加估算每道题的平均高度
        const currentQuestionIndex = Math.floor(currentScrollTop / questionHeight);
        
        // 如果已经接近顶部（小于6题的距离），直接跳到第一题
        if (currentQuestionIndex < 6) {
          // 直接滚动到顶部
          this.setData({
            scrollTop: 0
          });
          
          // 显示已到顶部的提示
          wx.showToast({
            title: '已到顶部',
            icon: 'none',
            duration: 800
          });
          return;
        }
        
        // 计算要跳转到的题目索引（向上15题，但不小于0）
        const targetQuestionIndex = Math.max(0, currentQuestionIndex - 15);
        
        // 计算目标滚动位置
        const targetScrollTop = targetQuestionIndex * questionHeight;
        
        // 设置新的滚动位置
        this.setData({
          scrollTop: targetScrollTop
        });
        
        // 滚动完成后查找当前实际显示的题号
        setTimeout(() => {
          if (currentTab === 1) { // 多选题特殊处理
            // 简化处理：直接显示估计的题号
            wx.showToast({
              title: `多选题${targetQuestionIndex + 1}`,
              icon: 'none',
              duration: 800
            });
          } else {
            // 单选题使用原来的方式
            let selector = '.question-card';
            
            wx.createSelectorQuery()
              .selectAll(selector)
              .boundingClientRect((cards) => {
                if (!cards || cards.length === 0) {
                  // 如果没有找到题目卡片，使用估算值
                  wx.showToast({
                    title: `第${targetQuestionIndex + 1}题`,
                    icon: 'none',
                    duration: 800
                  });
                  return;
                }
                
                // 找到当前可见的第一个题目卡片
                let firstVisibleIndex = 0;
                for (let i = 0; i < cards.length; i++) {
                  if (cards[i].top >= 0) {
                    firstVisibleIndex = i;
                    break;
                  }
                }
                
                // 显示实际题号的反馈
                wx.showToast({
                  title: `第${firstVisibleIndex + 1}题`,
                  icon: 'none',
                  duration: 800
                });
              })
              .exec();
          }
        }, 150); // 等待滚动完成
      }
    });
  },
  
  // 滚动到下15题
  scrollDown: function() {
    try {
      const currentTab = this.data.currentTab;
      let questions = [];
      
      // 获取当前标签页的问题数组和题目总数
      let totalQuestions = 0;
      if (currentTab === 0) {
        questions = this.data.allQuestions.single;
        totalQuestions = questions.length;
      } else if (currentTab === 1) {
        questions = this.data.allQuestions.multiple;
        totalQuestions = questions.length;
      } else if (currentTab === 2) {
        questions = this.data.allQuestions.judgment;
        totalQuestions = questions.length;
      } else if (currentTab === 3) {
        questions = this.data.allQuestions.fillblank;
        totalQuestions = questions.length;
      }
      
      // 安全检查：如果没有题目，直接返回
      if (totalQuestions === 0) {
        wx.showToast({
          title: '当前没有题目',
          icon: 'none',
          duration: 800
        });
        return;
      }
      
      // 获取当前滚动位置和高度信息
      const scrollView = wx.createSelectorQuery().select('#questionScrollView');
      scrollView.scrollOffset().exec((res) => {
        if (res && res[0]) {
          const currentScrollTop = res[0].scrollTop;
          
          // 调整题目高度估算值
          const questionHeight = 450;
          const currentQuestionIndex = Math.floor(currentScrollTop / questionHeight);
          
          // 计算要跳转到的题目索引（向下15题，但不超过题目总数）
          // 使用题目总数而不是容器高度来计算最大索引
          const maxQuestionIndex = totalQuestions - 1;
          
          // 如果已经接近底部（剩余少于6题的距离），直接跳到最后一题
          if (maxQuestionIndex - currentQuestionIndex < 6) {
            // 获取最大滚动位置
            let containerSelector = '.questions-container';
            if (currentTab === 1) { // 针对多选题特殊处理
              containerSelector = '.questions-container:not([hidden])';
            }
            
            wx.createSelectorQuery()
              .select('#questionScrollView')
              .boundingClientRect()
              .selectAll(containerSelector)
              .boundingClientRect((containers) => {
                let currentContainer = null;
                if (currentTab === 1) { // 针对多选题特殊处理
                  if (containers && containers.length > 0) {
                    currentContainer = containers[0];
                  }
                } else {
                  for (let i = 0; containers && i < containers.length; i++) {
                    if (!containers[i].hidden) {
                      currentContainer = containers[i];
                      break;
                    }
                  }
                }
                
                if (currentContainer) {
                  // 计算向下滚动的最大距离
                  const scrollView = wx.createSelectorQuery().select('#questionScrollView');
                  scrollView.boundingClientRect((rect) => {
                    const maxScrollTop = currentContainer.height - rect.height;
                    
                    // 直接滚动到底部
                    this.setData({
                      scrollTop: maxScrollTop > 0 ? maxScrollTop : 0
                    });
                    
                    // 显示已到底部的提示或最后一题
                    if (currentTab === 1) { // 针对多选题特殊处理
                      setTimeout(() => {
                        // 简化处理：直接显示已到底部
                        wx.showToast({
                          title: `多选题${totalQuestions}`,
                          icon: 'none',
                          duration: 800
                        });
                      }, 150);
                    } else {
                      wx.showToast({
                        title: '已到底部',
                        icon: 'none',
                        duration: 800
                      });
                    }
                  }).exec();
                }
              })
              .exec();
            return;
          }
          
          const targetQuestionIndex = Math.min(maxQuestionIndex, currentQuestionIndex + 15);
          
          // 计算目标滚动位置
          const targetScrollTop = targetQuestionIndex * questionHeight;
          
          // 设置新的滚动位置
          this.setData({
            scrollTop: targetScrollTop
          });
          
          // 滚动完成后查找当前实际显示的题号
          setTimeout(() => {
            if (currentTab === 1) { // 多选题特殊处理
              // 简化处理：直接显示估计的题号
              wx.showToast({
                title: `多选题${targetQuestionIndex + 1}`,
                icon: 'none',
                duration: 800
              });
            } else {
              // 单选题使用原来的方式
              let selector = '.question-card';
              
              wx.createSelectorQuery()
                .selectAll(selector)
                .boundingClientRect((cards) => {
                  if (!cards || cards.length === 0) {
                    // 如果没有找到题目卡片，使用估算值
                    wx.showToast({
                      title: `第${targetQuestionIndex + 1}题`,
                      icon: 'none',
                      duration: 800
                    });
                    return;
                  }
                  
                  // 找到当前可见的第一个题目卡片
                  let firstVisibleIndex = 0;
                  for (let i = 0; i < cards.length; i++) {
                    if (cards[i].top >= 0) {
                      firstVisibleIndex = i;
                      break;
                    }
                  }
                  
                  // 显示实际题号的反馈
                  wx.showToast({
                    title: `第${firstVisibleIndex + 1}题`,
                    icon: 'none',
                    duration: 800
                  });
                })
                .exec();
            }
          }, 150); // 等待滚动完成
        }
      });
    } catch (error) {
      console.error('导航出错：', error);
      wx.showToast({
        title: '导航出错，请重试',
        icon: 'none',
        duration: 800
      });
    }
  },

  /**
   * 题目加载完成后恢复滚动位置
   */
  restoreScrollPosition: function() {
    // 已经加载完成，可以恢复滚动位置
    const tabNames = ['single', 'multiple', 'judgment', 'fillblank'];
    const currentTabName = tabNames[this.data.currentTab];
    const scrollTop = this.data.scrollPositions[currentTabName] || 0;
    
    this.setData({
      scrollTop: scrollTop
    });
    
    this.pendingScrollRestore = false;
  }
}); 

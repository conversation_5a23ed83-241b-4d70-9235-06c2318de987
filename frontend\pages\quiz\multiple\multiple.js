// pages/quiz/multiple/multiple.js
const app = getApp();

Page({
  data: {
    loading: true,
    questions: [],
    currentQuestionIndex: 0,
    selectedOptions: {}, // 使用对象存储选中状态
    hasSelection: false, // 是否有选项被选中
    submitted: false,
    showExplanation: false,
    isCorrect: false,
    progress: 0,
    completed: false,
    startTime: 0,
    optionColors: [], // 存储每个选项的颜色状态
    results: {
      correct: 0,
      wrong: 0,
      accuracy: 0,
      timeTaken: '0分钟'
    },
    courseId: null,
    mode: null,
    questionIds: [],
    currentIndex: 0,
    currentQuestion: null,
    coursesList: [], // 课程列表
    activeCourse: 0, // 当前选中的课程ID，0表示全部
    courseTitle: '' // 课程标题
  },

  onLoad: function(options) {
    console.log("多选题页面加载, 选项:", options);
    // 初始化答题开始时间
    this.setData({
      startTime: Date.now(),
      selectedOptions: {}, // 确保选中状态初始化为空对象
      mode: options.mode || 'normal'
    });
    
    // 保存选项
    this.options = options || {};
    
    const { mode, questionId, courseId, courseTitle } = options;
    const fromWrong = options.fromWrong === 'true';
    
    // 设置课程ID和标题
    if (courseId) {
      this.setData({
        courseId: parseInt(courseId),
        activeCourse: parseInt(courseId),
        courseTitle: courseTitle || ''
      });
    }
    
    console.log(`页面模式: ${mode}, 课程ID: ${courseId}, 题目ID: ${questionId}, 来自错题: ${fromWrong}`);
    
    // 检查缓存中是否有题目数据
    const cachedQuestions = wx.getStorageSync('currentQuestions');
    console.log('缓存中题目数据:', cachedQuestions ? `${cachedQuestions.length}道题目` : '无');
    
    // 处理重新练习模式 (retry)
    if (mode === 'retry' && cachedQuestions && cachedQuestions.length > 0) {
      console.log('检测到重新练习模式，使用缓存中的题目数据');
      
      // 如果是来自错题模式
      if (fromWrong) {
        console.log('来自错题模式的再次练习');
        // 筛选多选题
        const multipleChoiceQuestions = cachedQuestions.filter(q => 
          q.type === 'multiple' || q.question_type === 'multiple'
        );
        
        if (multipleChoiceQuestions.length === 0) {
          wx.showToast({
            title: '没有可用的题目',
            icon: 'none'
          });
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
          return;
        }
        
        // 处理题目数据格式
        const processedQuestions = multipleChoiceQuestions.map(q => {
          // 确保答案是数值型数组
          let correctAnswers = [];
          
          // 增强对answer字段的处理
          if (Array.isArray(q.answer)) {
            correctAnswers = q.answer.map(Number);
          } else if (typeof q.answer === 'string') {
            try {
              correctAnswers = JSON.parse(q.answer).map(Number);
            } catch (e) {
              if (q.answer.includes(',')) {
                correctAnswers = q.answer.split(',').map(Number);
              } else {
                correctAnswers = [Number(q.answer)];
              }
            }
          } else if (q.answer !== undefined) {
            correctAnswers = [Number(q.answer)];
          } else if (q.correctAnswers) {
            correctAnswers = q.correctAnswers;
          }
          
          return {
            id: q.id,
            question: q.question,
            options: Array.isArray(q.options) ? 
                     (typeof q.options[0] === 'object' ? q.options : q.options.map((text, index) => ({ text }))) : 
                     [],
            correctAnswers: correctAnswers,
            explanation: q.explanation || ""
          };
        });
        
        this.setData({
          questions: processedQuestions,
          loading: false,
          progress: (1 / processedQuestions.length) * 100,
          currentQuestionIndex: 0,
          selectedOptions: {},
          submitted: false,
          showExplanation: false,
          hasSelection: false,
          mode: 'wrong' // 设置为错题模式
        });
        
        // 清除缓存，防止下次进入时仍使用这些题目
        wx.removeStorageSync('currentQuestions');
        
        return;
      }
      
      // 常规再次练习模式，处理题目数据
      const processedQuestions = cachedQuestions.map(q => {
        // 确保答案是数值型数组
        let correctAnswers = [];
        
        if (q.correctAnswers) {
          correctAnswers = q.correctAnswers;
        } else if (Array.isArray(q.answer)) {
          correctAnswers = q.answer.map(Number);
        } else if (typeof q.answer === 'string') {
          try {
            correctAnswers = JSON.parse(q.answer).map(Number);
          } catch (e) {
            correctAnswers = [Number(q.answer)];
          }
        } else if (q.answer !== undefined) {
          correctAnswers = [Number(q.answer)];
        }
        
        return {
          id: q.id,
          question: q.question,
          options: Array.isArray(q.options) ? 
                   (typeof q.options[0] === 'object' ? q.options : q.options.map((text, index) => ({ text }))) : 
                   [],
          correctAnswers: correctAnswers,
          explanation: q.explanation || ""
        };
      });
      
      this.setData({
        questions: processedQuestions,
        loading: false,
        progress: (1 / processedQuestions.length) * 100,
        currentQuestionIndex: 0,
        selectedOptions: {},
        submitted: false,
        showExplanation: false,
        hasSelection: false
      });
      
      // 清除缓存，防止下次进入时仍使用这些题目
      wx.removeStorageSync('currentQuestions');
      
      return;
    }
    
    // 只在非错题模式下加载课程列表
    if (mode !== 'wrong' && mode !== 'practice') {
      this.fetchCoursesList();
    }
    
    // 如果是练习单个题目模式
    if (mode === 'practice' && questionId) {
      console.log('加载单个题目模式，题目ID:', questionId);
      this.loadQuestion(questionId);
      return;
    }
    
    if (mode === 'wrong') {
      // 错题模式
      this.loadWrongQuestions();
    } else if (mode === 'course' || courseId) {
      // 课程模式
      this.fetchQuestions();
    } else {
      // 普通模式，加载所有题目
      this.loadAllQuestions();
    }
  },

  onShow: function() {
    // 页面展示时检查数据
    console.log("页面显示，当前数据状态：", {
      questions: this.data.questions.length,
      currentIndex: this.data.currentQuestionIndex,
      selectedOptions: this.data.selectedOptions
    });
    
    // 检查数据完整性
    if (this.data.questions.length > 0) {
      const currentQ = this.data.questions[this.data.currentQuestionIndex];
      if (currentQ) {
        console.log("当前题目：", currentQ.question);
        console.log("答案选项：", currentQ.options);
        console.log("正确答案：", currentQ.correctAnswers);
      }
    }
  },
  
  // 获取课程列表
  fetchCoursesList: function() {
    app.request({
      url: '/classes',
      method: 'GET'
    }).then(res => {
      console.log('获取课程列表数据:', res);
      
      if (res && Array.isArray(res)) {
        // 处理从API获取的课程数据
        const coursesData = res.map(course => {
          return {
            id: course.id,
            name: course.name,
            question_count: course.question_count || 0
          };
        });
        
        this.setData({
          coursesList: coursesData
        });
      }
    }).catch(err => {
      console.error('获取课程列表失败:', err);
    });
  },
  
  // 切换课程筛选
  switchCourse: function(e) {
    // 如果是错题模式或单个题目练习模式，不允许切换课程
    if (this.data.mode === 'wrong' || this.data.mode === 'practice') {
      console.log('错题模式或单题练习模式下不允许切换课程');
      return;
    }
    
    const courseId = parseInt(e.currentTarget.dataset.id) || 0;
    
    this.setData({ 
      activeCourse: courseId,
      loading: true 
    });
    
    // 跳转到新的URL以保持一致性
    wx.redirectTo({
      url: `/pages/quiz/multiple/multiple?mode=${this.data.mode || 'normal'}${courseId ? '&courseId=' + courseId : ''}`
    });
  },

  fetchQuestions: function() {
    this.setData({ 
      loading: true,
      selectedOptions: {} // 确保每次获取新题目时重置选中状态
    });
    
    // 检查是否是课程模式
    if (this.data.mode === 'course') {
      // 从存储中获取课程题目
      const courseQuestions = wx.getStorageSync('currentQuestions');
      if (courseQuestions && courseQuestions.length > 0) {
        // 处理题目数据
        const processedQuestions = courseQuestions.map(q => {
          return {
            id: q.id,
            question: q.question,
            options: q.options.map((text, index) => {
              return { text: text }
            }),
            correctAnswers: q.correct_answers || q.correctAnswers || [],
            explanation: q.explanation || ""
          };
        });
        
        this.setData({
          questions: processedQuestions,
          loading: false,
          progress: (1 / processedQuestions.length) * 100,
          currentQuestionIndex: 0,
          selectedOptions: {},
          submitted: false,
          showExplanation: false,
          hasSelection: false
        });
        
        console.log('处理后的课程题目数据:', processedQuestions);
        return;
      }
      
      // 如果没有缓存的课程题目，则通过API获取
      this.loadCourseQuestions();
      return;
    }
    
    // 如果不是课程模式，使用常规API获取所有题目
    app.request({
      url: '/questions/multiple',
      method: 'GET'
    })
    .then(res => {
      if (!res || res.length === 0) {
        wx.showToast({
          title: '暂无多选题',
          icon: 'none',
          duration: 2000
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 2000);
        return;
      }
      
      // 如果有课程ID筛选，过滤属于该课程的题目
      if (this.data.courseId) {
        res = res.filter(q => q.course_id === this.data.courseId);
        
        if (res.length === 0) {
          wx.showToast({
            title: '该课程暂无多选题',
            icon: 'none',
            duration: 2000
          });
          setTimeout(() => {
            wx.navigateBack();
          }, 2000);
          return;
        }
      }
      
      // 处理题目数据，直接使用API返回的答案
      const processedQuestions = res.map(q => {
        // 确保答案是数值型数组
        let correctAnswers = [];
        
        // 处理不同格式的答案数据
        if (Array.isArray(q.answer)) {
          correctAnswers = q.answer.map(Number);
        } else if (typeof q.answer === 'string') {
          try {
            correctAnswers = JSON.parse(q.answer).map(Number);
          } catch (e) {
              correctAnswers = [Number(q.answer)];
          }
        } else if (q.answer !== undefined) {
          correctAnswers = [Number(q.answer)];
        }
        
        return {
          id: q.id,
          question: q.question,
          options: q.options.map((text, index) => {
            return { text: text }
          }),
          correctAnswers: correctAnswers,
          explanation: q.explanation || ""
        };
      });
      
      this.setData({
        questions: processedQuestions,
        loading: false,
        progress: (1 / processedQuestions.length) * 100,
        currentQuestionIndex: 0,
        selectedOptions: {},
        submitted: false,
        showExplanation: false,
        hasSelection: false
      });
      
      console.log('处理后的题目数据:', processedQuestions);
    })
    .catch(err => {
      console.error('获取多选题失败:', err);
      this.setData({ loading: false });
      
      wx.showToast({
        title: '获取题目失败',
        icon: 'none'
      });
      
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    });
  },

  selectOption: function(e) {
    // 已提交答案时不允许再选择
    if (this.data.submitted) return;
    
    const index = e.currentTarget.dataset.index;
    const selectedOptions = {...this.data.selectedOptions};
    
    // 切换选中状态
    if (selectedOptions[index]) {
      delete selectedOptions[index];
    } else {
      selectedOptions[index] = true;
    }
    
    // 检查是否有选项被选中
    const hasSelection = Object.keys(selectedOptions).length > 0;
    
    // 更新状态
    this.setData({
      selectedOptions: selectedOptions,
      hasSelection: hasSelection
    });
    
    console.log('选中状态:', selectedOptions);
  },

  submitAnswer: function() {
    if (this.data.submitted) return;
    
    if (!this.data.hasSelection) {
      wx.showToast({
        title: '请至少选择一个选项',
        icon: 'none'
      });
      return;
    }
    
    try {
      const { currentQuestionIndex, questions, selectedOptions, results } = this.data;
      
      // 安全检查
      if (!questions || questions.length === 0 || !questions[currentQuestionIndex]) {
        console.error('题目数据不存在');
        wx.showToast({
          title: '题目数据错误',
          icon: 'none'
        });
        return;
      }
      
      const currentQuestion = questions[currentQuestionIndex];
      console.log('当前题目:', currentQuestion);
      
      // 增强对correctAnswers的检查和处理
      if (!currentQuestion.correctAnswers || !Array.isArray(currentQuestion.correctAnswers)) {
        console.error('正确答案数据缺失或格式不正确');
        
        // 尝试修复答案格式
        let fixedAnswers = [];
        if (currentQuestion.answer !== undefined) {
          if (Array.isArray(currentQuestion.answer)) {
            fixedAnswers = currentQuestion.answer.map(Number);
          } else if (typeof currentQuestion.answer === 'string') {
            try {
              fixedAnswers = JSON.parse(currentQuestion.answer).map(Number);
            } catch (e) {
              if (currentQuestion.answer.includes(',')) {
                fixedAnswers = currentQuestion.answer.split(',').map(Number);
              } else {
                fixedAnswers = [Number(currentQuestion.answer)];
              }
            }
          } else {
            fixedAnswers = [Number(currentQuestion.answer)];
          }
          
          // 更新题目中的正确答案
          currentQuestion.correctAnswers = fixedAnswers;
          console.log('已修复答案格式:', fixedAnswers);
        }
        
        // 如果仍然没有有效答案，设置一个默认值
        if (!fixedAnswers || fixedAnswers.length === 0 || fixedAnswers.some(isNaN)) {
          currentQuestion.correctAnswers = [0];
          console.warn('无法修复答案格式，使用默认值[0]');
        }
        
        // 更新题目数据
        let updatedQuestions = [...questions];
        updatedQuestions[currentQuestionIndex] = currentQuestion;
        this.setData({ questions: updatedQuestions });
      }
      
      // 将选中的选项转换为数组
      const selectedArray = Object.keys(selectedOptions).map(Number);
      console.log('提交答案:', selectedArray, '正确答案:', currentQuestion.correctAnswers);
      
      // 向后端提交答案
      app.request({
        url: '/submit',
        method: 'POST',
        data: {
          questionType: 'multiple',
          questionId: currentQuestion.id,
          userAnswer: selectedArray
        }
      })
      .then(res => {
        console.log('答案提交成功:', res);
        
        // 检查答案是否正确（多选逻辑）
        const isCorrect = this.checkIfCorrect(selectedArray, currentQuestion.correctAnswers);
        
        // 设置选项颜色状态
        const colors = [];
        // 获取当前题目的选项数量
        const optionsCount = currentQuestion.options.length;
        
        for (let i = 0; i < optionsCount; i++) {
          // 检查当前选项是否为正确答案
          const isCorrectAnswer = currentQuestion.correctAnswers.includes(i);
          if (isCorrectAnswer) {
            colors.push('correct'); // 正确答案 - 绿色
          } else {
            colors.push('wrong'); // 错误答案 - 红色
          }
        }
        
        // 更新结果
        const newResults = { ...results };
        if (isCorrect) {
          newResults.correct += 1;
          wx.showToast({
            title: '回答正确!',
            icon: 'success'
          });
        } else {
          newResults.wrong += 1;
          wx.showToast({
            title: '回答错误',
            icon: 'error'
          });
        }
        
        // 计算准确率
        const totalAnswered = newResults.correct + newResults.wrong;
        newResults.accuracy = Math.round((newResults.correct / totalAnswered) * 100);
        
        this.setData({
          submitted: true,
          showExplanation: true,
          isCorrect: isCorrect,
          results: newResults,
          optionColors: colors
        });
      })
      .catch(err => {
        console.error('提交答案到服务器失败:', err);
        wx.showToast({
          title: '提交失败，请重试',
          icon: 'none'
        });
      });
    } catch (error) {
      console.error("提交答案时出错:", error);
      wx.showToast({
        title: '提交失败，请重试',
        icon: 'none'
      });
    }
  },

  checkIfCorrect: function(selectedArray, correctAnswers) {
    // 多选题需要完全匹配
    if (selectedArray.length !== correctAnswers.length) {
      return false;
    }
    
    // 排序后比较是否完全一致
    const sortedSelected = [...selectedArray].sort();
    const sortedCorrect = [...correctAnswers].sort();
    
    for (let i = 0; i < sortedSelected.length; i++) {
      if (sortedSelected[i] !== sortedCorrect[i]) {
        return false;
      }
    }
    
    return true;
  },

  nextQuestion: function() {
    const nextIndex = this.data.currentQuestionIndex + 1;
    
    if (nextIndex >= this.data.questions.length) {
      // 计算用时
      const endTime = Date.now();
      const totalSeconds = Math.round((endTime - this.data.startTime) / 1000);
      const minutes = Math.floor(totalSeconds / 60);
      const seconds = totalSeconds % 60;
      
      const results = {
        ...this.data.results,
        timeTaken: `${minutes}分${seconds}秒`
      };
      
      // 已经是最后一题，显示完成确认对话框
      this.setData({
        completed: true,
        results: results
      });
      return;
    }
    
    // 重置下一题的状态
    this.setData({
      currentQuestionIndex: nextIndex,
      selectedOptions: {}, // 确保清空选中状态
      submitted: false,
      showExplanation: false,
      isCorrect: false,
      hasSelection: false,
      optionColors: [], // 清空颜色状态
      progress: ((nextIndex + 1) / this.data.questions.length) * 100
    });
  },

  recordQuizResults: function() {
    // 这里可以添加将答题结果发送到服务器的逻辑
    console.log('答题结果:', this.data.results);
  },

  continuePractice: function() {
    this.setData({
      completed: false
    });
    
    // 重新获取题目
    this.fetchQuestions();
  },

  viewResults: function() {
    this.setData({
      completed: false
    });
    
    // 获取课程信息
    const currentCourse = wx.getStorageSync('currentCourse') || {};
    
    // 前往结果页面
    const result = {
      totalQuestions: this.data.questions.length,
      correctCount: this.data.results.correct,
      wrongCount: this.data.results.wrong,
      accuracy: this.data.results.accuracy,
      timeTaken: this.data.results.timeTaken,
      questionType: 'multiple',
      courseId: this.data.courseId,
      courseName: this.data.courseTitle,
      courseColor: currentCourse.color || '#4e8df7',
      // 保存原始题目数据，供再次练习使用
      questions: this.data.questions,
      // 保存当前模式，尤其是对错题本模式很重要
      mode: this.data.mode
    };
    
    // 确保所有数据都是有效的数值
    Object.keys(result).forEach(key => {
      if (typeof result[key] === 'number' && isNaN(result[key])) {
        result[key] = 0;
      }
    });
    
    console.log('发送到结果页的数据:', result);
    
    wx.navigateTo({
      url: '/pages/result/result',
      success: (res) => {
        // 传递结果数据给结果页面
        res.eventChannel.emit('acceptResultData', result);
      }
    });
  },

  // 是否是正确答案
  isCorrectAnswer: function(index) {
    const { currentQuestionIndex, questions } = this.data;
    if (questions && questions[currentQuestionIndex] && questions[currentQuestionIndex].correctAnswers) {
      return questions[currentQuestionIndex].correctAnswers.includes(index);
    }
    return false;
  },

  // 获取错题本中的多选题
  loadWrongQuestions: function() {
    this.setData({ loading: true });
    
    app.request({
      url: '/wrong-questions',
      method: 'GET'
    }).then(res => {
      console.log('获取错题本数据:', res);
      
      // 筛选多选题
      let multipleQuestions = res.filter(q => q.type === 'multiple');
      
      // 如果有指定课程ID，进一步筛选
      if (this.data.courseId) {
        multipleQuestions = multipleQuestions.filter(q => q.course_id === this.data.courseId);
      }
      
      if (multipleQuestions.length === 0) {
        wx.showToast({
          title: '没有多选错题',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }

      // 处理题目数据，特别注意answer字段的处理
      const processedQuestions = multipleQuestions.map(q => {
        // 确保答案是数值型数组
        let correctAnswers = [];
        
        // 增强对answer字段的处理，解决错题本中的格式问题
        if (Array.isArray(q.answer)) {
          correctAnswers = q.answer.map(Number);
        } else if (typeof q.answer === 'string') {
          try {
            // 尝试解析字符串格式的答案
            correctAnswers = JSON.parse(q.answer).map(Number);
          } catch (e) {
            // 处理可能的字符串格式，如"[1,2]"或"1,2"
            if (q.answer.includes(',')) {
              correctAnswers = q.answer.split(',').map(Number);
            } else {
              correctAnswers = [Number(q.answer)];
            }
          }
        } else if (q.answer !== undefined) {
          correctAnswers = [Number(q.answer)];
        }
        
        // 确保正确答案存在
        if (!correctAnswers || correctAnswers.length === 0 || correctAnswers.some(isNaN)) {
          console.warn('题目ID:'+q.id+' 答案数据异常，使用默认值');
          correctAnswers = [0]; // 设置默认值避免页面崩溃
        }
        
        return {
          id: q.id,
          question: q.question,
          options: q.options.map((text, index) => {
            return { text: text }
          }),
          correctAnswers: correctAnswers,
          explanation: q.explanation || ""
        };
      });
      
      this.setData({
        questions: processedQuestions,
        loading: false,
        progress: (1 / processedQuestions.length) * 100,
        currentQuestionIndex: 0,
        selectedOptions: {},
        submitted: false,
        showExplanation: false,
        hasSelection: false
      });
      
    }).catch(err => {
      console.error('获取错题失败:', err);
      wx.showToast({
        title: '获取错题失败',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    });
  },
  
  // 获取特定题目
  loadQuestion: function(questionId) {
    this.setData({ loading: true });
    
    console.log('加载单个多选题，ID:', questionId);
    
    // 优先检查是否有缓存的练习题目
    const cachedQuestion = wx.getStorageSync('practiceQuestion');
    if (cachedQuestion && cachedQuestion.id == questionId) {
      console.log('使用缓存的练习题目:', cachedQuestion);
      
      // 确保答案是数值型数组
      let correctAnswers = [];
      
      // 处理不同格式的答案数据
      if (Array.isArray(cachedQuestion.answer)) {
        correctAnswers = cachedQuestion.answer.map(Number);
      } else if (typeof cachedQuestion.answer === 'string') {
        try {
          correctAnswers = JSON.parse(cachedQuestion.answer).map(Number);
        } catch (e) {
          correctAnswers = [Number(cachedQuestion.answer)];
        }
      } else if (cachedQuestion.answer !== undefined) {
        correctAnswers = [Number(cachedQuestion.answer)];
      }
      
      // 处理题目数据
      const processedQuestion = {
        id: cachedQuestion.id,
        question: cachedQuestion.question,
        options: Array.isArray(cachedQuestion.options) ? 
                 cachedQuestion.options.map((text, index) => ({ text: text })) : 
                 [],
        correctAnswers: correctAnswers,
        explanation: cachedQuestion.explanation || ""
      };
      
      this.setData({
        questions: [processedQuestion],
        loading: false,
        progress: 100,  // 只有一道题，进度为100%
        currentQuestionIndex: 0,
        selectedOptions: {},
        submitted: false,
        showExplanation: false,
        hasSelection: false
      });
      
      // 清除缓存
      wx.removeStorageSync('practiceQuestion');
      return;
    }
    
    // 如果没有缓存，则从API获取题目
    app.request({
      url: '/questions/multiple',
      method: 'GET'
    })
    .then(res => {
      if (!res || !res.length) {
        wx.showToast({
          title: '获取题目失败',
          icon: 'none'
        });
        
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }
      
      // 查找特定ID的题目
      const question = res.find(q => q.id == questionId);
      
      if (!question) {
        wx.showToast({
          title: '题目不存在',
          icon: 'none'
        });
        
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }
      
      // 处理题目数据
      const correctAnswers = Array.isArray(question.answer) 
        ? question.answer.map(Number) 
        : (question.answer !== undefined ? [Number(question.answer)] : []);
      
      const processedQuestion = {
        id: question.id,
        question: question.question,
        options: question.options.map((text, index) => {
          return { text: text }
        }),
        correctAnswers: correctAnswers,
        category: question.category,
        difficulty: question.difficulty,
        explanation: question.explanation || ""
      };
      
      this.setData({
        questions: [processedQuestion],
        loading: false,
        progress: 100,  // 只有一道题，进度为100%
        currentQuestionIndex: 0,
        selectedOptions: {},
        submitted: false,
        showExplanation: false,
        hasSelection: false
      });
    })
    .catch(err => {
      console.error('获取特定题目失败:', err);
      this.setData({ loading: false });
      
      wx.showToast({
        title: '获取题目失败',
        icon: 'none'
      });
      
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    });
  },

  // 打乱数组
  shuffleArray: function(array) {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]];
    }
  },

  loadAllQuestions: function() {
    this.fetchQuestions();
  },

  // 加载指定课程的多选题
  loadCourseQuestions: function() {
    if (!this.data.courseId) {
      console.error('加载课程题目失败：缺少课程ID');
      wx.showToast({
        title: '课程信息不完整',
        icon: 'none'
      });
      return;
    }
    
    this.setData({ loading: true });
    
    app.request({
      url: `/course/${this.data.courseId}/questions/multiple`
    })
    .then(res => {
      this.setData({ loading: false });
      
      if (!res || !res.length) {
        wx.showToast({
          title: '该课程暂无多选题',
          icon: 'none'
        });
        
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }
      
      // 处理题目数据
      const processedQuestions = res.map(q => {
        // 确保答案是数值型数组
        let correctAnswers = [];
        
        // 处理不同格式的答案数据
        if (Array.isArray(q.answer)) {
          correctAnswers = q.answer.map(Number);
        } else if (typeof q.answer === 'string') {
          try {
            correctAnswers = JSON.parse(q.answer).map(Number);
          } catch (e) {
            correctAnswers = [Number(q.answer)];
          }
        } else if (q.answer !== undefined) {
          correctAnswers = [Number(q.answer)];
        }
        
        return {
          id: q.id,
          question: q.question,
          options: q.options.map((text, index) => {
            return { text: text }
          }),
          correctAnswers: correctAnswers,
          explanation: q.explanation || ""
        };
      });
      
      this.setData({
        questions: processedQuestions,
        progress: (1 / processedQuestions.length) * 100,
        currentQuestionIndex: 0,
        selectedOptions: {},
        submitted: false,
        showExplanation: false,
        hasSelection: false
      });
      
      // 缓存课程题目
      wx.setStorageSync('currentQuestions', res);
      
      console.log('处理后的课程题目数据:', processedQuestions);
    })
    .catch(err => {
      console.error('获取课程多选题失败:', err);
      this.setData({ loading: false });
      
      wx.showToast({
        title: '获取题目失败',
        icon: 'none'
      });
      
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    });
  },
}); 
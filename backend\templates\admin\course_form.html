{% extends "admin/base.html" %}

{% block title %}
{% if action == 'add' %}
添加课程 - 题库管理系统
{% else %}
编辑课程 - 题库管理系统
{% endif %}
{% endblock %}

{% block header %}
{% if action == 'add' %}
添加课程
{% else %}
编辑课程
{% endif %}
{% endblock %}

{% block header_buttons %}
<a href="/admin/courses" class="btn btn-outline-secondary">
    <i class="fas fa-arrow-left me-1"></i>返回课程列表
</a>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-body">
        <form method="post" action="{% if action == 'add' %}/admin/courses/add{% else %}/admin/courses/edit/{{ course.id }}{% endif %}">
            <div class="mb-3">
                <label for="name" class="form-label">课程名称 <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="name" name="name" required 
                       value="{% if action == 'edit' %}{{ course.name }}{% endif %}">
                <div class="form-text">例如：Python编程、数据结构、Web开发等</div>
            </div>
            
            <div class="mb-3">
                <label for="class_id" class="form-label">所属班级 <span class="text-danger">*</span></label>
                <select class="form-select" id="class_id" name="class_id" required>
                    <option value="">请选择班级</option>
                    {% for class_obj in classes %}
                    <option value="{{ class_obj.id }}" {% if action == 'edit' and course.class_id == class_obj.id %}selected{% endif %}>
                        {{ class_obj.name }}
                    </option>
                    {% endfor %}
                </select>
                {% if classes|length == 0 %}
                <div class="alert alert-warning mt-2">
                    <i class="fas fa-exclamation-triangle me-1"></i> 没有可用的班级，请先 <a href="/admin/classes/add">添加班级</a>。
                </div>
                {% endif %}
            </div>
            
            <div class="mb-3">
                <label for="description" class="form-label">课程描述</label>
                <textarea class="form-control" id="description" name="description" rows="3">{% if action == 'edit' %}{{ course.description }}{% endif %}</textarea>
                <div class="form-text">可选：对课程的详细描述</div>
            </div>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="/admin/courses" class="btn btn-outline-secondary me-md-2">取消</a>
                <button type="submit" class="btn btn-primary" {% if classes|length == 0 %}disabled{% endif %}>
                    {% if action == 'add' %}
                    <i class="fas fa-plus me-1"></i>添加课程
                    {% else %}
                    <i class="fas fa-save me-1"></i>保存修改
                    {% endif %}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %} 
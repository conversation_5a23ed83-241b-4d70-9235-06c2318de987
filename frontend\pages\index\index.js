const app = getApp();
const globalNotificationMixin = require('../../utils/global-notification-mixin')

// 课程颜色数组，用于随机分配颜色
const COURSE_COLORS = [
  '#4e8df7', '#5e66ff', '#4caf50', '#ff7043', '#9c27b0',
  '#3f51b5', '#00bcd4', '#ff5722', '#607d8b', '#ff9800'
];

Page(Object.assign({}, globalNotificationMixin, {
  data: Object.assign({}, globalNotificationMixin.data, {
    userInfo: {},
    singleChoiceList: [],
    multipleChoiceList: [],
    judgmentList: [],
    fillblankList: [],
    isLoading: true,
    courses: [],
    className: '',
    lastRefresh: 0,
    currentDotIndex: 0,
    catComponentVisible: true, // 确保小猫组件默认可见
    isRefreshing: false // 添加刷新状态标记
  }),
  
  onLoad: function() {
    // 启用小猫组件
    this.setData({
      catComponentVisible: true
    });
    
    // 检查用户是否登录
    if (app.checkLogin()) {
      this.setData({
        userInfo: app.globalData.userInfo
      });
      
      // 获取用户课程
      this.fetchUserCourses();
      
      // 获取题目
      this.fetchQuestions();
    } else {
      wx.redirectTo({
        url: '/pages/login/login'
      });
    }
  },
  
  onShow: function() {
    // 更新自定义tabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 0
      });
    }

    // 确保小猫组件显示
    if (app.globalData.catVisible !== false) {
      this.setData({
        catComponentVisible: true
      });
    }

    // 检查用户是否登录
    if (app.checkLogin()) {
      this.setData({
        userInfo: app.globalData.userInfo
      });

      // 只有当距离上次刷新超过5秒时才刷新，避免频繁刷新
      const now = Date.now();
      if (now - this.data.lastRefresh > 5000) {
        this.fetchUserCourses();
        this.setData({ lastRefresh: now });
      }
    } else {
      wx.redirectTo({
        url: '/pages/login/login'
      });
    }

    // 检查用户是否已选择班级
    this.checkUserClass();
  },
  
  // 获取用户班级下的课程
  fetchUserCourses: function() {
    if (!app.checkLogin()) {
      console.log('未登录，无法获取课程');
      return;
    }
    
    wx.request({
      url: app.globalData.baseUrl + '/user/courses',
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        console.log('获取用户课程:', res.data);
        
        if (res.data && res.data.status === 'success') {
          // 为课程分配随机颜色
          const coursesWithColors = res.data.courses.map((course, index) => {
            course.color = COURSE_COLORS[index % COURSE_COLORS.length];
            return course;
          });
          
          this.setData({
            courses: coursesWithColors || [],
            className: res.data.class_name ? res.data.class_name + '的' : ''
          });
        } else {
          console.error('获取课程失败:', res.data);
          // 如果是因为用户未选择班级，跳转到班级选择页面
          if (res.data && res.data.message === '用户未加入班级') {
            wx.showToast({
              title: '请先选择班级',
              icon: 'none',
              duration: 2000
            });
            setTimeout(() => {
              wx.redirectTo({
                url: '/pages/class-selection/class-selection'
              });
            }, 1500);
          }
        }
      },
      fail: (err) => {
        console.error('获取课程请求失败:', err);
        wx.showToast({
          title: '网络错误',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  // 导航到课程页面
  navigateToCourse: function(e) {
    const courseId = e.currentTarget.dataset.id;
    const courseName = e.currentTarget.dataset.name;
    
    // 找到对应的课程对象
    const course = this.data.courses.find(c => c.id == courseId);
    if (!course) {
      wx.showToast({
        title: '课程信息不完整',
        icon: 'none'
      });
      return;
    }
    
    console.log('导航到课程:', courseId, courseName, course.color);
    
    // 显示加载中
    wx.showLoading({
      title: '加载中...',
    });
    
    // 存储课程信息到全局缓存
    wx.setStorageSync('selectedCourse', {
      id: courseId,
      name: courseName,
      color: course.color || '#4e8df7'
    });
    
    // 跳转到课程页面
    wx.navigateTo({
      url: `/pages/course/course?id=${courseId}&name=${encodeURIComponent(courseName)}`,
      success: () => {
        // 成功跳转后隐藏加载
        wx.hideLoading();
      },
      fail: (err) => {
        console.error('导航失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },
  
  // 统一用户信息格式，确保同时兼容 avatar/nickname 和 avatarUrl/nickName 两种格式
  normalizeUserInfo: function(userInfo) {
    if (!userInfo) return userInfo;
    
    // 确保两种格式的属性都存在
    if (userInfo.avatarUrl && !userInfo.avatar) {
      userInfo.avatar = userInfo.avatarUrl;
    } else if (userInfo.avatar && !userInfo.avatarUrl) {
      userInfo.avatarUrl = userInfo.avatar;
    }
    
    if (userInfo.nickName && !userInfo.nickname) {
      userInfo.nickname = userInfo.nickName;
    } else if (userInfo.nickname && !userInfo.nickName) {
      userInfo.nickName = userInfo.nickname;
    }
    
    return userInfo;
  },
  
  // 检查登录状态
  checkLogin: function() {
    if (!app.checkLogin()) {
      console.log('未登录，跳转到登录页');
      this.setData({ loginChecked: false });
      wx.navigateTo({
        url: '/pages/login/login'
      });
      return false;
    }
    
    // 确保有用户信息
    if (app.globalData.userInfo) {
      // 确保用户信息包含两种格式的属性名
      this.normalizeUserInfo(app.globalData.userInfo);
      this.setData({
        userInfo: app.globalData.userInfo,
        loginChecked: true
      });
      
      // 检查用户是否已选择班级
      this.checkUserClass();
    } else {
      // 如果全局没有用户信息，尝试从本地存储加载
      const userManager = require('../../utils/activity');
      const localUserInfo = userManager.getLocalUserInfo();
      if (localUserInfo) {
        // 确保用户信息包含两种格式的属性名
        this.normalizeUserInfo(localUserInfo);
        this.setData({
          userInfo: localUserInfo,
          loginChecked: true
        });
        // 同时更新全局数据
        app.globalData.userInfo = localUserInfo;
        console.log('从本地存储恢复用户信息:', localUserInfo);
        
        // 检查用户是否已选择班级
        this.checkUserClass();
      }
    }
    
    return true;
  },
  
  // 检查用户是否已选择班级
  checkUserClass: function() {
    if (!app.checkLogin()) return;
    
    // 获取用户信息，检查是否有班级
    wx.request({
      url: app.globalData.baseUrl + '/statistics',
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        console.log('检查用户班级:', res.data);
        
        if (res.data && res.data.user && !res.data.user.class_id) {
          console.log('用户未选择班级，跳转到班级选择页面');
          wx.redirectTo({
            url: '/pages/class-selection/class-selection'
          });
        }
      },
      fail: (err) => {
        console.error('获取用户信息失败:', err);
        // 失败时不做重定向，避免循环跳转
      }
    });
  },
  
  // 获取题目列表
  fetchQuestions: function() {
    if (!app.checkLogin()) {
      console.log('未登录，无法获取题目');
      return;
    }
    
    this.setData({ isLoading: true });

    // 获取所有题型的题目
    this.fetchSingleChoiceQuestions()
      .then(() => this.fetchMultipleChoiceQuestions())
      .then(() => this.fetchJudgmentQuestions())
      .then(() => this.fetchFillBlankQuestions())
      .catch(err => {
        console.error('获取题目失败:', err);
        this.setData({ isLoading: false });
        wx.showToast({
          title: '获取题目失败',
          icon: 'none',
          duration: 2000
        });
      });
  },
  
  // 获取单选题
  fetchSingleChoiceQuestions: function() {
    return new Promise((resolve, reject) => {
      app.request({
        url: '/questions/single',
        method: 'GET'
      })
      .then(res => {
        this.setData({
          singleChoiceList: res
        });
        resolve();
      })
      .catch(err => {
        reject(err);
      });
    });
  },
  
  // 获取多选题
  fetchMultipleChoiceQuestions: function() {
    return new Promise((resolve, reject) => {
      app.request({
        url: '/questions/multiple',
        method: 'GET'
      })
      .then(res => {
        this.setData({
          multipleChoiceList: res
        });
        resolve();
      })
      .catch(err => {
        reject(err);
      });
    });
  },
  
  // 获取判断题
  fetchJudgmentQuestions: function() {
    return new Promise((resolve, reject) => {
      app.request({
        url: '/questions/judgment',
        method: 'GET'
      })
      .then(res => {
        this.setData({
          judgmentList: res
        });
        resolve();
      })
      .catch(err => {
        reject(err);
      });
    });
  },
  
  // 获取填空题
  fetchFillBlankQuestions: function() {
    return new Promise((resolve, reject) => {
      app.request({
        url: '/questions/fillblank',
        method: 'GET'
      })
      .then(res => {
        this.setData({
          fillblankList: res,
          isLoading: false
        });
        resolve();
      })
      .catch(err => {
        reject(err);
      });
    });
  },
  
  // 开始单选题答题
  startSingleQuiz: function() {
    if (!this.data.singleChoiceList.length) {
      wx.showToast({
        title: '暂无单选题',
        icon: 'none'
      });
      return;
    }
    
    wx.navigateTo({
      url: '/pages/quiz/single/single'
    });
  },
  
  // 开始多选题答题
  startMultipleQuiz: function() {
    if (!this.data.multipleChoiceList.length) {
      wx.showToast({
        title: '暂无多选题',
        icon: 'none'
      });
      return;
    }
    
    wx.navigateTo({
      url: '/pages/quiz/multiple/multiple'
    });
  },
  
  // 开始判断题答题
  startJudgmentQuiz: function() {
    if (!this.data.judgmentList.length) {
      wx.showToast({
        title: '暂无判断题',
        icon: 'none'
      });
      return;
    }
    
    wx.navigateTo({
      url: '/pages/quiz/judgment/judgment'
    });
  },
  
  // 开始填空题答题
  startFillBlankQuiz: function() {
    if (!this.data.fillblankList.length) {
      wx.showToast({
        title: '暂无填空题',
        icon: 'none'
      });
      return;
    }
    
    wx.navigateTo({
      url: '/pages/quiz/fillblank/fillblank'
    });
  },
  
  // 进入错题本
  goToWrongQuestions: function() {
    wx.switchTab({
      url: '/pages/wrong-questions/wrong-questions'
    });
  },
  
  // 进入答题统计
  goToStatistics: function() {
    wx.switchTab({
      url: '/pages/statistics/statistics'
    });
  },
  
  // 查看全部课程
  viewAllCourses: function() {
    wx.navigateTo({
      url: '/pages/courses/courses'
    });
  },
  
  // 导航到所有课程页面
  navigateToAllCourses: function() {
    console.log('导航到所有课程页面');
    
    // 显示加载中
    wx.showLoading({
      title: '加载中...',
    });
    
    // 跳转到所有课程页面
    wx.navigateTo({
      url: '/pages/all-courses/all-courses',
      success: () => {
        // 成功跳转后隐藏加载
        wx.hideLoading();
      },
      fail: (err) => {
        console.error('导航失败:', err);
        wx.hideLoading();
        
        // 如果页面不存在，给用户提示
        if (err.errMsg.includes('page "pages/all-courses/all-courses" is not found')) {
          wx.showToast({
            title: '功能开发中',
            icon: 'none',
            duration: 2000
          });
        } else {
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none',
            duration: 2000
          });
        }
      }
    });
  },
  
  // 处理课程滚动事件
  onCourseScroll: function(e) {
    // 如果课程少于3个，不需要处理滚动
    if (this.data.courses.length <= 2) return;
    
    // 获取滚动位置
    const scrollLeft = e.detail.scrollLeft;
    
    // 如果当前滚动位置为0，直接激活第一个点
    if (scrollLeft === 0) {
      if (this.data.currentDotIndex !== 0) {
        this.setData({ currentDotIndex: 0 });
      }
      return;
    }
    
    // 获取视图信息和课程总数
    const numCourses = this.data.courses.length;
    const query = wx.createSelectorQuery();
    query.select('.course-scroll').boundingClientRect();
    query.exec(res => {
      if (!res || !res[0]) return;
      
      const viewWidth = res[0].width;
      
      // 获取每一项大致宽度
      const itemWidth = 200; // 卡片宽度 + 外边距
      
      // 总的内容宽度估计
      const contentWidth = numCourses * itemWidth;
      
      // 可滑动的最大宽度
      const maxScrollWidth = contentWidth - viewWidth;
      
      // 如果可滑动宽度太小，不处理
      if (maxScrollWidth <= 10) return;
      
      // 计算滑动比例 (0-1范围)
      const scrollRatio = Math.min(1, Math.max(0, scrollLeft / maxScrollWidth));
      
      // 将滑动区域划分成多个部分
      // 根据课程数量动态调整点的分配
      let activeDot;
      
      if (numCourses <= 3) {
        // 如果课程数小于等于3，直接映射到对应点
        activeDot = Math.min(Math.floor(scrollRatio * numCourses), numCourses - 1);
      } else {
        // 如果课程数大于3，均匀分配3个点
        // 更精确的区间划分，确保每个点都有机会被激活
        if (scrollRatio < 0.2) {
          activeDot = 0;
        } else if (scrollRatio < 0.8) {
          activeDot = 1;
        } else {
          activeDot = 2;
        }
      }
      
      // 更灵敏地检测边界情况
      if (scrollRatio > 0.95) {
        activeDot = 2; // 接近末尾
      }
      
      if (scrollRatio < 0.05) {
        activeDot = 0; // 接近开始
      }
      
      // 更新导航点状态
      if (this.data.currentDotIndex !== activeDot) {
        this.setData({
          currentDotIndex: activeDot
        });
      }
    });
  },
  
  // 滚动到末尾
  onScrollToEnd: function(e) {
    // 滚动到末尾，直接设置最后一个导航点为活跃
    this.setData({
      currentDotIndex: 2
    });
  },
  
  // 滚动到开始
  onScrollToStart: function(e) {
    // 滚动到开始，直接设置第一个导航点为活跃
    this.setData({
      currentDotIndex: 0
    });
  },

  /**
   * 刷新数据方法 - 点击刷新按钮调用
   */
  refreshData: function() {
    // 防止重复刷新
    if (this.data.isRefreshing || this.data.isLoading) {
      console.log('已有刷新操作正在进行中');
      return;
    }
    
    // 立即触发旋转动画（添加点击动画效果）
    wx.createSelectorQuery()
      .select('.refresh-unified-icon')
      .fields({
        node: true,
        size: true,
      })
      .exec((res) => {
        if (res[0] && res[0].node) {
          // 如果能直接操作节点，添加一个临时旋转动画
          const animation = wx.createAnimation({
            duration: 300,
            timingFunction: 'ease',
          });
          animation.rotate(180).step();
        }
      });
    
    // 显示刷新动画
    this.setData({
      isRefreshing: true
    });
    
    // 显示加载中提示
    wx.showToast({
      title: '刷新中...',
      icon: 'loading',
      mask: true,
      duration: 2000
    });
    
    // 并发请求刷新数据
    Promise.all([
      this.fetchUserCourses(),
      this.fetchQuestions()
    ]).then(() => {
      console.log('数据刷新成功');
      
      // 显示成功提示
      wx.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1500
      });
      
    }).catch(err => {
      console.error('数据刷新失败:', err);
      
      // 显示失败提示
      wx.showToast({
        title: '刷新失败',
        icon: 'error',
        duration: 1500
      });
      
    }).finally(() => {
      // 无论成功失败，都停止刷新状态
      this.setData({
        isRefreshing: false
      });
    });
  },
  
  /**
   * 下拉刷新事件
   * 注意：如果想要完全禁用下拉刷新，需要在app.json或页面的index.json中设置
   * "enablePullDownRefresh": false
   */
  onPullDownRefresh: function() {
    // 检查猫是否正在拖动
    const catComponent = this.selectComponent('#cat-component');
    if (catComponent && catComponent.data.isDragging) {
      console.log('猫正在拖动，取消下拉刷新');
      wx.stopPullDownRefresh();
      return;
    }

    // 模拟点击刷新按钮
    this.refreshData();
    
    // 立即停止下拉刷新动画，由refreshData控制刷新状态
    wx.stopPullDownRefresh();
  }
}));
<view class="profile-container">
  <view class="profile-header">
    <text class="profile-title">个人资料</text>
  </view>

  <form bindsubmit="onSubmit">
    <view class="profile-avatar-section">
      <text class="section-title">头像</text>
      <button class="avatar-wrapper" open-type="chooseAvatar" bindchooseavatar="onChooseAvatar">
        <image class="avatar" src="{{avatarUrl}}"></image>
      </button>
      <text class="tips">点击头像可更换</text>
    </view>

    <view class="profile-nickname-section">
      <text class="section-title">昵称</text>
      <input 
        type="nickname" 
        name="nickname" 
        placeholder="请输入昵称" 
        value="{{nickName}}"
        class="nickname-input"
      />
      <text class="tips">设置后，将用于小程序中展示</text>
    </view>

    <view class="btn-section">
      <button class="save-btn" form-type="submit" type="primary">保存</button>
    </view>
  </form>
</view>

<!-- 加载中提示 -->
<view class="loading-mask" wx:if="{{isLoading}}">
  <view class="loading-content">
    <image class="loading-icon" src="/images/loading.gif"></image>
    <text>加载中...</text>
  </view>
</view> 
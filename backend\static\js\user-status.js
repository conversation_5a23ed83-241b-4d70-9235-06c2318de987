/**
 * 用户在线状态实时更新
 */
(function() {
    // 存储用户ID列表
    let userIds = [];
    
    // 收集页面上的所有用户ID
    function collectUserIds() {
        userIds = [];
        // 查找所有带有data-user-id属性的元素
        document.querySelectorAll('[data-user-id]').forEach(el => {
            const userId = el.getAttribute('data-user-id');
            if (userId && !userIds.includes(userId)) {
                userIds.push(userId);
            }
        });
        return userIds;
    }
    
    // 更新用户状态指示器
    function updateStatusIndicator(userId, isOnline) {
        // 查找该用户的所有状态指示器
        document.querySelectorAll(`.user-status-indicator[data-user-id="${userId}"]`).forEach(indicator => {
            // 更新状态类
            if (isOnline) {
                indicator.classList.add('user-online');
                indicator.classList.remove('user-offline');
                indicator.setAttribute('title', '在线');
            } else {
                indicator.classList.add('user-offline');
                indicator.classList.remove('user-online');
                indicator.setAttribute('title', '离线');
            }
        });
    }
    
    // 请求用户状态更新
    function fetchUserStatus() {
        const ids = collectUserIds();
        if (ids.length === 0) return;
        
        fetch('/admin/api/user-status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_ids: ids
            })
        })
        .then(response => response.json())
        .then(data => {
            // 更新所有用户的状态
            for (const [userId, status] of Object.entries(data)) {
                updateStatusIndicator(userId, status.is_online);
            }
        })
        .catch(error => {
            console.error('获取用户状态失败:', error);
        });
    }
    
    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 立即获取一次状态
        fetchUserStatus();
        
        // 每30秒更新一次状态
        setInterval(fetchUserStatus, 30000);
    });
})(); 
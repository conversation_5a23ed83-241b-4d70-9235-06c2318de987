// 用户答题数据趋势图表修复 - 未来科技风格
document.addEventListener('DOMContentLoaded', function() {
  // 查找图表容器
  const activityChartCanvas = document.getElementById('activityChart');
  if (!activityChartCanvas) {
    console.error('找不到用户答题数据趋势图表元素');
    return;
  }

  console.log('正在初始化科技感霓虹灯图表...');
  
  try {
    // 获取真实数据
    let chartData = null;
    
    // 尝试获取真实数据
    try {
      if (activityChartCanvas.dataset && activityChartCanvas.dataset.chartValues) {
        const rawData = JSON.parse(activityChartCanvas.dataset.chartValues);
        if (rawData.labels && rawData.datasets && rawData.datasets[0] && rawData.datasets[0].data) {
          chartData = {
            labels: rawData.labels,
            values: rawData.datasets[0].data
          };
          console.log('已获取真实数据:', chartData);
        }
      }
    } catch (e) {
      console.error('无法解析数据:', e);
    }
    
    // 如果没有真实数据，显示错误提示
    if (!chartData) {
      console.error('未找到有效数据，显示错误信息');
      showDataError(activityChartCanvas, '未能加载答题趋势数据');
      return;
    }
    
    // 设置容器样式
    const container = activityChartCanvas.parentElement;
    container.style.position = 'relative';
    container.style.overflow = 'hidden';
    container.style.borderRadius = '12px';
    container.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.15)';
    
    // 获取容器尺寸
    const width = container.clientWidth;
    const height = container.clientHeight || 300;
    
    // 设置画布尺寸
    activityChartCanvas.width = width;
    activityChartCanvas.height = height;
    
    // 创建暗色背景
    const darkBackground = document.createElement('div');
    darkBackground.style.position = 'absolute';
    darkBackground.style.top = '0';
    darkBackground.style.left = '0';
    darkBackground.style.width = '100%';
    darkBackground.style.height = '100%';
    darkBackground.style.backgroundColor = '#111827';
    darkBackground.style.zIndex = '-2';
    container.appendChild(darkBackground);
    
    // 创建背景网格
    const grid = document.createElement('div');
    grid.style.position = 'absolute';
    grid.style.top = '0';
    grid.style.left = '0';
    grid.style.width = '100%';
    grid.style.height = '100%';
    grid.style.backgroundImage = 'linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px)';
    grid.style.backgroundSize = '20px 20px';
    grid.style.opacity = '0.4';
    grid.style.zIndex = '-1';
    container.appendChild(grid);
    
    // 创建发光粒子
    for (let i = 0; i < 15; i++) {
      const particle = document.createElement('div');
      const size = Math.random() * 6 + 2;
      const posX = Math.random() * 100;
      const posY = Math.random() * 100;
      const hue = Math.random() * 60 + 220; // 蓝色调范围
      
      particle.style.position = 'absolute';
      particle.style.width = `${size}px`;
      particle.style.height = `${size}px`;
      particle.style.borderRadius = '50%';
      particle.style.backgroundColor = `hsla(${hue}, 100%, 70%, 0.8)`;
      particle.style.boxShadow = `0 0 ${size*2}px ${size}px hsla(${hue}, 100%, 60%, 0.4)`;
      particle.style.left = `${posX}%`;
      particle.style.top = `${posY}%`;
      particle.style.opacity = Math.random() * 0.5 + 0.3;
      particle.style.zIndex = '-1';
      
      // 创建浮动动画
      const animName = `float-${i}`;
      const keyframes = `
        @keyframes ${animName} {
          0% { transform: translate(0, 0) scale(1); opacity: ${Math.random() * 0.3 + 0.3}; }
          50% { transform: translate(${Math.random() * 30 - 15}px, ${Math.random() * 30 - 15}px) scale(${Math.random() * 0.5 + 0.8}); opacity: ${Math.random() * 0.5 + 0.5}; }
          100% { transform: translate(0, 0) scale(1); opacity: ${Math.random() * 0.3 + 0.3}; }
        }
      `;
      
      const style = document.createElement('style');
      style.textContent = keyframes;
      document.head.appendChild(style);
      
      particle.style.animation = `${animName} ${Math.random() * 5 + 8}s ease-in-out infinite`;
      container.appendChild(particle);
    }
    
    // 绘制图表
    const ctx = activityChartCanvas.getContext('2d');
    
    // 创建图表实例
    const chart = new Chart(ctx, {
      type: 'line',
      data: {
        labels: chartData.labels,
        datasets: [{
          label: '答题趋势',
          data: chartData.values,
          borderColor: ctx => {
            // 创建荧光边框渐变
            const gradient = ctx.createLinearGradient(0, 0, width, 0);
            gradient.addColorStop(0, 'rgba(99, 102, 241, 0.8)');    // 淡紫色
            gradient.addColorStop(0.5, 'rgba(59, 130, 246, 0.8)');  // 蓝色
            gradient.addColorStop(1, 'rgba(6, 182, 212, 0.8)');     // 青色
            return gradient;
          },
          borderWidth: 3,
          fill: false,
          tension: 0.4,
          pointBackgroundColor: 'rgb(219, 234, 254)', // 淡蓝色点
          pointBorderColor: 'rgb(37, 99, 235)',       // 蓝色边框
          pointBorderWidth: 2,
          pointRadius: 6,
          pointHoverRadius: 9,
          pointHoverBackgroundColor: 'rgb(255, 255, 255)',  // 白色高亮
          pointHoverBorderColor: 'rgb(79, 70, 229)',        // 紫色边框
          pointHoverBorderWidth: 3,
          segment: {
            borderColor: ctx => {
              // 段落颜色基于值的正负
              const value1 = ctx.p0.parsed.y;
              const value2 = ctx.p1.parsed.y;
              
              // 如果两个点都在零线同侧
              if ((value1 >= 0 && value2 >= 0) || (value1 < 0 && value2 < 0)) {
                return value1 >= 0 ? 'rgba(37, 99, 235, 0.8)' : 'rgba(239, 68, 68, 0.8)';
              }
              
              // 如果跨零线，使用渐变
              const gradient = ctx.chart.ctx.createLinearGradient(
                ctx.p0.x, ctx.p0.y, ctx.p1.x, ctx.p1.y
              );
              gradient.addColorStop(0, value1 >= 0 ? 'rgba(37, 99, 235, 0.8)' : 'rgba(239, 68, 68, 0.8)');
              gradient.addColorStop(1, value2 >= 0 ? 'rgba(37, 99, 235, 0.8)' : 'rgba(239, 68, 68, 0.8)');
              return gradient;
            }
          }
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        backgroundColor: 'transparent',
        layout: {
          padding: {
            left: 10,
            right: 20,
            top: 20,
            bottom: 10
          }
        },
        animation: {
          duration: 2000,
          easing: 'easeOutQuart'
        },
        scales: {
          y: {
            grid: {
              color: 'rgba(148, 163, 184, 0.1)',
              drawBorder: false
            },
            ticks: {
              padding: 8,
              color: 'rgba(226, 232, 240, 0.8)',
              font: {
                family: "'Inter', sans-serif",
                size: 11
              },
              callback: function(value) {
                return value > 0 ? '+' + value : value;
              }
            },
            border: {
              display: false
            }
          },
          x: {
            grid: {
              display: false,
              drawBorder: false
            },
            ticks: {
              padding: 8,
              color: 'rgba(226, 232, 240, 0.8)',
              font: {
                family: "'Inter', sans-serif",
                size: 11
              }
            },
            border: {
              display: false
            }
          }
        },
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            backgroundColor: 'rgba(17, 24, 39, 0.9)',
            titleColor: 'rgba(255, 255, 255, 1)',
            bodyColor: 'rgba(226, 232, 240, 0.9)',
            borderColor: 'rgba(79, 70, 229, 0.5)',
            borderWidth: 1,
            padding: 10,
            cornerRadius: 8,
            displayColors: false,
            titleFont: {
              family: "'Inter', sans-serif",
              size: 13,
              weight: '600'
            },
            bodyFont: {
              family: "'Inter', sans-serif",
              size: 12
            },
            callbacks: {
              title: function(context) {
                return context[0].label + ' 数据';
              },
              label: function(context) {
                const value = context.parsed.y;
                const sign = value >= 0 ? '+' : '';
                return `变化: ${sign}${value}`;
              },
              afterLabel: function(context) {
                const value = context.parsed.y;
                if (value > 0) {
                  return '↗ 上升趋势';
                } else if (value < 0) {
                  return '↘ 下降趋势';
                } else {
                  return '→ 持平';
                }
              }
            }
          }
        },
        elements: {
          line: {
            borderCapStyle: 'round',
            borderJoinStyle: 'round'
          },
          point: {
            hitRadius:
            10
          }
        },
        interaction: {
          mode: 'index',
          intersect: false
        }
      },
      plugins: [{
        id: 'glowEffects',
        beforeDraw: function(chart) {
          // 全局阴影设置
          const ctx = chart.ctx;
          ctx.shadowColor = 'rgba(37, 99, 235, 0.6)';
          ctx.shadowBlur = 15;
        },
        afterDatasetsDraw: function(chart) {
          const ctx = chart.ctx;
          const dataset = chart.data.datasets[0];
          const meta = chart.getDatasetMeta(0);
          
          // 重置阴影以避免重复效果
          ctx.shadowBlur = 0;
          
          // 绘制荧光效果
          for (let i = 0; i < meta.data.length - 1; i++) {
            const current = meta.data[i];
            const next = meta.data[i + 1];
            const value = dataset.data[i];
            
            // 根据值的正负选择颜色
            const glowColor = value >= 0 ? 
              'rgba(37, 99, 235, 0.6)' : 
              'rgba(239, 68, 68, 0.6)';
            
            ctx.save();
            ctx.strokeStyle = glowColor;
            ctx.lineWidth = 3;
            ctx.shadowColor = glowColor;
            ctx.shadowBlur = 10;
            ctx.beginPath();
            ctx.moveTo(current.x, current.y);
            ctx.lineTo(next.x, next.y);
            ctx.stroke();
            ctx.restore();
          }
          
          // 绘制点的荧光效果
          meta.data.forEach((point, i) => {
            const value = dataset.data[i];
            const glowColor = value >= 0 ? 
              'rgba(37, 99, 235, 0.8)' : 
              'rgba(239, 68, 68, 0.8)';
            
            ctx.save();
            ctx.shadowColor = glowColor;
            ctx.shadowBlur = 15;
            ctx.fillStyle = value >= 0 ? 
              'rgba(219, 234, 254, 0.9)' : 
              'rgba(254, 202, 202, 0.9)';
            ctx.beginPath();
            ctx.arc(point.x, point.y, 4, 0, Math.PI * 2);
            ctx.fill();
            ctx.restore();
          });
        }
      }]
    });
    
    // 添加点击效果
    activityChartCanvas.addEventListener('click', function(e) {
      const rect = activityChartCanvas.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      
      // 创建点击波纹
      const ripple = document.createElement('div');
      ripple.style.position = 'absolute';
      ripple.style.left = (x - 25) + 'px';
      ripple.style.top = (y - 25) + 'px';
      ripple.style.width = '50px';
      ripple.style.height = '50px';
      ripple.style.borderRadius = '50%';
      ripple.style.backgroundColor = 'rgba(255, 255, 255, 0)';
      ripple.style.boxShadow = '0 0 15px 2px rgba(147, 197, 253, 0.8)';
      ripple.style.pointerEvents = 'none';
      ripple.style.zIndex = '10';
      ripple.style.animation = 'chartRipple 0.8s ease-out';
      
      // 添加动画样式
      const style = document.createElement('style');
      style.textContent = `
        @keyframes chartRipple {
          0% { transform: scale(0.3); opacity: 1; }
          100% { transform: scale(2); opacity: 0; }
        }
      `;
      document.head.appendChild(style);
      
      container.appendChild(ripple);
      
      // 自动移除
      setTimeout(() => {
        container.removeChild(ripple);
        document.head.removeChild(style);
      }, 800);
    });
    
    // 添加悬停效果
    activityChartCanvas.addEventListener('mousemove', function(e) {
      const points = chart.getElementsAtEventForMode(e, 'nearest', { intersect: false }, true);
      
      if (points.length) {
        const point = points[0];
        const dataset = chart.data.datasets[point.datasetIndex];
        const dataIndex = point.index;
        
        // 高亮当前点
        activityChartCanvas.style.cursor = 'pointer';
        
        if (chart.lastHoveredIndex !== dataIndex) {
          // 重置所有点的大小
          const originalSize = Array(dataset.data.length).fill(6);
          
          // 放大当前点
          originalSize[dataIndex] = 8;
          dataset.pointRadius = originalSize;
          
          chart.lastHoveredIndex = dataIndex;
          chart.update('none'); // 轻量级更新
        }
      } else {
        activityChartCanvas.style.cursor = 'default';
        
        if (chart.lastHoveredIndex !== undefined) {
          chart.data.datasets[0].pointRadius = 6;
          chart.lastHoveredIndex = undefined;
          chart.update('none');
        }
      }
    });
    
    // 添加信息提示
    const tooltip = document.createElement('div');
    tooltip.style.position = 'absolute';
    tooltip.style.top = '10px';
    tooltip.style.right = '10px';
    tooltip.style.backgroundColor = 'rgba(17, 24, 39, 0.7)';
    tooltip.style.color = 'rgba(255, 255, 255, 0.9)';
    tooltip.style.padding = '6px 10px';
    tooltip.style.borderRadius = '4px';
    tooltip.style.fontSize = '12px';
    tooltip.style.boxShadow = '0 0 10px rgba(37, 99, 235, 0.3)';
    tooltip.style.opacity = '0';
    tooltip.style.transition = 'opacity 0.5s';
    tooltip.textContent = '点击查看动画效果';
    
    container.appendChild(tooltip);
    
    // 显示提示，然后淡出
    setTimeout(() => {
      tooltip.style.opacity = '1';
      setTimeout(() => {
        tooltip.style.opacity = '0';
        setTimeout(() => container.removeChild(tooltip), 500);
      }, 3000);
    }, 800);
    
    // 保存图表实例以便外部访问
    activityChartCanvas.chart = chart;
    
    console.log('科技感霓虹灯图表初始化成功');
  } catch (error) {
    console.error('初始化图表失败:', error);
    showDataError(activityChartCanvas, '图表初始化失败');
  }
});

// 显示数据错误提示
function showDataError(canvas, message) {
  const parentElement = canvas.parentNode;
  const errorDiv = document.createElement('div');
  errorDiv.className = 'chart-error';
  errorDiv.style.width = '100%';
  errorDiv.style.height = '300px';
  errorDiv.style.display = 'flex';
  errorDiv.style.flexDirection = 'column';
  errorDiv.style.alignItems = 'center';
  errorDiv.style.justifyContent = 'center';
  errorDiv.style.backgroundColor = '#1e293b';
  errorDiv.style.borderRadius = '12px';
  errorDiv.style.color = '#e2e8f0';
  errorDiv.style.padding = '20px';
  errorDiv.style.textAlign = 'center';
  
  // 添加错误图标
  const iconElement = document.createElement('div');
  iconElement.innerHTML = '<i class="fas fa-exclamation-triangle" style="font-size: 2.5rem; color: #f59e0b; margin-bottom: 1rem;"></i>';
  
  // 添加错误信息
  const messageElement = document.createElement('div');
  messageElement.textContent = message || '数据加载失败';
  messageElement.style.marginBottom = '1rem';
  messageElement.style.fontSize = '14px';
  
  // 添加刷新按钮
  const refreshButton = document.createElement('button');
  refreshButton.className = 'btn btn-sm btn-primary';
  refreshButton.innerHTML = '<i class="fas fa-sync-alt me-1"></i> 刷新数据';
  refreshButton.style.backgroundColor = '#3b82f6';
  refreshButton.style.border = 'none';
  refreshButton.style.padding = '6px 12px';
  refreshButton.style.borderRadius = '4px';
  refreshButton.style.cursor = 'pointer';
  refreshButton.onclick = function() {
    window.location.reload();
  };
  
  errorDiv.appendChild(iconElement);
  errorDiv.appendChild(messageElement);
  errorDiv.appendChild(refreshButton);
  
  parentElement.replaceChild(errorDiv, canvas);
} 

// 用户活跃度分析图表修复
document.addEventListener('DOMContentLoaded', function() {
  // 查找用户活跃度分析图表容器
  const userActivityChartCanvas = document.getElementById('userActivityChart');
  if (!userActivityChartCanvas) {
    console.error('找不到用户活跃度分析图表元素');
    return;
  }

  console.log('正在初始化用户活跃度分析图表...');
  
  try {
    // 获取真实数据
    let chartData = null;
    
    // 尝试获取真实数据
    try {
      if (userActivityChartCanvas.dataset && userActivityChartCanvas.dataset.chartValues) {
        const rawData = JSON.parse(userActivityChartCanvas.dataset.chartValues);
        if (rawData.labels && rawData.datasets && rawData.datasets[0] && rawData.datasets[0].data) {
          chartData = {
            labels: rawData.labels,
            datasets: rawData.datasets
          };
          console.log('已获取用户活跃度分析数据:', chartData);
        }
      }
    } catch (e) {
      console.error('无法解析用户活跃度分析数据:', e);
    }
    
    // 如果没有真实数据，显示错误提示
    if (!chartData) {
      console.error('未找到有效的用户活跃度分析数据，显示错误信息');
      showDataError(userActivityChartCanvas, '未能加载用户活跃度分析数据');
      return;
    }
    
    // 设置容器样式
    const container = userActivityChartCanvas.parentElement;
    container.style.position = 'relative';
    container.style.minHeight = '250px';
    container.style.height = '100%';
    container.style.width = '100%';
    
    // 绘制图表
    const ctx = userActivityChartCanvas.getContext('2d');
    
    // 创建图表实例
    const chart = new Chart(ctx, {
      type: 'line',
      data: chartData,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        animation: {
          duration: 1200,
          easing: 'easeOutQuart'
        },
        layout: {
          padding: {
            left: 10,
            right: 20,
            top: 20,
            bottom: 10
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            grid: {
              color: 'rgba(0, 0, 0, 0.05)',
              drawBorder: false
            },
            ticks: {
              padding: 10,
              font: {
                size: 12,
                family: "'Inter', 'Helvetica', 'Arial', sans-serif"
              },
              color: '#64748b'
            }
          },
          x: {
            grid: {
              display: false,
              drawBorder: false
            },
            ticks: {
              padding: 10,
              font: {
                size: 12,
                family: "'Inter', 'Helvetica', 'Arial', sans-serif"
              },
              color: '#64748b'
            }
          }
        },
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            backgroundColor: '#1e293b',
            titleColor: '#ffffff',
            bodyColor: '#e2e8f0',
            padding: 12,
            cornerRadius: 8,
            displayColors: false,
            callbacks: {
              label: function(context) {
                return `活跃用户: ${context.raw} 人`;
              }
            }
          }
        }
      }
    });
    
    // 添加发光线条特效
    const gradient = ctx.createLinearGradient(0, 0, 0, ctx.canvas.height);
    gradient.addColorStop(0, 'rgba(79, 70, 229, 0.4)');
    gradient.addColorStop(1, 'rgba(79, 70, 229, 0.0)');
    
    // 更新图表配置
    chart.data.datasets[0].backgroundColor = gradient;
    chart.update();
    
    // 为图表添加波纹动画效果
    const chartContainer = userActivityChartCanvas.closest('.chart-container');
    if (chartContainer) {
      chartContainer.addEventListener('click', function(e) {
        // 创建波纹元素
        const ripple = document.createElement('div');
        ripple.classList.add('chart-ripple');
        ripple.style.position = 'absolute';
        ripple.style.width = '20px';
        ripple.style.height = '20px';
        ripple.style.background = 'rgba(79, 70, 229, 0.2)';
        ripple.style.borderRadius = '50%';
        ripple.style.transformOrigin = 'center';
        ripple.style.pointerEvents = 'none';
        ripple.style.zIndex = '1';
        
        // 定位波纹
        const rect = chartContainer.getBoundingClientRect();
        ripple.style.left = (e.clientX - rect.left - 10) + 'px';
        ripple.style.top = (e.clientY - rect.top - 10) + 'px';
        
        // 添加波纹元素
        chartContainer.appendChild(ripple);
        
        // 动画样式
        const style = document.createElement('style');
        style.textContent = `
          @keyframes chartRipple {
            0% {
              transform: scale(1);
              opacity: 0.8;
            }
            100% {
              transform: scale(20);
              opacity: 0;
            }
          }
        `;
        document.head.appendChild(style);
        
        // 应用动画
        ripple.style.animation = 'chartRipple 600ms linear';
        
        // 动画结束后移除
        setTimeout(() => {
          chartContainer.removeChild(ripple);
          document.head.removeChild(style);
        }, 600);
      });
    }
    
    console.log('用户活跃度分析图表初始化成功');
  } catch (err) {
    console.error('初始化用户活跃度分析图表时出错:', err);
    if (userActivityChartCanvas) {
      showDataError(userActivityChartCanvas, '图表加载失败');
    }
  }
}); 
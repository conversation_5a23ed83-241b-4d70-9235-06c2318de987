// 全局通知页面混入
const app = getApp()

const globalNotificationMixin = {
  data: {
    // 全局通知相关数据
    showGlobalNotification: false,
    globalNotificationTitle: '',
    globalNotificationMessage: '',
    globalNotificationPostId: null,
    globalNotificationId: null,
    lastShownNotificationId: null // 记录当前页面最后显示的通知ID
  },

  onLoad: function() {
    // 页面加载时检查是否有待显示的全局通知
    this.checkPendingGlobalNotification()
  },

  onShow: function() {
    // 页面显示时检查是否有待显示的全局通知
    this.checkPendingGlobalNotification()
  },

  // 检查待显示的全局通知
  checkPendingGlobalNotification: function() {
    if (app.globalData.notificationSystem.showNotification &&
        app.globalData.notificationSystem.notificationData) {
      const notificationId = app.globalData.notificationSystem.notificationData.notificationId

      // 检查这个通知是否已经在当前页面显示过
      if (!this.data.lastShownNotificationId || this.data.lastShownNotificationId !== notificationId) {
        this.showGlobalNotification(app.globalData.notificationSystem.notificationData)
        // 记录当前页面已显示的通知ID
        this.setData({ lastShownNotificationId: notificationId })
      }
    }
  },

  // 显示全局通知
  showGlobalNotification: function(notificationData) {
    this.setData({
      showGlobalNotification: true,
      globalNotificationTitle: notificationData.title,
      globalNotificationMessage: notificationData.message,
      globalNotificationPostId: notificationData.postId,
      globalNotificationId: notificationData.notificationId
    })
  },

  // 更新全局通知（app.js调用的方法）
  updateGlobalNotification: function() {
    this.checkPendingGlobalNotification()
  },

  // 隐藏全局通知
  hideGlobalNotification: function() {
    this.setData({
      showGlobalNotification: false,
      globalNotificationTitle: '',
      globalNotificationMessage: '',
      globalNotificationPostId: null,
      globalNotificationId: null
    })
  },

  // 处理全局通知点击
  onGlobalNotificationTap: function() {
    const postId = this.data.globalNotificationPostId
    const notificationId = this.data.globalNotificationId
    
    if (postId) {
      app.handleNotificationTap(postId, notificationId)
    }
  },

  // 处理全局通知隐藏
  onGlobalNotificationHide: function() {
    this.hideGlobalNotification()
    app.hideGlobalNotification()
  }
}

module.exports = globalNotificationMixin

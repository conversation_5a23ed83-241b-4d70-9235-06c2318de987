// class-selection.js
const app = getApp();

Page({
  data: {
    classes: [],
    loading: true,
    error: false,
    errorMsg: '',
    selectedClassId: null,
    selectedClassName: '',
    submitting: false
  },

  onLoad: function() {
    // 检查是否已有班级
    this.checkCurrentClass();
    // 获取班级列表
    this.loadClasses();
  },

  // 检查用户当前班级
  checkCurrentClass: function() {
    const that = this;

    // 使用专用API来获取用户当前班级信息
    wx.request({
      url: app.globalData.baseUrl + '/classes/current',
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        console.log('获取用户班级信息:', res.data);
        
        if (res.data && res.data.status === 'success' && res.data.has_class) {
          // 用户已经选择了班级，直接跳转到主页
          wx.showToast({
            title: '您已加入班级',
            icon: 'success'
          });
          
          setTimeout(() => {
            wx.switchTab({
              url: '/pages/index/index'
            });
          }, 1000);
        }
      },
      fail: (err) => {
        console.error('检查当前班级失败:', err);
        // 如果检查失败，不进行处理，让用户选择班级
      }
    });
  },

  // 加载班级列表
  loadClasses: function() {
    const that = this;
    
    this.setData({
      loading: true,
      error: false,
      errorMsg: ''
    });

    console.log('正在获取班级列表...');
    
    // 先尝试创建测试班级（如果没有班级数据）
    app.createTestClasses()
      .then(() => {
        // 然后获取班级列表
        return that.fetchClassList();
      })
      .catch(err => {
        console.error('创建测试班级失败:', err);
        // 即使创建失败，仍然尝试获取班级列表
        return that.fetchClassList();
      });
  },
  
  // 获取班级列表
  fetchClassList: function() {
    const that = this;
    
    return new Promise((resolve, reject) => {
      wx.request({
        url: app.globalData.baseUrl + '/public/classes',
        method: 'GET',
        success: (res) => {
          console.log('获取班级列表:', res.data);
          
          if (res.data && Array.isArray(res.data)) {
            that.setData({
              classes: res.data,
              loading: false
            });
            resolve(res.data);
          } else {
            console.error('班级列表数据格式不正确:', res.data);
            that.setData({
              error: true,
              errorMsg: '获取班级列表失败，返回格式不正确',
              loading: false
            });
            
            wx.showToast({
              title: '获取班级列表失败',
              icon: 'none'
            });
            reject(new Error('返回格式不正确'));
          }
        },
        fail: (err) => {
          console.error('获取班级列表失败:', err);
          
          that.setData({
            error: true,
            errorMsg: '网络错误，请稍后重试',
            loading: false
          });
          
          wx.showToast({
            title: '网络错误',
            icon: 'none'
          });
          reject(err);
        }
      });
    });
  },

  // 选择班级
  selectClass: function(e) {
    const classId = e.currentTarget.dataset.id;
    const className = e.currentTarget.dataset.name;
    
    console.log('选择班级:', classId, className);
    
    this.setData({
      selectedClassId: classId,
      selectedClassName: className
    });
  },

  // 确认班级选择
  confirmSelection: function() {
    if (!this.data.selectedClassId || this.data.submitting) {
      return;
    }
    
    this.setData({ submitting: true });
    
    const that = this;
    
    // 使用专用API保存用户选择的班级
    wx.request({
      url: app.globalData.baseUrl + '/user/select-class',
      method: 'POST',
      header: {
        'Authorization': app.globalData.token,
        'Content-Type': 'application/json'
      },
      data: {
        class_id: that.data.selectedClassId
      },
      success: (res) => {
        console.log('班级选择提交结果:', res.data);
        
        if (res.data.status === 'success') {
          // 更新全局用户信息中的班级ID
          if (app.globalData.userInfo) {
            app.globalData.userInfo.class_id = that.data.selectedClassId;
          }
          
          wx.showToast({
            title: '班级选择成功',
            icon: 'success',
            duration: 1500
          });
          
          // 延迟跳转到主页
          setTimeout(() => {
            wx.switchTab({
              url: '/pages/index/index'
            });
          }, 1500);
        } else {
          that.setData({
            error: true,
            errorMsg: res.data.error || '班级选择失败'
          });
          
          wx.showToast({
            title: '班级选择失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('班级选择请求失败:', err);
        
        that.setData({
          error: true,
          errorMsg: '网络错误，请稍后重试'
        });
        
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      },
      complete: () => {
        that.setData({ submitting: false });
      }
    });
  }
}); 
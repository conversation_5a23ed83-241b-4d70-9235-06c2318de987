from flask import Blueprint, request, jsonify
import datetime
import json
import sqlite3
import os
import uuid
from sqlalchemy import text

cat_circle_bp = Blueprint('cat_circle', __name__, url_prefix='/api/cat_circle')

db = None
User = None
SensitiveWord = None
CatCirclePost = None
CatCircleLike = None
CatCircleCollection = None
CatCircleComment = None
CatCircleView = None
CommentNotification = None

def get_user(user_id):
    """使用原始SQL查询获取用户，替代User.query.get"""
    try:
        sql = text("SELECT id, openid, nickname, avatar, created_at, is_active, is_online, last_active, class_id FROM user WHERE id = :user_id")
        result = db.session.execute(sql, {"user_id": user_id}).fetchone()
        if result:
            return {
                'id': result[0],
                'openid': result[1],
                'nickname': result[2],
                'avatar': result[3],
                'created_at': result[4],
                'is_active': result[5],
                'is_online': result[6],
                'last_active': result[7],
                'class_id': result[8]
            }
        return None
    except Exception as e:
        print(f"获取用户失败: {e}")
        return None

def get_user_info(user_id):
    """使用直接SQL查询获取用户信息，避免SQLAlchemy上下文问题"""
    try:
        # 使用原始SQL查询获取用户信息
        sql = text("SELECT id, nickname, avatar FROM user WHERE id = :user_id")
        result = db.session.execute(sql, {"user_id": user_id}).fetchone()
        if result:
            return {
                'id': result[0],
                'nickname': result[1] or f'用户{user_id}',
                'avatar': result[2] or '/images/default-avatar.png'
            }
        return {
            'id': user_id,
            'nickname': f'用户{user_id}',
            'avatar': '/images/default-avatar.png'
        }
    except Exception as e:
        print(f"获取用户信息失败: {e}")
        return {
            'id': user_id,
            'nickname': f'用户{user_id}',
            'avatar': '/images/default-avatar.png'
        }

def init_cat_circle_api(database):
    global db, User, SensitiveWord, CatCirclePost, CatCircleLike, CatCircleCollection, CatCircleComment, CatCircleView, CommentNotification
    db = database
    
    # Import User model from app.py - wrap in try/except to handle import errors
    try:
        from app import User as UserModel
        User = UserModel
        print("成功导入User模型")
    except Exception as e:
        # Fallback to None if import fails
        User = None
        print(f"导入User模型失败: {e}")
        print("将使用SQL查询方式获取用户信息")
    
    class SensitiveWord(db.Model):
        __tablename__ = 'sensitive_words'
        id = db.Column(db.Integer, primary_key=True)
        word = db.Column(db.String(50), nullable=False, unique=True)
        type = db.Column(db.String(20), default='normal')
        created_at = db.Column(db.DateTime, default=datetime.datetime.now)
        updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)

    class CatCirclePost(db.Model):
        __tablename__ = 'cat_circle_posts'
        id = db.Column(db.Integer, primary_key=True)
        user_id = db.Column(db.Integer, nullable=False)  # 移除外键约束
        content = db.Column(db.Text, nullable=False)
        images = db.Column(db.Text)
        type = db.Column(db.String(20), nullable=False, default='dynamic')
        contact_info = db.Column(db.String(200))
        gender = db.Column(db.String(10))
        status = db.Column(db.String(20), default='active')
        view_count = db.Column(db.Integer, default=0)
        like_count = db.Column(db.Integer, default=0)
        comment_count = db.Column(db.Integer, default=0)
        collect_count = db.Column(db.Integer, default=0)
        created_at = db.Column(db.DateTime, default=datetime.datetime.now)
        updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)

        def to_dict(self, current_user_id=None):
            images = []
            if self.images:
                try:
                    images = json.loads(self.images)
                except:
                    images = []

            result = {
                'id': self.id,
                'user_id': self.user_id,
                'content': self.content,
                'images': images,
                'type': self.type,
                'contact_info': self.contact_info,
                'gender': self.gender,
                'status': self.status,
                'view_count': self.view_count,
                'like_count': self.like_count,
                'comment_count': self.comment_count,
                'collect_count': self.collect_count,
                'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
                'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            }

            # 如果有额外字段，尝试添加
            try:
                if hasattr(self, 'price'):
                    result['price'] = self.price
                if hasattr(self, 'location'):
                    result['location'] = self.location
                if hasattr(self, 'time'):
                    result['time'] = self.time
            except:
                pass

            # 用户信息将在API路由中添加
            result['user_info'] = get_user_info(self.user_id)

            if current_user_id:
                result['is_liked'] = CatCircleLike.query.filter_by(
                    user_id=current_user_id, post_id=self.id
                ).first() is not None
                result['is_collected'] = CatCircleCollection.query.filter_by(
                    user_id=current_user_id, post_id=self.id
                ).first() is not None
            else:
                result['is_liked'] = False
                result['is_collected'] = False

            return result

    class CatCircleLike(db.Model):
        __tablename__ = 'cat_circle_likes'
        id = db.Column(db.Integer, primary_key=True)
        user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
        post_id = db.Column(db.Integer, db.ForeignKey('cat_circle_posts.id'), nullable=False)
        created_at = db.Column(db.DateTime, default=datetime.datetime.now)

        __table_args__ = (db.UniqueConstraint('user_id', 'post_id', name='unique_user_post_like'),)

    class CatCircleCollection(db.Model):
        __tablename__ = 'cat_circle_collections'
        id = db.Column(db.Integer, primary_key=True)
        user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
        post_id = db.Column(db.Integer, db.ForeignKey('cat_circle_posts.id'), nullable=False)
        created_at = db.Column(db.DateTime, default=datetime.datetime.now)

        __table_args__ = (db.UniqueConstraint('user_id', 'post_id', name='unique_user_post_collect'),)

    class CatCircleComment(db.Model):
        __tablename__ = 'cat_circle_comments'
        id = db.Column(db.Integer, primary_key=True)
        user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
        post_id = db.Column(db.Integer, db.ForeignKey('cat_circle_posts.id'), nullable=False)
        parent_id = db.Column(db.Integer, db.ForeignKey('cat_circle_comments.id'))
        reply_to_user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
        content = db.Column(db.Text, nullable=False)
        status = db.Column(db.String(20), default='active')
        created_at = db.Column(db.DateTime, default=datetime.datetime.now)
        updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)

        def to_dict(self):
            result = {
                'id': self.id,
                'user_id': self.user_id,
                'post_id': self.post_id,
                'parent_id': self.parent_id,
                'reply_to_user_id': self.reply_to_user_id,
                'content': self.content,
                'status': self.status,
                'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            }

            # 用户信息将在API路由中添加
            result['user_info'] = get_user_info(self.user_id)

            # 回复用户信息将在API路由中添加
            if self.reply_to_user_id:
                result['reply_to_user_info'] = get_user_info(self.reply_to_user_id)

            return result

    class CatCircleView(db.Model):
        __tablename__ = 'cat_circle_views'
        id = db.Column(db.Integer, primary_key=True)
        user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
        post_id = db.Column(db.Integer, db.ForeignKey('cat_circle_posts.id'), nullable=False)
        created_at = db.Column(db.DateTime, default=datetime.datetime.now)

        __table_args__ = (db.UniqueConstraint('user_id', 'post_id', name='unique_user_post_view'),)

    class CommentNotification(db.Model):
        __tablename__ = 'comment_notifications'
        id = db.Column(db.Integer, primary_key=True)
        user_id = db.Column(db.Integer, nullable=False)  # 接收通知的用户ID
        post_id = db.Column(db.Integer, nullable=False)  # 被评论的动态ID
        comment_id = db.Column(db.Integer, nullable=False)  # 评论ID
        commenter_id = db.Column(db.Integer, nullable=False)  # 评论者ID
        comment_content = db.Column(db.Text, nullable=False)  # 评论内容
        notification_type = db.Column(db.String(20), default='comment')  # 通知类型：comment/reply
        is_read = db.Column(db.Boolean, default=False)  # 是否已读
        read_at = db.Column(db.DateTime)  # 阅读时间
        status = db.Column(db.String(20), default='active')
        created_at = db.Column(db.DateTime, default=datetime.datetime.now)
        updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)

        def to_dict(self):
            return {
                'id': self.id,
                'user_id': self.user_id,
                'post_id': self.post_id,
                'comment_id': self.comment_id,
                'commenter_id': self.commenter_id,
                'comment_content': self.comment_content,
                'notification_type': self.notification_type,
                'is_read': self.is_read,
                'read_at': self.read_at.strftime('%Y-%m-%d %H:%M:%S') if self.read_at else None,
                'status': self.status,
                'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
                'commenter_info': get_user_info(self.commenter_id)
            }

    globals()['SensitiveWord'] = SensitiveWord
    globals()['CatCirclePost'] = CatCirclePost
    globals()['CatCircleLike'] = CatCircleLike
    globals()['CatCircleCollection'] = CatCircleCollection
    globals()['CatCircleComment'] = CatCircleComment
    globals()['CatCircleView'] = CatCircleView
    globals()['CommentNotification'] = CommentNotification

def check_sensitive_words(content):
    if not content:
        return False, []

    sensitive_words = SensitiveWord.query.all()
    found_words = []

    content_lower = content.lower()
    for word_obj in sensitive_words:
        word = word_obj.word.lower()
        if word in content_lower:
            found_words.append(word_obj.word)

    return len(found_words) > 0, found_words

@cat_circle_bp.route('/posts', methods=['POST'])
def create_post():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'code': 400, 'message': '请求数据不能为空'}), 400

        user_id = data.get('user_id')
        if not user_id:
            return jsonify({'code': 400, 'message': '用户ID不能为空'}), 400

        # 验证用户是否存在
        try:
            user_info = get_user_info(user_id)
            if not user_info:
                return jsonify({'code': 404, 'message': '用户不存在'}), 404
        except Exception as e:
            print(f"用户验证错误: {e}")
            # 如果SQL查询失败，我们仍然允许发布，但记录警告
            print("警告: 无法验证用户，可能User表不存在或数据库连接问题")
            
        content = data.get('content', '').strip()
        if not content:
            return jsonify({'code': 400, 'message': '发布内容不能为空'}), 400

        # 检查敏感词（如果敏感词表存在）
        try:
            has_sensitive, sensitive_words = check_sensitive_words(content)
            if has_sensitive:
                return jsonify({
                    'code': 400,
                    'message': f'发布失败，包含敏感词汇：{", ".join(sensitive_words)}'
                }), 400
        except Exception as e:
            print(f"敏感词检查错误: {e}")
            # 如果敏感词表不存在，跳过检查

        post_type = data.get('type', 'dynamic')
        valid_types = ['dynamic', 'secondhand', 'help', 'lost_found', 'cat_friend', 'campus_run']
        if post_type not in valid_types:
            return jsonify({'code': 400, 'message': '无效的发布类型'}), 400

        images = data.get('images', [])
        if len(images) > 10:
            return jsonify({'code': 400, 'message': '图片数量不能超过10张'}), 400

        contact_info = data.get('contact_info')
        gender = data.get('gender')
        price = data.get('price')
        location = data.get('location')
        time = data.get('time')

        # 验证必填字段
        if post_type in ['secondhand', 'help', 'lost_found', 'campus_run'] and not contact_info:
            return jsonify({'code': 400, 'message': '该类型发布需要填写联系方式'}), 400

        if post_type == 'cat_friend' and not gender:
            return jsonify({'code': 400, 'message': '猫友类型需要选择性别'}), 400

        if post_type == 'secondhand' and not price:
            return jsonify({'code': 400, 'message': '二手交易需要填写价格'}), 400

        if post_type == 'campus_run' and (not location or not time):
            return jsonify({'code': 400, 'message': '校园跑需要填写地点和时间'}), 400

        print("创建动态对象")

        # 将额外信息添加到content中
        extended_content = content
        if price:
            extended_content += f"\n💰 价格：{price}"
        if location:
            extended_content += f"\n📍 地点：{location}"
        if time:
            extended_content += f"\n⏰ 时间：{time}"

        # 创建基础字段的对象
        post = CatCirclePost(
            user_id=user_id,
            content=extended_content,
            images=json.dumps(images) if images else None,
            type=post_type,
            contact_info=contact_info,
            gender=gender
        )

        print("保存到数据库")
        db.session.add(post)
        db.session.commit()
        print("保存成功")

        return jsonify({
            'code': 200,
            'message': '发布成功',
            'data': post.to_dict()
        })

    except Exception as e:
        print(f"发布失败: {e}")
        import traceback
        traceback.print_exc()
        db.session.rollback()
        return jsonify({'code': 500, 'message': f'服务器错误：{str(e)}'}), 500

@cat_circle_bp.route('/posts', methods=['GET'])
def get_posts():
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        post_type = request.args.get('type', 'all')
        user_id = request.args.get('user_id', type=int)

        per_page = min(per_page, 50)

        query = CatCirclePost.query.filter_by(status='active')

        if post_type != 'all':
            query = query.filter_by(type=post_type)

        query = query.order_by(CatCirclePost.created_at.desc())

        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        posts = []
        for post in pagination.items:
            post_dict = post.to_dict(current_user_id=user_id)
            posts.append(post_dict)

        return jsonify({
            'code': 200,
            'message': '获取成功',
            'data': {
                'posts': posts,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': pagination.total,
                    'pages': pagination.pages,
                    'has_next': pagination.has_next,
                    'has_prev': pagination.has_prev
                }
            }
        })

    except Exception as e:
        return jsonify({'code': 500, 'message': f'服务器错误：{str(e)}'}), 500

@cat_circle_bp.route('/posts/<int:post_id>', methods=['GET'])
def get_post_detail(post_id):
    """获取单个动态详情"""
    try:
        user_id = request.args.get('user_id', type=int)

        post = CatCirclePost.query.filter_by(id=post_id, status='active').first()
        if not post:
            return jsonify({'code': 404, 'message': '动态不存在'}), 404

        # 增加浏览量
        if user_id:
            # 检查是否已经浏览过
            existing_view = CatCircleView.query.filter_by(
                user_id=user_id, post_id=post_id
            ).first()

            if not existing_view:
                view = CatCircleView(user_id=user_id, post_id=post_id)
                db.session.add(view)
                post.view_count += 1
                db.session.commit()

        post_dict = post.to_dict(current_user_id=user_id)

        return jsonify({
            'code': 200,
            'message': '获取成功',
            'data': {
                'post': post_dict
            }
        })

    except Exception as e:
        return jsonify({'code': 500, 'message': f'服务器错误：{str(e)}'}), 500

@cat_circle_bp.route('/posts/<int:post_id>/like', methods=['POST'])
def toggle_like(post_id):
    try:
        data = request.get_json()
        user_id = data.get('user_id')

        if not user_id:
            return jsonify({'code': 400, 'message': '用户ID不能为空'}), 400

        post = CatCirclePost.query.filter_by(id=post_id, status='active').first()
        if not post:
            return jsonify({'code': 404, 'message': '动态不存在'}), 404

        existing_like = CatCircleLike.query.filter_by(
            user_id=user_id, post_id=post_id
        ).first()

        if existing_like:
            db.session.delete(existing_like)
            post.like_count = max(0, post.like_count - 1)
            is_liked = False
            message = '取消点赞成功'
        else:
            like = CatCircleLike(user_id=user_id, post_id=post_id)
            db.session.add(like)
            post.like_count += 1
            is_liked = True
            message = '点赞成功'

        db.session.commit()

        return jsonify({
            'code': 200,
            'message': message,
            'data': {
                'is_liked': is_liked,
                'like_count': post.like_count
            }
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'code': 500, 'message': f'服务器错误：{str(e)}'}), 500

@cat_circle_bp.route('/posts/<int:post_id>/collect', methods=['POST'])
def toggle_collect(post_id):
    try:
        data = request.get_json()
        user_id = data.get('user_id')

        if not user_id:
            return jsonify({'code': 400, 'message': '用户ID不能为空'}), 400

        post = CatCirclePost.query.filter_by(id=post_id, status='active').first()
        if not post:
            return jsonify({'code': 404, 'message': '动态不存在'}), 404

        existing_collect = CatCircleCollection.query.filter_by(
            user_id=user_id, post_id=post_id
        ).first()

        if existing_collect:
            db.session.delete(existing_collect)
            post.collect_count = max(0, post.collect_count - 1)
            is_collected = False
            message = '取消收藏成功'
        else:
            collect = CatCircleCollection(user_id=user_id, post_id=post_id)
            db.session.add(collect)
            post.collect_count += 1
            is_collected = True
            message = '收藏成功'

        db.session.commit()

        return jsonify({
            'code': 200,
            'message': message,
            'data': {
                'is_collected': is_collected,
                'collect_count': post.collect_count
            }
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'code': 500, 'message': f'服务器错误：{str(e)}'}), 500

@cat_circle_bp.route('/posts/<int:post_id>/comments', methods=['GET'])
def get_comments(post_id):
    try:
        user_id = request.args.get('user_id', type=int)

        post = CatCirclePost.query.filter_by(id=post_id, status='active').first()
        if not post:
            return jsonify({'code': 404, 'message': '动态不存在'}), 404

        # 获取顶级评论
        comments = CatCircleComment.query.filter_by(
            post_id=post_id,
            parent_id=None,
            status='active'
        ).order_by(CatCircleComment.created_at.desc()).all()

        comment_list = []
        for comment in comments:
            comment_dict = comment.to_dict()

            # 获取评论用户信息
            comment_dict['user_info'] = get_user_info(comment.user_id)

            # 获取回复
            replies = CatCircleComment.query.filter_by(
                parent_id=comment.id,
                status='active'
            ).order_by(CatCircleComment.created_at.asc()).all()

            reply_list = []
            for reply in replies:
                reply_dict = reply.to_dict()

                # 获取回复用户信息
                reply_dict['user_info'] = get_user_info(reply.user_id)

                # 获取被回复用户信息
                if reply.reply_to_user_id:
                    reply_dict['reply_to_user_info'] = get_user_info(reply.reply_to_user_id)

                reply_list.append(reply_dict)

            comment_dict['replies'] = reply_list
            comment_list.append(comment_dict)

        return jsonify({
            'code': 200,
            'message': '获取成功',
            'data': {
                'comments': comment_list
            }
        })

    except Exception as e:
        return jsonify({'code': 500, 'message': f'服务器错误：{str(e)}'}), 500

@cat_circle_bp.route('/comments', methods=['POST'])
def create_comment():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'code': 400, 'message': '请求数据不能为空'}), 400

        user_id = data.get('user_id')
        post_id = data.get('post_id')
        content = data.get('content', '').strip()
        reply_to_comment_id = data.get('reply_to_comment_id')

        if not user_id:
            return jsonify({'code': 400, 'message': '用户ID不能为空'}), 400

        if not post_id:
            return jsonify({'code': 400, 'message': '动态ID不能为空'}), 400

        if not content:
            return jsonify({'code': 400, 'message': '评论内容不能为空'}), 400

        # 检查动态是否存在
        post = CatCirclePost.query.filter_by(id=post_id, status='active').first()
        if not post:
            return jsonify({'code': 404, 'message': '动态不存在'}), 404

        # 检查敏感词
        try:
            has_sensitive, sensitive_words = check_sensitive_words(content)
            if has_sensitive:
                return jsonify({
                    'code': 400,
                    'message': f'评论失败，包含敏感词汇：{", ".join(sensitive_words)}'
                }), 400
        except:
            pass

        # 创建评论
        comment = CatCircleComment(
            user_id=user_id,
            post_id=post_id,
            content=content,
            parent_id=reply_to_comment_id
        )

        # 如果是回复评论，设置回复用户ID
        if reply_to_comment_id:
            parent_comment = CatCircleComment.query.get(reply_to_comment_id)
            if parent_comment:
                comment.reply_to_user_id = parent_comment.user_id

        db.session.add(comment)

        # 更新动态评论数
        post.comment_count += 1

        db.session.flush()  # 获取评论ID

        # 创建评论通知
        try:
            # 只在以下两种情况下发送通知：
            # 1. 别人直接回复你的评论
            # 2. 别人评论你发布的动态

            # 如果是回复评论，只通知被回复的用户
            if reply_to_comment_id and comment.reply_to_user_id and comment.reply_to_user_id != user_id:
                notification = CommentNotification(
                    user_id=comment.reply_to_user_id,
                    post_id=post_id,
                    comment_id=comment.id,
                    commenter_id=user_id,
                    comment_content=content,
                    notification_type='reply'
                )
                db.session.add(notification)
                print(f"创建回复通知: 用户{user_id}回复了用户{comment.reply_to_user_id}")

            # 如果是对动态的直接评论（非回复），通知动态发布者
            elif not reply_to_comment_id and post.user_id != user_id:  # 只有直接评论动态且不是自己的动态才通知
                notification = CommentNotification(
                    user_id=post.user_id,
                    post_id=post_id,
                    comment_id=comment.id,
                    commenter_id=user_id,
                    comment_content=content,
                    notification_type='comment'
                )
                db.session.add(notification)
                print(f"创建评论通知: 用户{user_id}评论了用户{post.user_id}的动态")

        except Exception as e:
            print(f"创建评论通知失败: {e}")

        db.session.commit()

        return jsonify({
            'code': 200,
            'message': '评论成功',
            'data': comment.to_dict()
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'code': 500, 'message': f'服务器错误：{str(e)}'}), 500

@cat_circle_bp.route('/upload/image', methods=['POST'])
def upload_cat_image():
    """上传猫友圈图片"""
    try:
        # 检查是否有文件上传
        if 'image' not in request.files:
            return jsonify({
                'code': 400,
                'message': '没有上传文件'
            }), 400

        file = request.files['image']
        if file.filename == '':
            return jsonify({
                'code': 400,
                'message': '没有选择文件'
            }), 400

        # 检查文件类型
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
        if not file.filename.lower().endswith(tuple(allowed_extensions)):
            return jsonify({
                'code': 400,
                'message': '不支持的文件格式，请上传 PNG、JPG、JPEG、GIF 或 WEBP 格式的图片'
            }), 400

        # 生成随机文件名
        file_extension = file.filename.rsplit('.', 1)[1].lower()
        random_filename = f"{uuid.uuid4().hex}.{file_extension}"

        # 确保猫友圈图片目录存在
        cats_image_dir = os.path.join('static', 'images', 'cats')
        if not os.path.exists(cats_image_dir):
            os.makedirs(cats_image_dir, exist_ok=True)

        # 保存文件
        file_path = os.path.join(cats_image_dir, random_filename)
        file.save(file_path)

        # 生成访问URL
        # 检测环境：如果是本地开发环境使用localhost，否则使用线上域名
        host = request.headers.get('Host', '')
        if 'localhost' in host or '127.0.0.1' in host:
            base_url = 'http://localhost:5000'
        else:
            base_url = 'https://www.qiangs.xyz'

        image_url = f"{base_url}/static/images/cats/{random_filename}"

        print(f"猫友圈图片上传成功: {file_path} -> {image_url}")

        return jsonify({
            'code': 200,
            'message': '图片上传成功',
            'data': {
                'image_url': image_url,
                'filename': random_filename
            }
        })

    except Exception as e:
        print(f"猫友圈图片上传失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'图片上传失败: {str(e)}'
        }), 500

# ==================== 聊天功能相关模型和API ====================

# 全局变量声明
ChatSession = None
ChatMessage = None
EmojiCategory = None
Emoji = None
LocationMessage = None

def init_chat_models():
    """初始化聊天相关模型"""
    global ChatSession, ChatMessage, EmojiCategory, Emoji, LocationMessage

    class ChatSession(db.Model):
        __tablename__ = 'chat_sessions'
        id = db.Column(db.Integer, primary_key=True)
        user1_id = db.Column(db.Integer, nullable=False)
        user2_id = db.Column(db.Integer, nullable=False)
        last_message_id = db.Column(db.Integer)
        last_message_time = db.Column(db.DateTime)
        user1_unread_count = db.Column(db.Integer, default=0)
        user2_unread_count = db.Column(db.Integer, default=0)
        status = db.Column(db.String(20), default='active')
        created_at = db.Column(db.DateTime, default=datetime.datetime.now)
        updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)

        def to_dict(self, current_user_id=None):
            # 确定对方用户ID
            other_user_id = self.user2_id if self.user1_id == current_user_id else self.user1_id
            other_user_info = get_user_info(other_user_id)

            # 确定当前用户的未读消息数
            unread_count = self.user1_unread_count if self.user1_id == current_user_id else self.user2_unread_count

            return {
                'id': self.id,
                'user1_id': self.user1_id,
                'user2_id': self.user2_id,
                'other_user': other_user_info,
                'last_message_id': self.last_message_id,
                'last_message_time': self.last_message_time.strftime('%Y-%m-%d %H:%M:%S') if self.last_message_time else None,
                'unread_count': unread_count,
                'status': self.status,
                'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None
            }

    class ChatMessage(db.Model):
        __tablename__ = 'chat_messages'
        id = db.Column(db.Integer, primary_key=True)
        session_id = db.Column(db.Integer, nullable=False)
        sender_id = db.Column(db.Integer, nullable=False)
        receiver_id = db.Column(db.Integer, nullable=False)
        content = db.Column(db.Text, nullable=False)
        message_type = db.Column(db.String(20), default='text')
        extra_data = db.Column(db.Text)  # MySQL 5.7 使用 TEXT 而不是 JSON
        is_read = db.Column(db.Boolean, default=False)
        read_at = db.Column(db.DateTime)
        status = db.Column(db.String(20), default='active')
        created_at = db.Column(db.DateTime, default=datetime.datetime.now)
        updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)

        def to_dict(self):
            extra_data = None
            if self.extra_data:
                try:
                    extra_data = json.loads(self.extra_data)
                except:
                    extra_data = None

            return {
                'id': self.id,
                'session_id': self.session_id,
                'sender_id': self.sender_id,
                'receiver_id': self.receiver_id,
                'content': self.content,
                'message_type': self.message_type,
                'extra_data': extra_data,
                'is_read': self.is_read,
                'read_at': self.read_at.strftime('%Y-%m-%d %H:%M:%S') if self.read_at else None,
                'status': self.status,
                'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
                'sender_info': get_user_info(self.sender_id),
                'receiver_info': get_user_info(self.receiver_id)
            }

    class EmojiCategory(db.Model):
        __tablename__ = 'emoji_categories'
        id = db.Column(db.Integer, primary_key=True)
        name = db.Column(db.String(50), nullable=False)
        icon = db.Column(db.String(200))
        sort_order = db.Column(db.Integer, default=0)
        status = db.Column(db.String(20), default='active')
        created_at = db.Column(db.DateTime, default=datetime.datetime.now)
        updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)

        def to_dict(self):
            return {
                'id': self.id,
                'name': self.name,
                'icon': self.icon,
                'sort_order': self.sort_order,
                'status': self.status
            }

    class Emoji(db.Model):
        __tablename__ = 'emojis'
        id = db.Column(db.Integer, primary_key=True)
        category_id = db.Column(db.Integer, nullable=False)
        name = db.Column(db.String(100), nullable=False)
        image_url = db.Column(db.String(500), nullable=False)
        keywords = db.Column(db.String(200))
        sort_order = db.Column(db.Integer, default=0)
        usage_count = db.Column(db.Integer, default=0)
        status = db.Column(db.String(20), default='active')
        created_at = db.Column(db.DateTime, default=datetime.datetime.now)
        updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)

        def to_dict(self):
            return {
                'id': self.id,
                'category_id': self.category_id,
                'name': self.name,
                'image_url': self.image_url,
                'keywords': self.keywords,
                'sort_order': self.sort_order,
                'usage_count': self.usage_count,
                'status': self.status
            }

    class LocationMessage(db.Model):
        __tablename__ = 'location_messages'
        id = db.Column(db.Integer, primary_key=True)
        message_id = db.Column(db.Integer, nullable=False)  # 关联到chat_messages表
        latitude = db.Column(db.Numeric(10, 8), nullable=False)  # 纬度，精度到8位小数
        longitude = db.Column(db.Numeric(11, 8), nullable=False)  # 经度，精度到8位小数
        location_name = db.Column(db.String(200))  # 位置名称
        address = db.Column(db.String(500))  # 详细地址
        poi_id = db.Column(db.String(100))  # POI ID（如果有的话）
        city = db.Column(db.String(100))  # 城市
        district = db.Column(db.String(100))  # 区域
        province = db.Column(db.String(100))  # 省份
        country = db.Column(db.String(100), default='中国')  # 国家
        accuracy = db.Column(db.Float)  # 定位精度（米）
        altitude = db.Column(db.Float)  # 海拔高度（米）
        speed = db.Column(db.Float)  # 速度（米/秒）
        status = db.Column(db.String(20), default='active')
        created_at = db.Column(db.DateTime, default=datetime.datetime.now)
        updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)

        def to_dict(self):
            return {
                'id': self.id,
                'message_id': self.message_id,
                'latitude': float(self.latitude) if self.latitude else None,
                'longitude': float(self.longitude) if self.longitude else None,
                'location_name': self.location_name,
                'address': self.address,
                'poi_id': self.poi_id,
                'city': self.city,
                'district': self.district,
                'province': self.province,
                'country': self.country,
                'accuracy': self.accuracy,
                'altitude': self.altitude,
                'speed': self.speed,
                'status': self.status,
                'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None
            }

    # 将模型添加到全局变量
    globals()['ChatSession'] = ChatSession
    globals()['ChatMessage'] = ChatMessage
    globals()['EmojiCategory'] = EmojiCategory
    globals()['Emoji'] = Emoji
    globals()['LocationMessage'] = LocationMessage

# 在init_cat_circle_api函数中调用聊天模型初始化
def init_chat_api_models():
    init_chat_models()

# ==================== 聊天会话相关API ====================

@cat_circle_bp.route('/chat/sessions', methods=['GET'])
def get_chat_sessions():
    """获取用户的聊天会话列表"""
    try:
        user_id = request.args.get('user_id', type=int)
        if not user_id:
            return jsonify({'code': 400, 'message': '用户ID不能为空'}), 400

        # 初始化聊天模型（如果还没有初始化）
        if ChatSession is None:
            init_chat_models()

        # 查询用户参与的所有会话
        sessions = ChatSession.query.filter(
            db.or_(ChatSession.user1_id == user_id, ChatSession.user2_id == user_id),
            ChatSession.status == 'active'
        ).order_by(ChatSession.last_message_time.desc().nullslast()).all()

        session_list = []
        for session in sessions:
            session_dict = session.to_dict(current_user_id=user_id)

            # 获取最后一条消息
            if session.last_message_id:
                last_message = ChatMessage.query.get(session.last_message_id)
                if last_message:
                    session_dict['last_message'] = {
                        'content': last_message.content,
                        'message_type': last_message.message_type,
                        'created_at': last_message.created_at.strftime('%Y-%m-%d %H:%M:%S')
                    }

            session_list.append(session_dict)

        return jsonify({
            'code': 200,
            'message': '获取成功',
            'data': {
                'sessions': session_list
            }
        })

    except Exception as e:
        print(f"获取聊天会话失败: {e}")
        return jsonify({'code': 500, 'message': f'服务器错误：{str(e)}'}), 500

@cat_circle_bp.route('/chat/sessions', methods=['POST'])
def create_chat_session():
    """创建或获取聊天会话"""
    try:
        data = request.get_json()
        user1_id = data.get('user1_id')
        user2_id = data.get('user2_id')

        if not user1_id or not user2_id:
            return jsonify({'code': 400, 'message': '用户ID不能为空'}), 400

        if user1_id == user2_id:
            return jsonify({'code': 400, 'message': '不能与自己聊天'}), 400

        # 初始化聊天模型
        if ChatSession is None:
            init_chat_models()

        # 确保user1_id < user2_id，保持一致性
        if user1_id > user2_id:
            user1_id, user2_id = user2_id, user1_id

        # 查找是否已存在会话
        session = ChatSession.query.filter_by(
            user1_id=user1_id,
            user2_id=user2_id,
            status='active'
        ).first()

        if not session:
            # 创建新会话
            session = ChatSession(
                user1_id=user1_id,
                user2_id=user2_id
            )
            db.session.add(session)
            db.session.commit()

        return jsonify({
            'code': 200,
            'message': '会话创建成功',
            'data': {
                'session': session.to_dict(current_user_id=data.get('user1_id'))
            }
        })

    except Exception as e:
        db.session.rollback()
        print(f"创建聊天会话失败: {e}")
        return jsonify({'code': 500, 'message': f'服务器错误：{str(e)}'}), 500

@cat_circle_bp.route('/chat/sessions/<int:session_id>/messages', methods=['GET'])
def get_chat_messages(session_id):
    """获取聊天消息列表"""
    try:
        user_id = request.args.get('user_id', type=int)
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        if not user_id:
            return jsonify({'code': 400, 'message': '用户ID不能为空'}), 400

        # 初始化聊天模型
        if ChatMessage is None:
            init_chat_models()

        # 验证用户是否有权限访问该会话
        session = ChatSession.query.filter_by(id=session_id, status='active').first()
        if not session:
            return jsonify({'code': 404, 'message': '会话不存在'}), 404

        if user_id not in [session.user1_id, session.user2_id]:
            return jsonify({'code': 403, 'message': '无权限访问该会话'}), 403

        # 分页查询消息
        per_page = min(per_page, 50)
        pagination = ChatMessage.query.filter_by(
            session_id=session_id,
            status='active'
        ).order_by(ChatMessage.created_at.desc()).paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        messages = []
        for message in pagination.items:
            messages.append(message.to_dict())

        # 将消息按时间正序排列（最新的在最后）
        messages.reverse()

        # 标记消息为已读
        unread_messages = ChatMessage.query.filter_by(
            session_id=session_id,
            receiver_id=user_id,
            is_read=False,
            status='active'
        ).all()

        for message in unread_messages:
            message.is_read = True
            message.read_at = datetime.datetime.now()

        # 更新会话的未读计数
        if user_id == session.user1_id:
            session.user1_unread_count = 0
        else:
            session.user2_unread_count = 0

        db.session.commit()

        return jsonify({
            'code': 200,
            'message': '获取成功',
            'data': {
                'messages': messages,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': pagination.total,
                    'pages': pagination.pages,
                    'has_next': pagination.has_next,
                    'has_prev': pagination.has_prev
                }
            }
        })

    except Exception as e:
        print(f"获取聊天消息失败: {e}")
        return jsonify({'code': 500, 'message': f'服务器错误：{str(e)}'}), 500

@cat_circle_bp.route('/chat/messages', methods=['POST'])
def send_message():
    """发送聊天消息"""
    try:
        data = request.get_json()
        session_id = data.get('session_id')
        sender_id = data.get('sender_id')
        receiver_id = data.get('receiver_id')
        content = data.get('content', '').strip()
        message_type = data.get('message_type', 'text')
        extra_data = data.get('extra_data')

        if not all([session_id, sender_id, receiver_id]):
            return jsonify({'code': 400, 'message': '会话ID、发送者ID和接收者ID不能为空'}), 400

        if not content:
            return jsonify({'code': 400, 'message': '消息内容不能为空'}), 400

        # 初始化聊天模型
        if ChatMessage is None:
            init_chat_models()

        # 验证会话是否存在且用户有权限
        session = ChatSession.query.filter_by(id=session_id, status='active').first()
        if not session:
            return jsonify({'code': 404, 'message': '会话不存在'}), 404

        if sender_id not in [session.user1_id, session.user2_id]:
            return jsonify({'code': 403, 'message': '无权限发送消息'}), 403

        # 创建消息
        message = ChatMessage(
            session_id=session_id,
            sender_id=sender_id,
            receiver_id=receiver_id,
            content=content,
            message_type=message_type,
            extra_data=json.dumps(extra_data) if extra_data else None
        )

        db.session.add(message)
        db.session.flush()  # 获取消息ID

        # 更新会话信息
        session.last_message_id = message.id
        session.last_message_time = datetime.datetime.now()

        # 更新接收者的未读计数
        if receiver_id == session.user1_id:
            session.user1_unread_count += 1
        else:
            session.user2_unread_count += 1

        db.session.commit()

        return jsonify({
            'code': 200,
            'message': '发送成功',
            'data': {
                'message': message.to_dict()
            }
        })

    except Exception as e:
        db.session.rollback()
        print(f"发送消息失败: {e}")
        return jsonify({'code': 500, 'message': f'服务器错误：{str(e)}'}), 500

@cat_circle_bp.route('/chat/sessions/<int:session_id>/new_messages', methods=['GET'])
def get_new_messages(session_id):
    """获取指定会话的新消息（用于实时轮询）"""
    try:
        user_id = request.args.get('user_id', type=int)
        last_message_id = request.args.get('last_message_id', type=int, default=0)

        if not user_id:
            return jsonify({'code': 400, 'message': '用户ID不能为空'}), 400

        # 初始化聊天模型
        if ChatMessage is None:
            init_chat_models()

        # 验证会话是否存在且用户有权限
        session = ChatSession.query.filter_by(id=session_id, status='active').first()
        if not session:
            return jsonify({'code': 404, 'message': '会话不存在'}), 404

        if user_id not in [session.user1_id, session.user2_id]:
            return jsonify({'code': 403, 'message': '无权限访问此会话'}), 403

        # 查询新消息（ID大于last_message_id的消息）
        query = ChatMessage.query.filter(
            ChatMessage.session_id == session_id,
            ChatMessage.id > last_message_id
        ).order_by(ChatMessage.created_at.asc())

        new_messages = query.all()

        # 转换为字典格式并包含发送者信息
        messages_data = []
        for message in new_messages:
            message_dict = message.to_dict()
            # 获取发送者信息 - 使用get_user_info函数避免SQLAlchemy实例问题
            sender_info = get_user_info(message.sender_id)
            message_dict['sender_info'] = sender_info
            messages_data.append(message_dict)

        return jsonify({
            'code': 200,
            'message': '获取成功',
            'data': {
                'messages': messages_data,
                'count': len(messages_data)
            }
        })

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"获取新消息失败: {e}")
        print(f"详细错误信息: {error_details}")
        return jsonify({'code': 500, 'message': f'服务器错误：{str(e)}'}), 500

# ==================== 评论通知相关API ====================

@cat_circle_bp.route('/notifications/comments', methods=['GET'])
def get_comment_notifications():
    """获取用户的评论通知"""
    try:
        user_id = request.args.get('user_id', type=int)
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        unread_only = request.args.get('unread_only', 'false').lower() == 'true'

        if not user_id:
            return jsonify({'code': 400, 'message': '用户ID不能为空'}), 400

        # 构建查询
        query = CommentNotification.query.filter_by(user_id=user_id, status='active')

        if unread_only:
            query = query.filter_by(is_read=False)

        # 分页查询
        per_page = min(per_page, 50)
        pagination = query.order_by(CommentNotification.created_at.desc()).paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        notifications = []
        for notification in pagination.items:
            notification_dict = notification.to_dict()

            # 获取动态信息
            post = CatCirclePost.query.filter_by(id=notification.post_id, status='active').first()
            if post:
                notification_dict['post_info'] = {
                    'id': post.id,
                    'content': post.content[:50] + '...' if len(post.content) > 50 else post.content,
                    'type': post.type
                }

            notifications.append(notification_dict)

        # 获取未读数量
        unread_count = CommentNotification.query.filter_by(
            user_id=user_id,
            is_read=False,
            status='active'
        ).count()

        return jsonify({
            'code': 200,
            'message': '获取成功',
            'data': {
                'notifications': notifications,
                'unread_count': unread_count,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': pagination.total,
                    'pages': pagination.pages,
                    'has_next': pagination.has_next,
                    'has_prev': pagination.has_prev
                }
            }
        })

    except Exception as e:
        print(f"获取评论通知失败: {e}")
        return jsonify({'code': 500, 'message': f'服务器错误：{str(e)}'}), 500

@cat_circle_bp.route('/notifications/comments/unread_count', methods=['GET'])
def get_unread_comment_count():
    """获取用户未读评论通知数量"""
    try:
        user_id = request.args.get('user_id', type=int)
        post_id = request.args.get('post_id', type=int)  # 可选参数，获取特定动态的未读数量

        if not user_id:
            return jsonify({'code': 400, 'message': '用户ID不能为空'}), 400

        # 构建查询条件
        query = CommentNotification.query.filter_by(
            user_id=user_id,
            is_read=False,
            status='active'
        )

        # 如果指定了动态ID，只查询该动态的未读数量
        if post_id:
            query = query.filter_by(post_id=post_id)

        unread_count = query.count()

        return jsonify({
            'code': 200,
            'message': '获取成功',
            'data': {
                'unread_count': unread_count,
                'post_id': post_id
            }
        })

    except Exception as e:
        print(f"获取未读评论数量失败: {e}")
        return jsonify({'code': 500, 'message': f'服务器错误：{str(e)}'}), 500

@cat_circle_bp.route('/posts/unread_counts', methods=['GET'])
def get_posts_unread_counts():
    """批量获取多个动态的未读评论数量"""
    try:
        user_id = request.args.get('user_id', type=int)
        post_ids = request.args.get('post_ids', '')  # 逗号分隔的动态ID列表

        if not user_id:
            return jsonify({'code': 400, 'message': '用户ID不能为空'}), 400

        if not post_ids:
            return jsonify({'code': 400, 'message': '动态ID列表不能为空'}), 400

        try:
            post_id_list = [int(pid.strip()) for pid in post_ids.split(',') if pid.strip()]
        except ValueError:
            return jsonify({'code': 400, 'message': '动态ID格式错误'}), 400

        # 查询每个动态的未读评论数量
        unread_counts = {}
        for post_id in post_id_list:
            count = CommentNotification.query.filter_by(
                user_id=user_id,
                post_id=post_id,
                is_read=False,
                status='active'
            ).count()
            unread_counts[str(post_id)] = count

        return jsonify({
            'code': 200,
            'message': '获取成功',
            'data': {
                'unread_counts': unread_counts
            }
        })

    except Exception as e:
        print(f"批量获取未读评论数量失败: {e}")
        return jsonify({'code': 500, 'message': f'服务器错误：{str(e)}'}), 500

@cat_circle_bp.route('/notifications/comments/<int:notification_id>/read', methods=['POST'])
def mark_notification_read(notification_id):
    """标记通知为已读"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')

        if not user_id:
            return jsonify({'code': 400, 'message': '用户ID不能为空'}), 400

        notification = CommentNotification.query.filter_by(
            id=notification_id,
            user_id=user_id,
            status='active'
        ).first()

        if not notification:
            return jsonify({'code': 404, 'message': '通知不存在'}), 404

        notification.is_read = True
        notification.read_at = datetime.datetime.now()
        db.session.commit()

        return jsonify({
            'code': 200,
            'message': '标记成功',
            'data': notification.to_dict()
        })

    except Exception as e:
        db.session.rollback()
        print(f"标记通知已读失败: {e}")
        return jsonify({'code': 500, 'message': f'服务器错误：{str(e)}'}), 500

@cat_circle_bp.route('/notifications/comments/mark_all_read', methods=['POST'])
def mark_all_notifications_read():
    """标记所有通知为已读"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')

        if not user_id:
            return jsonify({'code': 400, 'message': '用户ID不能为空'}), 400

        # 更新所有未读通知
        notifications = CommentNotification.query.filter_by(
            user_id=user_id,
            is_read=False,
            status='active'
        ).all()

        for notification in notifications:
            notification.is_read = True
            notification.read_at = datetime.datetime.now()

        db.session.commit()

        return jsonify({
            'code': 200,
            'message': '全部标记成功',
            'data': {
                'marked_count': len(notifications)
            }
        })

    except Exception as e:
        db.session.rollback()
        print(f"标记所有通知已读失败: {e}")
        return jsonify({'code': 500, 'message': f'服务器错误：{str(e)}'}), 500

@cat_circle_bp.route('/notifications/comments/mark_post_read', methods=['POST'])
def mark_post_notifications_read():
    """标记指定动态的所有通知为已读"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        post_id = data.get('post_id')

        if not user_id:
            return jsonify({'code': 400, 'message': '用户ID不能为空'}), 400

        if not post_id:
            return jsonify({'code': 400, 'message': '动态ID不能为空'}), 400

        # 更新指定动态的所有未读通知
        notifications = CommentNotification.query.filter_by(
            user_id=user_id,
            post_id=post_id,
            is_read=False,
            status='active'
        ).all()

        for notification in notifications:
            notification.is_read = True
            notification.read_at = datetime.datetime.now()

        db.session.commit()

        return jsonify({
            'code': 200,
            'message': '标记成功',
            'data': {
                'marked_count': len(notifications),
                'post_id': post_id
            }
        })

    except Exception as e:
        db.session.rollback()
        print(f"标记动态通知已读失败: {e}")
        return jsonify({'code': 500, 'message': f'服务器错误：{str(e)}'}), 500

@cat_circle_bp.route('/posts/<int:post_id>/new_comments', methods=['GET'])
def get_new_comments(post_id):
    """获取指定动态的新评论（用于实时轮询）"""
    try:
        user_id = request.args.get('user_id', type=int)
        last_comment_id = request.args.get('last_comment_id', type=int, default=0)

        if not user_id:
            return jsonify({'code': 400, 'message': '用户ID不能为空'}), 400

        # 验证动态是否存在
        post = CatCirclePost.query.filter_by(id=post_id, status='active').first()
        if not post:
            return jsonify({'code': 404, 'message': '动态不存在'}), 404

        # 查询新评论（ID大于last_comment_id的评论）
        new_comments = CatCircleComment.query.filter(
            CatCircleComment.post_id == post_id,
            CatCircleComment.id > last_comment_id,
            CatCircleComment.status == 'active'
        ).order_by(CatCircleComment.created_at.asc()).all()

        comment_list = []
        for comment in new_comments:
            comment_dict = comment.to_dict()
            comment_dict['user_info'] = get_user_info(comment.user_id)

            # 获取回复
            if comment.parent_id is None:  # 只处理顶级评论的回复
                replies = CatCircleComment.query.filter_by(
                    parent_id=comment.id,
                    status='active'
                ).order_by(CatCircleComment.created_at.asc()).all()

                reply_list = []
                for reply in replies:
                    reply_dict = reply.to_dict()
                    reply_dict['user_info'] = get_user_info(reply.user_id)
                    if reply.reply_to_user_id:
                        reply_dict['reply_to_user_info'] = get_user_info(reply.reply_to_user_id)
                    reply_list.append(reply_dict)

                comment_dict['replies'] = reply_list

            comment_list.append(comment_dict)

        return jsonify({
            'code': 200,
            'message': '获取成功',
            'data': {
                'comments': comment_list,
                'count': len(comment_list)
            }
        })

    except Exception as e:
        print(f"获取新评论失败: {e}")
        return jsonify({'code': 500, 'message': f'服务器错误：{str(e)}'}), 500

# ==================== 定位消息相关API ====================

@cat_circle_bp.route('/chat/location/send', methods=['POST'])
def send_location_message():
    """发送定位消息"""
    try:
        data = request.get_json()
        session_id = data.get('session_id')
        sender_id = data.get('sender_id')
        receiver_id = data.get('receiver_id')

        # 位置信息
        latitude = data.get('latitude')
        longitude = data.get('longitude')
        location_name = data.get('location_name', '')
        address = data.get('address', '')
        poi_id = data.get('poi_id')
        city = data.get('city')
        district = data.get('district')
        province = data.get('province')
        accuracy = data.get('accuracy')
        altitude = data.get('altitude')
        speed = data.get('speed')

        # 验证必需参数
        if not all([session_id, sender_id, receiver_id, latitude, longitude]):
            return jsonify({'code': 400, 'message': '会话ID、发送者ID、接收者ID、经纬度不能为空'}), 400

        # 初始化聊天模型
        if ChatMessage is None or LocationMessage is None:
            init_chat_models()

        # 验证会话是否存在且用户有权限
        session = ChatSession.query.filter_by(id=session_id, status='active').first()
        if not session:
            return jsonify({'code': 404, 'message': '会话不存在'}), 404

        if sender_id not in [session.user1_id, session.user2_id]:
            return jsonify({'code': 403, 'message': '无权限发送消息'}), 403

        # 生成消息内容
        content = f"[位置] {location_name}" if location_name else "[位置] 位置信息"

        # 创建聊天消息
        message = ChatMessage(
            session_id=session_id,
            sender_id=sender_id,
            receiver_id=receiver_id,
            content=content,
            message_type='location',
            extra_data=json.dumps({
                'latitude': latitude,
                'longitude': longitude,
                'name': location_name,
                'address': address
            })
        )

        db.session.add(message)
        db.session.flush()  # 获取消息ID

        # 创建详细的位置信息记录
        location_message = LocationMessage(
            message_id=message.id,
            latitude=latitude,
            longitude=longitude,
            location_name=location_name,
            address=address,
            poi_id=poi_id,
            city=city,
            district=district,
            province=province,
            accuracy=accuracy,
            altitude=altitude,
            speed=speed
        )

        db.session.add(location_message)

        # 更新会话信息
        session.last_message_id = message.id
        session.last_message_time = datetime.datetime.now()

        # 更新接收者的未读计数
        if receiver_id == session.user1_id:
            session.user1_unread_count += 1
        else:
            session.user2_unread_count += 1

        db.session.commit()

        # 返回消息信息，包含位置详情
        message_dict = message.to_dict()
        message_dict['location_details'] = location_message.to_dict()

        return jsonify({
            'code': 200,
            'message': '定位消息发送成功',
            'data': {
                'message': message_dict
            }
        })

    except Exception as e:
        db.session.rollback()
        print(f"发送定位消息失败: {e}")
        return jsonify({'code': 500, 'message': f'服务器错误：{str(e)}'}), 500

@cat_circle_bp.route('/chat/location/<int:message_id>', methods=['GET'])
def get_location_details(message_id):
    """获取定位消息的详细信息"""
    try:
        user_id = request.args.get('user_id', type=int)
        if not user_id:
            return jsonify({'code': 400, 'message': '用户ID不能为空'}), 400

        # 初始化聊天模型
        if ChatMessage is None or LocationMessage is None:
            init_chat_models()

        # 查找消息
        message = ChatMessage.query.filter_by(id=message_id, message_type='location', status='active').first()
        if not message:
            return jsonify({'code': 404, 'message': '定位消息不存在'}), 404

        # 验证用户权限
        session = ChatSession.query.filter_by(id=message.session_id, status='active').first()
        if not session or user_id not in [session.user1_id, session.user2_id]:
            return jsonify({'code': 403, 'message': '无权限访问该消息'}), 403

        # 查找位置详情
        location_details = LocationMessage.query.filter_by(message_id=message_id, status='active').first()

        message_dict = message.to_dict()
        if location_details:
            message_dict['location_details'] = location_details.to_dict()

        return jsonify({
            'code': 200,
            'message': '获取成功',
            'data': {
                'message': message_dict
            }
        })

    except Exception as e:
        print(f"获取定位消息详情失败: {e}")
        return jsonify({'code': 500, 'message': f'服务器错误：{str(e)}'}), 500

# ==================== 表情包相关API ====================

@cat_circle_bp.route('/chat/emoji/categories', methods=['GET'])
def get_emoji_categories():
    """获取表情包分类"""
    try:
        # 初始化聊天模型
        if EmojiCategory is None:
            init_chat_models()

        # 查询所有分类，如果status为NULL则也包含在内
        categories = EmojiCategory.query.filter(
            db.or_(EmojiCategory.status == 'active', EmojiCategory.status.is_(None))
        ).order_by(EmojiCategory.sort_order).all()

        category_list = []
        for category in categories:
            category_dict = category.to_dict()

            # 获取该分类下的表情包数量，包含status为NULL的
            emoji_count = Emoji.query.filter(
                Emoji.category_id == category.id,
                db.or_(Emoji.status == 'active', Emoji.status.is_(None))
            ).count()
            category_dict['emoji_count'] = emoji_count

            category_list.append(category_dict)

        return jsonify({
            'code': 200,
            'message': '获取成功',
            'data': {
                'categories': category_list
            }
        })

    except Exception as e:
        print(f"获取表情包分类失败: {e}")
        return jsonify({'code': 500, 'message': f'服务器错误：{str(e)}'}), 500

@cat_circle_bp.route('/chat/emoji/categories/<int:category_id>/emojis', methods=['GET'])
def get_emojis_by_category(category_id):
    """获取指定分类的表情包"""
    try:
        # 初始化聊天模型
        if Emoji is None:
            init_chat_models()

        # 查询指定分类的表情包，包含status为NULL的
        emojis = Emoji.query.filter(
            Emoji.category_id == category_id,
            db.or_(Emoji.status == 'active', Emoji.status.is_(None))
        ).order_by(Emoji.sort_order, Emoji.usage_count.desc()).all()

        emoji_list = []
        for emoji in emojis:
            emoji_list.append(emoji.to_dict())

        return jsonify({
            'code': 200,
            'message': '获取成功',
            'data': {
                'emojis': emoji_list
            }
        })

    except Exception as e:
        print(f"获取表情包失败: {e}")
        return jsonify({'code': 500, 'message': f'服务器错误：{str(e)}'}), 500

@cat_circle_bp.route('/chat/emoji/search', methods=['GET'])
def search_emojis():
    """搜索表情包"""
    try:
        keyword = request.args.get('keyword', '').strip()
        if not keyword:
            return jsonify({'code': 400, 'message': '搜索关键词不能为空'}), 400

        # 初始化聊天模型
        if Emoji is None:
            init_chat_models()

        # 在名称和关键词中搜索，包含status为NULL的
        emojis = Emoji.query.filter(
            db.or_(
                Emoji.name.contains(keyword),
                Emoji.keywords.contains(keyword)
            ),
            db.or_(Emoji.status == 'active', Emoji.status.is_(None))
        ).order_by(Emoji.usage_count.desc()).limit(50).all()

        emoji_list = []
        for emoji in emojis:
            emoji_list.append(emoji.to_dict())

        return jsonify({
            'code': 200,
            'message': '搜索成功',
            'data': {
                'emojis': emoji_list,
                'keyword': keyword
            }
        })

    except Exception as e:
        print(f"搜索表情包失败: {e}")
        return jsonify({'code': 500, 'message': f'服务器错误：{str(e)}'}), 500







# ==================== 聊天图片上传API ====================

@cat_circle_bp.route('/chat/upload_image', methods=['POST'])
def upload_chat_image():
    """上传聊天图片"""
    try:
        if 'image' not in request.files:
            return jsonify({'code': 400, 'message': '没有上传图片'}), 400

        file = request.files['image']
        if file.filename == '':
            return jsonify({'code': 400, 'message': '没有选择文件'}), 400

        # 检查文件类型
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
        if not ('.' in file.filename and file.filename.rsplit('.', 1)[1].lower() in allowed_extensions):
            return jsonify({'code': 400, 'message': '不支持的图片格式'}), 400

        # 检查文件大小 (5MB)
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)

        if file_size > 5 * 1024 * 1024:
            return jsonify({'code': 400, 'message': '图片大小不能超过5MB'}), 400

        # 生成唯一文件名
        file_extension = file.filename.rsplit('.', 1)[1].lower()
        unique_filename = f"{uuid.uuid4().hex}.{file_extension}"

        # 确保目录存在
        chat_images_dir = os.path.join('static', 'images', 'chat')
        os.makedirs(chat_images_dir, exist_ok=True)

        # 保存文件
        file_path = os.path.join(chat_images_dir, unique_filename)
        file.save(file_path)

        # 根据环境生成URL
        if request.host.startswith('localhost') or request.host.startswith('127.0.0.1'):
            base_url = f"http://{request.host}"
        else:
            base_url = "https://www.qiangs.xyz"

        image_url = f"{base_url}/static/images/chat/{unique_filename}"

        return jsonify({
            'code': 200,
            'message': '图片上传成功',
            'data': {
                'image_url': image_url,
                'filename': unique_filename
            }
        })

    except Exception as e:
        print(f"聊天图片上传失败: {str(e)}")
        return jsonify({
            'code': 500,
            'message': f'图片上传失败: {str(e)}'
        }), 500

# ==================== 初始化函数更新 ====================

# 更新原有的init_cat_circle_api函数，添加聊天模型初始化
def update_init_function():
    """更新初始化函数以包含聊天模型"""
    # 这个函数需要在app.py中调用init_chat_models()
    pass
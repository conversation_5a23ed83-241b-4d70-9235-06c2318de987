# 简化版宝塔面板部署指南

本文档提供了一种更简单、更可靠的方法在宝塔面板上部署大数据题库应用。

## 准备工作

1. 一台安装了宝塔面板的Linux服务器（推荐CentOS 7+）
2. 已绑定域名到服务器IP
3. 宝塔面板已安装以下软件：
   - Nginx 1.18+
   - Python 3.8+
   - MySQL 5.7+

## 步骤 1: 创建数据库

1. 登录宝塔面板
2. 点击左侧"数据库"菜单
3. 点击"添加数据库"
4. 填写数据库信息：
   - 数据库名：quiz_app
   - 用户名：quiz_app 
   - 密码：设置一个安全密码并记住它
   - 访问权限：选择"所有人"或指定你的服务器IP
5. 点击"提交"创建数据库

## 步骤 2: 创建网站

1. 点击左侧"网站"菜单
2. 点击"添加站点"
3. 填写站点信息：
   - 域名：输入你的域名（如 www.qiangs.xyz）
   - 备注：可选
   - 根目录：保持默认 `/www/wwwroot/www.qiangs.xyz`
   - FTP：不创建
   - 数据库：不创建（已单独创建）
   - PHP版本：纯静态
4. 点击"提交"创建网站

## 步骤 3: 上传项目文件

1. 下载和解压本项目文件
2. 修改 `config.py` 中的数据库信息，使用你在步骤1中设置的用户名和密码
3. 使用以下方法之一上传文件到服务器的 `/www/wwwroot/www.qiangs.xyz` 目录：
   - 通过宝塔面板的文件管理器上传压缩包并解压
   - 使用 SFTP 工具上传

## 步骤 4: 初始化数据库和部署应用

1. 在宝塔面板中，点击左侧"终端"菜单
2. 执行以下命令：

```bash
cd /www/wwwroot/www.qiangs.xyz
chmod +x deploy-simple.sh
./deploy-simple.sh
```

这个脚本会：
- 创建Python虚拟环境
- 安装所有依赖
- 设置正确的文件权限
- 启动应用

## 步骤 5: 配置Nginx

1. 在宝塔面板中，点击"网站" -> 找到你的网站 -> "设置"
2. 点击"配置文件"
3. 将 `nginx_simple.conf` 中的内容复制粘贴到配置编辑器中
4. 点击"保存"
5. 重启Nginx：在宝塔面板中点击"Nginx" -> "重启"

## 步骤 6: 申请SSL证书（可选但推荐）

1. 在宝塔面板中，点击"网站" -> 找到你的网站 -> "设置" -> "SSL"
2. 选择"Let's Encrypt"选项卡
3. 勾选你的域名并点击"申请"
4. 证书颁发后，开启"强制HTTPS"

## 步骤 7: 测试应用

1. 在浏览器中访问你的网站（如 https://www.qiangs.xyz/admin/login）
2. 使用以下默认凭据登录管理后台：
   - 用户名：admin
   - 密码：admin123

## 故障排除

### 应用无法访问

1. 检查应用是否正在运行：
```bash
ps aux | grep gunicorn
```

2. 如果没有运行，重启应用：
```bash
cd /www/wwwroot/www.qiangs.xyz
./start.sh
```

3. 检查Nginx配置和日志：
```bash
# 测试Nginx配置
nginx -t

# 查看错误日志
tail -f /www/wwwlogs/www.qiangs.xyz.error.log
tail -f /www/wwwlogs/gunicorn_error.log
```

### 数据库连接问题

1. 验证数据库凭据和连接信息
2. 确保在`/www/wwwroot/www.qiangs.xyz/start.sh`中设置了正确的环境变量
3. 检查数据库服务是否正在运行

### 权限问题

如果遇到权限错误，执行以下命令：
```bash
chown -R www:www /www/wwwroot/www.qiangs.xyz
chmod -R 755 /www/wwwroot/www.qiangs.xyz
```

## 应用管理

### 启动应用
```bash
cd /www/wwwroot/www.qiangs.xyz
./start.sh
```

### 停止应用
```bash
cd /www/wwwroot/www.qiangs.xyz
./stop.sh
```

### 更新应用
当你需要更新应用代码时，上传新文件后执行：
```bash
cd /www/wwwroot/www.qiangs.xyz
./stop.sh
./deploy-simple.sh
```

### 查看日志
```bash
# 应用日志
tail -f /www/wwwlogs/gunicorn_access.log
tail -f /www/wwwlogs/gunicorn_error.log

# Nginx日志
tail -f /www/wwwlogs/www.qiangs.xyz.access.log
tail -f /www/wwwlogs/www.qiangs.xyz.error.log
``` 
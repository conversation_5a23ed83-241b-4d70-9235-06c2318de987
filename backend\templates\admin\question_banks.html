{% extends "admin/base.html" %}

{% block title %}题库管理 - 题库管理系统{% endblock %}

{% block header %}题库管理{% endblock %}

{% block header_buttons %}
<button type="button" class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#addQuestionBankModal">
    <i class="fas fa-plus me-1"></i>快速添加题库
</button>
<a href="{{ url_for('add_question_bank') }}" class="btn btn-outline-primary me-2">
    <i class="fas fa-plus-circle me-1"></i>高级添加
</a>
<button type="button" class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#importQuestionBanksModal">
    <i class="fas fa-file-import me-1"></i>导入题库
</button>
{% endblock %}

{% block content %}
<!-- 添加题库模态框 -->
<div class="modal fade" id="addQuestionBankModal" tabindex="-1" aria-labelledby="addQuestionBankModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addQuestionBankModalLabel">添加新题库</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('add_bank_quick') }}" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">题库名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required placeholder="请输入题库名称">
                    </div>
                    
                    <div class="mb-3">
                        <label for="course_id" class="form-label">所属课程 <span class="text-danger">*</span></label>
                        <select class="form-select" id="course_id" name="course_id" required>
                            <option value="">请选择课程</option>
                            {% for course in courses %}
                            <option value="{{ course.id }}">
                                {{ course.name }} 
                                {% if course.class_ %}
                                    ({{ course.class_.name }})
                                {% else %}
                                    (无班级)
                                {% endif %}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">题库描述</label>
                        <textarea class="form-control" id="description" name="description" rows="3" placeholder="请输入题库描述信息"></textarea>
                    </div>
                    
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                        <label class="form-check-label" for="is_active">立即上架</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>添加题库
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 导入题库模态框 -->
<div class="modal fade" id="importQuestionBanksModal" tabindex="-1" aria-labelledby="importQuestionBanksModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importQuestionBanksModalLabel">批量导入题库</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('admin_question_banks') }}" method="post" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="importFile" class="form-label">选择文件 <span class="text-danger">*</span></label>
                        <input type="file" class="form-control" id="importFile" name="importFile" required accept=".xlsx,.csv,.json">
                        <div class="form-text">支持格式：Excel(.xlsx)、CSV(.csv)、JSON(.json)</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="import_course_id" class="form-label">默认所属课程 <span class="text-danger">*</span></label>
                        <select class="form-select" id="import_course_id" name="import_course_id" required>
                            <option value="">请选择课程</option>
                            {% for course in courses %}
                            <option value="{{ course.id }}">
                                {{ course.name }} 
                                {% if course.class_ %}
                                    ({{ course.class_.name }})
                                {% else %}
                                    (无班级)
                                {% endif %}
                            </option>
                            {% endfor %}
                        </select>
                        <div class="form-text">导入的题库将默认关联到此课程</div>
                    </div>

                    <div class="form-check form-switch mb-3">
                        <input class="form-check-input" type="checkbox" id="import_is_active" name="import_is_active" checked>
                        <label class="form-check-label" for="import_is_active">导入后立即上架</label>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>提示：</strong>
                        <p class="mb-0">导入文件应包含题库名称、描述等信息。导入前请确保数据格式正确。</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-file-import me-1"></i>开始导入
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 显示操作提示消息 -->
{% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
        {% for category, message in messages %}
            <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                {% if category == 'success' %}
                    <i class="fas fa-check-circle me-2"></i>
                {% elif category == 'danger' %}
                    <i class="fas fa-exclamation-circle me-2"></i>
                {% elif category == 'warning' %}
                    <i class="fas fa-exclamation-triangle me-2"></i>
                {% else %}
                    <i class="fas fa-info-circle me-2"></i>
                {% endif %}
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}
{% endwith %}

<!-- 题库统计卡片 -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card h-100">
            <div class="card-body stats-card">
                <div class="number">{{ question_banks|length }}</div>
                <div class="label">题库总数</div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card h-100">
            <div class="card-body stats-card">
                <div class="number">{{ question_banks|selectattr('is_active', 'equalto', true)|list|length }}</div>
                <div class="label">已上架题库</div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card h-100">
            <div class="card-header">
                <i class="fas fa-info-circle me-2"></i>操作指南
            </div>
            <div class="card-body">
                <p class="mb-0">在此页面您可以管理所有题库，查看题库详情，以及上架或下架题库。</p>
            </div>
        </div>
    </div>
</div>

<!-- 添加题库按钮区域 -->
<div class="d-flex justify-content-between align-items-center mb-3">
    <h5 class="mb-0"><i class="fas fa-book-open me-2 text-primary"></i>题库管理</h5>
    <div>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addQuestionBankModal">
            <i class="fas fa-plus me-1"></i>添加题库
        </button>
        <button type="button" class="btn btn-success ms-2" data-bs-toggle="modal" data-bs-target="#importQuestionBanksModal">
            <i class="fas fa-file-import me-1"></i>导入题库
        </button>
    </div>
</div>

<!-- 题库列表卡片 -->
<div class="card shadow-sm">
    <div class="card-header bg-white">
        <div class="d-flex align-items-center">
            <i class="fas fa-book-open me-2 text-primary"></i>
            <h5 class="mb-0 fw-bold">题库列表</h5>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="questionBanksTable">
                <thead>
                    <tr>
                        <th width="5%">ID</th>
                        <th width="20%">题库名称</th>
                        <th width="15%">所属课程/班级</th>
                        <th width="10%">单选题</th>
                        <th width="10%">多选题</th>
                        <th width="10%">状态</th>
                        <th width="15%">创建时间</th>
                        <th width="15%">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for bank in question_banks %}
                    <tr>
                        <td>{{ bank.id }}</td>
                        <td>
                            <div class="description-text" title="{{ bank.name }}">
                                {{ bank.name }}
                            </div>
                        </td>
                        <td>
                            {{ bank.course.name }}
                            {% if bank.course.class_obj is defined and bank.course.class_obj %}
                            / {{ bank.course.class_obj.name }}
                            {% elif bank.course.classes is defined and bank.course.classes %}
                            / {{ bank.course.classes[0].name if bank.course.classes|length > 0 else '无班级' }}
                            {% else %}
                            / 无班级
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-primary rounded-pill">
                                {{ bank.single_questions|length }}
                            </span>
                        </td>
                        <td>
                            <span class="badge bg-info rounded-pill">
                                {{ bank.multiple_questions|length }}
                            </span>
                        </td>
                        <td>
                            {% if bank.is_active %}
                            <span class="badge bg-success">已上架</span>
                            {% else %}
                            <span class="badge bg-secondary">已下架</span>
                            {% endif %}
                        </td>
                        <td>
                            <small class="text-muted">
                                <i class="far fa-calendar-alt me-1"></i>
                                {% if bank.created_at %}
                                {{ bank.created_at.strftime('%Y-%m-%d %H:%M') }}
                                {% else %}
                                无数据
                                {% endif %}
                            </small>
                        </td>
                        <td>
                            <div class="d-flex">
                                <a href="{{ url_for('question_bank_questions', bank_id=bank.id) }}" class="btn btn-sm btn-outline-primary me-1" data-bs-toggle="tooltip" title="题目管理">
                                    <i class="fas fa-list-alt"></i>
                                </a>
                                <a href="{{ url_for('edit_question_bank', bank_id=bank.id) }}" class="btn btn-sm btn-outline-primary me-1" data-bs-toggle="tooltip" title="编辑题库">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="{{ url_for('toggle_question_bank', bank_id=bank.id) }}" method="post" class="d-inline me-1">
                                    <button type="submit" class="btn btn-sm {% if bank.is_active %}btn-outline-warning{% else %}btn-outline-success{% endif %}" data-bs-toggle="tooltip" title="{% if bank.is_active %}下架{% else %}上架{% endif %}题库">
                                        {% if bank.is_active %}
                                        <i class="fas fa-download"></i>
                                        {% else %}
                                        <i class="fas fa-upload"></i>
                                        {% endif %}
                                    </button>
                                </form>
                                <button type="button" class="btn btn-sm btn-outline-danger delete-btn" data-id="{{ bank.id }}" data-name="{{ bank.name }}" title="删除题库">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                            
                            <!-- 删除确认内容（隐藏） -->
                            <div class="delete-confirm-row-{{ bank.id }} mt-2 confirm-delete-container" style="display: none;">
                                <div class="alert alert-danger">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                                        <strong>确定要删除题库：{{ bank.name }}？</strong>
                                    </div>
                                    <div class="alert alert-warning py-2 mb-2">
                                        <small><i class="fas fa-info-circle me-1"></i>删除题库将会移除题库中所有题目的关联（但不会删除题目本身）。</small>
                                    </div>
                                    <div class="d-flex justify-content-end">
                                        <button type="button" class="btn btn-sm btn-secondary me-2 cancel-delete-btn" data-id="{{ bank.id }}">
                                            <i class="fas fa-times me-1"></i>取消
                                        </button>
                                        <form action="{{ url_for('delete_question_bank', bank_id=bank.id) }}" method="post" class="delete-form">
                                            <button type="submit" class="btn btn-sm btn-danger confirm-delete-btn">
                                                <i class="fas fa-trash me-1"></i>确认删除
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // 初始化数据表格
        $('#questionBanksTable').DataTable({
            language: {
                "processing": "处理中...",
                "lengthMenu": "每页显示 _MENU_ 条记录",
                "zeroRecords": "没有找到匹配的记录",
                "info": "第 _PAGE_ 页 / 共 _PAGES_ 页 ( 共 _TOTAL_ 条记录 )",
                "infoEmpty": "没有记录",
                "infoFiltered": "(从 _MAX_ 条记录中过滤)",
                "search": "搜索:",
                "emptyTable": "表中没有数据",
                "paginate": {
                    "first": "首页",
                    "previous": "上一页",
                    "next": "下一页",
                    "last": "末页"
                },
                "aria": {
                    "sortAscending": ": 升序排列",
                    "sortDescending": ": 降序排列"
                },
                "autoFill": {
                    "cancel": "取消",
                    "fill": "填充所有单元格",
                    "fillHorizontal": "填充水平单元格",
                    "fillVertical": "填充垂直单元格"
                },
                "buttons": {
                    "collection": "集合",
                    "colvis": "列可见性",
                    "copy": "复制",
                    "copyTitle": "复制到剪贴板",
                    "csv": "CSV",
                    "excel": "Excel",
                    "pdf": "PDF",
                    "print": "打印"
                }
            },
            responsive: true,
            order: [[0, 'desc']],
            pageLength: 10,  // 每页显示10条记录
            lengthMenu: [
                [10, 25, 50, 100, -1],
                ['10条', '25条', '50条', '100条', '全部']
            ],
            pagingType: "full_numbers"  // 显示所有分页按钮
        });
        
        // 初始化工具提示
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
        
        // 初始化Select2下拉框
        $('#course_id').select2({
            dropdownParent: $('#addQuestionBankModal'),
            placeholder: '请选择课程',
            width: '100%'
        });
        
        $('#import_course_id').select2({
            dropdownParent: $('#importQuestionBanksModal'),
            placeholder: '请选择课程',
            width: '100%'
        });
        
        // 打开模态框时重置表单
        $('#addQuestionBankModal').on('show.bs.modal', function() {
            $(this).find('form')[0].reset();
            $('#course_id').val('').trigger('change');
        });
        
        $('#importQuestionBanksModal').on('show.bs.modal', function() {
            $(this).find('form')[0].reset();
            $('#import_course_id').val('').trigger('change');
        });
        
        // 删除按钮点击事件
        $(document).on('click', '.delete-btn', function() {
            const id = $(this).data('id');
            // 隐藏所有其他确认框
            $('.confirm-delete-container').not('.delete-confirm-row-' + id).hide();
            // 显示当前确认框
            $('.delete-confirm-row-' + id).slideDown(200);
        });
        
        // 取消删除按钮点击事件
        $(document).on('click', '.cancel-delete-btn', function() {
            const id = $(this).data('id');
            $('.delete-confirm-row-' + id).slideUp(200);
        });
        
        // 提交删除表单时禁用按钮防止重复提交
        $('.delete-form').on('submit', function() {
            $(this).find('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>删除中...');
        });
        
        // ESC键关闭所有确认框
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape') {
                $('.confirm-delete-container').slideUp(200);
            }
        });
    });
</script>
{% endblock %} 
<!--pages/wrong-questions/wrong-questions.wxml-->
<view class="container">
  <!-- 全局通知组件 -->
  <notification-toast
    show="{{showGlobalNotification}}"
    title="{{globalNotificationTitle}}"
    message="{{globalNotificationMessage}}"
    postId="{{globalNotificationPostId}}"
    bind:hide="onGlobalNotificationHide"
    bind:tap="onGlobalNotificationTap">
  </notification-toast>

  <!-- 页面标题 -->
  <view class="header">
    <text class="title">错题本</text>
    <text class="subtitle">记录你的答错题目，方便复习</text>
  </view>

  <!-- 课程筛选选项卡 - 现在放在选项卡上方 -->
  <view class="course-filter" wx:if="{{coursesList.length > 0}}">
    <scroll-view scroll-x="true" class="course-scroll" enhanced show-scrollbar="{{false}}">
      <view class="course-tabs">
        <view class="course-tab {{activeCourse === 0 ? 'active' : ''}}" bindtap="switchCourse" data-id="0">
          <text>全部课程</text>
        </view>
        <block wx:for="{{coursesList}}" wx:key="id">
          <view class="course-tab {{activeCourse === item.id ? 'active' : ''}}" bindtap="switchCourse" data-id="{{item.id}}">
            <text>{{item.name}}</text>
            <text class="course-count">{{item.question_count}}</text>
          </view>
        </block>
      </view>
    </scroll-view>
  </view>

  <!-- 选项卡 -->
  <view class="tabs">
    <view class="tab {{activeTab === 'all' ? 'active' : ''}}" bindtap="switchTab" data-tab="all">全部</view>
    <view class="tab {{activeTab === 'single' ? 'active' : ''}}" bindtap="switchTab" data-tab="single">单选题</view>
    <view class="tab {{activeTab === 'multiple' ? 'active' : ''}}" bindtap="switchTab" data-tab="multiple">多选题</view>
    <view class="tab {{activeTab === 'judgment' ? 'active' : ''}}" bindtap="switchTab" data-tab="judgment">判断题</view>
    <view class="tab {{activeTab === 'fill_blank' ? 'active' : ''}}" bindtap="switchTab" data-tab="fill_blank">填空题</view>
  </view>
  
  <!-- 开始练习按钮 -->
  <view class="action-buttons" wx:if="{{!loading && (activeTab === 'multiple' || activeTab === 'single' || activeTab === 'judgment' || activeTab === 'fill_blank' || !isEmpty)}}">
    <button class="practice-all-btn" bindtap="practiceAll">开始练习</button>
  </view>

  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading"></view>
    <text class="loading-text">加载错题中...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:if="{{isEmpty && !loading}}">
    <image class="empty-image" src="/images/empty.png" mode="aspectFit"></image>
    <text class="empty-text">还没有错题记录哦</text>
    <text class="empty-hint">答错的题目会自动添加到错题本</text>
  </view>

  <!-- 错题列表 -->
  <view class="question-list" wx:if="{{!isEmpty && !loading}}">
    <block wx:for="{{filteredQuestions}}" wx:key="id">
      <view class="question-card">
        <!-- 题目类型和错误次数 -->
        <view class="question-header">
          <view class="question-type">
            <text class="type-tag {{item.type === 'single' ? 'single' : (item.type === 'multiple' ? 'multiple' : (item.type === 'judgment' ? 'judgment' : (item.type === 'fill_blank' ? 'fillblank' : '')))}}">{{item.type === 'single' ? '单选题' : (item.type === 'multiple' ? '多选题' : (item.type === 'judgment' ? '判断题' : (item.type === 'fill_blank' ? '填空题' : '未知')))}}</text>
            <text class="wrong-count">错误{{item.times_wrong}}次</text>
            <text class="course-name" wx:if="{{item.course_name}}">{{item.course_name}}</text>
          </view>
          <text class="last-wrong-time">{{item.last_wrong_time}}</text>
        </view>

        <!-- 题目内容 -->
        <view class="question-content">
          <text class="question-text">{{item.question}}</text>
        </view>

        <!-- 选项 -->
        <view class="options-list">
          <block wx:for="{{item.options}}" wx:for-item="option" wx:for-index="optionIndex" wx:key="*this">
            <view class="option {{item.answer.includes(optionIndex) ? 'correct' : ''}}">
              <text class="option-letter">{{optionIndex === 0 ? 'A' : optionIndex === 1 ? 'B' : optionIndex === 2 ? 'C' : 'D'}}</text>
              <text class="option-text">{{option}}</text>
              <text class="correct-mark" wx:if="{{item.answer.includes(optionIndex)}}">✓</text>
            </view>
          </block>
        </view>

        <!-- 操作按钮 -->
        <view class="card-actions">
          <button class="action-btn practice-btn" bindtap="practiceQuestion" data-index="{{index}}">练习</button>
          <button class="action-btn remove-btn" bindtap="removeQuestion" data-index="{{index}}">移除</button>
        </view>
      </view>
    </block>
  </view>
</view>
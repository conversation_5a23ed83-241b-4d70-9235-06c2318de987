/* Enhanced Dashboard Charts Styling */

/* Chart containers with better default sizing */
.chart-container {
    position: relative;
    width: 100%;
    min-height: 300px;
    margin: 0 auto;
    overflow: hidden;
}

.chart-container-sm {
    min-height: 180px;
}

.chart-container-lg {
    min-height: 350px;
}

.chart-container canvas {
    width: 100% !important;
    height: 100% !important;
    display: block;
}

/* Donut chart container styles */
.donut-chart-container {
    position: relative;
    width: 100%;
    min-height: 250px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.donut-chart-container canvas {
    max-width: 100%;
    max-height: 100%;
}

/* Bar chart container styles */
.bar-chart-container {
    position: relative;
    width: 100%;
    min-height: 250px;
    margin: 0 auto;
}

/* Chart card enhancements */
.chart-card {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
    height: 100%;
}

.chart-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
}

.chart-card .card-header {
    padding: 1.25rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    background-color: transparent;
}

.chart-card .card-body {
    padding: 1.25rem;
}

/* Chart area with subtle background */
.chart-area {
    background-color: rgba(250, 250, 250, 0.5);
    border-radius: 8px;
    padding: 15px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.03);
    height: 100%;
}

/* Chart fallback styling */
.chart-fallback {
    width: 100%;
    height: 100%;
    min-height: 250px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: rgba(245, 247, 250, 0.5);
    border-radius: 8px;
    padding: 2rem;
}

.chart-fallback .fallback-icon {
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Chart filter buttons */
.chart-actions .btn-group .btn.active {
    background-color: rgba(79, 70, 229, 1);
    color: white;
    border-color: rgba(79, 70, 229, 1);
}

.chart-actions .btn-group .btn {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
}

/* Tooltip style overrides */
.chart-tooltip {
    background-color: rgba(255, 255, 255, 0.95) !important;
    color: #1a202c !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    border-radius: 8px !important;
    padding: 10px !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
    font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif !important;
}

/* Legend style overrides */
.chart-legend {
    padding: 10px !important;
}

.chart-legend li {
    margin-bottom: 5px !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .chart-container {
        min-height: 200px;
    }
    
    .donut-chart-container,
    .bar-chart-container {
        min-height: 180px;
    }
    
    .chart-container-lg {
        min-height: 250px;
    }
}

/* Ensure chart row cards align properly */
.row.chart-row {
    display: flex;
    flex-wrap: wrap;
    align-items: stretch;
}

.row.chart-row > [class*="col-"] {
    display: flex;
    margin-bottom: 1.5rem;
}

.row.chart-row .chart-card {
    width: 100%;
    margin-bottom: 0;
    display: flex;
    flex-direction: column;
}

/* Ensure card body takes full height */
.row.chart-row .chart-card .card-body {
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
}

/* Ensure chart containers fill available space */
.row.chart-row .chart-container,
.row.chart-row .donut-chart-container,
.row.chart-row .bar-chart-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Fix for taller charts to not stretch cards */
.row.chart-row .chart-card.fixed-height {
    height: auto !important;
}

/* Fix for specific charts that need height constraints */
.categories-chart-container {
    min-height: 250px;
    height: 100%;
}

/* Animation keyframes for chart loading */
@keyframes chartFadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.chart-animate {
    animation: chartFadeIn 0.5s ease-out forwards;
} 
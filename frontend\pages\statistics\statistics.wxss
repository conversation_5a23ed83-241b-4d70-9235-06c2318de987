/* pages/statistics/statistics.wxss */
.container {
  padding: 20rpx;
  padding-bottom: 140rpx; /* 为底部tabBar留出足够空间 */
  box-sizing: border-box;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 标题样式 */
.header {
  padding: 20rpx 0 30rpx;
}

.title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
}

.refresh-button {
  display: flex;
  align-items: center;
  background-color: #e6f4ff;
  padding: 12rpx 20rpx;
  border-radius: 30rpx;
}

.refresh-icon {
  margin-right: 10rpx;
  font-size: 28rpx;
}

.refresh-icon.refreshing {
  display: inline-block;
  animation: spin 1s linear infinite;
}

.refresh-text {
  font-size: 26rpx;
  color:rgb(250, 250, 251);
  font-weight: 500;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}

/* 学习概览卡片样式 */
.overview-card {
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.overview-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10rpx;
}

.overview-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.overview-icon.blue {
  background: radial-gradient(circle at center, #f0f9ff 0%, #e6f7ff 100%);
}

.overview-icon.green {
  background: radial-gradient(circle at center, #f0fff0 0%, #e6fff0 100%);
}

.overview-icon.orange {
  background: radial-gradient(circle at center, #fffaf0 0%, #fff7e6 100%);
}

.overview-icon.purple {
  background: radial-gradient(circle at center, #fcf5ff 0%, #f5e8ff 100%);
}

.golden-character {
  font-size: 46rpx;
  font-weight: bold;
  /* Gold gradient */
  background: linear-gradient(to bottom, #f6e681 0%, #D4AF37 50%, #AA771C 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-family: "STXingkai", "华文行楷", "STKaiti", "楷体", serif;
  display: inline-block;
  transform: scale(1.1);
  z-index: 2;
  text-shadow: 0 0 10rpx rgba(255, 215, 0, 0.5),
               0 0 20rpx rgba(255, 215, 0, 0.3),
               0 0 30rpx rgba(255, 215, 0, 0.2);
  position: relative;
}

/* 增强金色文字荧光效果 */
.golden-character::before {
  content: '';
  position: absolute;
  top: -5rpx;
  left: -5rpx;
  right: -5rpx;
  bottom: -5rpx;
  background: radial-gradient(circle, rgba(255, 215, 0, 0.4) 0%, rgba(255, 215, 0, 0) 70%);
  z-index: -1;
  border-radius: 50%;
  filter: blur(5rpx);
  animation: pulse-gold 2s infinite;
}

@keyframes pulse-gold {
  0% { opacity: 0.4; transform: scale(0.95); }
  50% { opacity: 0.8; transform: scale(1.05); }
  100% { opacity: 0.4; transform: scale(0.95); }
}

/* Subtle shine effect */
.overview-icon::after {
  content: '';
  position: absolute;
  top: -10rpx;
  left: -50rpx;
  width: 40rpx;
  height: 100rpx;
  background-color: rgba(255, 255, 255, 0.5);
  transform: rotate(45deg);
  animation: shine 3s infinite;
}

@keyframes shine {
  0% { transform: translateX(-100%) rotate(45deg); }
  20% { transform: translateX(100%) rotate(45deg); }
  100% { transform: translateX(100%) rotate(45deg); }
}

.overview-icon .iconfont {
  font-size: 40rpx;
}

.overview-data {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.overview-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 6rpx;
}

.overview-label {
  font-size: 24rpx;
  color: #666;
}

/* 加载样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 200rpx;
}

/* 页面标题 */
.header {
  margin-bottom: 30rpx;
  border-bottom: 1px solid #eeeeee;
  padding-bottom: 20rpx;
}

.title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  display: block;
}

.refresh-button {
  display: flex;
  align-items: center;
  background-color: #4C84FF;
  color: white;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
}

.refresh-icon {
  margin-right: 10rpx;
  font-size: 28rpx;
}

.refresh-text {
  font-weight: 500;
}

.subtitle {
  font-size: 24rpx;
  color: #666666;
  margin-top: 10rpx;
  display: block;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 100rpx;
}

.loading {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #e0e0e0;
  border-top: 6rpx solid #4C84FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 26rpx;
  color: #666666;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 100rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 10rpx;
}

.empty-hint {
  font-size: 26rpx;
  color: #999999;
}

/* 统计卡片 */
.stats-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
  margin-bottom: 30rpx;
  overflow: hidden;
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20rpx); }
  to { opacity: 1; transform: translateY(0); }
}

.card-header {
  padding: 20rpx 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.card-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
}

.card-content {
  padding: 30rpx;
}

/* 数据总览 */
.data-overview {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.data-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}

.data-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  line-height: 1.2;
}

.primary-color {
  color: #4C84FF;
}

.data-label {
  font-size: 24rpx;
  color: #666666;
  margin-top: 10rpx;
}

/* 题型准确率 */
.accuracy-types {
  margin-top: 10rpx;
}

.type-item {
  margin-bottom: 30rpx;
}

.type-item:last-child {
  margin-bottom: 0;
}

.type-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.type-name {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.type-accuracy {
  font-size: 28rpx;
  color: #4C84FF;
  font-weight: 500;
}

.progress-bar {
  height: 16rpx;
  background-color: #f0f0f0;
  border-radius: 10rpx;
  overflow: hidden;
  margin-bottom: 10rpx;
}

.progress-inner {
  height: 100%;
  background-color: #4C84FF; /* 单选题蓝色 */
  border-radius: 10rpx;
  transition: width 0.5s ease;
}

.progress-inner.multiple {
  background-color: #FF9800; /* 多选题橙色 */
}

.progress-inner.judgment {
  background-color: #9C27B0; /* 判断题紫色 */
}

.progress-inner.fill-blank {
  background-color: #009688; /* 填空题蓝绿色 */
}

.type-details {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.type-detail {
  font-size: 24rpx;
  color: #666666;
  flex: 1;
}

.type-detail.correct {
  color: #4CAF50;
}

.type-detail.wrong {
  color: #F44336;
}

/* 图表 */
.statistics-canvas {
  width: 100%;
  height: 250px;
  margin: 10rpx 0;
}

/* 进度图表容器 */
.progress-chart-container {
  padding-right: 20px;
  overflow-x: visible;
  position: relative;
}

/* 自定义图例 */
.custom-legend {
  text-align: center;
  font-size: 24rpx;
  color: #666666;
  margin-top: 20rpx;
  line-height: 1.5;
}

/* 不同图表的数据点颜色 */
.legend-correct {
  color: #4CAF50;
}

.legend-wrong {
  color: #F44336;
}

.legend-single {
  color: #2196F3;
}

.legend-multiple {
  color: #FF9800;
}

.legend-judgment {
  color: #9C27B0;
}

.legend-fill-blank {
  color: #009688;
}

.legend-single-correct {
  color: #4CAF50;
}

.legend-single-wrong {
  color: #F44336;
}

.legend-multiple-correct {
  color: #8BC34A;
}

.legend-multiple-wrong {
  color: #FF5722;
}

.legend-judgment-correct {
  color: #BA68C8;
}

.legend-judgment-wrong {
  color: #7B1FA2;
}

.legend-fill-blank-correct {
  color: #4DB6AC;
}

.legend-fill-blank-wrong {
  color: #00695C;
}

.legend-accuracy {
  color: #4C84FF;
}

.legend-count {
  color: #FFC107;
}

/* 最近答题记录 */
.daily-records {
  width: 100%;
}

.record-header {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1px solid #eeeeee;
  font-weight: 500;
  color: #333333;
  font-size: 28rpx;
}

.record-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1px solid #f5f5f5;
  font-size: 26rpx;
  color: #666666;
  transition: all 0.3s;
}

.record-item:hover {
  background-color: #f9f9f9;
}

.record-date {
  flex: 1;
}

.record-count {
  flex: 1;
  text-align: center;
}

.record-accuracy {
  flex: 1;
  text-align: right;
  font-weight: 500;
} 
// 直接修复图表显示问题的脚本
// 这个脚本会立即执行，不等待DOM加载完成的事件

(function() {
  console.log('图表直接修复脚本开始执行');
  
  // 保存已初始化的图表
  let chartInstances = {};
  
  // 定义待初始化的图表ID列表
  const chartIds = [
    'questionTypeChart',
    'userActivityPeriodsChart',
    'userActivityChart',
    'activityChart',
    'difficultyChart',
    'activeTimeChart',
    'userAnswerTrendsChart'
  ];
  
  // 立即开始初始化尝试
  tryInitCharts();
  
  // 监听DOM加载完成事件
  document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM加载完成，尝试初始化图表');
    setTimeout(tryInitCharts, 100);
  });
  
  // 监听window加载完成事件
  window.addEventListener('load', function() {
    console.log('窗口加载完成，尝试初始化图表');
    setTimeout(tryInitCharts, 300);
    
    // 多次尝试初始化，确保图表能够显示
    setTimeout(tryInitCharts, 800);
    setTimeout(tryInitCharts, 1500);
    setTimeout(tryInitCharts, 3000);
  });
  
  // 尝试初始化所有图表
  function tryInitCharts() {
    // 检查Chart.js是否可用
    if (typeof Chart === 'undefined') {
      console.warn('Chart.js 未加载，尝试手动加载');
      loadChartJS();
      return;
    }
    
    // 遍历所有待初始化的图表ID
    chartIds.forEach(function(chartId) {
      try {
        initChart(chartId);
      } catch (error) {
        console.error(`初始化图表 ${chartId} 时出错:`, error);
      }
    });
    
    // 查找所有未初始化的图表
    document.querySelectorAll('canvas[id$="Chart"]').forEach(function(canvas) {
      if (!chartInstances[canvas.id]) {
        try {
          initChart(canvas.id);
        } catch (error) {
          console.error(`初始化图表 ${canvas.id} 时出错:`, error);
        }
      }
    });
  }
  
  // 初始化指定ID的图表
  function initChart(chartId) {
    // 如果图表已经初始化，则跳过
    if (chartInstances[chartId]) {
      return;
    }
    
    const canvas = document.getElementById(chartId);
    if (!canvas) {
      console.warn(`未找到图表元素 ${chartId}`);
      return;
    }
    
    // 标记为加载中
    markLoading(canvas);
    
    // 检查Canvas是否绑定了Chart实例
    try {
      const existingChart = Chart.getChart(canvas);
      if (existingChart) {
        console.log(`销毁图表 ${chartId} 的旧实例`);
        existingChart.destroy();
      }
    } catch (e) {
      console.warn(`检查图表 ${chartId} 实例时出错:`, e);
    }
    
    // 解析数据或使用默认值
    let chartData;
    let chartType;
    let chartOptions;
    
    try {
      if (canvas.dataset.chartValues) {
        chartData = JSON.parse(canvas.dataset.chartValues);
      }
    } catch (e) {
      console.warn(`解析图表 ${chartId} 数据时出错:`, e);
    }
    
    // 如果没有解析到数据，使用默认数据
    if (!chartData) {
      const defaults = getDefaultChartData(chartId);
      chartData = defaults.data;
      chartType = defaults.type;
      chartOptions = defaults.options;
    } else {
      // 根据图表ID确定类型和选项
      chartType = determineChartType(chartId);
      chartOptions = getChartOptions(chartId, chartType);
    }
    
    // 创建图表
    try {
      const ctx = canvas.getContext('2d');
      
      // 对于line图表，创建渐变背景
      if (chartType === 'line' && chartData.datasets && chartData.datasets[0]) {
        const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
        gradient.addColorStop(0, 'rgba(79, 70, 229, 0.4)');
        gradient.addColorStop(1, 'rgba(79, 70, 229, 0.0)');
        
        // 应用渐变
        if (chartData.datasets[0].backgroundColor) {
          chartData.datasets[0].backgroundColor = gradient;
        }
      }
      
      console.log(`创建图表 ${chartId}，类型: ${chartType}`);
      const chart = new Chart(ctx, {
        type: chartType,
        data: chartData,
        options: chartOptions
      });
      
      // 保存实例并标记为已初始化
      chartInstances[chartId] = chart;
      markInitialized(canvas);
      console.log(`图表 ${chartId} 初始化成功`);
      
    } catch (error) {
      console.error(`创建图表 ${chartId} 失败:`, error);
      // 即使失败也标记为已初始化，防止UI显示错误
      markInitialized(canvas);
    }
  }
  
  // 根据图表ID确定图表类型
  function determineChartType(chartId) {
    const id = chartId.toLowerCase();
    
    if (id.includes('pie') || id.includes('type') || id.includes('distribution')) {
      return 'pie';
    } else if (id.includes('doughnut') || id.includes('difficulty')) {
      return 'doughnut';
    } else if (id.includes('polar') || id.includes('active') && id.includes('time')) {
      return 'polarArea';
    } else if (id.includes('bar') || id.includes('period')) {
      return 'bar';
    } else {
      // 默认为折线图
      return 'line';
    }
  }
  
  // 获取图表选项
  function getChartOptions(chartId, chartType) {
    // 基本选项
    const baseOptions = {
      responsive: true,
      maintainAspectRatio: false,
      animation: {
        duration: 1000
      },
      plugins: {
        legend: {
          display: chartType === 'pie' || chartType === 'doughnut' || chartType === 'polarArea',
          position: 'bottom',
          labels: {
            usePointStyle: true,
            padding: 15,
            boxWidth: 10
          }
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleFont: {
            size: 14
          },
          bodyFont: {
            size: 13
          }
        }
      }
    };
    
    // 为不同类型的图表添加特定选项
    if (chartType === 'line') {
      return {
        ...baseOptions,
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              padding: 10
            }
          },
          x: {
            grid: {
              display: false
            }
          }
        },
        elements: {
          point: {
            radius: 4,
            hoverRadius: 6,
            backgroundColor: '#ffffff'
          },
          line: {
            tension: 0.4
          }
        }
      };
    } else if (chartType === 'bar') {
      return {
        ...baseOptions,
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              padding: 10
            }
          },
          x: {
            grid: {
              display: false
            }
          }
        }
      };
    } else if (chartType === 'doughnut') {
      return {
        ...baseOptions,
        cutout: '65%',
        plugins: {
          ...baseOptions.plugins,
          tooltip: {
            ...baseOptions.plugins.tooltip,
            callbacks: {
              label: function(context) {
                const label = context.label || '';
                const value = context.raw;
                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                const percentage = Math.round((value / total) * 100);
                return `${label}: ${percentage}%`;
              }
            }
          }
        },
        elements: {
          arc: {
            borderWidth: 0,
            borderRadius: 4
          }
        }
      };
    } else if (chartType === 'pie') {
      return {
        ...baseOptions,
        plugins: {
          ...baseOptions.plugins,
          tooltip: {
            ...baseOptions.plugins.tooltip,
            callbacks: {
              label: function(context) {
                const label = context.label || '';
                const value = context.raw;
                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                const percentage = Math.round((value / total) * 100);
                return `${label}: ${percentage}%`;
              }
            }
          }
        }
      };
    } else if (chartType === 'polarArea') {
      return {
        ...baseOptions,
        scales: {
          r: {
            ticks: {
              backdropColor: 'transparent',
              z: 100
            }
          }
        }
      };
    }
    
    return baseOptions;
  }
  
  // 根据图表ID获取默认数据
  function getDefaultChartData(chartId) {
    const id = chartId.toLowerCase();
    
    if (id === 'questiontypechart') {
      return {
        type: 'pie',
        data: {
          labels: ['单选题', '多选题'],
          datasets: [{
            data: [60, 40],
            backgroundColor: [
              'rgba(79, 70, 229, 0.8)',
              'rgba(245, 158, 11, 0.8)'
            ]
          }]
        },
        options: getChartOptions(chartId, 'pie')
      };
    } else if (id === 'useractivityperiodschart') {
      return {
        type: 'bar',
        data: {
          labels: ['上午', '下午', '晚上', '深夜'],
          datasets: [{
            label: '活跃人数',
            data: [15, 30, 20, 10],
            backgroundColor: [
              'rgba(79, 70, 229, 0.6)',
              'rgba(59, 130, 246, 0.6)',
              'rgba(16, 185, 129, 0.6)',
              'rgba(245, 158, 11, 0.6)'
            ]
          }]
        },
        options: getChartOptions(chartId, 'bar')
      };
    } else if (id === 'useractivitychart') {
      return {
        type: 'line',
        data: {
          labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
          datasets: [{
            label: '活跃用户数',
            data: [15, 25, 20, 12, 30, 35, 25],
            backgroundColor: 'rgba(79, 70, 229, 0.2)',
            borderColor: '#4f46e5',
            borderWidth: 2,
            tension: 0.4
          }]
        },
        options: getChartOptions(chartId, 'line')
      };
    } else if (id === 'activitychart') {
      return {
        type: 'line',
        data: {
          labels: ['一月', '二月', '三月', '四月', '五月', '六月', '七月'],
          datasets: [{
            label: '答题数',
            data: [45, 60, 55, 50, 65, 80, 70],
            backgroundColor: 'rgba(79, 70, 229, 0.2)',
            borderColor: '#4f46e5',
            borderWidth: 2,
            fill: true,
            tension: 0.4
          }]
        },
        options: getChartOptions(chartId, 'line')
      };
    } else if (id === 'difficultychart') {
      return {
        type: 'doughnut',
        data: {
          labels: ['简单', '中等', '困难'],
          datasets: [{
            data: [40, 35, 25],
            backgroundColor: [
              'rgba(16, 185, 129, 0.8)',
              'rgba(245, 158, 11, 0.8)',
              'rgba(239, 68, 68, 0.8)'
            ]
          }]
        },
        options: getChartOptions(chartId, 'doughnut')
      };
    } else if (id === 'activetimechart') {
      return {
        type: 'polarArea',
        data: {
          labels: ['上午', '下午', '晚上', '深夜'],
          datasets: [{
            label: '活跃人数',
            data: [15, 30, 20, 10],
            backgroundColor: [
              'rgba(79, 70, 229, 0.6)',
              'rgba(59, 130, 246, 0.6)',
              'rgba(16, 185, 129, 0.6)',
              'rgba(245, 158, 11, 0.6)'
            ]
          }]
        },
        options: getChartOptions(chartId, 'polarArea')
      };
    } else if (id === 'useranswertrendschart') {
      return {
        type: 'line',
        data: {
          labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
          datasets: [{
            label: '答题数',
            data: [45, 60, 55, 50, 65, 80, 70],
            backgroundColor: 'rgba(79, 70, 229, 0.2)',
            borderColor: '#4f46e5',
            borderWidth: 2,
            tension: 0.4,
            fill: true
          }]
        },
        options: getChartOptions(chartId, 'line')
      };
    } else {
      // 通用默认数据
      return {
        type: 'bar',
        data: {
          labels: ['类别1', '类别2', '类别3', '类别4', '类别5'],
          datasets: [{
            label: '数据系列',
            data: [25, 30, 20, 15, 10],
            backgroundColor: [
              'rgba(79, 70, 229, 0.6)',
              'rgba(59, 130, 246, 0.6)',
              'rgba(16, 185, 129, 0.6)',
              'rgba(245, 158, 11, 0.6)',
              'rgba(239, 68, 68, 0.6)'
            ]
          }]
        },
        options: getChartOptions(chartId, 'bar')
      };
    }
  }
  
  // 标记图表为加载中
  function markLoading(canvas) {
    if (!canvas) return;
    
    const container = canvas.closest('.chart-container') || canvas.parentElement;
    if (container) {
      container.classList.remove('chart-initialized');
      container.classList.remove('chart-error');
      container.classList.add('chart-loading');
    }
  }
  
  // 标记图表为已初始化
  function markInitialized(canvas) {
    if (!canvas) return;
    
    const container = canvas.closest('.chart-container') || canvas.parentElement;
    if (container) {
      container.classList.remove('chart-loading');
      container.classList.remove('chart-error');
      container.classList.add('chart-initialized');
      
      // 移除任何错误信息
      const errorMsg = container.querySelector('.chart-error-message');
      if (errorMsg) {
        errorMsg.remove();
      }
    }
  }
  
  // 手动加载Chart.js
  function loadChartJS() {
    // 检查是否已经有正在加载的脚本
    if (document.querySelector('script[src*="chart.min.js"]')) {
      console.log('Chart.js 已经在加载中');
      return;
    }
    
    console.log('手动加载 Chart.js');
    
    // 创建Chart.js脚本元素
    const chartScript = document.createElement('script');
    chartScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js';
    chartScript.integrity = 'sha512-ElRFoEQdI5Ht6kZvyzXhYG9NqjtkmlkfYk0wr6wHxU9JEHakS7UJZNeml5ALk+8IKlU6jDgMabC3vkumRokgJA==';
    chartScript.crossOrigin = 'anonymous';
    chartScript.referrerPolicy = 'no-referrer';
    
    // 创建DataLabels插件脚本元素
    const dataLabelsScript = document.createElement('script');
    dataLabelsScript.src = 'https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0';
    
    // 加载脚本后再次尝试初始化
    chartScript.onload = function() {
      console.log('Chart.js 加载成功');
      dataLabelsScript.onload = function() {
        console.log('ChartDataLabels 插件加载成功');
        window.ChartDataLabels = window.ChartDataLabels;
        
        // 延迟一段时间再初始化
        setTimeout(function() {
          tryInitCharts();
        }, 200);
      };
      document.head.appendChild(dataLabelsScript);
    };
    
    document.head.appendChild(chartScript);
  }
})(); 
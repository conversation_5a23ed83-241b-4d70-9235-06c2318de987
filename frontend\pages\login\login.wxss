/* login.wxss */
page {
  background: linear-gradient(to bottom, #f0f5ff, #f8f9fa);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* 自定义导航栏样式 */
.custom-navigation-bar {
  width: 100%;
  display: flex;
  align-items: flex-end;
  background-color: #4C84FF;
  box-sizing: border-box;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
}

.navigation-bar-title {
  width: 100%;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  color: white;
  padding-bottom: 10px;
  height: 44px;
  line-height: 44px;
}

/* 调整容器的上边距，为导航栏腾出空间 */
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-left: 30rpx;
  padding-right: 30rpx;
  padding-bottom: 40rpx;
  box-sizing: border-box;
  min-height: 100vh;
  position: relative;
}

/* Logo区域样式 */
.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 60rpx 0 40rpx;
  width: 100%;
  animation: fadeIn 0.8s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-20rpx); }
  to { opacity: 1; transform: translateY(0); }
}

.logo-circle {
  width: 180rpx;
  height: 180rpx;
  background: linear-gradient(135deg, #5e90f8, #4568DC);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 15rpx 30rpx rgba(78, 126, 232, 0.3);
  margin-bottom: 30rpx;
  position: relative;
  z-index: 2;
  border: 10rpx solid rgba(255, 255, 255, 0.8);
  overflow: hidden;
}

.logo-circle::before {
  content: "";
  position: absolute;
  top: -10rpx;
  left: -10rpx;
  right: -10rpx;
  bottom: -10rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  z-index: 1;
}

.logo-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
}

/* 图形图标 */
.logo-graphic {
  width: 100rpx;
  height: 100rpx;
  position: relative;
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
}

/* 柱状图样式 */
.bar {
  width: 25rpx;
  background: #fff;
  border-radius: 12rpx;
  position: relative;
  bottom: 0;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.bar1 {
  height: 50rpx;
  animation: barMove 2s infinite ease-in-out;
}

.bar2 {
  height: 75rpx;
  animation: barMove 2s infinite ease-in-out 0.3s;
}

.bar3 {
  height: 100rpx;
  animation: barMove 2s infinite ease-in-out 0.6s;
}

@keyframes barMove {
  0% { transform: scaleY(1); }
  50% { transform: scaleY(0.8); }
  100% { transform: scaleY(1); }
}

/* 数据点样式 */
.data-dot {
  width: 16rpx;
  height: 16rpx;
  background: #fff;
  border-radius: 50%;
  position: absolute;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.dot1 {
  top: 25rpx;
  left: 8rpx;
  animation: pulse 3s infinite ease-in-out;
}

.dot2 {
  top: 40rpx;
  left: 50%;
  transform: translateX(-50%);
  animation: pulse2 3s infinite ease-in-out 0.5s;
}

.dot3 {
  top: 15rpx;
  right: 8rpx;
  animation: pulse 3s infinite ease-in-out 1s;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

@keyframes pulse2 {
  0% { transform: translateX(-50%) scale(1); }
  50% { transform: translateX(-50%) scale(1.2); }
  100% { transform: translateX(-50%) scale(1); }
}

.app-name {
  font-size: 42rpx;
  font-weight: bold;
  color: #3a3a3a;
  margin-bottom: 16rpx;
  letter-spacing: 2rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.app-desc {
  font-size: 28rpx;
  color: #666;
  letter-spacing: 1rpx;
}

/* 主卡片样式 */
.main-card {
  width: 100%;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 24rpx;
  padding: 20rpx;
  box-shadow: 0 15rpx 40rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10rpx);
  position: relative;
  z-index: 2;
  animation: slideUp 0.6s ease-out;
  box-sizing: border-box;
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(60rpx); }
  to { opacity: 1; transform: translateY(0); }
}

.profile-form {
  width: 100%;
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.03);
  margin-bottom: 30rpx;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
}

.profile-form::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 10rpx;
  background: linear-gradient(90deg, #4e7ee8, #6a9dff);
  border-radius: 5rpx 5rpx 0 0;
}

.section-title {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
  font-weight: 600;
}

.avatar-section, .nickname-section {
  margin-bottom: 30rpx;
}

.avatar-container {
  display: flex;
  justify-content: center;
  margin-bottom: 20rpx;
}

.avatar-wrapper {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  background: none;
  padding: 0;
  margin: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  border: none;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 10rpx 20rpx rgba(78, 126, 232, 0.15);
}

.avatar-wrapper:active {
  transform: scale(0.95);
}

.avatar-wrapper::after {
  border: none;
}

.avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  border: 6rpx solid #fff;
  transition: all 0.3s ease;
}

.avatar-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50rpx;
  background: linear-gradient(to top, rgba(78, 126, 232, 0.8), rgba(78, 126, 232, 0.6));
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
}

.avatar-icon {
  color: #fff;
  font-size: 24rpx;
}

.avatar-tip, .nickname-tip {
  font-size: 24rpx;
  color: #888;
  text-align: center;
  display: block;
}

.input-container {
  position: relative;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}

.input-prefix {
  position: absolute;
  left: 20rpx;
  z-index: 1;
}

.input-prefix-icon {
  font-size: 28rpx;
  color: #6a9dff;
}

.nickname-input {
  height: 80rpx;
  border-radius: 40rpx;
  background: #f5f7fa;
  padding: 0 40rpx 0 60rpx;
  font-size: 28rpx;
  color: #333;
  border: 2rpx solid #e8edf5;
  width: 100%;
  box-sizing: border-box;
  transition: all 0.3s ease;
  box-shadow: inset 0 2rpx 5rpx rgba(0, 0, 0, 0.02);
}

.nickname-input:focus {
  border-color: #4e7ee8;
  background: #fff;
  box-shadow: 0 0 0 5rpx rgba(78, 126, 232, 0.1);
}

.input-icon {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  color: #4CAF50;
  font-size: 32rpx;
}

.login-btn {
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
  box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.08);
  border: none;
  font-weight: 600;
  letter-spacing: 2rpx;
  transition: all 0.3s ease;
}

.login-btn::after {
  border: none;
}

.wxlogin-btn {
  background: linear-gradient(90deg, #4568DC, #5E8BFE);
  color: #fff;
  position: relative;
  overflow: hidden;
}

.wxlogin-btn::before {
  content: "";
  position: absolute;
  top: -10rpx;
  left: -100rpx;
  width: 120rpx;
  height: 200%;
  background: rgba(255, 255, 255, 0.2);
  transform: rotate(20deg);
  transition: all 0.6s ease;
}

.wxlogin-btn:active::before {
  left: 120%;
}

.visitor-btn {
  background: #fff;
  color: #444;
  border: 2rpx solid #e8edf5;
  margin-bottom: 30rpx;
}

.btn-icon {
  margin-right: 16rpx;
  font-size: 32rpx;
}

/* 协议部分 */
.agreement-section {
  text-align: center;
  margin-bottom: 20rpx;
  animation: fadeIn 0.8s ease-out forwards;
  animation-delay: 0.3s;
  opacity: 0;
}

.agreement-text {
  font-size: 24rpx;
  color: #888;
}

.agreement-link {
  font-size: 24rpx;
  color: #4e7ee8;
  margin: 0 6rpx;
  font-weight: 500;
}

/* 按钮悬浮效果 */
.button-hover {
  transform: translateY(-5rpx);
  box-shadow: 0 15rpx 25rpx rgba(0, 0, 0, 0.1);
}

/* 波浪背景效果 */
.wave-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 240rpx;
  overflow: hidden;
  z-index: 1;
  pointer-events: none;
}

.wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 200%;
  height: 100%;
  background-repeat: repeat no-repeat;
  background-position: 0 bottom;
  transform-origin: center bottom;
}

.wave1 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%234e7ee8' fill-opacity='0.08' d='M0,192L48,197.3C96,203,192,213,288,229.3C384,245,480,267,576,250.7C672,235,768,181,864,181.3C960,181,1056,235,1152,234.7C1248,235,1344,181,1392,154.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
  animation: wave 15s linear infinite;
}

.wave2 {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%234e7ee8' fill-opacity='0.05' d='M0,256L48,245.3C96,235,192,213,288,186.7C384,160,480,128,576,128C672,128,768,160,864,160C960,160,1056,128,1152,117.3C1248,107,1344,117,1392,122.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
  animation: wave 10s linear infinite;
}

@keyframes wave {
  0% {
    transform: translateX(0) translateZ(0);
  }
  50% {
    transform: translateX(-25%) translateZ(0);
  }
  100% {
    transform: translateX(-50%) translateZ(0);
  }
} 
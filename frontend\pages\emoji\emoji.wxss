/* pages/emoji/emoji.wxss */

/* 状态栏占位 */
.status-bar-placeholder {
  height: env(safe-area-inset-top);
  background-color: #4C84FF;
}

.emoji-container {
  height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 导航栏 */
.emoji-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 16px;
  padding-top: calc(10px + env(safe-area-inset-top));
  background: #4C84FF;
  color: #fff;
}

.header-left {
  width: 60px;
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 24px;
  font-weight: bold;
}

.header-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}

.header-right {
  width: 60px;
}

/* 搜索框 */
.search-container {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
}

.search-input {
  flex: 1;
  height: 36px;
  background: #f8f9fa;
  border-radius: 18px;
  padding: 0 16px;
  font-size: 14px;
  border: none;
}

.search-btn {
  width: 36px;
  height: 36px;
  margin-left: 8px;
  background: #4C84FF;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}

/* 分类标签 */
.category-tabs {
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  white-space: nowrap;
}

.tab-item {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 16px;
  margin-right: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: #e3f2fd;
  color: #4C84FF;
}

.tab-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.tab-name {
  font-size: 12px;
}

/* 表情包列表 */
.emoji-list {
  flex: 1;
  background: #fff;
}

.emoji-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 16px;
  gap: 12px;
}

.emoji-item {
  width: calc(25% - 9px);
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: transparent;
  border-radius: 12px;
  padding: 8px;
  transition: all 0.3s ease;
}

.emoji-item:active {
  background: rgba(76, 132, 255, 0.1);
  transform: scale(0.95);
}

.emoji-icon {
  font-size: 32px;
  margin-bottom: 4px;
  background: none !important;
  background-color: transparent !important;
  background-image: none !important;
  text-shadow: none !important;
  box-shadow: none !important;
  border: none !important;
  outline: none !important;
}

.emoji-name {
  font-size: 10px;
  color: #666;
  text-align: center;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}

/* 空状态 */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.empty-text {
  color: #999;
  font-size: 14px;
}

/* 加载状态 */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.loading-text {
  color: #999;
  font-size: 14px;
}

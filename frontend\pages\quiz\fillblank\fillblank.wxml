<!--pages/quiz/fillblank/fillblank.wxml-->
<view class="container">
  <!-- 头部进度条 -->
  <view class="header">
    <view class="progress-container">
      <view class="progress-text-container">
        <text class="progress-text">{{currentQuestionIndex + 1}}/{{questions.length}}</text>
      </view>
      <progress percent="{{progress}}" stroke-width="3" activeColor="#009688" backgroundColor="#e5e5e5" />
    </view>
    
    <!-- 课程筛选 -->
    <view class="course-filter" wx:if="{{coursesList.length > 0 && mode !== 'wrong'}}">
      <scroll-view scroll-x="true" class="course-list">
        <view 
          class="course-item {{activeCourse === 0 ? 'active' : ''}}" 
          bindtap="switchCourse" 
          data-id="0"
        >
          全部
        </view>
        <view 
          wx:for="{{coursesList}}" 
          wx:key="id"
          class="course-item {{activeCourse === item.id ? 'active' : ''}}"
          bindtap="switchCourse"
          data-id="{{item.id}}"
        >
          {{item.name}}
        </view>
      </scroll-view>
    </view>
  </view>
  
  <view class="quiz-content" wx:if="{{!loading && questions.length > 0}}">
    <view class="question">
      <text class="question-text">{{questions[currentQuestionIndex].question}}</text>
      <text class="question-hint">(填空题，请在下方输入答案)</text>
    </view>
    
    <view class="answer-input-container {{showResult ? (isCorrect ? 'correct' : 'wrong') : ''}} {{isFocused ? 'focused' : ''}}">
      <view class="option-letter">A</view>
      <input 
        class="answer-input" 
        placeholder="请输入答案" 
        value="{{userAnswer}}"
        bindinput="handleInput"
        bindfocus="handleFocus"
        bindblur="handleBlur"
        disabled="{{showResult}}"
      />
      <view class="status-icon" wx:if="{{showResult}}">
        <text class="icon-text">{{isCorrect ? '✓' : '✗'}}</text>
      </view>
    </view>
    
    <!-- 提交按钮 -->
    <view class="action-buttons" wx:if="{{!showResult}}">
      <button 
        class="action-btn submit-btn" 
        bindtap="submitAnswer"
        disabled="{{!userAnswer}}"
      >
        提交答案
      </button>
    </view>
    
    <!-- 显示正确答案及解释 -->
    <view class="answer-section" wx:if="{{showResult}}">
      <view class="correct-answer">
        <text class="answer-label">正确答案：</text>
        <view class="answer-value">
          <text class="answer-pill">{{correctAnswer}}</text>
        </view>
      </view>
      
      <view class="result-feedback">
        <text class="{{isCorrect ? 'success-text' : 'error-text'}}">{{isCorrect ? '太棒了，回答正确！' : '很遗憾，回答错误！'}}</text>
      </view>
      
      <view wx:if="{{questions[currentQuestionIndex].explanation}}" class="explanation">
        <text class="explanation-text">{{questions[currentQuestionIndex].explanation}}</text>
      </view>
      
      <!-- 下一题按钮 -->
      <view class="action-buttons next-btn-container">
        <button 
          class="action-btn next-btn" 
          bindtap="nextQuestion"
        >
          {{currentQuestionIndex + 1 >= questions.length ? '完成答题' : '下一题'}}
        </button>
      </view>
    </view>
  </view>
  
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading"></view>
    <text class="loading-text">加载题目中...</text>
  </view>
  
  <!-- 空状态 - 没有题目 -->
  <view class="empty-state" wx:if="{{!loading && emptyState}}">
    <image class="empty-icon" src="/images/empty.png" mode="aspectFit"></image>
    <text class="empty-text">{{emptyStateMessage}}</text>
    <button class="btn-return" bindtap="navigateBack">返回上一页</button>
  </view>
  
  <!-- 完成确认对话框 -->
  <view class="modal-container" wx:if="{{showConfirmFinish}}">
    <view class="modal">
      <view class="modal-header">
        <text class="modal-title">完成答题</text>
      </view>
      <view class="modal-content">
        <text>您已完成所有填空题，共{{questions.length}}题，答对{{correctCount}}题。</text>
        <text>查看详细答题结果？</text>
      </view>
      <view class="modal-footer">
        <button class="modal-btn cancel-btn" bindtap="cancelFinish">继续</button>
        <button class="modal-btn confirm-btn" bindtap="confirmFinish">查看结果</button>
      </view>
    </view>
  </view>
</view> 
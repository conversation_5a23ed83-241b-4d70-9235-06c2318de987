<!-- single.wxml -->
<view class="container">
  <!-- 顶部进度条 -->
  <view class="progress-bar">
    <view class="progress-inner" style="width: {{progress}}%"></view>
  </view>
  
  <text class="question-counter">{{currentQuestionIndex + 1}} / {{questions.length}}</text>
  
  <!-- 课程筛选选项卡 - 在错题本模式或单题练习模式下不显示 -->
  <view class="course-filter" wx:if="{{coursesList && coursesList.length > 0 && mode !== 'wrong' && mode !== 'practice'}}">
    <scroll-view scroll-x="true" class="course-scroll" enhanced show-scrollbar="{{false}}">
      <view class="course-tabs">
        <view class="course-tab {{activeCourse === 0 ? 'active' : ''}}" bindtap="switchCourse" data-id="0">
          <text>全部课程</text>
        </view>
        <block wx:for="{{coursesList}}" wx:key="id">
          <view class="course-tab {{activeCourse === item.id ? 'active' : ''}}" bindtap="switchCourse" data-id="{{item.id}}">
            <text>{{item.name}}</text>
            <text class="course-count">{{item.question_count}}</text>
          </view>
        </block>
      </view>
    </scroll-view>
  </view>
  
  <!-- 题目内容 -->
  <view class="question-container" wx:if="{{!loading && questions.length > 0}}">
    <text class="question">{{questions[currentQuestionIndex].question}}</text>
    
    <!-- 选项列表 -->
    <view class="options-list">
      <view 
        class="option {{selectedOption === index ? 'selected' : ''}} {{showResult && index === correctAnswer ? 'correct' : ''}} {{showResult && selectedOption === index && index !== correctAnswer ? 'wrong' : ''}}"
        wx:for="{{questions[currentQuestionIndex].options}}" 
        wx:key="index"
        bindtap="selectOption"
        data-index="{{index}}"
      >
        <text class="option-letter">{{index === 0 ? 'A' : index === 1 ? 'B' : index === 2 ? 'C' : 'D'}}</text>
        <text class="option-text">{{item}}</text>
        
        <!-- 对错图标 -->
        <view class="result-icon" wx:if="{{showResult}}">
          <text wx:if="{{index === correctAnswer}}" class="correct-icon">✓</text>
          <text wx:if="{{selectedOption === index && index !== correctAnswer}}" class="wrong-icon">✗</text>
        </view>
      </view>
    </view>
    
    <!-- 答案结果提示 -->
    <view class="result-tip" wx:if="{{showResult}}">
      <text class="{{isCorrect ? 'correct-tip' : 'wrong-tip'}}">
        {{isCorrect ? '恭喜你，回答正确！' : '很遗憾，回答错误！'}}
      </text>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button 
        class="action-btn submit-btn" 
        bindtap="submitAnswer" 
        wx:if="{{!showResult}}"
        loading="{{isSubmitting}}"
      >
        提交答案
      </button>
      
      <button 
        class="action-btn next-btn" 
        bindtap="nextQuestion" 
        wx:if="{{showResult}}"
      >
        {{currentQuestionIndex + 1 >= questions.length ? '完成答题' : '下一题'}}
      </button>
    </view>
  </view>
  
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading"></view>
    <text class="loading-text">加载题目中...</text>
  </view>
  
  <!-- 完成确认对话框 -->
  <view class="modal-container" wx:if="{{showConfirmFinish}}">
    <view class="modal">
      <view class="modal-header">
        <text class="modal-title">完成答题</text>
      </view>
      <view class="modal-content">
        <text>您已完成所有单选题，共{{questions.length}}题，答对{{correctCount}}题。</text>
        <text>查看详细答题结果？</text>
      </view>
      <view class="modal-footer">
        <button class="modal-btn cancel-btn" bindtap="cancelFinish">继续</button>
        <button class="modal-btn confirm-btn" bindtap="confirmFinish">查看结果</button>
      </view>
    </view>
  </view>
</view> 
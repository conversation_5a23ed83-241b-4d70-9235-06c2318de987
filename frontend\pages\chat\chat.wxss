/* 页面包装器 */
.page-wrapper {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 状态栏占位 */
.status-bar-placeholder {
  height: env(safe-area-inset-top);
  background-color: #4C84FF;
}

/* 主容器 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  background: linear-gradient(135deg, #4C84FF 0%, #6c5ce7 100%);
  padding: 0 16px;
  padding-top: 44px;
  box-shadow: 0 2px 10px rgba(76, 132, 255, 0.3);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.nav-left, .nav-right {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 22px;
  transition: all 0.3s ease;
}

.nav-left:active, .nav-right:active {
  background-color: rgba(255, 255, 255, 0.1);
  transform: scale(0.95);
}

.nav-icon {
  font-size: 20px;
  color: #fff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.back-icon {
  font-size: 32px;
  font-weight: bold;
}

.nav-title {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 聊天消息区域 */
.chat-messages {
  flex: 1;
  padding: 12px; /* 减少左右内边距，让头像到边缘距离相等 */
  padding-bottom: 120px; /* 增加底部内边距，防止消息被遮挡 */
  margin-bottom: 80px;
  margin-top: 88px; /* 为固定导航栏留出空间 */
  min-height: calc(100vh - 168px); /* 确保内容区域高度足够 */
}

.message-list {
  min-height: 100%;
}

.message-item {
  margin-bottom: 20px;
  animation: fadeInUp 0.3s ease-out;
  width: 100%;
  display: block;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  max-width: 85%;
}

/* 接收的消息布局 - 头像在左，气泡在右 */
.received-message {
  flex-direction: row;
  justify-content: flex-start;
  margin-left: 8px; /* 减少左侧头像边距 */
}

/* 发送的消息布局 - 气泡在左，头像在右 */
.sent-message {
  flex-direction: row;
  justify-content: flex-end;
  max-width: 85%;
  margin-left: auto;
  margin-right: 20px; /* 增加右侧头像边距，让它离屏幕边缘更远 */
}

.message-avatar {
  width: 42px;
  height: 42px;
  border-radius: 21px;
  flex-shrink: 0;
  border: 2px solid #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}



.message-bubble {
  max-width: calc(100% - 54px);
  padding: 14px 18px;
  border-radius: 20px;
  position: relative;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

/* 接收的消息气泡 */
.received-message .message-bubble {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-bottom-left-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.received-message .message-bubble::before {
  content: '';
  position: absolute;
  left: -8px;
  bottom: 8px;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 8px 8px 0;
  border-color: transparent #ffffff transparent transparent;
  filter: drop-shadow(-1px 1px 1px rgba(0, 0, 0, 0.1));
}

/* 发送的消息气泡 */
.sent-message .message-bubble {
  background: linear-gradient(135deg, #4C84FF 0%, #6c5ce7 100%);
  border-bottom-right-radius: 8px;
  color: #fff;
}

.sent-message .message-bubble::before {
  content: '';
  position: absolute;
  right: -8px;
  bottom: 8px;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 8px 0 0 8px;
  border-color: #4C84FF transparent transparent transparent;
  filter: drop-shadow(1px 1px 1px rgba(0, 0, 0, 0.1));
}

.sent-message .message-text {
  color: #fff;
}

.message-text {
  font-size: 16px;
  line-height: 1.5;
  color: #333;
  word-wrap: break-word;
  margin: 0;
}

/* 媒体消息容器（图片和表情包，无气泡） */
.message-media {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 220px;
}

.sent-message .message-media {
  align-items: flex-end;
}

.received-message .message-media {
  align-items: flex-start;
}

/* 图片消息样式 */
.message-image {
  width: 200px;
  height: 200px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  border: 2px solid rgba(255, 255, 255, 0.8);
}

/* 表情包消息样式 */
.message-emoji {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
}

.message-emoji .emoji-icon {
  font-size: 48px;
  background: none !important;
  background-color: transparent !important;
  background-image: none !important;
  text-shadow: none !important;
  box-shadow: none !important;
  border: none !important;
  outline: none !important;
}

/* 定位消息样式 */
.message-location {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 1px solid rgba(76, 132, 255, 0.2);
  border-radius: 12px;
  padding: 12px;
  min-width: 200px;
  max-width: 250px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.message-location:active {
  transform: scale(0.98);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.location-icon {
  font-size: 24px;
  margin-right: 12px;
  color: #4C84FF;
}

.location-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.location-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  line-height: 1.2;
}

.location-address {
  font-size: 12px;
  color: #666;
  line-height: 1.3;
  word-break: break-all;
}

/* 媒体消息时间样式 */
.message-time-media {
  font-size: 10px;
  color: #999;
  margin-top: 4px;
  text-align: center;
  opacity: 0.8;
}

/* 消息包装器 */
.message-wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  max-width: 100%;
  flex: 1;
}

.sent-message .message-wrapper {
  align-items: flex-end;
}

/* 气泡内时间（5分钟内显示） */
.message-time-inside {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 4px;
  text-align: right;
  align-self: flex-end;
}

.received-message .message-time-inside {
  color: rgba(0, 0, 0, 0.5);
}

/* 气泡外时间（超过5分钟显示） */
.message-time-outside {
  font-size: 11px;
  color: #999;
  margin-top: 4px;
  margin-left: 8px;
  margin-right: 8px;
}

.sent-message .message-time-outside {
  text-align: right;
  align-self: flex-end;
}

/* 保持原有的message-time样式以兼容 */
.message-time {
  font-size: 11px;
  color: #999;
  margin-top: 6px;
  display: block;
  text-align: right;
}

.received-message .message-time {
  text-align: left;
}

.sent-message .message-time {
  color: rgba(255, 255, 255, 0.8);
  text-align: right;
}

/* 空状态 */
.empty-messages {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  opacity: 0.8;
}

.empty-text {
  font-size: 16px;
  color: #666;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 20px 30px;
  border-radius: 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(76, 132, 255, 0.1);
}

/* 输入区域 */
.chat-input-area {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 16px 20px;
  padding-bottom: calc(16px + env(safe-area-inset-bottom));
  border-top: 1px solid rgba(76, 132, 255, 0.1);
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.input-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
  background-color: #fff;
  border-radius: 25px;
  padding: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(76, 132, 255, 0.1);
}

.chat-input {
  flex: 1;
  height: 44px;
  background-color: transparent;
  border-radius: 20px;
  padding: 0 18px;
  font-size: 16px;
  border: none;
  color: #333;
  outline: none;
}

.chat-input::placeholder {
  color: #999;
}

.send-btn {
  width: 72px;
  height: 40px;
  background: #e5e5e5;
  color: #999;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.send-btn.active {
  background: #4C84FF !important;
  color: #fff !important;
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(76, 132, 255, 0.3);
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(76, 132, 255, 0.8) 0%, rgba(108, 92, 231, 0.8) 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.2);
  border-top: 4px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #fff;
  font-size: 16px;
  margin-top: 20px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  font-weight: 500;
}

/* 加号按钮 */
.plus-btn {
  width: 36px;
  height: 36px;
  background: #f0f0f0;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.plus-btn:active {
  background: #e0e0e0;
  transform: scale(0.95);
}

.plus-icon {
  font-size: 20px;
  color: #666;
  font-weight: bold;
}

/* 功能弹窗 */
.action-sheet-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.action-sheet {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 20px 20px 0 0;
  z-index: 1001;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.action-sheet-header {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}

.action-sheet-title {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.action-sheet-content {
  display: flex;
  justify-content: space-around;
  padding: 30px 20px;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.action-item:active {
  background: #f5f5f5;
  transform: scale(0.95);
}

.action-icon {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  background: #f8f9fa;
}

.photo-icon {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
}

.emoji-icon {
  background: linear-gradient(135deg, #FF9800 0%, #f57c00 100%);
}

.video-icon {
  background: linear-gradient(135deg, #2196F3 0%, #1976d2 100%);
}

.location-icon {
  background: linear-gradient(135deg, #4C84FF 0%, #6c5ce7 100%);
}

.action-text {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.action-sheet-cancel {
  padding: 15px;
  text-align: center;
  border-top: 8px solid #f5f5f5;
  color: #666;
  font-size: 16px;
}



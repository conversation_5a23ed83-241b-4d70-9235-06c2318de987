<!-- 全局消息通知组件 -->
<view class="notification-toast {{show ? 'show' : ''}}" wx:if="{{show}}" bindtap="onToastTap">
  <view class="toast-content">
    <view class="toast-icon">
      <text class="icon">💬</text>
    </view>
    <view class="toast-text">
      <view class="toast-title">{{title}}</view>
      <view class="toast-message">{{message}}</view>
    </view>
    <view class="toast-close" bindtap="onClose" catchtap="onClose">
      <text class="close-icon">×</text>
    </view>
  </view>
</view>

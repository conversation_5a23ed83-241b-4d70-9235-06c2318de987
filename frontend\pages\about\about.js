const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    appInfo: {
      name: '大数据答题小程序',
      version: 'v2.0.1',
      description: '专注于高校学习和题目练习的小程序，帮助用户巩固知识，提高技能。',
      developer: '强I科技创新集团',
      contact: '<EMAIL>'
    },
    features: [
      {
        title: '单选题练习',
        description: '精选大数据相关单选题，帮助巩固基础知识'
      },
      {
        title: '多选题练习',
        description: '涵盖大数据各领域多选题，全面提升专业能力'
      },
      {
        title: '错题本',
        description: '自动记录错题，方便复习和巩固'
      },
      {
        title: '数据统计',
        description: '详细记录学习数据，直观展示学习进度'
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 检查登录状态
    if (!app.checkLogin()) {
      wx.redirectTo({
        url: '/pages/login/login'
      });
    }
  },
  
  // 复制联系方式
  copyContact: function() {
    wx.setClipboardData({
      data: this.data.appInfo.contact,
      success: function() {
        wx.showToast({
          title: '联系方式已复制',
          icon: 'success'
        });
      }
    });
  }
}) 
/* pages/about/about.wxss */
.container {
  min-height: 100vh;
  padding: 30rpx;
  background-color: #f5f7fa;
}

.about-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
  padding: 40rpx 0;
}

.app-logo {
  width: 160rpx;
  height: 160rpx;
  border-radius: 40rpx;
  margin-bottom: 20rpx;
  background: linear-gradient(135deg, #4e82ff, #3b5cff);
  box-shadow: 0 10rpx 20rpx rgba(59, 92, 255, 0.25), 
              inset 0 -5rpx 15rpx rgba(0, 0, 0, 0.1),
              inset 0 5rpx 15rpx rgba(255, 255, 255, 0.25);
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.app-logo::before {
  content: "Q";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 90rpx;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.95);
  font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
  text-shadow: 0 2rpx 3rpx rgba(0, 0, 0, 0.3);
  letter-spacing: 0;
  line-height: 1;
}

.app-logo::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);
  transform: rotate(30deg);
}

.app-info {
  text-align: center;
}

.app-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.app-version {
  font-size: 24rpx;
  color: #999;
}

.about-card {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}

.card-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 6rpx;
  width: 8rpx;
  height: 30rpx;
  background-color: #4C84FF;
  border-radius: 4rpx;
}

.app-description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.feature-list {
  margin-top: 10rpx;
}

.feature-item {
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.feature-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.feature-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.feature-description {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.developer-info {
  display: flex;
  flex-direction: column;
}

.developer-name {
  font-size: 32rpx;
  font-weight: bold;
  background-image: linear-gradient(135deg,rgb(215, 39, 209), #4C84FF);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  text-shadow: 0 2rpx 5rpx rgba(76, 132, 255, 0.2);
  margin-bottom: 15rpx;
  padding: 5rpx 0;
  letter-spacing: 1rpx;
  font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
}

.contact-info {
  font-size: 26rpx;
  color: #4C84FF;
  margin-bottom: 5rpx;
}

.copy-hint {
  font-size: 24rpx;
  color: #999;
}

.about-footer {
  text-align: center;
  padding: 30rpx 0;
}

.copyright {
  font-size: 24rpx;
  color: #999;
} 
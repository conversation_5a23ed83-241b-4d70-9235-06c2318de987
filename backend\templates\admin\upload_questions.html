{% extends "admin/base.html" %}

{% block title %}批量导入题库 - 大数据题库管理后台{% endblock %}

{% block additional_head %}
<link rel="stylesheet" href="/static/css/admin.css">
<style>
    .format-example {
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 5px;
        margin-top: 10px;
        font-size: 14px;
        color: #666;
        white-space: pre;
        overflow-x: auto;
    }
    .file-format-tips {
        display: none;
    }
    .file-upload-wrapper {
        margin-bottom: 20px;
    }
    .preview-container {
        max-height: 400px;
        overflow-y: auto;
        margin-top: 20px;
    }
    .preview-table {
        width: 100%;
    }
    .step {
        padding: 20px;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    .step-header {
        margin-bottom: 15px;
        font-size: 18px;
        font-weight: 500;
        color: #495057;
    }
    .step-number {
        display: inline-block;
        width: 30px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        background-color: #6c757d;
        color: white;
        border-radius: 50%;
        margin-right: 10px;
    }
</style>
{% endblock %}

{% block header %}批量导入题库{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">批量导入题库</h5>
    </div>
    <div class="card-body">
        <form action="/admin/questions/upload" method="post" enctype="multipart/form-data">
            <div class="mb-3">
                <label for="fileType" class="form-label">文件类型</label>
                <select class="form-select" id="fileType" name="fileType" required>
                    <option value="">选择文件类型</option>
                    <option value="json">JSON</option>
                    <option value="csv">CSV</option>
                    <option value="excel">Excel</option>
                </select>
            </div>
            
            <div class="mb-3">
                <label for="class_id" class="form-label">班级</label>
                <select class="form-select" id="class_id" name="class_id">
                    <option value="">选择班级</option>
                    {% for class_item in classes %}
                    <option value="{{ class_item.id }}">{{ class_item.name }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="mb-3">
                <label for="course_id" class="form-label">课程</label>
                <select class="form-select" id="course_id" name="course_id" required disabled>
                    <option value="">请先选择班级</option>
                </select>
            </div>
            
            <div class="mb-3">
                <label for="questionFile" class="form-label">选择文件</label>
                <input class="form-control" type="file" id="questionFile" name="questionFile" required>
            </div>
            
            <button type="submit" class="btn btn-primary">上传并导入</button>
            <a href="/admin/questions" class="btn btn-secondary">返回题目管理</a>
        </form>
    </div>
</div>

<!-- 文件格式说明 -->
<div class="card mt-4">
    <div class="card-header">
        <h5 class="card-title mb-0">文件格式说明</h5>
    </div>
    <div class="card-body">
        <div class="accordion" id="formatAccordion">
            <!-- JSON格式说明 -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="jsonHeading">
                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#jsonExample" aria-expanded="true" aria-controls="jsonExample">
                        JSON格式示例
                    </button>
                </h2>
                <div id="jsonExample" class="accordion-collapse collapse show" aria-labelledby="jsonHeading">
                    <div class="accordion-body">
                        <pre class="bg-light p-3 rounded">
{
    "singleChoice": [
        {
            "question": "单选题问题文本",
            "options": ["选项A", "选项B", "选项C", "选项D"],
            "answer": 0,
            "category": "分类",
            "difficulty": 1
        }
    ],
    "multipleChoice": [
        {
            "question": "多选题问题文本",
            "options": ["选项A", "选项B", "选项C", "选项D"],
            "answer": [0, 1],
            "category": "分类",
            "difficulty": 2
        }
    ],
    "judgment": [
        {
            "question": "判断题问题文本",
            "answer": true,
            "category": "分类",
            "difficulty": 1
        }
    ],
    "fillBlank": [
        {
            "question": "填空题问题文本，请填写___和___",
            "answer": ["答案1", "答案2"],
            "category": "分类",
            "difficulty": 2
        }
    ]
}
                        </pre>
                    </div>
                </div>
            </div>
            
            <!-- CSV格式说明 -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="csvHeading">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#csvExample" aria-expanded="false" aria-controls="csvExample">
                        CSV格式示例
                    </button>
                </h2>
                <div id="csvExample" class="accordion-collapse collapse" aria-labelledby="csvHeading">
                    <div class="accordion-body">
                        <p>CSV文件需要包含以下列:</p>
                        <pre class="bg-light p-3 rounded">
type,question,options,answer,category,difficulty
single,单选题问题文本,选项A|选项B|选项C|选项D,0,分类,1
multiple,多选题问题文本,选项A|选项B|选项C|选项D,"0,1",分类,2
judgment,判断题问题文本,,true,分类,1
fill_blank,填空题问题文本，请填写___和___,,"答案1,答案2",分类,2
                        </pre>
                        <p class="text-muted">说明：
                        <ul>
                            <li>选项使用 | 分隔</li>
                            <li>多选题答案使用 , 分隔的数字索引</li>
                            <li>判断题答案为 true/false</li>
                            <li>填空题答案使用 , 分隔</li>
                        </ul>
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Excel格式说明 -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="excelHeading">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#excelExample" aria-expanded="false" aria-controls="excelExample">
                        Excel格式示例
                    </button>
                </h2>
                <div id="excelExample" class="accordion-collapse collapse" aria-labelledby="excelHeading">
                    <div class="accordion-body">
                        <p>Excel文件需要以下列头:</p>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>type</th>
                                        <th>question</th>
                                        <th>options</th>
                                        <th>answer</th>
                                        <th>category</th>
                                        <th>difficulty</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>single</td>
                                        <td>单选题问题文本</td>
                                        <td>选项A|选项B|选项C|选项D</td>
                                        <td>0</td>
                                        <td>分类</td>
                                        <td>1</td>
                                    </tr>
                                    <tr>
                                        <td>multiple</td>
                                        <td>多选题问题文本</td>
                                        <td>选项A|选项B|选项C|选项D</td>
                                        <td>0,1</td>
                                        <td>分类</td>
                                        <td>2</td>
                                    </tr>
                                    <tr>
                                        <td>judgment</td>
                                        <td>判断题问题文本</td>
                                        <td></td>
                                        <td>true</td>
                                        <td>分类</td>
                                        <td>1</td>
                                    </tr>
                                    <tr>
                                        <td>fill_blank</td>
                                        <td>填空题问题文本，请填写___和___</td>
                                        <td></td>
                                        <td>答案1,答案2</td>
                                        <td>分类</td>
                                        <td>2</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <p class="text-muted">注意事项与CSV格式相同</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const classSelect = document.getElementById('class_id');
        const courseSelect = document.getElementById('course_id');
        
        // 当班级选择变化时，加载对应的课程
        classSelect.addEventListener('change', function() {
            const classId = this.value;
            
            // 清空当前的课程选项
            courseSelect.innerHTML = '<option value="">选择课程</option>';
            
            if (classId) {
                // 启用课程选择
                courseSelect.disabled = false;
                
                // 获取班级对应的课程
                fetch(`/admin/courses/search?class_id=${classId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data && data.courses && Array.isArray(data.courses)) {
                            data.courses.forEach(course => {
                                const option = document.createElement('option');
                                option.value = course.id;
                                option.textContent = course.name;
                                courseSelect.appendChild(option);
                            });
                            console.log(`已加载 ${data.courses.length} 个课程`);
                        } else {
                            console.error('获取课程数据格式不正确', data);
                        }
                    })
                    .catch(error => {
                        console.error('获取课程失败:', error);
                    });
            } else {
                // 禁用课程选择
                courseSelect.disabled = true;
            }
        });
    });
</script>
{% endblock %} 
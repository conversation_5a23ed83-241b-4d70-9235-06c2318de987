/* 图表全屏显示样式 */

/* 确保图表容器有足够高度 */
.chart-container {
    min-height: 300px;
    height: 100%;
}

/* 确保图表完全填充容器 */
.chart-container img.fullsize-chart {
    width: 100%;
    height: 100%;
    object-fit: contain;
    display: block;
    max-width: 100%;
}

/* 增加卡片高度 */
.card.h-100 {
    min-height: 400px;
}

/* 图表容器布局 */
.chart-container-flex {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    flex-grow: 1;
} 
<view class="cat-container">
  <!-- 调试信息，上线前可移除 -->
  <view class="debug-info" style="position:fixed; top:5px; left:5px; font-size:10px; color:#999; z-index:10001; display:none;">
    Cat position: {{x}}, {{y}}
  </view>

  <!-- 小猫图片 - 点击后显示聊天窗口 -->
  <view class="cat-wrapper" style="left: {{x}}px; top: {{y}}px;" 
        catchtouchmove="handleTouchMove"
        bindtouchend="handleCatTouchEnd"
        bindtouchcancel="handleCatTouchEnd"
        catchlongtap="preventEvent"
        catchtap="toggleChatOrRestore">
    <!-- 添加猫咪提示气泡 -->
    <view class="cat-tooltip">来与喵喵对话吧~</view>
    <view class="animation-container {{isMeowing ? 'meowing' : ''}}">
      <image 
        class="cat-image {{isMinimized ? 'visible' : ''}}" 
        src="/icons/cats.png" 
        binderror="onImageError"
      />
    </view>
    <!-- 喵喵叫气泡 -->
    <view class="meow-bubble {{showMeowBubble ? 'show' : ''}}">喵～</view>
  </view>
  
  <!-- 猫咪音效 - 本地存储链接 -->
  <audio id="catAudio" src="{{catAudioSrc}}" bindended="onAudioEnd"></audio>

  <!-- Chat window (shows when clicked) -->
  <view class="chat-container {{isMinimized ? 'minimized' : ''}} {{isExpanded ? 'expanded' : ''}}" 
        wx:if="{{showChat}}" 
        catchtap="preventBubble" 
        style="left: {{chatX}}px; top: {{chatY}}px; {{autoSize ? 'width: auto; height: auto;' : ''}} {{isMinimized ? 'opacity: 0; pointer-events: none;' : ''}}">
    <view class="chat-header" 
          catchtouchmove="handleChatHeaderMove" 
          bindtouchstart="startHeaderDrag"
          bindtouchend="endHeaderDrag"
          bindtouchcancel="endHeaderDrag">
      <text>与{{catName}}聊天</text>
      <view class="header-controls">
        <view class="control-icon minimize-icon" catchtap="minimizeChat">
          <text>-</text>
        </view>
        <view class="control-icon resize-icon" catchtap="toggleAutoSize">
          <text>⧉</text>
        </view>
        <view class="control-icon expand-icon" catchtap="expandChat">
          <text>{{isExpanded ? '❐' : '□'}}</text>
        </view>
        <view class="control-icon close-icon" catchtap="closeChat">
          <text>×</text>
        </view>
      </view>
    </view>
    
    <!-- 功能工具栏 -->
    <view class="ai-service-selector">
      <view class="service-info">
        <text>{{catName}}助手</text>
      </view>
      <view class="reset-button" catchtap="resetConversation">
        <text>重置会话</text>
      </view>
    </view>
    
    <scroll-view class="chat-messages" scroll-y="true" scroll-into-view="message-{{messages.length - 1}}">
      <!-- 欢迎消息 -->
      <view wx:if="{{messages.length === 0}}" class="welcome-message">
        <view class="message-row assistant-row">
          <image class="avatar ai-avatar" src="/icons/cats.png" mode="aspectFill"></image>
          <view class="message bot-message">
            <text>您好！我是{{catName}}，有什么我可以帮您的吗？          喵~ </text>
            <text class="pink-paw">🐾</text>
          </view>
        </view>
      </view>
      
      <block wx:for="{{messages}}" wx:key="index">
        <view id="message-{{index}}" class="message-row {{item.role === 'user' ? 'user-row' : 'assistant-row'}}">
          <!-- 头像 - 左侧AI小猫头像或右侧用户头像 -->
          <image 
            class="avatar {{item.role === 'user' ? 'user-avatar' : 'ai-avatar'}}" 
            src="{{item.role === 'user' ? userAvatar : '/icons/cats.png'}}"
            mode="aspectFill"
          ></image>
          
          <!-- 消息气泡 -->
          <view class="message {{item.role === 'user' ? 'user-message' : 'bot-message'}}">
            <block wx:if="{{item.role === 'user'}}">
            <text>{{item.content}}</text>
            </block>
            <block wx:else>
              <!-- AI消息需要处理猫爪显示 -->
              <text>{{item.role === 'assistant' && item.content.indexOf('🐾') >= 0 ? item.content.split('🐾')[0] : item.content}}</text>
              <text wx:if="{{item.role === 'assistant' && item.content.indexOf('🐾') >= 0}}" class="pink-paw">🐾</text>
            </block>
          </view>
        </view>
      </block>
      
      <view class="loading" wx:if="{{loading}}">
        <view class="typing-indicator">
          <view class="dot"></view>
          <view class="dot"></view>
          <view class="dot"></view>
        </view>
      </view>
    </scroll-view>
    
    <view class="chat-input" wx:if="{{!isMinimized}}">
      <input 
        value="{{inputValue}}" 
        bindinput="onInputChange" 
        placeholder="输入消息..." 
        confirm-type="send"
        bindconfirm="sendMessage"
        focus="{{showChat && !isMinimized}}"
      />
      <button class="send-btn" catchtap="sendMessage">发送</button>
    </view>
  </view>
</view> 
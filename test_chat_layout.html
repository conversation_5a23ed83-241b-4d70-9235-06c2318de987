<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天界面布局测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 375px;
            margin: 0 auto;
            min-height: 100vh;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            display: flex;
            flex-direction: column;
            position: relative;
        }

        /* 导航栏 */
        .nav-bar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 44px;
            background: linear-gradient(135deg, #4C84FF 0%, #6c5ce7 100%);
            padding: 0 16px;
            padding-top: 44px;
            box-shadow: 0 2px 10px rgba(76, 132, 255, 0.3);
        }

        .nav-left, .nav-right {
            width: 44px;
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 22px;
            transition: all 0.3s ease;
        }

        .nav-icon {
            font-size: 20px;
            color: #fff;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .back-icon {
            font-size: 32px;
            font-weight: bold;
        }

        .nav-title {
            font-size: 18px;
            font-weight: 600;
            color: #fff;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        /* 聊天消息区域 */
        .chat-messages {
            flex: 1;
            padding: 16px;
            margin-bottom: 80px;
            background: transparent;
            overflow-y: auto;
        }

        .message-item {
            margin-bottom: 20px;
            width: 100%;
            display: block;
        }

        .message-content {
            display: flex;
            align-items: flex-end;
            gap: 12px;
            max-width: 85%;
        }

        /* 接收的消息布局 - 头像在左，气泡在右 */
        .received-message {
            flex-direction: row;
            justify-content: flex-start;
        }

        /* 发送的消息布局 - 头像在右，气泡在左 */
        .sent-message {
            flex-direction: row-reverse;
            justify-content: flex-end;
            margin-left: auto;
        }

        .message-avatar {
            width: 42px;
            height: 42px;
            border-radius: 21px;
            flex-shrink: 0;
            border: 2px solid #fff;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .message-bubble {
            max-width: calc(100% - 54px);
            padding: 14px 18px;
            border-radius: 20px;
            position: relative;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        /* 接收的消息气泡 */
        .received-message .message-bubble {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-bottom-left-radius: 8px;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .received-message .message-bubble::before {
            content: '';
            position: absolute;
            left: -8px;
            bottom: 8px;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 0 8px 8px 0;
            border-color: transparent #ffffff transparent transparent;
            filter: drop-shadow(-1px 1px 1px rgba(0, 0, 0, 0.1));
        }

        /* 发送的消息气泡 */
        .sent-message .message-bubble {
            background: linear-gradient(135deg, #4C84FF 0%, #6c5ce7 100%);
            border-bottom-right-radius: 8px;
            color: #fff;
        }

        .sent-message .message-bubble::before {
            content: '';
            position: absolute;
            right: -8px;
            bottom: 8px;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 0 0 8px 8px;
            border-color: transparent transparent #4C84FF transparent;
            filter: drop-shadow(1px 1px 1px rgba(0, 0, 0, 0.1));
        }

        .message-text {
            font-size: 16px;
            line-height: 1.5;
            color: #333;
            word-wrap: break-word;
            margin: 0;
        }

        .sent-message .message-text {
            color: #fff;
        }

        .message-time {
            font-size: 11px;
            color: #999;
            margin-top: 6px;
            display: block;
        }

        .received-message .message-time {
            text-align: left;
        }

        .sent-message .message-time {
            color: rgba(255, 255, 255, 0.8);
            text-align: right;
        }

        /* 输入区域 */
        .chat-input-area {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 375px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            padding: 16px 20px;
            border-top: 1px solid rgba(76, 132, 255, 0.1);
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .input-wrapper {
            display: flex;
            align-items: center;
            gap: 12px;
            background-color: #fff;
            border-radius: 25px;
            padding: 4px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(76, 132, 255, 0.1);
        }

        .chat-input {
            flex: 1;
            height: 44px;
            background-color: transparent;
            border-radius: 20px;
            padding: 0 18px;
            font-size: 16px;
            border: none;
            color: #333;
            outline: none;
        }

        .send-btn {
            width: 72px;
            height: 40px;
            background: linear-gradient(135deg, #4C84FF 0%, #6c5ce7 100%);
            color: #fff;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(76, 132, 255, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 导航栏 -->
        <div class="nav-bar">
            <div class="nav-left">
                <span class="nav-icon back-icon">‹</span>
            </div>
            <div class="nav-title">凉辰木雪</div>
            <div class="nav-right">
                <!-- 移除三点菜单图标 -->
            </div>
        </div>

        <!-- 聊天消息区域 -->
        <div class="chat-messages">
            <!-- 接收的消息 -->
            <div class="message-item received">
                <div class="message-content received-message">
                    <img class="message-avatar" src="https://via.placeholder.com/42x42/8B4513/FFFFFF?text=猫" alt="头像">
                    <div class="message-bubble">
                        <div class="message-text">你好！看到你发布的动态了</div>
                        <div class="message-time">5分钟前</div>
                    </div>
                </div>
            </div>

            <!-- 发送的消息 -->
            <div class="message-item sent">
                <div class="message-content sent-message">
                    <div class="message-bubble">
                        <div class="message-text">你好！有什么可以帮助你的吗？</div>
                        <div class="message-time">4分钟前</div>
                    </div>
                    <img class="message-avatar" src="https://via.placeholder.com/42x42/FFD700/000000?text=我" alt="我的头像">
                </div>
            </div>

            <!-- 接收的消息 -->
            <div class="message-item received">
                <div class="message-content received-message">
                    <img class="message-avatar" src="https://via.placeholder.com/42x42/8B4513/FFFFFF?text=猫" alt="头像">
                    <div class="message-bubble">
                        <div class="message-text">想了解一下你发布的二手商品</div>
                        <div class="message-time">3分钟前</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 输入区域 -->
        <div class="chat-input-area">
            <div class="input-wrapper">
                <input class="chat-input" placeholder="输入消息..." type="text">
                <button class="send-btn">发送</button>
            </div>
        </div>
    </div>
</body>
</html>

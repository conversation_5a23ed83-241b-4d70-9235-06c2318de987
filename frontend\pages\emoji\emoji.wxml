<!-- pages/emoji/emoji.wxml -->
<view class="emoji-container">
  <!-- 状态栏占位 -->
  <view class="status-bar-placeholder"></view>

  <!-- 导航栏 -->
  <view class="emoji-header">
    <view class="header-left" bindtap="goBack">
      <text class="back-icon">‹</text>
    </view>
    <view class="header-title">
      <text>选择表情包</text>
    </view>
    <view class="header-right"></view>
  </view>

  <!-- 搜索框 -->
  <view class="search-container">
    <input 
      class="search-input" 
      placeholder="搜索表情包..." 
      value="{{searchKeyword}}"
      bindinput="onSearchInput"
      bindconfirm="searchEmojis"
    />
    <view class="search-btn" bindtap="searchEmojis">
      <text>🔍</text>
    </view>
  </view>

  <!-- 分类标签 -->
  <scroll-view class="category-tabs" scroll-x="true" wx:if="{{!isSearching}}">
    <view class="tab-item {{currentCategoryId === category.id ? 'active' : ''}}" 
          wx:for="{{categories}}" 
          wx:key="id"
          wx:for-item="category"
          bindtap="selectCategory"
          data-id="{{category.id}}">
      <text class="tab-icon">{{category.icon}}</text>
      <text class="tab-name">{{category.name}}</text>
    </view>
  </scroll-view>

  <!-- 表情包列表 -->
  <scroll-view class="emoji-list" scroll-y="true">
    <view class="emoji-grid">
      <view class="emoji-item" 
            wx:for="{{emojis}}" 
            wx:key="id"
            bindtap="selectEmoji"
            data-emoji="{{item}}">
        <text class="emoji-icon">{{item.image_url}}</text>
        <text class="emoji-name">{{item.name}}</text>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{emojis.length === 0 && !loading}}">
      <text class="empty-text">{{isSearching ? '没有找到相关表情包' : '暂无表情包'}}</text>
    </view>

    <!-- 加载状态 -->
    <view class="loading-state" wx:if="{{loading}}">
      <text class="loading-text">加载中...</text>
    </view>
  </scroll-view>
</view>

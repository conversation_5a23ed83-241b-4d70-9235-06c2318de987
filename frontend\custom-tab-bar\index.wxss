.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50px;
  background: white;
  display: flex;
  padding-bottom: env(safe-area-inset-bottom);
  border-top: 1px solid #e5e5e5;
  z-index: 9999;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.tab-bar-item {
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  position: relative;
  padding: 5px 0;
}

/* 特殊图标样式 */
.special-item {
  position: relative;
  top: -8px; /* 向上偏移 */
}

.special-icon-container {
  position: relative;
  margin-bottom: 2px;
}

.special-icon-bg {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 3px solid white;
  position: relative;
}

/* 添加一个内阴影效果 */
.special-icon-bg::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
  pointer-events: none;
}

.special-icon-bg.active {
  background: linear-gradient(135deg, #4e8df7, #6ba3ff);
  box-shadow: 0 8px 25px rgba(78, 141, 247, 0.5);
  transform: scale(1.15);
}

.special-icon {
  width: 28px;
  height: 28px;
  z-index: 1;
  position: relative;
}

/* 普通图标样式 */
.normal-icon-container {
  margin-bottom: 4px;
  transition: all 0.2s ease;
}

.normal-icon {
  width: 26px;
  height: 26px;
  transition: all 0.2s ease;
}

/* 普通图标激活时的效果 */
.tab-bar-item:not(.special-item):active .normal-icon-container {
  transform: scale(0.95);
}

/* 文字样式 */
.tab-bar-text {
  font-size: 10px;
  line-height: 1.2;
  margin-top: 2px;
  font-weight: 400;
}

.tab-bar-text.active {
  font-weight: 600;
}

/* 特殊项的文字样式 */
.special-item .tab-bar-text {
  margin-top: 4px;
  font-size: 9px;
  font-weight: 500;
}

.special-item .tab-bar-text.active {
  font-weight: 600;
  color: #4e8df7 !important;
}

/* 添加一个小的脉冲动画给特殊图标 */
@keyframes pulse {
  0% {
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
  }
  50% {
    box-shadow: 0 6px 25px rgba(255, 107, 107, 0.6);
  }
  100% {
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
  }
}

.special-icon-bg:not(.active) {
  animation: pulse 3s ease-in-out infinite;
}

/* 适配不同屏幕 */
@media screen and (max-width: 320px) {
  .tab-bar {
    height: 55px;
  }

  .special-icon-bg {
    width: 42px;
    height: 42px;
  }

  .special-icon {
    width: 24px;
    height: 24px;
  }

  .normal-icon {
    width: 24px;
    height: 24px;
  }

  .tab-bar-text {
    font-size: 9px;
  }

  .special-item .tab-bar-text {
    font-size: 8px;
  }
}

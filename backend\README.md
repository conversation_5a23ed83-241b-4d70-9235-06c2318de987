# 答题小程序后端

这是一个基于Flask的答题微信小程序后端服务，提供API接口支持用户登录、获取题目、提交答案、错题本及数据分析等功能。

## 技术栈

- Python 3.8+
- Flask: Web框架
- SQLAlchemy: ORM框架
- PyMySQL: MySQL数据库驱动
- Matplotlib: 数据可视化
- Pandas: 数据分析
- JWT: 用户认证

## 项目结构

```
backend/
  ├── app.py           # 主应用文件
  ├── init_db.py       # 数据库初始化脚本
  ├── requirements.txt # 依赖包列表
  └── questions.json   # 题目数据
```

## 安装与配置

1. 安装依赖包

```bash
pip install -r requirements.txt
```

2. 配置数据库

编辑`app.py`中的数据库连接信息：

```python
app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql+pymysql://root:123456@localhost/quiz_app'
```

请根据实际情况修改用户名、密码、主机和数据库名。

3. 初始化数据库

```bash
python init_db.py
```

## 运行

```bash
python app.py
```

服务器默认在 http://0.0.0.0:5000 启动。

## API接口说明

### 用户认证

- **POST /api/login**
  - 功能: 用户登录/注册
  - 参数: `{openid, nickname, avatar}`
  - 返回: `{token, user}`

### 题目管理

- **GET /api/questions/single**
  - 功能: 获取单选题列表
  - 需要认证: 是
  - 返回: 单选题列表

- **GET /api/questions/multiple**
  - 功能: 获取多选题列表
  - 需要认证: 是
  - 返回: 多选题列表

### 答题相关

- **POST /api/submit**
  - 功能: 提交答案
  - 需要认证: 是
  - 参数: `{questionType, questionId, userAnswer}`
  - 返回: `{isCorrect, correctAnswer, recordId}`

- **GET /api/wrong-questions**
  - 功能: 获取用户错题本
  - 需要认证: 是
  - 返回: 错题列表

### 数据分析

- **GET /api/statistics**
  - 功能: 获取用户答题统计信息
  - 需要认证: 是
  - 返回: 统计数据和图表 
{% extends "admin/base.html" %}

{% block title %}关键词管理 - 猫友圈管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-filter" style="color: #fd7e14;"></i> 关键词管理
                </h1>
                <p class="page-subtitle">管理敏感词过滤关键词</p>
            </div>
        </div>
    </div>

    <!-- 添加关键词 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">添加关键词</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>单个添加</h6>
                            <form id="addSingleKeywordForm" class="row g-3">
                                <div class="col-md-8">
                                    <input type="text" id="singleKeyword" class="form-control" placeholder="输入敏感词..." required>
                                </div>
                                <div class="col-md-4">
                                    <select id="singleKeywordType" class="form-select">
                                        <option value="normal">普通</option>
                                        <option value="strict">严格</option>
                                        <option value="warning">警告</option>
                                    </select>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> 添加
                                    </button>
                                </div>
                            </form>
                        </div>
                        <div class="col-md-6">
                            <h6>批量添加</h6>
                            <form id="addBatchKeywordsForm" class="row g-3">
                                <div class="col-12">
                                    <textarea id="batchKeywords" class="form-control" rows="3" placeholder="输入多个敏感词，每行一个..."></textarea>
                                </div>
                                <div class="col-md-6">
                                    <select id="batchKeywordType" class="form-select">
                                        <option value="normal">普通</option>
                                        <option value="strict">严格</option>
                                        <option value="warning">警告</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-upload"></i> 批量添加
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-8">
                            <label class="form-label">搜索关键词</label>
                            <input type="text" name="search" class="form-control" placeholder="搜索关键词..." value="{{ current_search }}">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> 搜索
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 关键词列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">关键词列表</h5>
                    <div>
                        {% if pagination %}
                            <span class="text-muted">共 {{ pagination.total }} 条记录</span>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if keywords %}
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>ID</th>
                                        <th>关键词</th>
                                        <th>类型</th>
                                        <th>创建时间</th>
                                        <th>更新时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for keyword in keywords %}
                                    <tr>
                                        <td>{{ keyword.id }}</td>
                                        <td>
                                            <span class="fw-bold">{{ keyword.word }}</span>
                                        </td>
                                        <td>
                                            {% if keyword.type == 'normal' %}
                                                <span class="badge bg-primary">普通</span>
                                            {% elif keyword.type == 'strict' %}
                                                <span class="badge bg-danger">严格</span>
                                            {% elif keyword.type == 'warning' %}
                                                <span class="badge bg-warning">警告</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ keyword.type }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="small">
                                                {{ keyword.created_at.strftime('%Y-%m-%d') if keyword.created_at else '' }}<br>
                                                {{ keyword.created_at.strftime('%H:%M:%S') if keyword.created_at else '' }}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="small">
                                                {{ keyword.updated_at.strftime('%Y-%m-%d') if keyword.updated_at else '' }}<br>
                                                {{ keyword.updated_at.strftime('%H:%M:%S') if keyword.updated_at else '' }}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-outline-primary" onclick="editKeyword({{ keyword.id }}, '{{ keyword.word }}', '{{ keyword.type }}')">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-danger" onclick="deleteKeyword({{ keyword.id }})">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">暂无关键词数据</h5>
                            <p class="text-muted">当前筛选条件下没有找到任何关键词</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 分页 -->
    {% if pagination and pagination.pages > 1 %}
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="关键词分页">
                <ul class="pagination justify-content-center">
                    {% if pagination.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('cat_circle_admin.manage_keywords', page=pagination.prev_num, search=current_search) }}">上一页</a>
                        </li>
                    {% endif %}
                    
                    {% for page_num in pagination.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != pagination.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('cat_circle_admin.manage_keywords', page=page_num, search=current_search) }}">{{ page_num }}</a>
                                </li>
                            {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                            {% endif %}
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">…</span>
                            </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if pagination.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('cat_circle_admin.manage_keywords', page=pagination.next_num, search=current_search) }}">下一页</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}
</div>

<!-- 编辑关键词模态框 -->
<div class="modal fade" id="editKeywordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑关键词</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editKeywordForm">
                    <input type="hidden" id="editKeywordId">
                    <div class="mb-3">
                        <label class="form-label">关键词</label>
                        <input type="text" id="editKeywordWord" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">类型</label>
                        <select id="editKeywordType" class="form-select">
                            <option value="normal">普通</option>
                            <option value="strict">严格</option>
                            <option value="warning">警告</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateKeyword()">保存</button>
            </div>
        </div>
    </div>
</div>

<script>
// 单个添加关键词
document.getElementById('addSingleKeywordForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const word = document.getElementById('singleKeyword').value.trim();
    const type = document.getElementById('singleKeywordType').value;
    
    if (!word) {
        alert('请输入关键词');
        return;
    }
    
    fetch('/admin/cat-circle/api/keywords', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ word: word, type: type })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('添加失败: ' + data.message);
        }
    })
    .catch(error => {
        alert('添加失败: ' + error);
    });
});

// 批量添加关键词
document.getElementById('addBatchKeywordsForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const wordsText = document.getElementById('batchKeywords').value.trim();
    const type = document.getElementById('batchKeywordType').value;
    
    if (!wordsText) {
        alert('请输入关键词');
        return;
    }
    
    const words = wordsText.split('\n').map(w => w.trim()).filter(w => w);
    
    if (words.length === 0) {
        alert('请输入有效的关键词');
        return;
    }
    
    fetch('/admin/cat-circle/api/keywords/batch', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ words: words, type: type })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('批量添加失败: ' + data.message);
        }
    })
    .catch(error => {
        alert('批量添加失败: ' + error);
    });
});

function editKeyword(id, word, type) {
    document.getElementById('editKeywordId').value = id;
    document.getElementById('editKeywordWord').value = word;
    document.getElementById('editKeywordType').value = type;
    
    const modal = new bootstrap.Modal(document.getElementById('editKeywordModal'));
    modal.show();
}

function updateKeyword() {
    const id = document.getElementById('editKeywordId').value;
    const word = document.getElementById('editKeywordWord').value.trim();
    const type = document.getElementById('editKeywordType').value;
    
    if (!word) {
        alert('请输入关键词');
        return;
    }
    
    fetch(`/admin/cat-circle/api/keywords/${id}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ word: word, type: type })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('更新失败: ' + data.message);
        }
    })
    .catch(error => {
        alert('更新失败: ' + error);
    });
}

function deleteKeyword(keywordId) {
    if (confirm('确定要删除这个关键词吗？此操作不可恢复！')) {
        fetch(`/admin/cat-circle/api/keywords/${keywordId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('删除失败: ' + data.message);
            }
        })
        .catch(error => {
            alert('删除失败: ' + error);
        });
    }
}
</script>

<style>
.page-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}
</style>
{% endblock %}

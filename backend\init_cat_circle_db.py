#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
import datetime

# 创建Flask应用
app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///instance/quiz_app.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# 初始化数据库
db = SQLAlchemy(app)

# 导入猫友圈API并初始化
from cat_circle_api import init_cat_circle_api

def init_database():
    """初始化猫友圈数据库表"""
    with app.app_context():
        try:
            # 初始化猫友圈API
            init_cat_circle_api(db)
            
            # 创建所有表
            db.create_all()
            
            print("✅ 猫友圈数据库表创建成功!")
            
            # 检查表是否存在
            from cat_circle_api import CatCirclePost, SensitiveWord
            
            # 添加一些测试敏感词
            if SensitiveWord.query.count() == 0:
                sensitive_words = ['垃圾', '骗子', '傻逼', '白痴', '死']
                for word in sensitive_words:
                    sw = SensitiveWord(word=word, type='normal')
                    db.session.add(sw)
                
                db.session.commit()
                print(f"✅ 已添加 {len(sensitive_words)} 个敏感词")
            
            print("✅ 数据库初始化完成!")
            
        except Exception as e:
            print(f"❌ 数据库初始化失败: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    init_database()

// pages/profile/profile.js
const app = getApp();
const globalNotificationMixin = require('../../utils/global-notification-mixin')

Page(Object.assign({}, globalNotificationMixin, {

  /**
   * 页面的初始数据
   */
  data: Object.assign({}, globalNotificationMixin.data, {
    userInfo: null,
    statistics: {
      totalQuestions: 0,
      correctCount: 0,
      wrongCount: 0,
      accuracy: 0,
      streak: 0,
      lastActive: ''
    },
    functionList: [
      {
        id: 'wrong-questions',
        icon: '❌',
        name: '错题本',
        description: '查看并复习错题',
        url: '/pages/wrong-questions/wrong-questions'
      },
      {
        id: 'statistics',
        icon: '📊',
        name: '答题统计',
        description: '查看你的学习进度',
        url: '/pages/statistics/statistics'
      },
      // {
      //   id: 'feedback',
      //   icon: '📝',
      //   name: '意见反馈',
      //   description: '帮助我们改进应用',
      //   url: '/pages/feedback/feedback'
      // },
      {
        id: 'about',
        icon: 'ℹ️',
        name: '关于应用',
        description: '了解更多信息',
        url: '/pages/about/about'
      }
    ],
    loading: true,
    lastRefreshTime: 0 // 添加最后刷新时间戳
  }),

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.checkLogin();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 更新自定义tabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 4
      });
    }

    if (app.checkLogin()) {
      // 始终刷新用户数据
      this.refreshUserData();

      // 从存储获取最新用户信息显示（可能在其他页面更新过）
      const userManager = require('../../utils/activity');
      const localUserInfo = userManager.getLocalUserInfo();
      if (localUserInfo) {
        this.setData({
          userInfo: localUserInfo
        });
      }
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    if (app.checkLogin()) {
      this.refreshUserData(true);
    }
    // 完成后停止下拉刷新动画
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  
  /**
   * 监听页面切换事件（点击tabBar时触发）
   */
  onTabItemTap(item) {
    console.log('点击了"我的"选项卡:', item);
    // 无论如何都刷新数据，保证页面数据最新
    if (app.checkLogin()) {
      this.refreshUserData(true);
    }
  },

  // 检查登录状态
  checkLogin: function() {
    if (!app.checkLogin()) {
      wx.redirectTo({
        url: '/pages/login/login'
      });
      return false;
    }
    this.setData({
      userInfo: app.globalData.userInfo
    });
    return true;
  },

  // 刷新用户数据和统计信息
  refreshUserData: function(forceRefresh = false) {
    // 防止频繁刷新，除非强制刷新
    const now = Date.now();
    if (!forceRefresh && (now - this.data.lastRefreshTime < 10000)) {
      console.log('刷新间隔太短，跳过刷新');
      return;
    }
    
    this.setData({ 
      loading: true,
      lastRefreshTime: now
    });
    
    // 获取最新用户信息
    const userManager = require('../../utils/activity');
    const localUserInfo = userManager.getLocalUserInfo();
    if (localUserInfo) {
      this.setData({
        userInfo: localUserInfo
      });
    }
    
    // 获取统计数据
    app.request({
      url: '/statistics',
      method: 'GET'
    })
    .then(res => {
      console.log('获取用户统计数据:', res);
      
      // 处理统计数据
      const statistics = {
        totalQuestions: res.totalQuestions || 0,
        correctCount: res.correctCount || 0,
        wrongCount: res.wrongCount || 0,
        accuracy: parseFloat((res.accuracy || 0).toFixed(2)),
        streak: res.streakDays || 0, // 使用后端返回的真实连续练习天数
        lastActive: this.formatDate(new Date())
      };
      
      this.setData({
        statistics: statistics,
        loading: false
      });
      
      // wx.showToast({
      //   title: '刷新成功',
      //   icon: 'success',
      //   duration: 1000
      // });
    })
    .catch(err => {
      console.error('获取用户数据失败:', err);
      this.setData({ loading: false });
      
      wx.showToast({
        title: '获取数据失败',
        icon: 'none'
      });
    });
  },

  // 获取用户数据和统计信息 (原函数保留以兼容旧代码)
  fetchUserData: function() {
    this.refreshUserData();
  },

  // 格式化日期为 YYYY-MM-DD 格式
  formatDate: function(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  // 跳转到相应功能页面
  navigateTo: function(e) {
    const url = e.currentTarget.dataset.url;
    if (!url) return;
    
    // 区分 tabBar 页面和普通页面
    if (url.includes('wrong-questions') || url.includes('statistics')) {
      wx.switchTab({
        url: url
      });
    } else {
      wx.navigateTo({
        url: url
      });
    }
  },

  // 退出登录
  logout: function() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 调用app.js中的logout方法，确保用户被标记为离线
          app.logout();
          
          // app.logout()已处理清除数据和页面跳转
          console.log('用户已登出并标记为离线');
        }
      }
    });
  }
}))
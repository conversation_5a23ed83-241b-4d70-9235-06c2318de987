// Chart.js 配置文件
document.addEventListener('DOMContentLoaded', function() {
    // 检查Chart.js是否已加载
    if (typeof Chart === 'undefined') {
        console.error('Chart.js库未加载，正在尝试加载本地副本');
        
        // 动态加载Chart.js
        var script = document.createElement('script');
        script.src = '/static/js/chart.min.js';
        script.async = true;
        script.onload = function() {
            console.log('Chart.js加载成功');
            initializeCharts();
        };
        script.onerror = function() {
            console.error('Chart.js本地加载失败，显示静态内容');
            showStaticCharts();
        };
        document.head.appendChild(script);
    } else {
        console.log('Chart.js已加载');
        initializeCharts();
    }
});

// 初始化所有图表
function initializeCharts() {
    // 用户答题数据趋势图 - 全新设计-霓虹灯效果
    var activityCtx = document.getElementById('activityChart');
    if (activityCtx) {
        // 获取真实数据
        let chartData = null;
        
        try {
            if (activityCtx.dataset && activityCtx.dataset.chartValues) {
                const rawData = JSON.parse(activityCtx.dataset.chartValues);
                if (rawData.labels && rawData.datasets && rawData.datasets[0] && rawData.datasets[0].data) {
                    chartData = {
                        labels: rawData.labels,
                        values: rawData.datasets[0].data
                    };
                    console.log('已获取真实数据:', chartData);
                }
            }
        } catch (e) {
            console.error('无法解析数据:', e);
        }
        
        // 如果没有真实数据，显示错误提示
        if (!chartData) {
            console.error('未找到有效数据，显示错误信息');
            showDataError(activityCtx, '未能加载真实数据');
            return;
        }
        
        // 设置父容器为相对定位以便添加特效
        const parentContainer = activityCtx.parentElement;
        parentContainer.style.position = 'relative';
        parentContainer.style.overflow = 'hidden';
        parentContainer.style.borderRadius = '12px';
        
        // 创建全新的背景元素，添加动态效果
        const backgroundElement = document.createElement('div');
        backgroundElement.style.position = 'absolute';
        backgroundElement.style.top = '0';
        backgroundElement.style.left = '0';
        backgroundElement.style.right = '0';
        backgroundElement.style.bottom = '0';
        backgroundElement.style.backgroundColor = '#111827';
        backgroundElement.style.zIndex = '-1';
        backgroundElement.style.overflow = 'hidden';
        
        // 添加动态背景元素
        for (let i = 0; i < 20; i++) {
            const glowDot = document.createElement('div');
            const size = Math.random() * 4 + 2;
            const left = Math.random() * 100;
            const top = Math.random() * 100;
            const duration = Math.random() * 8 + 10;
            const hue = Math.random() * 60 + 200; // 200-260 色相范围（蓝紫色）
            
            glowDot.style.position = 'absolute';
            glowDot.style.left = `${left}%`;
            glowDot.style.top = `${top}%`;
            glowDot.style.width = `${size}px`;
            glowDot.style.height = `${size}px`;
            glowDot.style.borderRadius = '50%';
            glowDot.style.backgroundColor = `hsla(${hue}, 100%, 70%, 0.8)`;
            glowDot.style.boxShadow = `0 0 ${size * 3}px ${size}px hsla(${hue}, 100%, 70%, 0.3)`;
            glowDot.style.animation = `float-${i} ${duration}s ease-in-out infinite alternate`;
            
            const keyframes = `
                @keyframes float-${i} {
                    0% { transform: translate(0, 0) scale(1); opacity: ${Math.random() * 0.5 + 0.3}; }
                    100% { transform: translate(${Math.random() * 40 - 20}px, ${Math.random() * 40 - 20}px) scale(${Math.random() * 0.5 + 0.8}); opacity: ${Math.random() * 0.5 + 0.5}; }
                }
            `;
            
            const style = document.createElement('style');
            style.textContent = keyframes;
            document.head.appendChild(style);
            
            backgroundElement.appendChild(glowDot);
        }
        
        // 添加网格线效果
        const gridContainer = document.createElement('div');
        gridContainer.style.position = 'absolute';
        gridContainer.style.top = '0';
        gridContainer.style.left = '0';
        gridContainer.style.right = '0';
        gridContainer.style.bottom = '0';
        gridContainer.style.backgroundImage = 'linear-gradient(rgba(86, 106, 227, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(86, 106, 227, 0.1) 1px, transparent 1px)';
        gridContainer.style.backgroundSize = '20px 20px';
        gridContainer.style.opacity = '0.4';
        gridContainer.style.zIndex = '-1';
        
        parentContainer.appendChild(backgroundElement);
        parentContainer.appendChild(gridContainer);
        
        // 获取canvas上下文
        const ctx = activityCtx.getContext('2d');
        
        // 创建荧光线条效果
        const glowLine = function(ctx, x, y, width, height, color, blur) {
            ctx.save();
            ctx.shadowColor = color;
            ctx.shadowBlur = blur;
            ctx.strokeStyle = color;
            ctx.beginPath();
            ctx.moveTo(x, y);
            ctx.lineTo(x + width, y + height);
            ctx.stroke();
            ctx.restore();
        };
        
        // 创建数据点高光效果
        const radialGradient = ctx.createRadialGradient(0, 0, 0, 0, 0, 10);
        radialGradient.addColorStop(0, 'rgba(191, 219, 254, 1)');
        radialGradient.addColorStop(0.5, 'rgba(147, 197, 253, 0.8)');
        radialGradient.addColorStop(1, 'rgba(59, 130, 246, 0)');
        
        // 创建图表
        const chart = new Chart(activityCtx, {
            type: 'line',
            data: {
                labels: chartData.labels,
                datasets: [{
                    label: '趋势变化',
                    data: chartData.values,
                    fill: false,
                    borderColor: 'rgba(37, 99, 235, 0.9)',
                    borderWidth: 3,
                    pointBackgroundColor: 'rgba(191, 219, 254, 1)',
                    pointBorderColor: 'rgba(37, 99, 235, 1)',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                    pointHoverBackgroundColor: 'rgba(239, 246, 255, 1)',
                    pointHoverBorderColor: 'rgba(147, 197, 253, 1)',
                    pointHoverBorderWidth: 3,
                    tension: 0.3,
                    spanGaps: true,
                    segment: {
                        borderColor: ctx => {
                            const value = ctx.p1.parsed.y;
                            const value2 = ctx.p0.parsed.y;
                            
                            if ((value >= 0 && value2 >= 0) || (value < 0 && value2 < 0)) {
                                return value >= 0 ? 'rgba(59, 130, 246, 0.9)' : 'rgba(239, 68, 68, 0.9)';
                            }
                            
                            // 创建跨越零线的渐变
                            const gradient = ctx.chart.ctx.createLinearGradient(
                                ctx.p0.x, ctx.p0.y, ctx.p1.x, ctx.p1.y
                            );
                            gradient.addColorStop(0, value2 >= 0 ? 'rgba(59, 130, 246, 0.9)' : 'rgba(239, 68, 68, 0.9)');
                            gradient.addColorStop(1, value >= 0 ? 'rgba(59, 130, 246, 0.9)' : 'rgba(239, 68, 68, 0.9)');
                            return gradient;
                        }
                    },
                    pointStyle: 'circle'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                backgroundColor: 'transparent',
                animation: {
                    duration: 2000,
                    easing: 'easeOutQuart',
                    delay: (context) => context.dataIndex * 100
                },
                layout: {
                    padding: {
                        left: 10,
                        right: 20,
                        top: 30,
                        bottom: 10
                    }
                },
                scales: {
                    y: {
                        grid: {
                            color: 'rgba(148, 163, 184, 0.1)',
                            lineWidth: 1,
                            drawBorder: false
                        },
                        ticks: {
                            padding: 10,
                            color: 'rgba(226, 232, 240, 0.8)',
                            font: {
                                family: "'Inter', 'system-ui', sans-serif",
                                weight: '500',
                                size: 11
                            },
                            callback: function(value) {
                                return value > 0 ? '+' + value : value;
                            }
                        },
                        border: {
                            display: false
                        }
                    },
                    x: {
                        grid: {
                            display: false,
                            drawBorder: false
                        },
                        ticks: {
                            padding: 10,
                            color: 'rgba(226, 232, 240, 0.8)',
                            font: {
                                family: "'Inter', 'system-ui', sans-serif",
                                weight: '500',
                                size: 11
                            }
                        },
                        border: {
                            display: false
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        enabled: true,
                        backgroundColor: 'rgba(15, 23, 42, 0.9)',
                        titleColor: 'rgba(226, 232, 240, 1)',
                        bodyColor: 'rgba(226, 232, 240, 0.8)',
                        padding: {
                            top: 12,
                            right: 16,
                            bottom: 12,
                            left: 16
                        },
                        cornerRadius: 8,
                        titleFont: {
                            family: "'Inter', 'system-ui', sans-serif",
                            weight: '600',
                            size: 13
                        },
                        bodyFont: {
                            family: "'Inter', 'system-ui', sans-serif",
                            weight: '400',
                            size: 12
                        },
                        displayColors: false,
                        borderColor: 'rgba(51, 65, 85, 0.8)',
                        borderWidth: 1,
                        caretSize: 6,
                        callbacks: {
                            title: function(context) {
                                return context[0].label + ' 数据';
                            },
                            label: function(context) {
                                const value = context.parsed.y;
                                const sign = value >= 0 ? '+' : '';
                                return `变化: ${sign}${value}`;
                            },
                            afterLabel: function(context) {
                                const value = context.parsed.y;
                                if (value > 0) {
                                    return '↗ 上升趋势';
                                } else if (value < 0) {
                                    return '↘ 下降趋势';
                                } else {
                                    return '→ 持平';
                                }
                            }
                        }
                    }
                },
                interaction: {
                    mode: 'index',
                    intersect: false
                },
                hover: {
                    mode: 'nearest',
                    intersect: false
                },
                elements: {
                    line: {
                        tension: 0.3,
                        borderWidth: 3,
                        borderCapStyle: 'round',
                        borderJoinStyle: 'round',
                        capBezierPoints: true
                    },
                    point: {
                        radius: 6,
                        hitRadius: 10
                    }
                }
            },
            plugins: [{
                id: 'glowEffect',
                beforeDraw: function(chart) {
                    const ctx = chart.ctx;
                    ctx.save();
                    ctx.shadowColor = 'rgba(37, 99, 235, 0.5)';
                    ctx.shadowBlur = 15;
                    ctx.shadowOffsetX = 0;
                    ctx.shadowOffsetY = 0;
                    ctx.restore();
                },
                afterDatasetDraw: function(chart, args) {
                    const { ctx, data, chartArea } = chart;
                    const { top, bottom, left, right } = chartArea;
                    const dataset = data.datasets[args.index];
                    
                    // 绘制荧光效果
                    if (args.index === 0) {  // 只为主数据集应用
                        // 首先储存所有点位置
                        const points = [];
                        for (let i = 0; i < dataset.data.length; i++) {
                            const dataPoint = dataset.data[i];
                            const xPos = chart.scales.x.getPixelForValue(i);
                            const yPos = chart.scales.y.getPixelForValue(dataPoint);
                            points.push({ x: xPos, y: yPos, value: dataPoint });
                        }
                        
                        // 为每个点添加荧光效果
                        points.forEach((point, i) => {
                            if (i < points.length - 1) {
                                const nextPoint = points[i + 1];
                                // 创建基于值的荧光颜色
                                const glowColor = point.value >= 0 ? 
                                    'rgba(59, 130, 246, 0.6)' : 
                                    'rgba(239, 68, 68, 0.6)';
                                
                                // 添加线段荧光
                                ctx.save();
                                ctx.shadowColor = glowColor;
                                ctx.shadowBlur = 10;
                                ctx.strokeStyle = glowColor;
                                ctx.lineWidth = 2;
                                ctx.beginPath();
                                ctx.moveTo(point.x, point.y);
                                ctx.lineTo(nextPoint.x, nextPoint.y);
                                ctx.stroke();
                                ctx.restore();
                            }
                            
                            // 添加点的荧光效果
                            ctx.save();
                            ctx.shadowColor = point.value >= 0 ? 
                                'rgba(59, 130, 246, 0.8)' : 
                                'rgba(239, 68, 68, 0.8)';
                            ctx.shadowBlur = 15;
                            ctx.beginPath();
                            ctx.arc(point.x, point.y, 4, 0, Math.PI * 2);
                            ctx.fillStyle = point.value >= 0 ? 
                                'rgba(191, 219, 254, 0.8)' : 
                                'rgba(254, 202, 202, 0.8)';
                            ctx.fill();
                            ctx.restore();
                        });
                    }
                }
            }]
        });
        
        // 添加交互动画
        activityCtx.addEventListener('mousemove', function(e) {
            const rect = activityCtx.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            const points = chart.getElementsAtEventForMode(e, 'nearest', { intersect: true }, false);
            
            if (points.length) {
                activityCtx.style.cursor = 'pointer';
                
                // 如果有动态光点特效，这里可以添加
                const point = points[0];
                const dataset = chart.data.datasets[point.datasetIndex];
                const idx = point.index;
                const value = dataset.data[idx];
                
                // 动态调整点的大小和亮度
                dataset.pointRadius = Array(dataset.data.length).fill(6);
                dataset.pointRadius[idx] = 8;
                
                chart.update('none');
            } else {
                activityCtx.style.cursor = 'default';
                
                // 重置点大小
                if (chart.lastHoverIndex !== undefined) {
                    chart.data.datasets[0].pointRadius = 6;
                    chart.update('none');
                    chart.lastHoverIndex = undefined;
                }
            }
        });
        
        // 添加点击动画
        activityCtx.addEventListener('click', function(e) {
            const points = chart.getElementsAtEventForMode(e, 'nearest', { intersect: true }, false);
            
            if (points.length) {
                const point = points[0];
                const meta = chart.getDatasetMeta(point.datasetIndex);
                const rect = activityCtx.getBoundingClientRect();
                
                // 创建点击波纹效果
                const ripple = document.createElement('div');
                ripple.style.position = 'absolute';
                ripple.style.width = '50px';
                ripple.style.height = '50px';
                ripple.style.left = (e.clientX - rect.left - 25) + 'px';
                ripple.style.top = (e.clientY - rect.top - 25) + 'px';
                ripple.style.borderRadius = '50%';
                ripple.style.background = 'radial-gradient(circle, rgba(255,255,255,0.7) 0%, rgba(255,255,255,0) 70%)';
                ripple.style.animation = 'ripple 0.7s ease-out';
                
                const keyframes = `
                    @keyframes ripple {
                        0% { transform: scale(0.3); opacity: 1; }
                        100% { transform: scale(2); opacity: 0; }
                    }
                `;
                
                const style = document.createElement('style');
                style.textContent = keyframes;
                document.head.appendChild(style);
                
                parentContainer.appendChild(ripple);
                
                // 自动移除波纹效果
                setTimeout(() => {
                    parentContainer.removeChild(ripple);
                    document.head.removeChild(style);
                }, 700);
            }
        });
        
        // 添加chart实例到DOM元素
        activityCtx.chart = chart;
    }
    
    // 用户活跃度分析图表
    var userActivityCtx = document.getElementById('userActivityChart');
    if (userActivityCtx) {
        // 读取用户活跃度数据
        let activityData = null;
        
        try {
            if (userActivityCtx.dataset && userActivityCtx.dataset.chartValues) {
                const rawData = JSON.parse(userActivityCtx.dataset.chartValues);
                if (rawData.labels && rawData.datasets && rawData.datasets[0] && rawData.datasets[0].data) {
                    activityData = {
                        labels: rawData.labels,
                        values: rawData.datasets[0].data,
                        borderColor: rawData.datasets[0].borderColor || "#4f46e5",
                        backgroundColor: rawData.datasets[0].backgroundColor || "rgba(79, 70, 229, 0.2)"
                    };
                    console.log('已获取用户活跃度数据:', activityData);
                }
            }
        } catch (e) {
            console.error('无法解析用户活跃度数据:', e);
        }
        
        // 如果没有真实数据，显示错误提示
        if (!activityData) {
            console.error('未找到有效的用户活跃度数据，显示错误信息');
            showDataError(userActivityCtx, '未能加载用户活跃度数据');
            return;
        }
        
        new Chart(userActivityCtx, {
            type: 'line',
            data: {
                labels: activityData.labels,
                datasets: [{
                    label: '活跃用户数',
                    data: activityData.values,
                    backgroundColor: activityData.backgroundColor,
                    borderColor: activityData.borderColor,
                    borderWidth: 2,
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 1000,
                    easing: 'easeOutQuart'
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)',
                            drawBorder: false
                        },
                        ticks: {
                            padding: 10,
                            font: {
                                size: 12,
                                family: "'Inter', 'Helvetica', 'Arial', sans-serif"
                            },
                            color: '#64748b'
                        }
                    },
                    x: {
                        grid: {
                            display: false,
                            drawBorder: false
                        },
                        ticks: {
                            padding: 10,
                            font: {
                                size: 12,
                                family: "'Inter', 'Helvetica', 'Arial', sans-serif"
                            },
                            color: '#64748b'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: '#1e293b',
                        titleColor: '#ffffff',
                        bodyColor: '#e2e8f0',
                        padding: 12,
                        cornerRadius: 8,
                        displayColors: false,
                        callbacks: {
                            label: function(context) {
                                return `活跃用户: ${context.raw} 人`;
                            }
                        }
                    }
                }
            }
        });
    }
    
    // 用户活跃时段图
    var periodsCtx = document.getElementById('userActivityPeriodsChart');
    if (periodsCtx) {
        // 读取用户活跃时段数据
        let periodsData = null;
        
        try {
            if (periodsCtx.dataset && periodsCtx.dataset.chartValues) {
                const rawData = JSON.parse(periodsCtx.dataset.chartValues);
                if (rawData.labels && rawData.datasets && rawData.datasets[0] && rawData.datasets[0].data) {
                    periodsData = {
                        labels: rawData.labels,
                        values: rawData.datasets[0].data,
                        backgroundColor: rawData.datasets[0].backgroundColor || [
                            'rgba(79, 70, 229, 0.6)',
                            'rgba(59, 130, 246, 0.6)',
                            'rgba(16, 185, 129, 0.6)',
                            'rgba(245, 158, 11, 0.6)'
                        ]
                    };
                    console.log('已获取用户活跃时段数据:', periodsData);
                }
            }
        } catch (e) {
            console.error('无法解析用户活跃时段数据:', e);
        }
        
        // 如果没有真实数据，显示错误提示
        if (!periodsData) {
            console.error('未找到有效的用户活跃时段数据，显示错误信息');
            showDataError(periodsCtx, '未能加载用户活跃时段数据');
            return;
        }
        
        new Chart(periodsCtx, {
            type: 'bar',
            data: {
                labels: periodsData.labels,
                datasets: [{
                    label: '活跃人数',
                    data: periodsData.values,
                    backgroundColor: periodsData.backgroundColor,
                    borderWidth: 0,
                    borderRadius: 6,
                    maxBarThickness: 40,
                    barPercentage: 0.6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 1000,
                    easing: 'easeOutQuart'
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)',
                            drawBorder: false
                        },
                        ticks: {
                            padding: 10,
                            font: {
                                size: 12,
                                family: "'Inter', 'Helvetica', 'Arial', sans-serif"
                            },
                            color: '#64748b'
                        }
                    },
                    x: {
                        grid: {
                            display: false,
                            drawBorder: false
                        },
                        ticks: {
                            padding: 10,
                            font: {
                                size: 12,
                                family: "'Inter', 'Helvetica', 'Arial', sans-serif"
                            },
                            color: '#64748b'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: '#1e293b',
                        titleColor: '#ffffff',
                        bodyColor: '#e2e8f0',
                        padding: 12,
                        cornerRadius: 8,
                        displayColors: false,
                        callbacks: {
                            label: function(context) {
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${context.label || '活跃人数'}: ${value} 人 (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }
    
    // 题型分布图
    var typeCtx = document.getElementById('questionTypeChart');
    if (typeCtx) {
        // 读取题型分布数据
        let typeData = null;
        
        try {
            if (typeCtx.dataset && typeCtx.dataset.chartValues) {
                const rawData = JSON.parse(typeCtx.dataset.chartValues);
                if (rawData.labels && rawData.datasets && rawData.datasets[0] && rawData.datasets[0].data) {
                    typeData = {
                        labels: rawData.labels,
                        values: rawData.datasets[0].data
                    };
                    console.log('已获取题型分布数据:', typeData);
                }
            }
        } catch (e) {
            console.error('无法解析题型分布数据:', e);
        }
        
        // 如果没有真实数据，显示错误提示
        if (!typeData) {
            console.error('未找到有效的题型分布数据，显示错误信息');
            showDataError(typeCtx, '未能加载题型分布数据');
            return;
        }
        
        new Chart(typeCtx, {
            type: 'doughnut',
            data: {
                labels: typeData.labels,
                datasets: [{
                    data: typeData.values,
                    backgroundColor: [
                        'rgba(79, 70, 229, 0.8)',
                        'rgba(245, 158, 11, 0.8)'
                    ],
                    borderColor: [
                        'rgba(79, 70, 229, 1)',
                        'rgba(245, 158, 11, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    animateRotate: true,
                    animateScale: true,
                    duration: 1000,
                    easing: 'easeOutQuart'
                },
                layout: {
                    padding: {
                        top: 10,
                        bottom: 10,
                        left: 10,
                        right: 10
                    }
                },
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            usePointStyle: true,
                            padding: 15,
                            font: {
                                size: 12,
                                family: "'Inter', 'Helvetica', 'Arial', sans-serif"
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: '#1e293b',
                        titleColor: '#ffffff',
                        bodyColor: '#e2e8f0',
                        padding: 12,
                        cornerRadius: 8,
                        displayColors: true,
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${label}: ${value} 题 (${percentage}%)`;
                            }
                        }
                    }
                },
                cutout: '65%'
            }
        });
    }
    
    // 用户活跃时段分布图
    var timeCtx = document.getElementById('activeTimeChart');
    if (timeCtx) {
        // 尝试获取真实数据
        let timeData = null;
        
        try {
            if (timeCtx.dataset && timeCtx.dataset.chartValues) {
                const rawData = JSON.parse(timeCtx.dataset.chartValues);
                if (rawData.labels && rawData.datasets && rawData.datasets[0] && rawData.datasets[0].data) {
                    timeData = {
                        labels: rawData.labels,
                        values: rawData.datasets[0].data
                    };
                    console.log('已获取活跃时段分布数据:', timeData);
                }
            }
        } catch (e) {
            console.error('无法解析活跃时段分布数据:', e);
        }
        
        // 如果没有真实数据，显示错误提示
        if (!timeData) {
            console.error('未找到有效的活跃时段分布数据，显示错误信息');
            showDataError(timeCtx, '未能加载活跃时段分布数据');
            return;
        }
        
        new Chart(timeCtx, {
            type: 'bar',
            data: {
                labels: timeData.labels,
                datasets: [{
                    label: '活跃用户数',
                    data: timeData.values,
                    backgroundColor: [
                        'rgba(79, 70, 229, 0.6)',
                        'rgba(59, 130, 246, 0.6)',
                        'rgba(16, 185, 129, 0.6)',
                        'rgba(245, 158, 11, 0.6)'
                    ],
                    borderColor: [
                        'rgba(79, 70, 229, 0.8)',
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(16, 185, 129, 0.8)',
                        'rgba(245, 158, 11, 0.8)'
                    ],
                    borderWidth: 1,
                    borderRadius: 6,
                    maxBarThickness: 30,
                    barPercentage: 0.8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    delay: function(context) {
                        return context.dataIndex * 100;
                    },
                    duration: 1000,
                    easing: 'easeOutQuart'
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)',
                            drawBorder: false
                        },
                        ticks: {
                            padding: 10,
                            font: {
                                size: 12,
                                family: "'Inter', 'Helvetica', 'Arial', sans-serif"
                            },
                            color: '#64748b'
                        }
                    },
                    x: {
                        grid: {
                            display: false,
                            drawBorder: false
                        },
                        ticks: {
                            padding: 10,
                            font: {
                                size: 12,
                                family: "'Inter', 'Helvetica', 'Arial', sans-serif"
                            },
                            color: '#64748b'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: '#1e293b',
                        titleColor: '#ffffff',
                        bodyColor: '#e2e8f0',
                        padding: 12,
                        cornerRadius: 8,
                        displayColors: false,
                        callbacks: {
                            label: function(context) {
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${context.dataset.label}: ${value} 人 (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }
}

// 显示数据错误提示
function showDataError(canvas, message) {
    const parentElement = canvas.parentNode;
    const errorDiv = document.createElement('div');
    errorDiv.className = 'chart-error';
    errorDiv.style.width = '100%';
    errorDiv.style.height = '300px';
    errorDiv.style.display = 'flex';
    errorDiv.style.flexDirection = 'column';
    errorDiv.style.alignItems = 'center';
    errorDiv.style.justifyContent = 'center';
    errorDiv.style.backgroundColor = '#1e293b';
    errorDiv.style.borderRadius = '12px';
    errorDiv.style.color = '#e2e8f0';
    errorDiv.style.padding = '20px';
    errorDiv.style.textAlign = 'center';
    
    // 添加错误图标
    const iconElement = document.createElement('div');
    iconElement.innerHTML = '<i class="fas fa-exclamation-triangle" style="font-size: 2.5rem; color: #f59e0b; margin-bottom: 1rem;"></i>';
    
    // 添加错误信息
    const messageElement = document.createElement('div');
    messageElement.textContent = message || '数据加载失败';
    messageElement.style.marginBottom = '1rem';
    messageElement.style.fontSize = '14px';
    
    // 添加刷新按钮
    const refreshButton = document.createElement('button');
    refreshButton.className = 'btn btn-sm btn-primary';
    refreshButton.innerHTML = '<i class="fas fa-sync-alt me-1"></i> 刷新数据';
    refreshButton.style.backgroundColor = '#3b82f6';
    refreshButton.style.border = 'none';
    refreshButton.style.padding = '6px 12px';
    refreshButton.style.borderRadius = '4px';
    refreshButton.style.cursor = 'pointer';
    refreshButton.onclick = function() {
        window.location.reload();
    };
    
    errorDiv.appendChild(iconElement);
    errorDiv.appendChild(messageElement);
    errorDiv.appendChild(refreshButton);
    
    parentElement.replaceChild(errorDiv, canvas);
}

// 显示静态替代图表
function showStaticCharts() {
    // 替换活动图表
    replaceCanvasWithFallback('activityChart', '用户答题数据趋势');
    
    // 替换题型分布图
    replaceCanvasWithFallback('questionTypeChart', '题型分布');
    
    // 替换活跃时段图
    replaceCanvasWithFallback('userActivityPeriodsChart', '用户活跃时段');
    
    // 替换用户活跃度分析图
    replaceCanvasWithFallback('userActivityChart', '用户活跃度分析');
    
    // 替换活跃时段分布图
    replaceCanvasWithFallback('activeTimeChart', '用户活跃时段分布');
}

// 用静态内容替换Canvas
function replaceCanvasWithFallback(canvasId, title) {
    var canvas = document.getElementById(canvasId);
    if (canvas) {
        var parent = canvas.parentNode;
        var fallback = document.createElement('div');
        fallback.className = 'chart-fallback';
        fallback.innerHTML = `
            <div class="text-center p-4">
                <h5>${title}</h5>
                <p class="text-muted">图表加载失败，显示静态内容</p>
                <div class="fallback-icon">
                    <i class="fas fa-chart-bar fa-3x text-secondary"></i>
                </div>
                <button class="btn btn-sm btn-primary mt-3" onclick="location.reload()">
                    <i class="fas fa-sync-alt me-1"></i> 刷新页面
                </button>
            </div>
        `;
        parent.replaceChild(fallback, canvas);
    }
} 
<!-- all-courses.wxml -->
<view class="container">
  <!-- 顶部标题栏 -->
  <view class="header">
    <view class="header-content">
      <view class="back-btn" bindtap="navigateBack">
        <text class="back-icon">←</text>
      </view>
      <view class="page-title">{{className}}全部课程</view>
      <view class="placeholder"></view> <!-- 用于平衡布局 -->
    </view>
  </view>
  
  <!-- 课程网格列表 -->
  <view class="courses-grid" wx:if="{{courses.length > 0}}">
    <view wx:for="{{courses}}" wx:key="id" class="course-card" bindtap="navigateToCourse" data-id="{{item.id}}" data-name="{{item.name}}">
      <view class="course-icon" style="background-color: {{item.color || '#4e8df7'}}">
        <text class="course-icon-text">{{item.name[0]}}</text>
      </view>
      <view class="course-info">
        <text class="course-name">{{item.name}}</text>
        <view class="course-stats">
          <text class="course-question-count">{{item.question_count.total}}题</text>
          <text class="course-question-type" wx:if="{{item.question_count.single > 0}}">单选: {{item.question_count.single}}</text>
          <text class="course-question-type" wx:if="{{item.question_count.multiple > 0}}">多选: {{item.question_count.multiple}}</text>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 无课程提示 -->
  <view class="empty-courses" wx:if="{{!isLoading && courses.length === 0}}">
    <view class="empty-icon">📚</view>
    <text class="empty-text">暂无课程</text>
    <text class="empty-subtext">请联系管理员添加课程</text>
  </view>
  
  <!-- 加载提示 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view> 
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <!-- Required Meta Tags Always Come First -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <!-- Title -->
  <title>Bootstrap中文网</title>
  <!-- Favicon -->
  <link rel="shortcut icon" href="assets/favicons/favicon.ico">
  <!--  Meta tags -->
  <meta name="keywords" content="Bootstrap,CSS,CSS框架,CSS framework,javascript,bootcss,bootstrap开发,bootstrap代码,bootstrap入门">
  <meta name="description" content="Bootstrap是Twitter推出的一个用于前端开发的开源工具包。它由Twitter的设计师Mark Otto和Jacob Thornton合作开发，是一个CSS/HTML框架。目前，Bootstrap最新版本为5.0 。Bootstrap中文网致力于为广大国内开发者提供详尽的中文文档、代码实例等，助力开发者掌握并使用这一框架。">
  <!-- Open Graph -->
  <meta property="og:title" content="Bootstrap中文网">
  <meta property="og:type" content="article">
  <meta property="og:url" content="https://www.bootcss.com/">
  <meta property="og:image" content="assets/brand/bootstrap-social.png">
  <meta property="og:description" content="Bootstrap是Twitter推出的一个用于前端开发的开源工具包。它由Twitter的设计师Mark Otto和Jacob Thornton合作开发，是一个CSS/HTML框架。目前，Bootstrap最新版本为5.0 。Bootstrap中文网致力于为广大国内开发者提供详尽的中文文档、代码实例等，助力开发者掌握并使用这一框架。">
  <meta property="og:site_name" content="Bootstrap中文网">
  <!-- CSS Implementing Plugins -->
  <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.2.1/css/all.min.css">
  <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/bootstrap-icons/1.10.0/font/bootstrap-icons.min.css">

  <!-- CSS Template -->
  <link rel="stylesheet" href="assets/css/theme.css">
  <style>
    .text-secondary {
      color: #712cf9 !important;
    }
    .btn-secondary {
      background-color: #712cf9;
      border-color: #712cf9;
    }
  </style>
  <!-- 以下是 www.bootcss.com 网站所使用的统计代码，如果你使用本页面作为自己的模板，请将下面的统计代码删掉！！！ -->
<!-- 上面是 www.bootcss.com 网站所使用的统计代码，如果你使用本页面作为自己的模板，请将上面面的统计代码删掉！！！ -->
</head>
<body>
  <!-- Header -->
<header class="duik-header">
  <!-- Navbar -->
  <nav class="navbar navbar-expand-lg navbar-light bg-white position-absolute left-0 right-0 flex-nowrap z-index-3">
    <div class="container">
      <a class="navbar-brand" href="/"><img src="assets/img/navlogo-small.png"> Bootstrap 中文网</a>
      <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarTogglerDemo" aria-controls="navbarTogglerDemo" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarTogglerDemo">
        <ul class="navbar-nav ml-auto mt-2 mt-lg-0">
          <li class="nav-item dropdown ml-lg-6 mb-2 mb-lg-0 d-none d-lg-block">
            <a class="nav-link px-0" href="#" id="navbarDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
              Bootstrap 中文文档 <i class="fas fa-angle-down small ml-1"></i>
            </a>
            <div class="dropdown-menu" aria-labelledby="navbarDropdown">
              <a class="dropdown-item" href="https://v3.bootcss.com/" target="_blank" >Bootstrap v3 中文文档</a>
              <a class="dropdown-item" href="https://v4.bootcss.com/" target="_blank" >Bootstrap v4 中文文档</a>
              <a class="dropdown-item" href="https://v5.bootcss.com/" target="_blank" >Bootstrap v5 中文文档</a>
              <div class="dropdown-divider"></div>
              <a class="dropdown-item" href="https://v2.bootcss.com/" target="_blank" >Bootstrap v2 中文文档</a>
            </div>
          </li>
          <li class="nav-item ml-lg-6 mb-2 mb-lg-0 d-lg-none">
            <a class="nav-link px-0 active" href="https://v3.bootcss.com/" target="_blank" >Bootstrap v3 中文文档</a>
          </li>
          <li class="nav-item ml-lg-6 mb-2 mb-lg-0 d-lg-none">
            <a class="nav-link px-0 active" href="https://v4.bootcss.com/" target="_blank" >Bootstrap v4 中文文档</a>
          </li>
          <li class="nav-item ml-lg-6 mb-2 mb-lg-0 d-lg-none">
            <a class="nav-link px-0 active" href="https://v5.bootcss.com/" target="_blank" >Bootstrap v5 中文文档</a>
          </li>
          <li class="nav-item ml-lg-6 mb-2 mb-lg-0">
            <a class="nav-link px-0 active position-relative reddot" href="https://expo.bootcss.com/" target="_blank" >网站实例</a>
          </li>
          <li class="nav-item ml-lg-6 mb-2 mb-lg-0">
            <a class="nav-link px-0 active position-relative reddot" href="https://mb.bootcss.com" target="_blank" >精选模板</a>
          </li>
          <li><hr class="dropdown-divider"></li>
          <li class="nav-item ml-lg-6 mb-2 mb-lg-0 d-lg-none">
            <a class="nav-link px-0 active" href="https://v2.bootcss.com/" target="_blank" >Bootstrap v2 中文文档</a>
          </li>
          <li class="nav-item dropdown ml-lg-6 mb-2 mb-lg-0 d-none d-lg-block">
            <a class="nav-link px-0" href="#" id="navbarDropdown2" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
              <i class="fas fa-solid fa-globe small ml-1"></i>语言
            </a>
            <div class="dropdown-menu" aria-labelledby="navbarDropdown2">
              <a class="dropdown-item" href="#" target="_blank" >中文</a>
              <a class="dropdown-item" href="#" target="_blank" >EN</a>
            </div>
          </li>		  
        </ul>
      </div>
    </div>
  </nav>
  <!-- End Navbar -->
</header>
<!-- End Header -->
<!-- Promo Section -->
<section class="duik-promo bg-light text-center">
  <div class="container duik-promo-container">
    <div class="d-flex justify-content-center mh-30rem py-11">
      <div class="align-self-center">
        <h1 class="hero-title text-white mb-2">Bootstrap</h1>
        <div class="lead text-white">简洁、直观、强悍的前端开发框架，让web开发更迅速、简单。</div>
      </div>
    </div>
  </div>

</section>
<!-- End Promo Section -->
<!-- Icon Blocks -->
<div class="container z-index-2 position-relative">
  <section class="duik-icon-block duik-icon-block--pull2top rounded-top-home">
    <div class="row no-gutters">
      <div class="col-md border-bottom border-md-bottom-0">
        <div class="d-flex flex-column align-items-center duik-icon-block__item py-9 px-3 h-100">
          <h3 class="mb-2">Bootstrap v3</h3>
          <div class="d-flex justify-content-center text-left">
            <ul class="list-unstyled mb-3">
              <li><i class="fas fa-check mr-2 text-secondary"></i>支持 IE8+ 浏览器</li>
              <li><i class="fas fa-times mr-2 text-secondary"></i>依赖 jQuery</li>
            </ul>
          </div>
          <div class="mt-auto">
            <a href="https://v3.bootcss.com/" class="btn btn-outline-secondary btn-sm mb-2 mr-2" target="_blank" title="Bootstrap v3 中文文档" >Bootstrap v3 中文文档</a>
          </div>
        </div>
      </div>
      <div class="col-md border-bottom border-md-bottom-0 border-md-left">
        <div class="d-flex flex-column align-items-center duik-icon-block__item py-9 px-3 h-100">
          <h3 class="mb-2">Bootstrap v4</h3>
          <div class="d-flex justify-content-center text-left">
            <ul class="list-unstyled mb-3">
              <li><i class="fas fa-check mr-2 text-secondary"></i>支持 IE10+ 浏览器</li>
              <li><i class="fas fa-check mr-2 text-secondary"></i>支持 Flexbox 布局</li>
              <li><i class="fas fa-check mr-2 text-secondary"></i>不依赖 jQuery</li>
            </ul>
          </div>
          <div class="mt-auto">
            <a href="https://v4.bootcss.com/" class="btn btn-outline-secondary btn-sm mb-2 mr-2" target="_blank" title="Bootstrap v4 中文文档">Bootstrap v4 中文文档</a>
          </div>
        </div>
      </div>
      <div class="col-md border-bottom border-md-bottom-0 border-md-left">
        <div class="d-flex flex-column align-items-center duik-icon-block__item py-9 px-3 h-100">
          <h3 class="mb-2">Bootstrap v5</h3>
          <div class="d-flex justify-content-center text-left">
            <ul class="list-unstyled mb-3">
              <li><i class="fas fa-check mr-2 text-secondary"></i>支持 Flexbox 布局</li>
              <li><i class="fas fa-check mr-2 text-secondary"></i>支持 Grid 布局</li>
              <li><i class="fas fa-check mr-2 text-secondary"></i>支持 CSS 自定义属性</li>
              <li><i class="fas fa-check mr-2 text-secondary"></i>不依赖 jQuery</li>
              <li><i class="fas fa-times mr-2 text-secondary"></i>不支持 IE 浏览器</li>
            </ul>
          </div>
          <div class="mt-auto">
            <a href="https://v5.bootcss.com/" class="btn btn-outline-secondary btn-sm mb-2 mr-2" target="_blank" title="Bootstrap v5 中文文档">Bootstrap v5 中文文档</a>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>
<!-- End Icon Blocks -->
<main>
<section class="text-center pt-11 pb-6">
  <div class="container">
    <div class="w-md-75 w-lg-50 mx-auto text-center mb-5">
      <h2 class="h3 text-center">前端开源项目 CDN 加速服务</h2>
      <p>共收录了 4387 个前端开源项目</p>
    </div>
	
	<div class="row justify-content-md-between">
		<div class="search-wraper" role="search">
							<div class="form-group">
								<input type="text" class="form-control search clearable" placeholder="搜索开源库，例如：jquery" autocomplete="off"  tabindex="0" autocorrect="off" autocapitalize="off" spellcheck="false">
								<i class="fa fa-search"></i>
							</div>
		</div>
        <div class="p-5" id="search-results" style="display: none"></div>		
	</div>
	<div class="row">
	  <div class="col-sm-4">
		<div class="card flex-row border-0">		
		<div class="d-flex align-items-center">
		<img class="card-img-left " src="assets/img/item1.png" width="64" height="64" />
		</div>
		  <div class="card-body">
			<h3 class="card-title text-left">稳定</h3>
			<p class="card-text text-left">领先的分布式技术，确保您的网站在任何时候稳定运行。</p>
		  </div>
		</div>
	  </div>
	  <div class="col-sm-4">
		<div class="card flex-row border-0">
		  <div class="d-flex align-items-center">
		  <img class="card-img-left " src="assets/img/item2.png" width="64" height="64" />
		  </div>
		  <div class="card-body">
			<h3 class="card-title text-left">快速</h3>
			<p class="card-text text-left">多层优化，加速网站内容的加载速度</p>
		  </div>
		</div>
	  </div>
	  <div class="col-sm-4">
		<div class="card flex-row border-0">
		<div class="d-flex align-items-center">
		<img class="card-img-left " src="assets/img/item3.png" width="64" height="64" />
		</div>
		  <div class="card-body">
			<h3 class="card-title text-left">免费</h3>
			<p class="card-text text-left">免费的CDN加速服务，访问更加便捷。</p>
		  </div>
		</div>
	  </div>  
	</div>	
    <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-4 mt-10">
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/expo.png" width="300" height="150" alt="Bootstrap 优站精选" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.youzhan.org/" >优站精选</a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Bootstrap 优站精选频道收集了众多基于 Bootstrap 构建、设计精美的、有创意的网站。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/react.png" width="300" height="150" alt="React - 用于构建用户界面的 JavaScript 框架" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.bootcdn.cn/react/" >React</a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">React 起源于 Facebook 的内部项目，是一个用于构建用户界面的 JavaScript 库。</p>
          </div>
        </div>
      </div>
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/vuejs.png" width="300" height="150" alt="Webpack 是前端资源模块化管理和打包工具" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.bootcdn.cn/vue/" >Vue.js</a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Vue 是一套用于构建用户界面的渐进式框架。与其它大型框架不同的是，Vue 被设计为可以自底向上逐层应用。Vue 的核心库只关注视图层，不仅易于上手，还便于与第三方库或既有项目整合。</p>
          </div>
        </div>
      </div>
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/typescript.png" width="300" height="150" alt="TypeScript 中文手册" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.bootcdn.cn/typescript/" >TypeScript</a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">TypeScript 是由微软开源的编程语言。它是 JavaScript 的一个超集，而且本质上向这个语言添加了可选的静态类型和基于类的面向对象编程。</p>
          </div>
        </div>
      </div>
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/bootmb.png" width="300" height="150" alt="Bootstrap 免费模板" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://mb.bootcss.com/" >Bootstrap免费模板</a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">用我们收集的大量优秀的 Bootstrap 免费模板助力您创建精美的 Bootstrap 网站！</p>
          </div>
        </div>
      </div>
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/nextjs.png" width="300" height="150" alt="Next.js 是一个轻量级的 React 服务端渲染应用框架。" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.nextjs.cn/" >Next.js</a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Next.js 是一个轻量级的 React 服务端渲染应用框架。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/babeljs.png" width="300" height="150" alt="Babel 是一个 JavaScript 编译器。" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.bootcdn.cn/babel-core/" >Babel</a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Babel 是一个 JavaScript 编译器。Babel 通过语法转换器支持最新版本的 JavaScript 语法。</p>
          </div>
        </div>
      </div>
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/npm.png" width="300" height="150" alt="NPM 中文文档" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.npmjs.cn/" >NPM</a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">NPM（node package manager）是 Node.js 世界的包管理器。NPM 可以让 JavaScript 开发者在共享代码、复用代码以及更新共享的代码上更加方便。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/yarn.png" width="300" height="150" alt="Yarn 是一个快速、可靠、安全的依赖管理工具。是 NPM 的替代品。" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://classic.yarnpkg.cn/" >Yarn </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Yarn 是一个快速、可靠、安全的依赖管理工具。是 NPM 的替代品。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/yarn.png" width="300" height="150" alt="Yarn 是一个快速、可靠、安全的依赖管理工具。是 NPM 的替代品。Yarn v2 与 v1 版本有很大的不同，Yarn v2 改进了 CLI 交互、支持 workspace、PnP 等新功能。" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.yarnpkg.cn/" >Yarn v2 </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Yarn 是一个快速、可靠、安全的依赖管理工具。是 NPM 的替代品。Yarn v2 与 v1 版本有很大的不同，Yarn v2 改进了 CLI 交互、支持 workspace、PnP 等新功能。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/pnpm.png" width="300" height="150" alt="pnpm" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.pnpm.cn/" >pnpm</a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">pnpm 是一个速度快、节省磁盘空间的软件包管理器。pnpm 在功能上与 npm 和 Yarn 类似。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/tailwindcss.png" width="300" height="150" alt="Tailwind CSS 中文网 / 中文文档" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.bootcdn.cn/tailwindcss/" >Tailwind CSS </a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Tailwind CSS 是一个用于快速UI开发的实用工具集 CSS 框架。与 Bootstrap 、Foundation 不同，Tailwind CSS 没有内置的 UI 组件。完全需要开发者根据自身情况来定制设计。</p>
          </div>
        </div>
      </div>
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/alpinejs.png" width="300" height="150" alt="Alpine.js" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.bootcdn.cn/alpinejs/" >Alpine.js</a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Alpine.js 通过很低的成本提供了与 Vue 或 React 这类大型框架相近的响应式和声明式特性。Alpine.js 的语法几乎完全借用自 Vue。</p>
          </div>
        </div>
      </div>
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/docusaurus.png" width="300" height="150" alt="Docusaurus 中文文档" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.docusaurus.cn/" >Docusaurus </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Docusaurus 是一款基于 React 框架构建的易于维护的静态网站创建工具。Docusaurus 能够帮你快速建立文档网站、博客、营销页面等。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/svelte.png" width="300" height="150" alt="Svelte" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.svelte.cn/" >Svelte</a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Svelte 是构建 Web 应用程序的一种新方法。Svelte 是一个编译器，它将声明性组件转换成高效的 JavaScript 代码，并像做外科手术一样细粒度地更新 DOM。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/solidjs.png" width="300" height="150" alt="Solid.js" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.solidjs.cn/" >Solid.js</a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Solid.js 是一个用于构建用户界面、简单高效、性能卓越的 JavaScript 库，是 React.js 的有力竞争者。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/lernajs.png" width="300" height="150" alt="Lerna 是一个管理工具，用于管理包含多个软件包（package）的 JavaScript 项目。" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.lernajs.cn/" >Lerna </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Lerna 是一个管理工具，用于管理包含多个软件包（package）的 JavaScript 项目。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/nuxtjs.png" width="300" height="150" alt="Nuxt.js 中文文档" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.nuxtjs.cn/" >Nuxt.js </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Nuxt.js 是一个基于 Vue.js 的通用应用框架。通过对客户端/服务端基础架构的抽象组织，Nuxt.js 主要关注的是应用的 UI渲染。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/recoil.png" width="300" height="150" alt="Recoil 中文文档" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.recoiljs.cn/" >Recoil</a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Recoil 是一个针对 React 应用程序的状态管理库。 它提供了仅使用 React 难以实现的几种功能，同时与 React 的最新功能兼容。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/redux.png" width="300" height="150" alt="Redux 中文文档" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.bootcdn.cn/redux/" >Redux </a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Redux 是 JavaScript 状态容器，提供可预测化的状态管理、构建一致化的应用，运行于不同的环境（客户端、服务器、原生应用），并且易于测试。</p>
          </div>
        </div>
      </div>
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/mobx.png" width="300" height="150" alt="MobX 中文文档" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.bootcdn.cn/mobx/" >MobX </a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">MobX 是一个简单、可扩展的状态管理工具库</p>
          </div>
        </div>
      </div>
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/rollup.png" width="300" height="150" alt="rollup.js 是新一代的 JavaScript 模块打包工具。" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.bootcdn.cn/rollup/" >Rollup </a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Rollup 是一个 JavaScript 模块打包工具，可以将小块代码编译成大块复杂的代码。Rollup 对 JavaScript 代码模块使用新的 ES6 标准化格式，如 CommonJS 和 AMD。</p>
          </div>
        </div>
      </div>
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/parcel.png" width="300" height="150" alt="Parcel - 极速、零配置的 web 应用打包工具。" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.parceljs.cn/" >Parcel </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Parcel - 极速、零配置的 web 应用打包工具。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/lodash.png" width="300" height="150" alt="Lodash 是最流行的 JavaScript 工具库。" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.bootcdn.cn/lodash.js/" >Lodash</a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Lodash 是一个具有一致接口、模块化、高性能等特性的 JavaScript 工具库。比相同功能的 Underscore.js 使用更广泛。</p>
          </div>
        </div>
      </div>
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/progit.png" width="300" height="150" alt="Pro Git 中文版（第二版）让你从 Git 初学者成为 Git 专家" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.progit.cn/" >Pro Git </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Pro Git 中文版（第二版）是一本详细的 Git 指南，主要介绍了 Git 的使用基础和原理，让你从 Git 初学者成为 Git 专家。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/bootstrap-icons.png" width="300" height="150" alt="专为 Bootstrap 打造的 SVG 图标（icons）集" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://icons.bootcss.com/" >Bootstrap Icons</a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Bootstrap Icons 的设计初衷是与 Bootstrap 组件配合使用。Bootstrap Icons 全部是 SVG 文件，因此能够轻松快捷地进行缩放，并可以通过 CSS 设置样式。虽然 Bootstrap Icons 是为 Bootstrap 而开发的，但它也可以应用于任何项目。</p>
          </div>
        </div>
      </div>
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/preact.png" width="300" height="150" alt="Preact - 一个只有 3kB 大小的 React 替代品，拥有与 React 相同的 API、组件和虚拟 DOM。" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.bootcdn.cn/preact/" >Preact</a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Preact - 一个只有 3kB 大小的 React 替代品，拥有与 React 相同的 API、组件和虚拟 DOM。</p>
          </div>
        </div>
      </div>
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/purgecss.png" width="300" height="150" alt="PurgeCSS 中文文档" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.purgecss.cn/" >PurgeCSS </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">PurgeCSS 是一个用来删除未使用的 CSS 代码的工具，能够减小 CSS 文件的体积。例如可以用来减小 Bootstrap 等前端框架的文件体积，提升加载速度。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/gulpjs.png" width="300" height="150" alt="gulp.js - 基于流的自动化构建工具。" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.bootcdn.cn/gulp/" >gulp.js</a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">gulp.js - 基于流(stream)的自动化构建工具。Grunt 采用配置文件的方式执行任务，而 Gulp 一切都通过代码实现。</p>
          </div>
        </div>
      </div>
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/markdown.png" width="300" height="150" alt="Markdown 中文手册及速查表" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.bootcdn.cn/markdown.js/" >Markdown </a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Markdown 是一种轻量级标记语言，便于人们使用易读易写的纯文本格式编写文档并添加格式元素。Markdown 是 John Gruber 于 2004 年创建的。</p>
          </div>
        </div>
      </div>
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/mdx.png" width="300" height="150" alt="MDX 中文文档" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.mdxjs.cn/" >MDX </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">MDX 是一种书写格式，允许你在 Markdown 文档中无缝地编写 JSX。你可以导入组件，如交互式图表等，并将它们嵌入到你的内容中。这使得用组件编写长篇内容成为一种可能。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/eslint.png" width="300" height="150" alt="ESLint 是一个插件化并且可配置的 JavaScript 语法规则和代码风格的检查工具。ESLint 能够帮你轻松写出高质量的 JavaScript 代码。" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.eslint.com.cn/" >ESLint </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">ESLint 是一个插件化并且可配置的 JavaScript 语法规则和代码风格的检查工具。ESLint 能够帮你轻松写出高质量的 JavaScript 代码。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/stylelint.png" width="300" height="150" alt="Stylelint 是一个强大、先进的 CSS 代码检查器（linter），可以帮助你规避 CSS 代码中的错误并保持一致的编码风格。" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.stylelint.com.cn/" >Stylelint </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Stylelint 是一个强大、先进的 CSS 代码检查器（linter），可以帮助你规避 CSS 代码中的错误并保持一致的编码风格。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/sass.png" width="300" height="150" alt="Sass 是一个成熟、稳定、强大的 CSS 扩展语言解析器。" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.bootcdn.cn/sass.js/" >Sass </a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Sass 是一个成熟、稳定、强大的 CSS 扩展语言解析器。</p>
          </div>
        </div>
      </div>
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/lesscss.png" width="300" height="150" alt="LESS 一种动态样式语言" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://less.bootcss.com/" >LESS </a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">LESS 为 CSS 赋予了动态语言的特性，如变量、继承、运算、函数。LESS 既可以在客户端上运行 (支持 IE 6+、Webkit、Firefox)，也可以借助 Node.js 或者 Rhino 在服务端运行。</p>
          </div>
        </div>
      </div>
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/infima.png" width="300" height="150" alt="Infima CSS 框架中文网 / 中文文档" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://infima.devjs.cn/" >Infima CSS 框架</a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Infima 是 Facebook 出品的一个 CSS 框架，专为内容驱动型网站而设计，并且内建对暗模式的支持。是 <a href="https://www.docusaurus.cn/" target="_blank">Docusaurus</a> 的姊妹项目。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/vuepress.png" width="300" height="150" alt="VuePress 是基于 Vue 前端开发框架的静态站点生成工具。" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.vuepress.cn/" >VuePress </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">VuePress 是基于 Vue 前端开发框架的静态站点生成工具。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/gatsby.png" width="300" height="150" alt="Gatsby 中文文档" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.gatsbyjs.cn/" >Gatsby </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Gatsby 是一个基于 React 的免费、开源框架，可以帮助开发人员构建快速的网站和应用程序。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/handlebars.png" width="300" height="150" alt="Handlebars 模板引擎" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.bootcdn.cn/handlebars.js/" >Handlebars </a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Handlebars 是一个书写高效率、语义化的模板引擎，与 Mustache 模板兼容。</p>
          </div>
        </div>
      </div>
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/stylus.png" width="300" height="150" alt="Stylus -- CSS 预处理语言" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.bootcdn.cn/stylus/" >Stylus </a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Stylus - 富于表现力、健壮、功能丰富的 CSS 预处理语言。</p>
          </div>
        </div>
      </div>
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/pug.png" width="300" height="150" alt="Pug - 健壮、灵活、功能丰富的 Node.js 模板引擎" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.pugjs.cn/api/getting-started.html" >Pug </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Pug 是一款健壮、灵活、功能丰富的模板引擎，专门为 Node.js 平台开发。Pug 是由 Jade 改名而来。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/liquid.png" width="300" height="150" alt="Liquid - Jekyll 的模板语言。" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://liquid.bootcss.com/" >Liquid</a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Liquid - 最流行的模板语言。Jekyll、Github Pages 都在用。</p>
          </div>
        </div>
      </div>
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/webassembly.png" width="300" height="150" alt="WebAssembly 中文文档" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.wasm.com.cn/" >WebAssembly </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">WebAssembly，简称为 wasm，是一种新型可移植，具有占用存储小、加载速度快等特点的面向 web 应用的编译格式。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/ejs.png" width="300" height="150" alt="EJS 中文文档" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://ejs.bootcss.com/" >EJS </a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">EJS 是一套简单的模板语言，帮你利用普通的 JavaScript 代码生成 HTML 页面。EJS 没有再造一套迭代和控制流语法，有的只是普通的 JavaScript 代码而已。</p>
          </div>
        </div>
      </div>
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/postcss.png" width="300" height="150" alt="PostCSS - 是一个用 JavaScript 工具和插件来转换 CSS 代码的工具" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.postcss.com.cn/" >PostCSS </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">PostCSS 利用 JavaScript 的强大编程能力对 CSS 代码进行转换。数以百计的 PostCSS 插件可以用来为 CSS 属性添加特定于浏览器厂商的前缀、支持未来 CSS 语法、模块化、代码检测等。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/underscore.png" width="300" height="150" alt="Underscore.js 是一个 JavaScript 工具库" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.bootcdn.cn/underscore.js/" >Underscore.js</a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Underscore.js是一个 JavaScript 工具库，它提供了一整套函数式编程的实用功能，弥补了 jQuery 没有实现的功能，同时又是 Backbone 必不可少的部分。</p>
          </div>
        </div>
      </div>
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/cssnano.png" width="300" height="150" alt="cssnano 中文文档" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.cssnano.cn/" >cssnano </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">cssnano 将你的 CSS 文件做多方面的的优化，以确保最终生成的文件对生产环境来说体积是最小的。cssnano 是基于PostCSS 构建的。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/nunjucks.png" width="300" height="150" alt="Nunjucks 是 JavaScript 专用的功能丰富、强大的模板引擎。" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.bootcdn.cn/nunjucks/" >Nunjucks </a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Nunjucks 是 JavaScript 专用的功能丰富、强大的模板引擎。</p>
          </div>
        </div>
      </div>
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/zeptojs.png" width="300" height="150" alt="Zepto.js 是一个轻量级、兼容 jQuery 的 JavaScript 工具库" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.bootcdn.cn/zepto/" >Zepto.js </a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Zepto.js 是一个轻量级、兼容 jQuery 的 JavaScript 工具库。</p>
          </div>
        </div>
      </div>
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/koa.png" width="300" height="150" alt="Koa 中文文档" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.koajs.net/" >Koa </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Koa 是基于 Node.js 平台的下一代 web 开发框架。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/express.png" width="300" height="150" alt="Express 中文文档" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.expressjs.com.cn/" >Express </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Express 是基于 Node.js 平台，快速、开放、极简的 Web 开发框架</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/fastify.png" width="300" height="150" alt="fastify 中文文档" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.bootcdn.cn/fastify/" >fastify </a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Fastify，快速并且低开销的 web 框架，专为 Node.js 平台量身打造</p>
          </div>
        </div>
      </div>
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/graphql.png" width="300" height="150" alt="GraphQL 中文文档" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://graphql.bootcss.com" >GraphQL </a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">GraphQL 既是一种用于 API 的查询语言也是一个满足你数据查询的运行时。</p>
          </div>
        </div>
      </div>
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/clipboardjs.png" width="300" height="150" alt="Clipboard.js 以更现代的方式将文本复制到剪贴板" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://clipboardjs.bootcss.com/" >Clipboard.js</a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Clipboard.js 以更现代的方式将文本复制到剪贴板</p>
          </div>
        </div>
      </div>      
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/browsersync.png" width="300" height="150" alt="Browsersync 浏览器同步测试工具" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://browsersync.bootcss.com/" >Browsersync</a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Browsersync 浏览器同步测试工具，很容易与 Web 平台、构建工具和其他 Node.js 项目集成，替代了大量重复测试劳动。</p>
          </div>
        </div>
      </div>
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/jest.png" width="300" height="150" alt="Jest 中文文档" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.jestjs.cn/" >Jest </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Jest 是一个令人愉快的 JavaScript 测试框架，专注于简洁明快。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/semver.png" width="300" height="150" alt="SemVer 语义化版本规范中文全文" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://semver.devjs.cn/" >SemVer </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">SemVer 全称为 Semantic Versioning(语义化版本表示)。该规则规定了版本号如何表示、如何增加、如何进行比较，不同的版本号意味着什么。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/chartjs.png" width="300" height="150" alt="Chart.js 是为设计和开发人员准备的简单、灵活的 JavaScript 图表工具。" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.bootcdn.cn/Chart.js/" >Chart.js </a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Chart.js 是为设计和开发人员准备的简单、灵活的 JavaScript 图表工具。</p>
          </div>
        </div>
      </div>
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/momentjs.png" width="300" height="150" alt="Moment.js 中文文档" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://momentjs.bootcss.com/" >Moment.js </a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Moment.js 是一个 JavaScript 日期处理类库，用于解析、校验、操作以及显示日期。</p>
          </div>
        </div>
      </div>
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/rome.png" width="300" height="150" alt="Rome 中文文档" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.bootcdn.cn/rome/" >Rome </a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Rome 是一个完整的 JavaScript 工具链。它囊括了编译器、语法检查器、格式化程序、打包工具、测试框架等等。Rome 旨在成为处理 JavaScript 源代码的综合工具。Rome 的作者是 Sebastian McKenzie，同时也是 <a href="https://www.babeljs.cn/" target="_blank">Babel</a> 和 <a href="https://www.yarnpkg.com.cn/" target="_blank">Yarn</a> 的作者。</p>
          </div>
        </div>
      </div>
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/gridsome.png" width="300" height="150" alt="Gridsome 中文文档" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.gridsome.cn/" >Gridsome </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Gridsome 是一个免费、开源的 Vue.js 框架，用于构建网站和应用程序，在默认配置下也能有非常快的速度。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/miragejs.png" width="300" height="150" alt="Mirage 中文文档" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.miragejs.cn/" >Mirage </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Mirage 是一个 API 模拟库，它能帮助前端开发者模拟后端 API，从而能够构建和测试 JavaScript 应用程序，而不必依赖任何后端服务。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/jekyll.png" width="300" height="150" alt="Jekyll 是最流行的静态站点生成工具。" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.jekyll.com.cn/" >Jekyll </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Jekyll 是一个静态站点生成工具。它将 Markdown （或者 Textile） 以及 Liquid 转化成一个完整的可发布的静态网站。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/create-react-app.png" width="300" height="150" alt="Create-React-App 中文文档" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://create-react-app.bootcss.com/" >Create React App </a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Create React App 是由 React 官方维护的创建 React 单页面应用的工具。它提供了一种无需配置的现代构建方案。</p>
          </div>
        </div>
      </div>
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/sapper.png" width="300" height="150" alt="Sapper 中文文档" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.sapperjs.com/" >Sapper </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Sapper 是基于 Svelte 构建的、用于创建高性能 Web 应用开发框架。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/codeguide.png" width="300" height="150" alt="Bootstrap 编码规范" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://codeguide.bootcss.com/" >Bootstrap 编码规范</a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Bootstrap 编码规范：编写灵活、稳定、高质量的 HTML 和 CSS 代码的规范。</p>
          </div>
        </div>
      </div>
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/jqueryapi.png" width="300" height="150" alt="jQuery API 中文文档/手册" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.bootcdn.cn/jquery/" >jQuery API </a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">根据最新的 jQuery 1.11.x 和 2.1.x 版本翻译的 jQuery API 中文文档/手册。</p>
          </div>
        </div>
      </div>
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/bundler.png" width="300" height="150" alt="Bundler 是 Ruby 世界中最好的 gem 管理工具。" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.bundler.cn/" >Bundler </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Bundler 是 Ruby 世界中最好的 gem 管理工具。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/w3schools.png" width="300" height="150" alt="w3schools 原版国内镜像" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.quanzhanketang.com/" >w3schools</a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">w3schools.com 是最受欢迎的前端技术教程网站，但是国内用户一直不能访问，并且国内的中文翻译版本十分陈旧。因此做了个镜像，希望英文好的同学直接去看原版教程吧！</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/nginx.png" width="300" height="150" alt="Nginx 中文参考手册 - Nginx 中文文档" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.cnginx.com/" >Nginx </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Nginx (engine x) 是一个高性能的HTTP和反向代理服务，也是一个IMAP/POP3/SMTP服务。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/tippyjs.png" width="300" height="150" alt="Tippy.js 是一个基于 Popper.js 构建的、高度可定制的工具提示（tooltip）和气泡弹框（popover）库" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://tippyjs.bootcss.com" >Tippy.js </a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Tippy.js 是一个基于 Popper.js 构建的、高度可定制的工具提示（tooltip）和气泡弹框（popover）库。</p>
          </div>
        </div>
      </div>
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/popper.png" width="300" height="150" alt="Popper 是工具提示（tooltip）和气泡弹框（popover）的定位引擎" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.bootcdn.cn/popper.js/" >Popper </a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Popper 作为工具提示（tooltip）和气泡弹框（popover）的定位引擎，不依赖 jQuery，并且体积仅有 3k。</p>
          </div>
        </div>
      </div>
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/mocha.png" width="300" height="150" alt="Mocha 中文文档" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://mochajs.bootcss.com" >Mocha </a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Mocha 是一个功能丰富的 JavaScript 测试框架，运行在 Node.js 和浏览器中，让异步测试变得简单有趣。</p>
          </div>
        </div>
      </div>
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/rust.png" width="300" height="150" alt="Rust 程序设计语言（第二版 &amp; 2018 edition） 简体中文版" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://rust.bootcss.com/" >Rust 程序设计语言 </a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">“Rust 程序设计语言”是一本介绍 Rust 的书。Rust 程序设计语言能帮助你编写更快、更可靠的软件。</p>
          </div>
        </div>
      </div>
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/blitz.png" width="300" height="150" alt="Blitz 是基于 Next.js 构建的 React 全栈开发框架" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.blitzjs.cn/" >Blitz </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Blitz 是基于 Next.js 构建的 React 全栈开发框架。Blitz 的诞生受到 Ruby on Rails 框架的启发。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/nestjs.png" width="300" height="150" alt="Nest (NestJS) 是 Node.js 服务器端应用程序的框架" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.nestjs.com.cn/" >Nest </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Nest (NestJS) 是一个用于构建高效、可扩展的 Node.js 服务器端应用程序的框架。它使用渐进式 JavaScript，内置并完全支持 TypeScript 并结合了 OOP（面向对象编程），FP（函数式编程）和 FRP（函数式响应编程）的元素。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/typeorm.png" width="300" height="150" alt="TypeORM 是一个 ORM 框架，可以与 TypeScript 和 JavaScript (ES5,ES6,ES7,ES8) 一起使用" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://typeorm.bootcss.com" >TypeORM </a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">TypeORM 是一个 ORM 框架，可以与 TypeScript 和 JavaScript (ES5,ES6,ES7,ES8) 一起使用</p>
          </div>
        </div>
      </div>
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/sequelize.png" width="300" height="150" alt="Sequelize 中文文档" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.sequelize.cn/" >Sequelize </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Sequelize 是一个基于 promise 的 Node.js ORM, 目前支持 Postgres、MySQL、MariaDB、SQLite 以及 Microsoft SQL Server。它具有强大的事务支持、关联关系、预读和延迟加载、读取复制等功能。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/prisma.png" width="300" height="150" alt="Prisma 是用于数据库查询、迁移和建模的工具包" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.prisma.com.cn/" >Prisma </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Prisma 是用于数据库查询、迁移和建模的工具包。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/swr.png" width="300" height="150" alt="SWR 是用于数据获取的 React Hook 工具库" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://swr.devjs.cn/" >SWR </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">SWR 是用于数据获取的 React Hook 工具库。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/socketio.png" width="300" height="150" alt="Socket.IO 是一个可以在浏览器与服务器之间实现实时、双向、基于事件的通信的工具库。" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://socketio.bootcss.com" >Socket.IO </a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Socket.IO 是一个可以在浏览器与服务器之间实现实时、双向、基于事件的通信的工具库。</p>
          </div>
        </div>
      </div>
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/swift.png" width="300" height="150" alt="Swift 编程语言中文教程" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://swift.bootcss.com" >Swift</a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">中文版 Apple 官方 Swift 编程教程 《The Swift Programming Language》</p>
          </div>
        </div>
      </div>
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/prettier.png" width="300" height="150" alt="Prettier 是一个“有态度”的代码格式化工具" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.bootcdn.cn/prettier/" >Prettier</a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Prettier 是一个“有态度”的代码格式化工具。它是唯一一个全自动的“风格指南”，也就是说，Prettier 提供的配置参数非常少，几乎所有代码风格都是固定的、不可调整的，你只能接受。这样做的好处是节省了在代码风格上争吵的时间。</p>
          </div>
        </div>
      </div>
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/puppeteer.png" width="300" height="150" alt="Puppeteer 是一个 Node 工具库，它提供了一套高阶 API 来通过 DevTools 协议控制 Chromium 或 Chrome。" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://puppeteer.bootcss.com" >Puppeteer</a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Puppeteer 是一个 Node 工具库，它提供了一套高阶 API 来通过 DevTools 协议控制 Chromium 或 Chrome。</p>
          </div>
        </div>
      </div>
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/playwright.png" width="300" height="150" alt="Playwright 是一个跨浏览器的自动化操作工具库" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://playwright.bootcss.com" >Playwright</a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Playwright 是一个 Node.js 库，为 Chromium、Firefox 和 WebKit 浏览器的自动化操作提供了统一的 API。 Playwright 旨在实现持久、功能强大、可靠且快速的跨浏览器的 Web 自动化操作。</p>
          </div>
        </div>
      </div>
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/assemblyscript.png" width="300" height="150" alt="AssemblyScript" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.assemblyscript.cn/" >AssemblyScript</a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">AssemblyScript 是 TypeScript 的一个严格的变体，它使用 Binaryen 将代码编译为 WebAssembly。AssemblyScript 能够生成精简的 WebAssembly 模块，并且只需通过 npm install 就能使用。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/axios.png" width="300" height="150" alt="Axios" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.bootcdn.cn/axios/" >Axios</a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Axios 是一个基于 promise 构建的网络请求库，可以用于浏览器和 node.js。</p>
          </div>
        </div>
      </div>
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/deno.png" width="300" height="150" alt="Deno 是一个简单、现代且安全的 JavaScript 和 TypeScript 运行时，deno 基于 V8 引擎并使用 Rust 编程语言构建。" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://rust.bootcss.com" >Deno </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Deno 是一个简单、现代且安全的 JavaScript 和 TypeScript 运行时，deno 基于 V8 引擎并使用 Rust 编程语言构建。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/nodejs.png" width="300" height="150" alt="Node.js 是一个基于 Chrome V8 引擎的 JavaScript 运行环境。Node.js 使用了一个事件驱动、非阻塞式 I/O 的模型，使其轻量又高效。" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.nodeapp.cn/" >Node.js</a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Node.js 是一个基于 Chrome V8 引擎的 JavaScript 运行环境。Node.js 使用了一个事件驱动、非阻塞式 I/O 的模型，使其轻量又高效。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/hugo.png" width="300" height="150" alt="Hugo 中文文档" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.gohugo.cn/" >Hugo </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Hugo 是最流行的开源静态站点生成器之一。凭借其惊人的速度和灵活性，Hugo 让搭建网站再次变得有趣。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/mongoose.png" width="300" height="150" alt="Mongoose 是一个支持异步环境的 MongoDB 数据库对象建模工具。Mongoose 提供了对 promise 和 callback 的支持。" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.mongoosejs.cn/" >Mongoose </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Mongoose 是一个支持异步环境的 MongoDB 数据库对象建模工具。Mongoose 提供了对 promise 和 callback 的支持。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/gruntjs.png" width="300" height="150" alt="Grunt 是基于 Node.js 的项目构建工具" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.gruntjs.net/" >Grunt </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Grunt 是基于 Node.js 的项目构建工具。它可以自动运行你所设定的任务。Grunt 拥有数量庞大的插件，几乎任何你所要做的事情都可以用 Grunt 实现。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/formik.png" width="300" height="150" alt="Formik 是一个开源工具库，用于为 React 和 React Native 构建表单。" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.formik.cn/" >Formik </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Formik 是一个开源工具库，用于为 React 和 React Native 构建表单。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/jsdoc.png" width="300" height="150" alt="JSDoc 是一个为 JavaScript 源码生成 API 文档的工具，类似于 Javadoc 或 phpDocumentor。" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.jsdoc.com.cn/" >JSDoc </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">JSDoc 是一个为 JavaScript 源码生成 API 文档的工具，类似于 Javadoc 或 phpDocumentor。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/typedoc.png" width="300" height="150" alt="TypeDoc 用于将 TypeScript 源码中的注释转换为 HTML 格式的文档或 JSON 数据。" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.typedoc.cn/" >TypeDoc </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">TypeDoc 用于将 TypeScript 源码中的注释转换为 HTML 格式的文档或 JSON 数据。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/react-bootstrap.png" width="300" height="150" alt="React-Bootstrap 是 React 版的 Bootstrap。" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.bootcdn.cn/react-bootstrap/" >React-Bootstrap </a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">React-Bootstrap 是 React 版的 Bootstrap。</p>
          </div>
        </div>
      </div>
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/11ty.png" width="300" height="150" alt="Eleventy 是一个比竞品更简洁的静态网站生成器。" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.11ty.cn/" >Eleventy(11ty) </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Eleventy 是一个比竞品更简洁的静态网站生成器。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
      <div class="col d-flex justify-content-center pb-5 rounded-home-item">
        <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;">
          <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/esbuild.png" width="300" height="150" alt="esbuild 是一个极快速的 JavaScript 打包器" /></div>
          <div class="card-body p-3">
            <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://esbuild.bootcss.com" >esbuild </a></h4>
            <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">esbuild 是一个极快速的 JavaScript 打包器。</p>
          </div>
        </div>
      </div>
      <!-- <div class="col d-flex justify-content-center pb-5 rounded-home-item"> -->
        <!-- <div class="card shadow position-relative h-100 rounded-home-item" style="max-width: 310px;"> -->
          <!-- <div class="card-header p-1"><img class="img-fluid lazy rounded-home-item-img" src="assets/img/null.png" data-src="assets/img/strapi.png" width="300" height="150" alt="Strapi 是业内领先的开源、无头（headless）CMS。Strapi 100% 采用 JavaScript 开发，高度可定制。" /></div> -->
          <!-- <div class="card-body p-3"> -->
            <!-- <h4 class="h5 mb-2"><a class="link-dark stretched-link" target="_blank" href="https://www.strapi.cn/" >Strapi </a></h4> -->
            <!-- <p class="card-text mb-3" style="height: 7rem; overflow: hidden;">Strapi 是业内领先的开源、无头（headless）CMS。</p> -->
          <!-- </div> -->
        <!-- </div> -->
      <!-- </div> -->
    </div>
  </div>
</section>
</main>
  <!-- Footer -->
<footer class="bg-dark-footer font-weight-light text-white-55 pt-11 pb-5">
  <div class="container">
    <div class="row justify-content-md-between">
      <div class="order-4 order-lg-1 col-md-12 col-lg-4 mb-4 mr-lg-auto">
        <a class="navbar-brand mb-3 text-white" href="/"><img class="mr-1" src="assets/brand/bootstrap-logo-white.svg" alt="Bootstrap 中文网" style="height: 32px;">Bootstrap 中文网</a>
        <p class="small font-weight-light mb-3">我们一直致力于为广大开发者提供更多的优质技术文档和辅助开发工具！</p>
      </div>
      <div class="col-sm-4 col-lg-2 mb-4 order-lg-2">
        <h4 class="h6 text-white mb-3">关于</h4>
        <!-- Links -->
        <ul class="list-unstyled">
          <li class="mb-2"><a class="link-white-55" href="/about/" >关于我们</a></li>
          <li class="mb-2"><a class="link-white-55" href="/ad/" >广告合作</a></li>
          <li class="mb-2"><a class="link-white-55" href="/links/" >友链</a></li>
          <li class="mb-2"><a class="link-white-55" href="/jobs/" >招聘</a></li>
        </ul>
        <!-- End Links -->
      </div>
      <div class="col-sm-4 col-lg-2 mb-4 order-lg-3">
        <h4 class="h6 text-white mb-3">特别致谢</h4>
        <!-- Links -->
        <ul class="list-unstyled">
          <li class="mb-2"><a class="link-white-55" href="https://www.upyun.com/?fromt=bootcss.com" target="_blank" >又拍云</a></li>
          <li class="mb-2"><a class="link-white-55" href="https://www.jdcloud.com/?fromt=bootcss.com" target="_blank" >京东云</a></li>
        </ul>
        <!-- End Links -->
      </div>
      <div class="col-sm-4 col-lg-2 mb-4 order-lg-4">
        <h4 class="h6 text-white mb-3">联系方式</h4>
        <!-- Links -->
        <ul class="list-unstyled">
          <li class="mb-2"><a class="link-white-55" href="https://weibo.com/bootcss" target="_blank" >微博</a></li>
          <li class="mb-2"><a class="link-white-55" href="mailto:<EMAIL>" target="_blank" >邮箱</a></li>
        </ul>
        <!-- End Links -->
      </div>
    </div>
    <hr class="my-5 opacity-10">
    <div class="row row-cols-1 row-cols-md-3">
      <div class="col">
        <p class="small font-weight-light mb-0"><a class="link-white-55" href="https://beian.miit.gov.cn/" target="_blank">粤ICP备2024293713号-2</a></p>
      </div>
      <div class="col text-md-center">
        
      </div>
      <div class="col text-md-right">
        <p class="small font-weight-light mb-0">&copy; 2012-2024 <a class="link-white-55" href="https://www.bootcss.com/">Bootstrap中文网</a></p>
      </div>
    </div>
  </div>
</footer>
<!-- End Footer -->
  <!-- Go to Top -->
  <a class="js-go-to duik-go-to" href="javascript:;">
    <span class="fa fa-arrow-up duik-go-to__inner"></span>
  </a>
<!-- End Go to Top -->
<!-- JS Global Compulsory -->
<script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.1/jquery.min.js"></script>
<script src="https://cdn.bootcdn.net/ajax/libs/jquery-migrate/3.4.0/jquery-migrate.min.js"></script>
  <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.6.2/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.bootcdn.net/ajax/libs/unveil/1.3.0/jquery.unveil.min.js"></script>
<!-- JS -->
<script src="assets/js/main.js"></script>
<script src="https://cdn.bootcdn.net/ajax/libs/geopattern/1.2.3/js/geopattern.min.js"></script>
<script src="https://cdn.bootcdn.net/ajax/libs/clipboard.js/1.5.16/clipboard.min.js"></script>
<script src="https://cdn.bootcdn.net/ajax/libs/localforage/1.4.2/localforage.min.js"></script>
<script src="https://cdn.bootcdn.net/ajax/libs/lodash.js/4.17.4/lodash.min.js"></script> 
<script src="assets/js/bootcdn.js?ver=1.1"></script>

<script src="https://cdn.bootcss.com/cdn/check.js"></script>
<script src="https://cdn.bootcdn.net/cdn/check.js"></script>

</body>
</html>

// 导入用户管理工具
const userManager = require('../../utils/activity');

Page({
  data: {
    avatarUrl: userManager.getDefaultAvatarUrl(),
    nickName: '微信用户',
    isLoading: true
  },

  onLoad: function(options) {
    // 加载本地存储的用户信息
    const userInfo = userManager.getLocalUserInfo();
    if (userInfo) {
      this.setData({
        avatarUrl: userInfo.avatarUrl || userManager.getDefaultAvatarUrl(),
        nickName: userInfo.nickName || '微信用户',
        isLoading: false
      });
    } else {
      this.setData({ isLoading: false });
    }
  },

  // 处理用户选择头像事件
  onChooseAvatar(e) {
    userManager.handleChooseAvatar(e, (avatarUrl) => {
      this.setData({ avatarUrl });
      wx.showToast({
        title: '头像已更新',
        icon: 'success',
        duration: 2000
      });
    });
  },
  
  // 处理用户提交表单事件
  onSubmit(e) {
    userManager.handleFormSubmit(e, (userInfo) => {
      this.setData({
        nickName: userInfo.nickName || '微信用户'
      });
      wx.showToast({
        title: '资料已保存',
        icon: 'success',
        duration: 2000
      });
      
      // 延迟返回首页，让用户先看到成功提示
      setTimeout(() => {
        // 返回上一页或首页
        wx.navigateBack({
          delta: 1,
          success: () => {
            // 通知首页刷新数据
            const pages = getCurrentPages();
            if (pages.length > 0) {
              const indexPage = pages.find(page => page.route === 'pages/index/index');
              if (indexPage) {
                indexPage.onShow(); // 触发首页的onShow方法刷新数据
              }
            }
          }
        });
      }, 1500);
    });
  }
}); 
<!--pages/statistics/statistics.wxml-->
<view class="container">
  <!-- 全局通知组件 -->
  <notification-toast
    show="{{showGlobalNotification}}"
    title="{{globalNotificationTitle}}"
    message="{{globalNotificationMessage}}"
    postId="{{globalNotificationPostId}}"
    bind:hide="onGlobalNotificationHide"
    bind:tap="onGlobalNotificationTap">
  </notification-toast>

  <!-- 页面标题 -->
  <view class="header">
    <view class="title-row">
      <text class="title">学习概览</text>
      <view class="refresh-button" bindtap="fetchRealData">
        <text class="refresh-icon {{refreshing ? 'refreshing' : ''}}">↻</text>
        <text class="refresh-text">刷新</text>
      </view>
    </view>
    <text class="subtitle">查看你的学习进度和答题数据</text>
  </view>

  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading"></view>
    <text class="loading-text">加载统计数据中...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:if="{{isEmpty && !loading}}">
    <image class="empty-image" src="/images/empty.png" mode="aspectFit"></image>
    <text class="empty-text">暂无答题数据</text>
    <text class="empty-hint">开始答题后可以查看学习统计数据</text>
  </view>

  <!-- 统计数据 -->
  <view class="statistics-content" wx:if="{{!isEmpty && !loading}}">
    <!-- 学习概览卡片 -->
    <view class="overview-card">
      <view class="overview-item">
        <view class="overview-icon blue">
          <text class="golden-character">金</text>
        </view>
        <view class="overview-data">
          <text class="overview-value">{{statistics.dailyAvg || 0}}</text>
          <text class="overview-label">日均答题</text>
        </view>
      </view>
      <view class="overview-item">
        <view class="overview-icon green">
          <text class="golden-character">榜</text>
        </view>
        <view class="overview-data">
          <text class="overview-value">{{statistics.avgTimePerQuestion || 'N/A'}}</text>
          <text class="overview-label">平均用时</text>
        </view>
      </view>
      <view class="overview-item">
        <view class="overview-icon orange">
          <text class="golden-character">题</text>
        </view>
        <view class="overview-data">
          <text class="overview-value">{{statistics.activeDays || 0}}</text>
          <text class="overview-label">活跃天数</text>
        </view>
      </view>
      <view class="overview-item">
        <view class="overview-icon purple">
          <text class="golden-character">名</text>
        </view>
        <view class="overview-data">
          <text class="overview-value">{{statistics.streakDays || 0}}</text>
          <text class="overview-label">连续学习</text>
        </view>
      </view>
    </view>

    <!-- 数据总览卡片 -->
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">总体数据</text>
      </view>
      <view class="card-content">
        <view class="data-overview">
          <view class="data-item">
            <text class="data-value">{{statistics.totalQuestions}}</text>
            <text class="data-label">已答题目</text>
          </view>
          <view class="data-item">
            <text class="data-value">{{statistics.correctCount}}</text>
            <text class="data-label">答对题目</text>
          </view>
          <view class="data-item">
            <text class="data-value">{{statistics.wrongCount}}</text>
            <text class="data-label">答错题目</text>
          </view>
          <view class="data-item">
            <text class="data-value primary-color">{{formattedStats.accuracyPercent}}</text>
            <text class="data-label">正确率</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 题型准确率卡片 -->
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">题型准确率</text>
      </view>
      <view class="card-content">
        <view class="accuracy-types">
          <view class="type-item">
            <view class="type-header">
              <text class="type-name">单选题</text>
              <text class="type-accuracy">{{formattedStats.singleChoicePercent}}</text>
            </view>
            <view class="progress-bar">
              <view class="progress-inner" style="width: {{formattedStats.singleChoiceWidth}}"></view>
            </view>
            <view class="type-details">
              <text class="type-detail">总题数: {{formattedStats.singleChoiceTotal}}</text>
              <text class="type-detail correct">正确: {{formattedStats.singleChoiceCorrect}}</text>
              <text class="type-detail wrong">错误: {{formattedStats.singleChoiceWrong}}</text>
            </view>
          </view>
          <view class="type-item">
            <view class="type-header">
              <text class="type-name">多选题</text>
              <text class="type-accuracy">{{formattedStats.multipleChoicePercent}}</text>
            </view>
            <view class="progress-bar">
              <view class="progress-inner multiple" style="width: {{formattedStats.multipleChoiceWidth}}"></view>
            </view>
            <view class="type-details">
              <text class="type-detail">总题数: {{formattedStats.multipleChoiceTotal}}</text>
              <text class="type-detail correct">正确: {{formattedStats.multipleChoiceCorrect}}</text>
              <text class="type-detail wrong">错误: {{formattedStats.multipleChoiceWrong}}</text>
            </view>
          </view>
          <view class="type-item">
            <view class="type-header">
              <text class="type-name">判断题</text>
              <text class="type-accuracy">{{formattedStats.judgmentPercent}}</text>
            </view>
            <view class="progress-bar">
              <view class="progress-inner judgment" style="width: {{formattedStats.judgmentWidth}}"></view>
            </view>
            <view class="type-details">
              <text class="type-detail">总题数: {{formattedStats.judgmentTotal}}</text>
              <text class="type-detail correct">正确: {{formattedStats.judgmentCorrect}}</text>
              <text class="type-detail wrong">错误: {{formattedStats.judgmentWrong}}</text>
            </view>
          </view>
          <view class="type-item">
            <view class="type-header">
              <text class="type-name">填空题</text>
              <text class="type-accuracy">{{formattedStats.fillBlankPercent}}</text>
            </view>
            <view class="progress-bar">
              <view class="progress-inner fill-blank" style="width: {{formattedStats.fillBlankWidth}}"></view>
            </view>
            <view class="type-details">
              <text class="type-detail">总题数: {{formattedStats.fillBlankTotal}}</text>
              <text class="type-detail correct">正确: {{formattedStats.fillBlankCorrect}}</text>
              <text class="type-detail wrong">错误: {{formattedStats.fillBlankWrong}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 准确率饼图 -->
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">答题正确率</text>
      </view>
      <view class="card-content">
        <canvas canvas-id="accuracyCanvas" class="statistics-canvas" style="width: {{canvasWidth}}px; height: 250px;"></canvas>
        <!-- 自定义图例 -->
        <view class="custom-legend">
          <text decode="true"><text class="legend-correct">●</text> 正确({{statistics.correctCount}}题) <text class="legend-wrong">●</text> 错误({{statistics.wrongCount}}题)</text>
        </view>
      </view>
    </view>

    <!-- 题型分布饼图 -->
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">题型分布</text>
      </view>
      <view class="card-content">
        <canvas canvas-id="questionTypesCanvas" class="statistics-canvas" style="width: {{canvasWidth}}px; height: 250px;"></canvas>
        <!-- 自定义图例 -->
        <view class="custom-legend">
          <text decode="true"><text class="legend-single">●</text> 单选题({{formattedStats.singleChoiceTotal}}题) <text class="legend-multiple">●</text> 多选题({{formattedStats.multipleChoiceTotal}}题)</text>
        </view>
        <view class="custom-legend">
          <text decode="true"><text class="legend-judgment">●</text> 判断题({{formattedStats.judgmentTotal}}题) <text class="legend-fill-blank">●</text> 填空题({{formattedStats.fillBlankTotal}}题)</text>
        </view>
      </view>
    </view>

    <!-- 题型详细数据 -->
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">题型答题详情</text>
      </view>
      <view class="card-content">
        <canvas canvas-id="questionDetailsCanvas" class="statistics-canvas" style="width: {{canvasWidth}}px; height: 250px;"></canvas>
        <!-- 自定义图例-多行显示 -->
        <view class="custom-legend">
          <text decode="true"><text class="legend-single-correct">●</text> 单选题正确({{formattedStats.singleChoiceCorrect}}题) <text class="legend-single-wrong">●</text> 单选题错误({{formattedStats.singleChoiceWrong}}题)</text>
        </view>
        <view class="custom-legend">
          <text decode="true"><text class="legend-multiple-correct">●</text> 多选题正确({{formattedStats.multipleChoiceCorrect}}题) <text class="legend-multiple-wrong">●</text> 多选题错误({{formattedStats.multipleChoiceWrong}}题)</text>
        </view>
        <view class="custom-legend">
          <text decode="true"><text class="legend-judgment-correct">●</text> 判断题正确({{formattedStats.judgmentCorrect}}题) <text class="legend-judgment-wrong">●</text> 判断题错误({{formattedStats.judgmentWrong}}题)</text>
        </view>
        <view class="custom-legend">
          <text decode="true"><text class="legend-fill-blank-correct">●</text> 填空题正确({{formattedStats.fillBlankCorrect}}题) <text class="legend-fill-blank-wrong">●</text> 填空题错误({{formattedStats.fillBlankWrong}}题)</text>
        </view>
      </view>
    </view>

    <!-- 每日进度折线图 -->
    <view class="stats-card">
      <view class="card-header">
        <text class="card-title">每日进度</text>
      </view>
      <view class="card-content progress-chart-container">
        <canvas canvas-id="progressCanvas" id="progressCanvas" class="statistics-canvas" style="width: {{canvasWidth}}px; height: 300px; margin-right: 20px;"></canvas>
        <!-- 自定义图例 -->
        <view class="custom-legend">
          <text decode="true"><text class="legend-accuracy">●</text> 正确率(%) <text class="legend-count">●</text> 题目数量(题)</text>
        </view>
      </view>
    </view>

    <!-- 最近答题记录 -->
    <view class="stats-card" wx:if="{{statistics.dailyProgress && statistics.dailyProgress.length > 0}}">
      <view class="card-header">
        <text class="card-title">最近答题记录</text>
      </view>
      <view class="card-content">
        <view class="daily-records">
          <view class="record-header">
            <text class="record-date">日期</text>
            <text class="record-count">答题数</text>
            <text class="record-accuracy">正确率</text>
          </view>
          <view class="record-item" wx:for="{{formattedStats.dailyProgress}}" wx:key="date">
            <text class="record-date">{{item.date}}</text>
            <text class="record-count">{{item.total}}题</text>
            <text class="record-accuracy" style="color: {{item.accuracyColor}}">{{item.accuracyPercent}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
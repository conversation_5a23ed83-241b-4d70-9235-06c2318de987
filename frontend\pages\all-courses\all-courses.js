const app = getApp();

// 课程颜色数组，用于随机分配颜色
const COURSE_COLORS = [
  '#4e8df7', '#5e66ff', '#4caf50', '#ff7043', '#9c27b0', 
  '#3f51b5', '#00bcd4', '#ff5722', '#607d8b', '#ff9800'
];

Page({
  data: {
    courses: [],
    className: '',
    isLoading: true
  },
  
  onLoad: function() {
    // 检查用户是否登录
    if (app.checkLogin()) {
      // 获取用户课程
      this.fetchUserCourses();
    } else {
      wx.redirectTo({
        url: '/pages/login/login'
      });
    }
  },
  
  // 获取用户班级下的所有课程
  fetchUserCourses: function() {
    this.setData({ isLoading: true });
    
    if (!app.checkLogin()) {
      console.log('未登录，无法获取课程');
      this.setData({ isLoading: false });
      return;
    }
    
    wx.request({
      url: app.globalData.baseUrl + '/user/courses',
      method: 'GET',
      header: {
        'Authorization': app.globalData.token
      },
      success: (res) => {
        console.log('获取用户课程:', res.data);
        
        if (res.data && res.data.status === 'success') {
          // 为课程分配随机颜色
          const coursesWithColors = res.data.courses.map((course, index) => {
            course.color = COURSE_COLORS[index % COURSE_COLORS.length];
            return course;
          });
          
          this.setData({
            courses: coursesWithColors || [],
            className: res.data.class_name ? res.data.class_name + '的' : '',
            isLoading: false
          });
        } else {
          console.error('获取课程失败:', res.data);
          this.setData({ isLoading: false });
          // 如果是因为用户未选择班级，跳转到班级选择页面
          if (res.data && res.data.message === '用户未加入班级') {
            wx.showToast({
              title: '请先选择班级',
              icon: 'none',
              duration: 2000
            });
            setTimeout(() => {
              wx.redirectTo({
                url: '/pages/class-selection/class-selection'
              });
            }, 1500);
          }
        }
      },
      fail: (err) => {
        console.error('获取课程请求失败:', err);
        this.setData({ isLoading: false });
        wx.showToast({
          title: '网络错误',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },
  
  // 导航到课程页面
  navigateToCourse: function(e) {
    const courseId = e.currentTarget.dataset.id;
    const courseName = e.currentTarget.dataset.name;
    
    // 找到对应的课程对象
    const course = this.data.courses.find(c => c.id == courseId);
    if (!course) {
      wx.showToast({
        title: '课程信息不完整',
        icon: 'none'
      });
      return;
    }
    
    console.log('导航到课程:', courseId, courseName, course.color);
    
    // 显示加载中
    wx.showLoading({
      title: '加载中...',
    });
    
    // 存储课程信息到全局缓存
    wx.setStorageSync('selectedCourse', {
      id: courseId,
      name: courseName,
      color: course.color || '#4e8df7'
    });
    
    // 跳转到课程页面
    wx.navigateTo({
      url: `/pages/course/course?id=${courseId}&name=${encodeURIComponent(courseName)}`,
      success: () => {
        // 成功跳转后隐藏加载
        wx.hideLoading();
      },
      fail: (err) => {
        console.error('导航失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 返回上一页
  navigateBack: function() {
    wx.navigateBack();
  }
}); 
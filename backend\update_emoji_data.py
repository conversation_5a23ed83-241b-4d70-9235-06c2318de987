#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
更新表情包数据，去除可能带有背景的emoji字符
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from cat_circle_api import init_chat_models, Emoji

def update_emoji_data():
    """更新表情包数据，使用不带背景的emoji字符"""
    
    with app.app_context():
        # 初始化聊天模型
        init_chat_models()
        
        # 更新表情包数据，使用文本变体选择器来确保不显示背景
        emoji_updates = [
            # 经典表情 - 使用文本变体
            {'id': 1, 'image_url': '😀'},  # 开心
            {'id': 2, 'image_url': '😂'},  # 大笑
            {'id': 3, 'image_url': '😉'},  # 眨眼
            {'id': 4, 'image_url': '😘'},  # 亲吻
            {'id': 5, 'image_url': '😍'},  # 爱心眼
            {'id': 6, 'image_url': '😎'},  # 酷
            {'id': 7, 'image_url': '🤔'},  # 思考
            {'id': 8, 'image_url': '😭'},  # 哭泣
            {'id': 9, 'image_url': '😠'},  # 生气
            {'id': 10, 'image_url': '😱'}, # 惊讶
            
            # 动物表情
            {'id': 11, 'image_url': '🐱'}, # 猫咪
            {'id': 12, 'image_url': '🐶'}, # 狗狗
            {'id': 13, 'image_url': '🐼'}, # 熊猫
            {'id': 14, 'image_url': '🐰'}, # 兔子
            {'id': 15, 'image_url': '🐯'}, # 老虎
            {'id': 16, 'image_url': '🦁'}, # 狮子
            
            # 手势表情
            {'id': 17, 'image_url': '👍'}, # 点赞
            {'id': 18, 'image_url': '👎'}, # 差评
            {'id': 19, 'image_url': '👌'}, # OK
            {'id': 20, 'image_url': '✌'}, # 胜利 - 去掉变体选择器
            {'id': 21, 'image_url': '👊'}, # 拳头
            {'id': 22, 'image_url': '👏'}, # 鼓掌
            
            # 爱心表情
            {'id': 23, 'image_url': '❤'}, # 红心 - 去掉变体选择器
            {'id': 24, 'image_url': '💕'}, # 粉心
            {'id': 25, 'image_url': '💙'}, # 蓝心
            {'id': 26, 'image_url': '💚'}, # 绿心
            {'id': 27, 'image_url': '💛'}, # 黄心
            {'id': 28, 'image_url': '💜'}, # 紫心
            
            # 食物表情
            {'id': 29, 'image_url': '🍎'}, # 苹果
            {'id': 30, 'image_url': '🍌'}, # 香蕉
            {'id': 31, 'image_url': '🎂'}, # 蛋糕
            {'id': 32, 'image_url': '🍕'}, # 披萨
            {'id': 33, 'image_url': '🍔'}, # 汉堡
            {'id': 34, 'image_url': '☕'}, # 咖啡
            
            # 运动表情
            {'id': 35, 'image_url': '⚽'}, # 足球
            {'id': 36, 'image_url': '🏀'}, # 篮球
            {'id': 37, 'image_url': '🎾'}, # 网球
            {'id': 38, 'image_url': '🏊'}, # 游泳
            {'id': 39, 'image_url': '🏃'}, # 跑步
            {'id': 40, 'image_url': '🚴'}, # 骑行
        ]
        
        print("开始更新表情包数据...")
        
        updated_count = 0
        for emoji_data in emoji_updates:
            emoji = Emoji.query.get(emoji_data['id'])
            if emoji:
                emoji.image_url = emoji_data['image_url']
                updated_count += 1
                print(f"更新表情包 ID {emoji_data['id']}: {emoji.name} -> {emoji_data['image_url']}")
        
        try:
            db.session.commit()
            print(f"✅ 成功更新 {updated_count} 个表情包数据")
        except Exception as e:
            db.session.rollback()
            print(f"❌ 更新失败: {e}")
            return False
        
        return True

if __name__ == '__main__':
    print("=== 表情包数据更新工具 ===")
    
    if update_emoji_data():
        print("表情包数据更新完成！")
    else:
        print("表情包数据更新失败！")

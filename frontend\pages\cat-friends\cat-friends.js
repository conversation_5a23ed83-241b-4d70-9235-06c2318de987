const app = getApp()
const globalNotificationMixin = require('../../utils/global-notification-mixin')

Page(Object.assign({}, globalNotificationMixin, {
  data: Object.assign({}, globalNotificationMixin.data, {
    // 当前选中的标签
    currentTab: 'all',
    sortExpanded: false,

    // 动态列表
    posts: [],
    loading: false,
    hasMore: true,
    page: 1,

    // 发布按钮拖动相关
    fabX: 0,
    fabY: 0,
    isDragging: false,

    // 发布相关
    showPublishModal: false,
    publishContent: '',
    publishImages: [],
    publishType: 'dynamic',
    publishContact: '',
    publishGender: 'male',
    publishPrice: '',
    publishLocation: '',
    selectedTime: '',
    timeIndex: [0, 0],
    publishing: false,
    canPublish: false,

    // 类型选项
    typeOptions: [
      { value: 'dynamic', name: '动态', icon: '💭' },
      { value: 'secondhand', name: '二手交易', icon: '🛒' },
      { value: 'help', name: '求助', icon: '🆘' },
      { value: 'lost_found', name: '失物招领', icon: '🔍' },
      { value: 'cat_friend', name: '猫友', icon: '🐱' },
      { value: 'campus_run', name: '校园跑', icon: '🏃' }
    ],

    // 时间选择器数据
    timeRange: [
      ['今天', '明天', '后天'],
      ['08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00']
    ],

    // 评论相关
    showCommentModal: false,
    currentPostId: null,
    comments: [],
    commentContent: '',
    replyToCommentId: null,
    replyToUserId: null,
    replyPlaceholder: '写评论...',
    // 悬浮通话状态
    showFloatingCall: false,
    currentCall: null
  }),

  onLoad: function(options) {
    this.loadPosts()
    this.initFabPosition()
  },

  onShow: function() {
    // 更新自定义tabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 2
      });
    }
    
    // 检查是否需要刷新页面
    if (app.globalData.needRefreshCatFriends) {
      console.log('从详情页返回，刷新列表');
      // 重置标志
      app.globalData.needRefreshCatFriends = false;
      // 重置列表数据
      this.setData({
        posts: [],
        page: 1,
        hasMore: true
      });
      // 重新加载列表
      this.loadPosts();
    } else {
      // 即使不刷新列表，也要刷新未读评论数量
      this.loadUnreadCommentCounts()
    }

    // 检查全局通话状态
    this.checkGlobalCallStatus()
  },

  // 切换标签
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab
    this.setData({
      currentTab: tab,
      posts: [],
      page: 1,
      hasMore: true
    })
    this.loadPosts()
  },

  // 切换排序展开状态
  toggleSort: function() {
    this.setData({
      sortExpanded: !this.data.sortExpanded
    })
  },

  // 展开/收起内容
  toggleExpand: function(e) {
    const postId = e.currentTarget.dataset.id
    const posts = this.data.posts.map(post => {
      if (post.id === postId) {
        return { ...post, expanded: !post.expanded }
      }
      return post
    })
    this.setData({ posts })
  },

  // 显示更多选项
  showMoreOptions: function(e) {
    const postId = e.currentTarget.dataset.id
    wx.showActionSheet({
      itemList: ['举报', '屏蔽', '分享'],
      success: (res) => {
        switch(res.tapIndex) {
          case 0:
            this.reportPost(postId)
            break
          case 1:
            this.blockPost(postId)
            break
          case 2:
            this.sharePost(postId)
            break
        }
      }
    })
  },

  // 举报动态
  reportPost: function(postId) {
    wx.showToast({
      title: '举报成功',
      icon: 'success'
    })
  },

  // 屏蔽动态
  blockPost: function(postId) {
    wx.showModal({
      title: '确认屏蔽',
      content: '屏蔽后将不再显示该用户的动态',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '已屏蔽',
            icon: 'success'
          })
        }
      }
    })
  },

  // 分享动态
  sharePost: function(postId) {
    wx.showShareMenu({
      withShareTicket: true
    })
  },

  // 加载动态列表
  loadPosts: function() {
    if (this.data.loading || !this.data.hasMore) return

    this.setData({ loading: true })

    const userInfo = wx.getStorageSync('userInfo')
    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      this.setData({ loading: false })
      return
    }

    // 获取用户特定的点赞和收藏缓存
    const likedPosts = wx.getStorageSync('liked_posts') || {}
    const collectedPosts = wx.getStorageSync('collected_posts') || {}
    
    // 确保用户ID的缓存对象存在
    if (!likedPosts[userInfo.id]) likedPosts[userInfo.id] = {}
    if (!collectedPosts[userInfo.id]) collectedPosts[userInfo.id] = {}

    wx.request({
      url: `${app.globalData.baseUrl}/cat_circle/posts`,
      method: 'GET',
      data: {
        page: this.data.page,
        per_page: 20,
        type: this.data.currentTab === 'all' ? 'all' : this.data.currentTab,
        user_id: userInfo.id
      },
      success: (res) => {
        if (res.data.code === 200) {
          let newPosts = res.data.data.posts || []
          
          // 确保使用当前用户的点赞和收藏状态
          newPosts = newPosts.map(post => {
            // 如果有本地缓存的点赞状态，优先使用缓存
            if (likedPosts[userInfo.id][post.id] !== undefined) {
              post.is_liked = likedPosts[userInfo.id][post.id]
            }
            
            // 如果有本地缓存的收藏状态，优先使用缓存
            if (collectedPosts[userInfo.id][post.id] !== undefined) {
              post.is_collected = collectedPosts[userInfo.id][post.id]
            }
            
            return post
          })
          
          this.setData({
            posts: this.data.page === 1 ? newPosts : [...this.data.posts, ...newPosts],
            hasMore: res.data.data.pagination.has_next,
            page: this.data.page + 1
          })

          // 如果是第一页，加载未读评论数量
          if (this.data.page === 2) { // page已经+1了，所以这里是2表示刚加载的是第一页
            this.loadUnreadCommentCounts()
          }
        } else {
          wx.showToast({
            title: res.data.message || '加载失败',
            icon: 'none'
          })
        }
      },
      fail: () => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
      },
      complete: () => {
        this.setData({ loading: false })
      }
    })
  },

  // 加载更多
  loadMore: function() {
    this.loadPosts()
  },

  // 获取类型文本
  getTypeText: function(type) {
    const typeMap = {
      'dynamic': '动态',
      'secondhand': '二手交易',
      'help': '求助',
      'lost_found': '失物招领',
      'cat_friend': '猫友',
      'campus_run': '校园跑'
    }
    return typeMap[type] || '动态'
  },

  // 预览图片
  previewImage: function(e) {
    const current = e.currentTarget.dataset.current
    const urls = e.currentTarget.dataset.urls
    wx.previewImage({
      current: current,
      urls: urls
    })
  },

  // 显示发布弹窗
  showPublishModal: function() {
    this.setData({ showPublishModal: true })
  },

  // 阻止事件冒泡
  stopPropagation: function(e) {
    // 阻止事件冒泡，防止弹窗关闭
  },

  // 隐藏发布弹窗
  hidePublishModal: function() {
    this.setData({
      showPublishModal: false,
      publishContent: '',
      publishImages: [],
      publishType: 'dynamic',
      publishContact: '',
      publishGender: 'male',
      publishPrice: '',
      publishLocation: '',
      selectedTime: '',
      timeIndex: [0, 0],
      canPublish: false
    })
  },

  // 内容输入
  onContentInput: function(e) {
    this.setData({
      publishContent: e.detail.value
    }, () => {
      this.checkCanPublish()
    })
  },

  // 选择类型
  selectType: function(e) {
    const type = e.currentTarget.dataset.type
    this.setData({
      publishType: type
    }, () => {
      this.checkCanPublish()
    })
  },

  // 价格输入
  onPriceInput: function(e) {
    this.setData({
      publishPrice: e.detail.value
    }, () => {
      this.checkCanPublish()
    })
  },

  // 地点输入
  onLocationInput: function(e) {
    this.setData({
      publishLocation: e.detail.value
    }, () => {
      this.checkCanPublish()
    })
  },

  // 时间选择
  onTimeChange: function(e) {
    const values = e.detail.value
    const timeRange = this.data.timeRange
    const selectedTime = `${timeRange[0][values[0]]} ${timeRange[1][values[1]]}`
    this.setData({
      timeIndex: values,
      selectedTime: selectedTime
    }, () => {
      this.checkCanPublish()
    })
  },

  // 性别选择
  selectGender: function(e) {
    const gender = e.currentTarget.dataset.gender
    this.setData({
      publishGender: gender
    }, () => {
      this.checkCanPublish()
    })
  },

  // 检查是否可以发布
  checkCanPublish: function() {
    const { publishContent, publishType, publishContact, publishPrice, publishLocation, publishGender, selectedTime } = this.data

    let canPublish = publishContent.trim().length > 0

    // 根据类型检查必填字段
    if (publishType === 'secondhand') {
      canPublish = canPublish && publishPrice.trim().length > 0 && publishContact.trim().length > 0
      console.log('二手交易检查:', {
        content: publishContent.trim().length > 0,
        price: publishPrice.trim().length > 0,
        contact: publishContact.trim().length > 0,
        canPublish: canPublish
      })
    } else if (publishType === 'help' || publishType === 'lost_found') {
      canPublish = canPublish && publishContact.trim().length > 0
    } else if (publishType === 'campus_run') {
      canPublish = canPublish && publishContact.trim().length > 0 && publishLocation.trim().length > 0 && selectedTime.length > 0
    } else if (publishType === 'cat_friend') {
      canPublish = canPublish && publishGender.length > 0
    }

    this.setData({ canPublish })
  },

  // 判断是否需要联系方式
  needContactInfo: function(type) {
    return ['secondhand', 'help', 'lost_found', 'campus_run'].includes(type)
  },

  // 获取内容标签
  getContentLabel: function(type) {
    const labels = {
      'dynamic': '✍️ 分享内容',
      'secondhand': '📝 商品描述',
      'help': '📢 求助说明',
      'lost_found': '🔍 物品详情',
      'cat_friend': '👋 自我介绍',
      'campus_run': '🏃 跑步信息'
    }
    return labels[type] || '✍️ 分享内容'
  },

  // 获取内容占位符
  getContentPlaceholder: function(type) {
    const placeholders = {
      'dynamic': '分享你的生活、想法、见闻...',
      'secondhand': '请详细描述：\n• 商品名称和品牌\n• 使用时长和成色\n• 购买价格和出售原因\n• 交易方式和地点',
      'help': '请详细说明：\n• 需要什么帮助\n• 具体要求和时间\n• 是否有报酬\n• 其他注意事项',
      'lost_found': '请详细描述：\n• 物品名称和特征\n• 丢失时间和地点\n• 物品价值和重要性\n• 联系方式',
      'cat_friend': '介绍一下自己：\n• 年级专业和兴趣爱好\n• 养猫经验和猫咪情况\n• 希望交流的内容\n• 期望的交友方式',
      'campus_run': '跑步详情：\n• 跑步路线和距离\n• 配速要求和时长\n• 出发时间和集合地点\n• 适合人群'
    }
    return placeholders[type] || '分享你的想法...'
  },

  // 获取联系方式占位符
  getContactPlaceholder: function(type) {
    const placeholders = {
      'secondhand': '请留下微信号、QQ号或手机号',
      'help': '请留下微信号、QQ号或手机号',
      'lost_found': '请留下微信号、QQ号或手机号',
      'campus_run': '请留下微信号或QQ号'
    }
    return placeholders[type] || '请输入联系方式'
  },

  // 获取输入提示
  getInputTips: function(type) {
    const tips = {
      'dynamic': '分享你的生活点滴、想法感悟或有趣见闻',
      'secondhand': '详细描述商品信息，包括品牌、成色、使用时长等',
      'help': '清楚说明需要什么帮助，具体要求和时间安排',
      'lost_found': '详细描述物品特征、丢失时间和地点信息',
      'cat_friend': '介绍自己的基本情况和养猫经验，寻找猫友',
      'campus_run': '说明跑步路线、配速要求和适合的人群'
    }
    return tips[type] || '请输入相关内容'
  },

  // 选择图片
  chooseImage: function() {
    const maxCount = 10 - this.data.publishImages.length
    wx.chooseImage({
      count: maxCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 显示上传进度
        wx.showLoading({
          title: '上传图片中...',
          mask: true
        });

        // 逐个上传图片
        this.uploadImages(res.tempFilePaths);
      }
    })
  },

  // 上传图片到服务器
  uploadImages: function(tempFilePaths) {
    const uploadPromises = tempFilePaths.map(filePath => {
      return this.uploadSingleImage(filePath);
    });

    Promise.all(uploadPromises)
      .then(imageUrls => {
        wx.hideLoading();
        // 将上传成功的图片URL添加到发布图片数组中
        this.setData({
          publishImages: [...this.data.publishImages, ...imageUrls]
        });
        wx.showToast({
          title: '图片上传成功',
          icon: 'success'
        });
      })
      .catch(error => {
        wx.hideLoading();
        console.error('图片上传失败:', error);
        wx.showToast({
          title: '图片上传失败',
          icon: 'none'
        });
      });
  },

  // 上传单张图片
  uploadSingleImage: function(filePath) {
    return new Promise((resolve, reject) => {
      wx.uploadFile({
        url: `${app.globalData.baseUrl}/cat_circle/upload/image`,
        filePath: filePath,
        name: 'image',
        header: {
          'Content-Type': 'multipart/form-data'
        },
        success: (res) => {
          console.log('图片上传响应:', res);
          try {
            const data = JSON.parse(res.data);
            if (data.code === 200 && data.data && data.data.image_url) {
              console.log('图片上传成功:', data.data.image_url);
              resolve(data.data.image_url);
            } else {
              console.error('图片上传失败:', data.message || '未知错误');
              reject(new Error(data.message || '图片上传失败'));
            }
          } catch (e) {
            console.error('解析上传响应失败:', e);
            reject(new Error('图片上传响应格式错误'));
          }
        },
        fail: (err) => {
          console.error('图片上传请求失败:', err);
          reject(new Error('图片上传失败'));
        }
      });
    });
  },

  // 删除图片
  deleteImage: function(e) {
    const index = e.currentTarget.dataset.index
    const images = this.data.publishImages
    images.splice(index, 1)
    this.setData({ publishImages: images })
  },

  // 类型选择
  onTypeChange: function(e) {
    const index = parseInt(e.detail.value)
    this.setData({ publishTypeIndex: index })
  },

  // 联系方式输入
  onContactInput: function(e) {
    this.setData({
      publishContact: e.detail.value
    }, () => {
      this.checkCanPublish()
    })
  },

  // 计算是否需要联系方式
  needContact: function() {
    const type = this.data.typeOptions[this.data.publishTypeIndex].value
    return ['secondhand', 'help', 'lost_found', 'campus_run'].includes(type)
  },

  // 计算是否需要性别
  needGender: function() {
    const type = this.data.typeOptions[this.data.publishTypeIndex].value
    return type === 'cat_friend'
  },

  // 发布动态
  submitPost: function() {
    if (!this.data.canPublish) {
      wx.showToast({
        title: '请完善信息',
        icon: 'none'
      })
      return
    }

    const content = this.data.publishContent.trim()
    const type = this.data.publishType

    const userInfo = wx.getStorageSync('userInfo')
    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    this.setData({ publishing: true })

    const postData = {
      user_id: userInfo.id,
      content: content,
      type: type,
      images: this.data.publishImages
    }

    // 根据类型添加额外字段
    if (type === 'secondhand') {
      postData.price = this.data.publishPrice
      postData.contact_info = this.data.publishContact.trim()
    } else if (type === 'help' || type === 'lost_found') {
      postData.contact_info = this.data.publishContact.trim()
    } else if (type === 'campus_run') {
      postData.contact_info = this.data.publishContact.trim()
      postData.location = this.data.publishLocation.trim()
      postData.time = this.data.selectedTime
    } else if (type === 'cat_friend') {
      postData.gender = this.data.publishGender
    }

    if (type === 'lost_found') {
      postData.location = this.data.publishLocation.trim()
    }

    wx.request({
      url: `${app.globalData.baseUrl}/cat_circle/posts`,
      method: 'POST',
      data: postData,
      success: (res) => {
        if (res.data.code === 200) {
          wx.showToast({
            title: '发布成功',
            icon: 'success'
          })
          this.hidePublishModal()
          // 重新加载列表
          this.setData({
            posts: [],
            page: 1,
            hasMore: true
          })
          this.loadPosts()
        } else {
          wx.showToast({
            title: res.data.message || '发布失败',
            icon: 'none'
          })
        }
      },
      fail: () => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
      },
      complete: () => {
        this.setData({ publishing: false })
      }
    })
  },

  // 点赞/取消点赞
  toggleLike: function(e) {
    const postId = e.currentTarget.dataset.id
    const userInfo = wx.getStorageSync('userInfo')

    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    // 获取当前用户的点赞状态缓存
    let likedPosts = wx.getStorageSync('liked_posts') || {}
    if (!likedPosts[userInfo.id]) {
      likedPosts[userInfo.id] = {}
    }

    wx.request({
      url: `${app.globalData.baseUrl}/cat_circle/posts/${postId}/like`,
      method: 'POST',
      data: { user_id: userInfo.id },
      success: (res) => {
        if (res.data.code === 200) {
          // 更新本地数据
          const posts = this.data.posts.map(post => {
            if (post.id === postId) {
              // 更新点赞缓存
              likedPosts[userInfo.id][postId] = res.data.data.is_liked
              wx.setStorageSync('liked_posts', likedPosts)
              
              return {
                ...post,
                is_liked: res.data.data.is_liked,
                like_count: res.data.data.like_count
              }
            }
            return post
          })
          this.setData({ posts })
        } else {
          wx.showToast({
            title: res.data.message || '操作失败',
            icon: 'none'
          })
        }
      }
    })
  },

  // 收藏/取消收藏
  toggleCollect: function(e) {
    const postId = e.currentTarget.dataset.id
    const userInfo = wx.getStorageSync('userInfo')

    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    // 获取当前用户的收藏状态缓存
    let collectedPosts = wx.getStorageSync('collected_posts') || {}
    if (!collectedPosts[userInfo.id]) {
      collectedPosts[userInfo.id] = {}
    }

    wx.request({
      url: `${app.globalData.baseUrl}/cat_circle/posts/${postId}/collect`,
      method: 'POST',
      data: { user_id: userInfo.id },
      success: (res) => {
        if (res.data.code === 200) {
          // 更新本地数据
          const posts = this.data.posts.map(post => {
            if (post.id === postId) {
              // 更新收藏缓存
              collectedPosts[userInfo.id][postId] = res.data.data.is_collected
              wx.setStorageSync('collected_posts', collectedPosts)
              
              return {
                ...post,
                is_collected: res.data.data.is_collected,
                collect_count: res.data.data.collect_count
              }
            }
            return post
          })
          this.setData({ posts })
        } else {
          wx.showToast({
            title: res.data.message || '操作失败',
            icon: 'none'
          })
        }
      }
    })
  },

  // 跳转到动态详情页
  goToPostDetail: function(e) {
    const postId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/post-detail/post-detail?id=${postId}`
    })
  },

  // 跳转到聊天页面
  goToChat: function(e) {
    const userId = e.currentTarget.dataset.userId
    const nickname = e.currentTarget.dataset.nickname
    const avatar = e.currentTarget.dataset.avatar

    // 检查是否是自己
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo && userInfo.id && userInfo.id === userId) {
      wx.showToast({
        title: '不能和自己聊天',
        icon: 'none'
      })
      return
    }

    if (!userId) {
      wx.showToast({
        title: '用户信息错误',
        icon: 'none'
      })
      return
    }

    wx.navigateTo({
      url: `/pages/chat/chat?userId=${userId}&nickname=${encodeURIComponent(nickname || '用户')}&avatar=${encodeURIComponent(avatar || '')}`
    })
  },

  // 显示评论
  showComments: function(e) {
    const postId = e.currentTarget.dataset.id
    this.setData({
      showCommentModal: true,
      currentPostId: postId,
      comments: [],
      commentContent: '',
      replyToCommentId: null,
      replyPlaceholder: '写评论...'
    })
    this.loadComments(postId)
  },

  // 隐藏评论弹窗
  hideCommentModal: function() {
    this.setData({
      showCommentModal: false,
      currentPostId: null,
      comments: [],
      commentContent: '',
      replyToCommentId: null
    })
  },

  // 加载评论
  loadComments: function(postId) {
    const userInfo = wx.getStorageSync('userInfo')
    if (!userInfo || !userInfo.id) return

    wx.request({
      url: `${app.globalData.baseUrl}/cat_circle/posts/${postId}/comments`,
      method: 'GET',
      data: { user_id: userInfo.id },
      success: (res) => {
        if (res.data.code === 200) {
          this.setData({
            comments: res.data.data.comments || []
          })
        }
      }
    })
  },

  // 评论输入
  onCommentInput: function(e) {
    this.setData({ commentContent: e.detail.value })
  },

  // 回复评论
  replyComment: function(e) {
    const commentId = e.currentTarget.dataset.id
    const userName = e.currentTarget.dataset.user
    this.setData({
      replyToCommentId: commentId,
      replyPlaceholder: `回复 ${userName}：`
    })
  },

  // 提交评论
  submitComment: function() {
    const content = this.data.commentContent.trim()
    if (!content) return

    const userInfo = wx.getStorageSync('userInfo')
    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    const commentData = {
      user_id: userInfo.id,
      content: content,
      post_id: this.data.currentPostId
    }

    if (this.data.replyToCommentId) {
      commentData.reply_to_comment_id = this.data.replyToCommentId
    }

    wx.request({
      url: `${app.globalData.baseUrl}/cat_circle/comments`,
      method: 'POST',
      data: commentData,
      success: (res) => {
        if (res.data.code === 200) {
          wx.showToast({
            title: '评论成功',
            icon: 'success'
          })
          this.setData({
            commentContent: '',
            replyToCommentId: null,
            replyPlaceholder: '写评论...'
          })
          // 重新加载评论
          this.loadComments(this.data.currentPostId)
          // 更新动态的评论数
          this.updatePostCommentCount(this.data.currentPostId)
        } else {
          wx.showToast({
            title: res.data.message || '评论失败',
            icon: 'none'
          })
        }
      }
    })
  },

  // 更新动态评论数
  updatePostCommentCount: function(postId) {
    const posts = this.data.posts.map(post => {
      if (post.id === postId) {
        return { ...post, comment_count: (post.comment_count || 0) + 1 }
      }
      return post
    })
    this.setData({ posts })
  },

  // 初始化发布按钮位置
  initFabPosition: function() {
    const systemInfo = wx.getSystemInfoSync()
    const windowWidth = systemInfo.windowWidth
    const windowHeight = systemInfo.windowHeight

    // 初始位置由 CSS 控制，这里保留函数以备将来使用
    this.setData({
      fabX: windowWidth - (120 / 2 + 40), // 按钮宽度的一半 + right间距
      fabY: windowHeight - (120 / 2 + 120)  // 按钮高度的一半 + bottom间距
    })
  },

  // 拖动开始
  onDragStart: function(e) {
    this.setData({
      isDragging: true
    })
  },

  // 拖动结束
  onDragEnd: function(e) {
    this.setData({
      isDragging: false
    })

    // 保持在拖动到的位置，不进行自动吸附
    // 按钮会停留在用户拖动到的任何位置
  },

  // 检查全局通话状态
  checkGlobalCallStatus: function() {
    const app = getApp()
    if (app.globalData.currentCall && app.globalData.showFloatingCall) {
      this.setData({
        showFloatingCall: true,
        currentCall: app.globalData.currentCall
      })
    }
  },

  // 通话状态变化回调（由app.js调用）
  onCallStatusChanged: function(callStatus) {
    this.setData({
      showFloatingCall: callStatus.showFloatingCall,
      currentCall: callStatus.currentCall
    })
  },

  // 获取未读评论数量
  loadUnreadCommentCounts: function() {
    const userInfo = wx.getStorageSync('userInfo')
    if (!userInfo || !userInfo.id) return

    // 为每个动态获取未读评论数量
    const posts = this.data.posts
    if (posts.length === 0) return

    // 收集当前用户发布的动态ID
    const userPostIds = posts
      .filter(post => post.user_id === userInfo.id)
      .map(post => post.id)

    if (userPostIds.length === 0) return

    // 批量获取未读评论数量
    wx.request({
      url: `${app.globalData.baseUrl}/cat_circle/posts/unread_counts`,
      method: 'GET',
      data: {
        user_id: userInfo.id,
        post_ids: userPostIds.join(',')
      },
      success: (res) => {
        if (res.data.code === 200) {
          const unreadCounts = res.data.data.unread_counts || {}

          // 更新每个动态的未读数量
          const updatedPosts = this.data.posts.map(post => {
            if (post.user_id === userInfo.id) {
              const unreadCount = unreadCounts[post.id.toString()] || 0
              return { ...post, unread_comment_count: unreadCount }
            }
            return post
          })

          this.setData({ posts: updatedPosts })
        }
      },
      fail: (err) => {
        console.error('获取未读评论数量失败:', err)
      }
    })
  },

  // 实时更新指定动态的未读评论数量
  updateUnreadCommentCount: function(postId) {
    const userInfo = wx.getStorageSync('userInfo')
    if (!userInfo || !userInfo.id) return

    // 检查这个动态是否是当前用户发布的
    const targetPost = this.data.posts.find(post => post.id === postId && post.user_id === userInfo.id)
    if (!targetPost) return

    // 获取指定动态的未读评论数量
    wx.request({
      url: `${app.globalData.baseUrl}/cat_circle/notifications/comments/unread_count`,
      method: 'GET',
      data: {
        user_id: userInfo.id,
        post_id: postId
      },
      success: (res) => {
        if (res.data.code === 200) {
          const unreadCount = res.data.data.unread_count || 0

          // 更新指定动态的未读数量
          const updatedPosts = this.data.posts.map(post => {
            if (post.id === postId && post.user_id === userInfo.id) {
              return { ...post, unread_comment_count: unreadCount }
            }
            return post
          })

          this.setData({ posts: updatedPosts })
          console.log(`动态${postId}的未读评论数量已更新为: ${unreadCount}`)
        }
      },
      fail: (err) => {
        console.error('更新未读评论数量失败:', err)
      }
    })
  }
}))
{% extends "admin/base.html" %}

{% block title %}猫友圈管理 - 仪表盘{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">
                    <svg t="1751971050916" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5591" width="64" height="64" style="vertical-align: middle; margin-right: 8px; color: #6f42c1;">
                        <path d="M234.993371 907.062126s156.768549-7.835063 260.953235-8.788846c115.758811-1.059109 300.163657 8.788846 300.163657 8.788846l191.318308 21.632731s-298.896823 17.712274-491.481965 17.577692C307.053714 946.140891 32.182857 928.694857 32.182857 928.694857l202.810514-21.632731z" fill="currentColor" opacity=".5" p-id="5592"></path>
                        <path d="M142.286263 66.58048c4.765989-6.606263 13.320777-15.108389 25.78432-13.788891 10.491611 1.111771 27.390537 6.038674 46.650514 12.603977 19.766126 6.73792 43.476114 15.72864 68.312503 25.755063 44.696137 18.039954 93.561417 39.637577 130.323017 57.870628A533.735131 533.735131 0 0 1 509.074286 140.434286c35.064686 0 69.210697 3.3792 102.022583 9.777737 35.989211-17.855634 82.727497-38.654537 125.597988-56.118126 24.608183-10.026423 48.101669-19.017143 67.689326-25.757988 19.078583-6.565303 35.845851-11.498057 46.273097-12.61568 12.501577-1.337051 21.024183 7.267474 25.719954 13.838628 5.12 7.162149 9.371063 16.828709 13.039909 27.4432 7.422537 21.480594 13.80352 50.936686 19.213166 82.970332 10.8544 64.3072 18.230126 141.80352 21.714651 193.536 0.084846 1.29024 0.002926 2.548297-0.231131 3.759542C954.745417 425.688503 968.411429 479.176411 968.411429 535.405714c0 218.135406-205.651383 394.971429-459.337143 394.971429S49.737143 753.54112 49.737143 535.405714c0-56.618423 13.853257-110.454491 38.806674-159.135451a14.587611 14.587611 0 0 1-0.73728-5.696366c3.513783-51.735406 10.959726-129.237577 21.916526-193.550628 5.459383-32.036571 11.901806-61.498514 19.397486-82.98496 3.703954-10.620343 7.998903-20.29568 13.165714-27.457829z m754.761143 257.287314c-3.996526-44.061257-9.80992-94.851657-17.267566-139.027017-5.336503-31.612343-11.401509-59.143314-18.013623-78.280411-3.349943-9.689966-6.509714-16.237714-9.189668-19.98848a20.84864 20.84864 0 0 0-1.114698-1.439452c-7.285029 1.214171-20.155246 4.880091-37.557394 10.866103-18.908891 6.509714-41.869897 15.289783-66.17088 25.1904-30.485943 12.419657-62.83264 26.498194-91.659703 39.906743 100.343223 29.12256 185.162606 87.2448 240.973532 162.772114z m-308.068937-147.924114a14.628571 14.628571 0 0 0 20.450742 7.90528 845.4144 845.4144 0 0 1 5.833875-2.993006C803.831223 221.432686 939.154286 367.539931 939.154286 535.405714c0 197.988937-188.25216 365.714286-430.08 365.714286S78.994286 733.394651 78.994286 535.405714c0-169.6768 138.26048-317.126949 330.017645-355.825371l2.68288 1.369234a14.628571 14.628571 0 0 0 19.08736-5.257508A509.29664 509.29664 0 0 1 509.074286 169.691429c27.387611 0 54.08768 2.1504 79.904183 6.255177zM272.082651 118.280777c31.808366 12.84096 65.623771 27.451977 95.451429 41.264274-102.882743 28.625189-189.878857 87.61344-246.693303 164.676755 4.043337-44.801463 10.011794-97.016686 17.723977-142.280412 5.38624-31.609417 11.506834-59.134537 18.180389-78.262857 3.376274-9.68704 6.565303-16.228937 9.268663-19.976777 0.476891-0.661211 0.871863-1.149806 1.18784-1.509669 7.358171 1.193691 20.41856 4.877166 38.078171 10.898286 19.093211 6.509714 42.27072 15.289783 66.802834 25.1904zM168.126171 81.3056c0 0.008777-0.058514 0.058514-0.181394 0.128731 0.119954-0.1024 0.181394-0.134583 0.181394-0.128731z m682.370195 2.884754c0 0.008777 0.064366 0.064366 0.187245 0.137509-0.12288-0.108251-0.187246-0.146286-0.187245-0.137509z" fill="#5A3924" p-id="5593"></path>
                        <path d="M936.228571 535.405714c0 201.979611-191.24224 365.714286-427.154285 365.714286S81.92 737.385326 81.92 535.405714 273.16224 169.691429 509.074286 169.691429s427.154286 163.734674 427.154285 365.714285z" fill="#FDB64A" p-id="5594"></path>
                        <path d="M525.165714 168.228571c143.36 0 352.841143 70.509714 400.822857 264.777143 59.977143 242.834286-454.948571-35.108571-400.822857-264.777143z" fill="#5A3924" p-id="5595"></path>
                        <path d="M169.691429 81.92c34.026057 3.677623 185.730194 62.598583 258.925714 100.937143-231.084617 63.253943-269.165714 197.485714-307.2 206.262857C117.9648 285.608229 135.65952 78.239451 169.691429 81.92z" fill="#FDB64A" p-id="5596"></path>
                        <path d="M859.36128 61.483886c-34.03776 3.662994-192.231131 76.089051-265.438354 114.249143C825.048503 238.691474 845.531429 348.16 912.822857 389.12c3.452343-103.026103-19.426743-331.299109-53.461577-327.636114z" fill="#5A3924" p-id="5597"></path>
                        <path d="M174.963566 128.731429c34.175269 20.140617 117.332846 72.197851 100.837668 82.306194-48.888686 29.965166-117.543497 86.884937-128.936228 101.235566-11.392731 14.350629 6.834469-174.477897 28.09856-183.54176z m671.147154 0c-34.175269 20.140617-117.332846 72.197851-100.837669 82.306194 48.888686 29.965166 117.543497 86.884937 128.936229 101.235566 11.392731 14.350629-6.834469-174.477897-28.09856-183.54176z" fill="#FFFFFF" opacity=".3" p-id="5598"></path>
                        <path d="M308.662857 422.76864c-73.991314 33.797851-150.279314 36.688457-210.651428 8.777143-81.92 216.502857 112.657554 469.577143 392.045714 469.577143 339.084434 0 498.834286-248.685714 434.468571-469.577143-36.571429 17.554286-152.137143 49.734217-245.76-8.777143-71.574674-44.734171-45.348571-247.222857-156.525714-251.611429s-95.085714 197.485714-213.577143 251.611429z" fill="#FFFFFF" p-id="5599"></path>
                        <path d="M362.788571 544.182857c37.165349 0 67.291429-29.473646 67.291429-65.828571S399.95392 412.525714 362.788571 412.525714s-67.291429 29.473646-67.291428 65.828572 30.12608 65.828571 67.291428 65.828571z m272.091429 0c37.165349 0 67.291429-29.473646 67.291429-65.828571S672.045349 412.525714 634.88 412.525714s-67.291429 29.473646-67.291429 65.828572 30.12608 65.828571 67.291429 65.828571z" fill="#FFC976" p-id="5600"></path>
                        <path d="M533.322606 545.923657c1.468709-1.026926 2.083109-2.165029 2.083108-3.712731 0-1.793463-0.842606-4.224731-2.799908-7.010012-1.928046-2.738469-4.786469-5.596891-8.405577-8.194925-7.244069-5.196069-17.261714-9.15456-28.291658-9.15456-11.029943 0-21.047589 3.958491-28.291657 9.15456-3.619109 2.598034-6.477531 5.456457-8.405577 8.194925-1.957303 2.78528-2.799909 5.216549-2.799908 7.010012 0 1.547703 0.6144 2.685806 2.083108 3.712731 1.629623 1.138103 4.23936 2.059703 7.931612 2.720914 7.366949 1.319497 17.680091 1.389714 29.482422 1.389715s22.115474-0.070217 29.482423-1.389715c3.692251-0.661211 6.301989-1.582811 7.931612-2.720914zM495.908571 552.96c23.42912 0 42.422857-0.24576 42.422858-10.749074S519.337691 514.925714 495.908571 514.925714s-42.422857 16.781897-42.422857 27.285212c0 10.503314 18.993737 10.749074 42.422857 10.749074z" fill="#634011" p-id="5601"></path>
                        <path d="M374.491429 878.232137c0 11.0592-2.884754 18.733349-7.176778 24.640366-4.514377 6.217143-11.881326 12.220709-23.309165 17.451886C320.415451 931.129051 284.914834 936.228571 241.371429 936.228571s-79.044023-5.09952-102.634058-15.904182c-11.42784-5.231177-18.794789-11.234743-23.309165-17.451886-4.292023-5.907017-7.176777-13.581166-7.176777-24.640366 0-22.870309 12.744411-49.122743 37.443291-70.489234C170.060069 786.666057 203.945691 772.388571 241.371429 772.388571c37.425737 0 71.31136 14.277486 95.676708 35.354332C361.747017 829.109394 374.491429 855.361829 374.491429 878.232137z m29.257142 0C403.748571 945.075931 331.050423 965.485714 241.371429 965.485714S78.994286 945.075931 78.994286 878.232137C78.994286 811.391269 151.692434 743.131429 241.371429 743.131429s162.377143 68.25984 162.377142 135.100708z m500.297143 0c0 11.0592-2.884754 18.733349-7.176777 24.640366-4.514377 6.217143-11.881326 12.220709-23.309166 17.451886-23.590034 10.804663-59.090651 15.904183-102.634057 15.904182s-79.044023-5.09952-102.634057-15.904182c-11.42784-5.231177-18.794789-11.234743-23.309166-17.451886-4.292023-5.907017-7.176777-13.581166-7.176777-24.640366 0-22.870309 12.744411-49.122743 37.443292-70.489234C699.614354 786.666057 733.499977 772.388571 770.925714 772.388571c37.425737 0 71.31136 14.277486 95.676709 35.354332C891.301303 829.109394 904.045714 855.361829 904.045714 878.232137z m29.257143 0C933.302857 945.075931 860.604709 965.485714 770.925714 965.485714s-162.377143-20.409783-162.377143-87.253577C608.548571 811.391269 681.24672 743.131429 770.925714 743.131429s162.377143 68.25984 162.377143 135.100708z" fill="#5A3924" p-id="5602"></path>
                        <path d="M374.491429 876.751726C374.491429 931.281189 314.891703 947.931429 241.371429 947.931429S108.251429 931.281189 108.251429 876.751726 167.851154 766.537143 241.371429 766.537143s133.12 55.68512 133.12 110.214583zM906.971429 875.493669C906.971429 926.503497 846.716343 942.08 772.388571 942.08s-134.582857-15.576503-134.582857-66.586331C637.805714 824.480914 698.0608 772.388571 772.388571 772.388571s134.582857 52.092343 134.582858 103.105098z" fill="#FFFFFF" p-id="5603"></path>
                        <path d="M285.075749 859.162331a8.777143 8.777143 0 0 0-17.124206-3.853165c-2.363977 10.500389-4.251063 27.118446-2.23232 43.382491 1.980709 15.989029 8.04864 33.83296 23.727543 43.537554a8.777143 8.777143 0 1 0 9.242331-14.924068c-8.897097-5.506194-13.800594-16.676571-15.550171-30.772663-1.714469-13.824-0.087771-28.3648 1.936823-37.370149z m-98.075795 11.969098a8.777143 8.777143 0 1 0-16.9984-4.385646c-2.153326 8.349989-4.049189 21.714651-2.878903 35.380663 1.158583 13.499246 5.459383 28.885577 17.302675 39.245531a8.777143 8.777143 0 1 0 11.556571-13.2096c-6.88128-6.02112-10.380434-15.991954-11.369326-27.533897-0.974263-11.372251 0.643657-22.729874 2.387383-29.497051z m541.71648-9.043383a8.777143 8.777143 0 0 1 17.127132-3.853166c2.363977 10.500389 4.248137 27.118446 2.23232 43.382491-1.983634 15.989029-8.04864 33.83296-23.730469 43.537555a8.777143 8.777143 0 1 1-9.239406-14.924069c8.897097-5.506194 13.800594-16.676571 15.550172-30.772663 1.711543-13.824 0.087771-28.3648-1.939749-37.370148z m98.075795 11.969097a8.777143 8.777143 0 0 1 16.9984-4.385646c2.156251 8.349989 4.052114 21.714651 2.878902 35.380663-1.155657 13.499246-5.459383 28.885577-17.299748 39.245531a8.777143 8.777143 0 1 1-11.559497-13.2096c6.88128-6.02112 10.380434-15.991954 11.369325-27.533897 0.974263-11.372251-0.640731-22.729874-2.387382-29.497051zM351.015497 571.731383c-30.790217-2.677029-78.412069-6.421943-121.22112-8.958537-21.4016-1.266834-41.621211-2.235246-57.946697-2.615589-16.187977-0.374491-28.882651-0.187246-34.985691 0.959634a2.779429 2.779429 0 0 0-2.200138 3.39968c0.365714 1.579886 1.948526 2.618514 3.528412 2.320092 5.38624-1.012297 17.416777-1.240503 33.768594-0.86016 16.22016 0.377417 36.360777 1.339977 57.73312 2.606811 42.735909 2.533669 90.299246 6.272731 121.054354 8.946835a2.788206 2.788206 0 0 0 3.072-2.644846 3.074926 3.074926 0 0 0-2.802834-3.15392z m335.125943 4.455863c27.621669-3.121737 70.345874-7.551269 108.745874-10.705189 19.198537-1.57696 37.340891-2.837943 51.989943-3.452343 14.496914-0.608549 25.945234-0.611474 31.492389 0.462263 1.579886 0.304274 2.56 1.831497 2.188434 3.411383-0.36864 1.579886-1.951451 2.609737-3.531337 2.305463-4.739657-0.918674-15.450697-0.977189-30.129006-0.359863-14.532023 0.611474-32.586606 1.86368-51.750034 3.437714-38.323931 3.148069-80.983771 7.571749-108.567406 10.684709a2.796983 2.796983 0 0 1-3.150994-2.56 3.054446 3.054446 0 0 1 2.712137-3.224137zM263.92576 612.360777c-40.834194 11.021166-85.99552 24.178103-115.121006 32.934766a2.78528 2.78528 0 0 0-1.816868 3.622034 3.072 3.072 0 0 0 3.741988 1.951452c29.090377-8.74496 74.196114-21.884343 114.965943-32.887955 20.386377-5.503269 39.663909-10.46528 55.304777-14.072685 15.766674-3.639589 27.510491-5.822171 33.031315-5.91872a2.773577 2.773577 0 0 0 2.758948-2.957898 3.080777 3.080777 0 0 0-3.110034-2.861348c-6.255177 0.108251-18.633874 2.463451-34.245486 6.065006-15.737417 3.630811-35.093943 8.613303-55.509577 14.125348z m602.796617 37.285303c-26.05056-8.320731-66.445897-20.80768-102.982217-31.226149-18.265234-5.207771-35.588389-9.903543-49.681554-13.306148-13.952731-3.367497-25.099703-5.558857-30.790217-5.573486a3.069074 3.069074 0 0 0-3.060298 2.902309 2.791131 2.791131 0 0 0 2.814538 2.919863c4.865463 0.014629 15.310263 2.004114 29.435611 5.415497 13.984914 3.373349 31.223223 8.045714 49.456274 13.244708 36.460251 10.395063 76.797074 22.864457 102.806675 31.173486a3.060297 3.060297 0 0 0 3.765394-1.892937 2.796983 2.796983 0 0 0-1.764206-3.657143zM368.64 526.628571c24.236617 0 43.885714-19.649097 43.885714-43.885714 0-24.236617-19.649097-43.885714-43.885714-43.885714-24.236617 0-43.885714 19.649097-43.885714 43.885714 0 24.236617 19.649097 43.885714 43.885714 43.885714z m263.314286 0c24.236617 0 43.885714-19.649097 43.885714-43.885714 0-24.236617-19.649097-43.885714-43.885714-43.885714-24.236617 0-43.885714 19.649097-43.885715 43.885714 0 24.236617 19.649097 43.885714 43.885715 43.885714z" fill="#5A3924" p-id="5604"></path>
                        <path d="M368.64 459.337143a5.851429 5.851429 0 1 0 0.002926-11.699932A5.851429 5.851429 0 0 0 368.64 459.337143z m254.537143 0a5.851429 5.851429 0 1 0 0.002926-11.699932A5.851429 5.851429 0 0 0 623.177143 459.337143z" fill="#FFFFFF" p-id="5605"></path>
                        <path d="M506.517211 552.904411c18.297417-0.272091 31.814217-1.831497 31.814218-10.693485 0-10.503314-18.993737-27.285211-42.422858-27.285212s-42.422857 16.781897-42.422857 27.285212c0 8.861989 13.525577 10.421394 31.82592 10.693485 0 0.260389 0.008777 0.526629 0.032183 0.792869 0.318903 3.777097-0.3072 10.283886-2.638994 17.34656-2.311314 7.007086-6.053303 13.80352-11.328366 18.727497-5.102446 4.763063-11.85792 8.010606-21.0944 7.92576-9.48224-0.087771-22.250057-3.703954-38.955886-14.145828a8.777143 8.777143 0 0 0-9.303771 14.886034c18.402743 11.500983 34.333257 16.685349 48.098743 16.811154 14.005394 0.128731 25.041189-4.99712 33.233188-12.644937a55.129234 55.129234 0 0 0 9.104823-11.158674c0.637806 1.301943 1.357531 2.586331 2.173806 3.844388 10.500389 16.211383 32.72704 22.77376 71.527863 16.05632a8.777143 8.777143 0 1 0-2.995932-17.296823c-37.267749 6.4512-49.406537-1.521371-53.80096-8.303177-2.513189-3.879497-3.639589-8.891246-3.856091-14.76608-0.181394-4.888869 0.26624-9.631451 0.699246-14.25408 0.096549-1.006446 0.190171-2.00704 0.277943-2.998857 0.023406-0.277943 0.035109-0.550034 0.032182-0.822126z" fill="#634011" p-id="5606"></path>
                    </svg>
                    猫友圈管理
                </h1>
                <p class="page-subtitle">管理猫友圈的动态、评论、点赞、收藏和敏感词</p>
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row">
        <div class="col-lg-3 col-md-6">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3 class="card-title">{{ stats.total_posts or 0 }}</h3>
                            <p class="card-text">总动态数</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-edit fa-2x"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small>今日新增: {{ stats.today_posts or 0 }}</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3 class="card-title">{{ stats.total_comments or 0 }}</h3>
                            <p class="card-text">总评论数</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-comments fa-2x"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small>今日新增: {{ stats.today_comments or 0 }}</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3 class="card-title">{{ stats.total_likes or 0 }}</h3>
                            <p class="card-text">总点赞数</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-heart fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3 class="card-title">{{ stats.total_collections or 0 }}</h3>
                            <p class="card-text">总收藏数</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-star fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 动态类型统计 -->
    <div class="row mt-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">动态类型分布</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">动态 (dynamic)</label>
                                <div class="progress">
                                    <div class="progress-bar bg-primary" style="width: {{ (stats.post_types.dynamic or 0) / (stats.total_posts or 1) * 100 }}%">
                                        {{ stats.post_types.dynamic or 0 }}
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">二手交易 (secondhand)</label>
                                <div class="progress">
                                    <div class="progress-bar bg-success" style="width: {{ (stats.post_types.secondhand or 0) / (stats.total_posts or 1) * 100 }}%">
                                        {{ stats.post_types.secondhand or 0 }}
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">求助 (help)</label>
                                <div class="progress">
                                    <div class="progress-bar bg-info" style="width: {{ (stats.post_types.help or 0) / (stats.total_posts or 1) * 100 }}%">
                                        {{ stats.post_types.help or 0 }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">失物招领 (lost_found)</label>
                                <div class="progress">
                                    <div class="progress-bar bg-warning" style="width: {{ (stats.post_types.lost_found or 0) / (stats.total_posts or 1) * 100 }}%">
                                        {{ stats.post_types.lost_found or 0 }}
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">猫友 (cat_friends)</label>
                                <div class="progress">
                                    <div class="progress-bar progress-bar-purple" style="width: {{ (stats.post_types.cat_friends or 0) / (stats.total_posts or 1) * 100 }}%">
                                        {{ stats.post_types.cat_friends or 0 }}
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">校园跑 (campus_run)</label>
                                <div class="progress">
                                    <div class="progress-bar bg-secondary" style="width: {{ (stats.post_types.campus_run or 0) / (stats.total_posts or 1) * 100 }}%">
                                        {{ stats.post_types.campus_run or 0 }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">敏感词管理</h5>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <h3 class="text-purple">{{ stats.total_sensitive_words or 0 }}</h3>
                        <p class="text-muted">敏感词总数</p>
                        <a href="/admin/cat-circle/keywords" class="btn btn-primary">
                            <i class="fas fa-filter"></i> 管理敏感词
                        </a>
                    </div>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title">快速操作</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="/admin/cat-circle/posts" class="btn btn-outline-primary">
                            <i class="fas fa-edit"></i> 管理动态
                        </a>
                        <a href="/admin/cat-circle/comments" class="btn btn-outline-success">
                            <i class="fas fa-comments"></i> 管理评论
                        </a>
                        <a href="/admin/cat-circle/likes" class="btn btn-outline-info">
                            <i class="fas fa-heart"></i> 管理点赞
                        </a>
                        <a href="/admin/cat-circle/collections" class="btn btn-outline-warning">
                            <i class="fas fa-star"></i> 管理收藏
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.page-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.page-title {
    margin-bottom: 0.5rem;
    color: #495057;
}

.page-subtitle {
    color: #6c757d;
    margin-bottom: 0;
}

.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.progress {
    height: 1.5rem;
}

.progress-bar {
    font-size: 0.875rem;
    line-height: 1.5rem;
}
</style>
{% endblock %}

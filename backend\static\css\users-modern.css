/* Users Panel Modern CSS - Enhanced Visual Design */
:root {
  --primary-color: #4e73df;
  --primary-light: #eaedff;
  --secondary-color: #6d757d;
  --success-color: #2dce89;
  --info-color: #11cdef;
  --warning-color: #ffb84c;
  --danger-color: #f5365c;
  --light-color: #f8f9fe;
  --dark-color: #172b4d;
  --white: #ffffff;
  
  --gradient-primary: linear-gradient(87deg, #5e72e4 0, #825ee4 100%);
  --gradient-success: linear-gradient(87deg, #2dce89 0, #2fcca0 100%);
  
  --shadow-sm: 0 .125rem .25rem rgba(0,0,0,.075);
  --shadow: 0 .5rem 1rem rgba(0,0,0,.08);
  --shadow-lg: 0 1rem 3rem rgba(0,0,0,.1);
  
  --card-border-radius: 1rem;
  --transition-normal: all 0.3s ease;
}

/* User stats cards */
.user-stats-card {
  border: none;
  border-radius: var(--card-border-radius);
  background: var(--white);
  box-shadow: var(--shadow);
  transition: var(--transition-normal);
  overflow: hidden;
  position: relative;
  z-index: 1;
  padding: 1.5rem;
}

.user-stats-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.user-stats-card::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 150px;
  height: 100%;
  background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(246,249,252,1) 100%);
  opacity: 0.8;
  z-index: -1;
}

.user-stats-card .icon {
  width: 55px;
  height: 55px;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-bottom: 1.25rem;
  transition: transform 0.3s ease;
}

.user-stats-card:hover .icon {
  transform: scale(1.1) rotate(5deg);
}

.user-stats-card .card-value {
  font-size: 2.25rem;
  font-weight: 700;
  color: #32325d;
  margin-bottom: 0.5rem;
}

.user-stats-card .card-title {
  color: #8898aa;
  font-size: 1rem;
  font-weight: 600;
}

/* User search and filter section */
.user-search-section {
  background: var(--white);
  border-radius: var(--card-border-radius);
  box-shadow: var(--shadow);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.user-search-input {
  border-radius: 10px;
  border: 1px solid #e9ecef;
  padding: 0.75rem 1rem;
  font-size: 0.9rem;
  transition: var(--transition-normal);
  background-color: #f8f9fa;
}

.user-search-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.15);
  background-color: var(--white);
}

.filter-dropdown select {
  border-radius: 10px;
  border: 1px solid #e9ecef;
  padding: 0.75rem 1rem;
  font-size: 0.9rem;
  transition: var(--transition-normal);
  background-color: #f8f9fa;
  width: 100%;
}

.filter-dropdown select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.15);
  background-color: var(--white);
}

.filter-button {
  border-radius: 10px;
  padding: 0.75rem 1.25rem;
  font-weight: 600;
  transition: var(--transition-normal);
  background: var(--gradient-primary);
  border: none;
  color: var(--white);
}

.filter-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(94, 114, 228, 0.2);
}

/* Enhanced user list */
.user-list-section {
  background: var(--white);
  border-radius: var(--card-border-radius);
  box-shadow: var(--shadow);
  overflow: hidden;
  margin-bottom: 1.5rem;
}

.user-list-header {
  background-color: #f8f9fe;
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid rgba(0,0,0,0.05);
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.user-list-body {
  padding: 0;
}

.user-list-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.user-list-table th {
  font-weight: 600;
  color: #8898aa;
  padding: 1.25rem 1rem;
  border-bottom: 1px solid #e9ecef;
  text-align: left;
}

.user-list-table td {
  padding: 1.25rem 1rem;
  vertical-align: middle;
  border-top: 1px solid #f8f9fc;
  transition: var(--transition-normal);
}

.user-list-table tbody tr {
  transition: var(--transition-normal);
}

.user-list-table tbody tr:hover {
  background-color: #f8f9fe;
}

.user-list-table tbody tr:hover td {
  border-top-color: transparent;
}

/* User avatar */
.user-avatar {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background-color: var(--primary-light);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  overflow: hidden;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* User actions */
.user-action-button {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-normal);
  background-color: var(--white);
  border: 1px solid #e9ecef;
  color: #8898aa;
}

.user-action-button:hover {
  background-color: var(--primary-color);
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(94, 114, 228, 0.2);
}

/* User view mode toggle */
.view-mode-toggle {
  display: flex;
  border-radius: 10px;
  overflow: hidden;
  background-color: #f1f3f9;
  padding: 3px;
}

.view-mode-toggle button {
  border: none;
  background: none;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  transition: var(--transition-normal);
  border-radius: 8px;
}

.view-mode-toggle button.active {
  background-color: var(--white);
  box-shadow: var(--shadow-sm);
  font-weight: 600;
}

/* User grid view */
.user-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1.5rem;
  padding: 1.5rem;
}

.user-grid-card {
  border-radius: 15px;
  background-color: var(--white);
  box-shadow: var(--shadow);
  overflow: hidden;
  transition: var(--transition-normal);
  text-align: center;
  padding: 1.5rem;
}

.user-grid-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.user-grid-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  margin: 0 auto 1rem;
  background-color: var(--primary-light);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  overflow: hidden;
}

.user-grid-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-grid-info h4 {
  margin-bottom: 0.25rem;
  font-weight: 600;
}

.user-grid-info p {
  color: #8898aa;
  margin-bottom: 1rem;
}

.user-grid-actions {
  margin-top: 1rem;
}

.user-grid-actions button {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  margin: 0 0.25rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-normal);
  background-color: var(--white);
  border: 1px solid #e9ecef;
  color: #8898aa;
}

.user-grid-actions button:hover {
  background-color: var(--primary-color);
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(94, 114, 228, 0.2);
}

/* Activity status indicator */
.activity-status {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 0.5rem;
}

.activity-status.active {
  background-color: var(--success-color);
  box-shadow: 0 0 0 3px rgba(45, 206, 137, 0.2);
}

.activity-status.inactive {
  background-color: #e9ecef;
}

/* Users active indicator in header - Hide original text and replace with Chinese */
[title="1 users active"]::before {
  content: "1 位用户在线";
  font-size: inherit;
  color: inherit;
}

[title="1 users active"] {
  font-size: 0;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .user-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  }
}

@media (max-width: 768px) {
  .user-stats-container {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .user-search-section {
    padding: 1.25rem;
  }
  
  .user-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
  }
}

@media (max-width: 576px) {
  .user-stats-container {
    grid-template-columns: 1fr;
  }
  
  .user-list-table th:nth-child(3),
  .user-list-table td:nth-child(3) {
    display: none;
  }
  
  .user-grid {
    grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
  }
} 
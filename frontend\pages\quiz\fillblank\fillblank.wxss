/* pages/quiz/fillblank/fillblank.wxss */
.container {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  height: 100vh;
  box-sizing: border-box;
  background-color: #f5f7fa;
}

.header {
  margin-bottom: 30rpx;
}

.progress-container {
  margin-bottom: 20rpx;
}

.progress-text-container {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  margin-bottom: 8rpx;
}

.progress-text {
  font-size: 26rpx;
  color: #666;
  margin-top: 10rpx;
}

.course-filter {
  margin: 20rpx 0;
}

.course-list {
  white-space: nowrap;
}

.course-item {
  display: inline-block;
  padding: 12rpx 24rpx;
  margin-right: 16rpx;
  background-color: #f0f0f0;
  border-radius: 32rpx;
  font-size: 26rpx;
}

.course-item.active {
  background-color: #009688; /* 青绿色主题 */
  color: white;
}

.quiz-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.question {
  margin-bottom: 40rpx;
}

.question-text {
  font-size: 32rpx;
  line-height: 1.5;
  font-weight: 500;
  display: block;
  margin-bottom: 10rpx;
}

.question-hint {
  font-size: 24rpx;
  color: #888;
  display: block;
}

.answer-input-container {
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  border: 1px solid #e0e0e0;
  background-color: white;
  transition: all 0.2s;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

/* 添加左侧颜色条 */
.answer-input-container::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 8rpx;
  background-color: #e0e0e0;
}

/* 输入框获取焦点时的样式 */
.answer-input-container.focused {
  border-color: #009688;
  background-color: rgba(0, 150, 136, 0.05);
}

.answer-input-container.focused::before {
  background-color: #009688;
}

.answer-input-container.correct {
  border-color: #4caf50;
  background-color: rgba(76, 175, 80, 0.05);
}

.answer-input-container.correct::before {
  background-color: #4caf50;
}

.answer-input-container.wrong {
  border-color: #f44336;
  background-color: rgba(244, 67, 54, 0.05);
}

.answer-input-container.wrong::before {
  background-color: #f44336;
}

.answer-input-container.focused .option-letter {
  background-color: #009688;
  color: white;
}

.option-letter {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background-color: #e0e0e0;
  color: #333;
  font-size: 28rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.answer-input {
  flex: 1;
  height: 80rpx;
  font-size: 32rpx;
  border: none;
  background: transparent;
}

.status-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  font-size: 36rpx;
  font-weight: bold;
  flex-shrink: 0;
}

.answer-input-container.correct .status-icon {
  color: #4caf50;
}

.answer-input-container.correct .icon-text {
  color: #4caf50;
}

.answer-input-container.wrong .status-icon {
  color: #f44336;
}

.answer-input-container.wrong .icon-text {
  color: #f44336;
}

.answer-section {
  background-color: white;
  padding: 24rpx;
  border-radius: 12rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border-left: 8rpx solid #009688; /* 青绿色主题 */
}

.correct-answer {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.answer-label {
  font-size: 28rpx;
  color: #333;
  margin-right: 10rpx;
}

.answer-value {
  display: flex;
}

.answer-pill {
  background-color: #4caf50;
  color: white;
  padding: 4rpx 16rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  margin-right: 10rpx;
}

.result-feedback {
  margin-bottom: 16rpx;
}

.success-text {
  color: #4caf50;
  font-weight: bold;
  font-size: 30rpx;
}

.error-text {
  color: #f44336;
  font-weight: bold;
  font-size: 30rpx;
}

.explanation {
  margin-top: 16rpx;
  border-top: 1px solid #eee;
  padding-top: 16rpx;
}

.explanation-text {
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
}

.action-buttons {
  margin: 20rpx 0 40rpx;
  display: flex;
  justify-content: center;
}

.next-btn-container {
  margin: 30rpx 0 10rpx;
}

.action-btn {
  width: 60%;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 45rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.submit-btn {
  background-color: #009688;
  color: white;
}

.submit-btn[disabled] {
  background-color: #b2dfdb; /* 淡青绿色 */
  color: #666666;
}

.next-btn {
  background-color: #009688;
  color: white;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.loading {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #009688; /* 青绿色主题 */
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: #666;
  font-size: 28rpx;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 70vh;
  padding: 30rpx;
}

.empty-icon {
  width: 180rpx;
  height: 180rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 30rpx;
  color: #666;
  text-align: center;
  margin-bottom: 40rpx;
}

.btn-return {
  background-color: #009688; /* 青绿色主题 */
  color: white;
  padding: 20rpx 60rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
}

.modal-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.modal {
  width: 80%;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
}

.modal-header {
  padding: 30rpx;
  border-bottom: 1px solid #eee;
  text-align: center;
}

.modal-title {
  font-size: 34rpx;
  font-weight: bold;
}

.modal-content {
  padding: 40rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.modal-footer {
  display: flex;
  border-top: 1px solid #eee;
}

.modal-btn {
  flex: 1;
  border: none;
  padding: 24rpx 0;
  font-size: 32rpx;
  border-radius: 0;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #333;
}

.confirm-btn {
  background-color: #009688; /* 青绿色主题 */
  color: white;
} 
/* all-courses.wxss */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 30rpx;
  padding-top: 0;
}

/* 顶部标题栏 */
.header {
  background-color: #4e8df7;
  color: white;
  padding: 120rpx 30rpx 40rpx;
  border-radius: 0 0 30rpx 30rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 24rpx;
  position: relative;
  z-index: 1;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.back-btn {
  padding: 12rpx;
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transition: all 0.3s;
}

.back-btn:active {
  transform: scale(0.9);
  background-color: rgba(255, 255, 255, 0.3);
}

.back-icon {
  font-size: 34rpx;
  font-weight: bold;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  flex: 1;
  text-align: center;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.placeholder {
  width: 64rpx;
}

/* 课程网格 */
.courses-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  padding: 0 24rpx;
}

.course-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s;
  border: 1rpx solid rgba(78, 141, 247, 0.08);
  position: relative;
  overflow: hidden;
}

.course-card::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4rpx;
  background: linear-gradient(90deg, #4e8df7, #5e66ff);
  border-radius: 4rpx;
}

.course-card:active {
  transform: scale(0.95);
  opacity: 0.9;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

.course-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 24rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16rpx;
  color: white;
  box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.course-icon::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 30%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 100%);
}

.course-icon-text {
  font-size: 40rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.course-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.course-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
  text-align: center;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.course-stats {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.course-question-count {
  font-size: 24rpx;
  color: #666;
  background-color: rgba(78, 141, 247, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 30rpx;
  font-weight: 500;
  margin-bottom: 6rpx;
}

.course-question-type {
  font-size: 22rpx;
  color: #888;
  margin-top: 4rpx;
}

/* 空状态 */
.empty-courses {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #666;
  margin-bottom: 16rpx;
}

.empty-subtext {
  font-size: 28rpx;
  color: #999;
}

/* 加载提示 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 999;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.loading {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid rgba(78, 141, 247, 0.1);
  border-top: 6rpx solid #4e8df7;
  border-radius: 50%;
  margin-bottom: 20rpx;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
} 
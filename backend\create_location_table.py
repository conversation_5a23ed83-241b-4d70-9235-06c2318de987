#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建定位消息表的数据库迁移脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from cat_circle_api import init_chat_models

def create_location_table():
    """创建定位消息表"""
    try:
        with app.app_context():
            # 初始化聊天模型
            init_chat_models()
            
            # 创建表
            db.create_all()
            
            print("✅ 定位消息表创建成功！")
            
            # 显示创建的表信息
            print("\n📋 已创建的表：")
            print("- location_messages: 定位消息详细信息表")
            
            print("\n📝 表结构说明：")
            print("location_messages 表字段：")
            print("  - id: 主键")
            print("  - message_id: 关联到chat_messages表的消息ID")
            print("  - latitude: 纬度（精度到8位小数）")
            print("  - longitude: 经度（精度到8位小数）")
            print("  - location_name: 位置名称")
            print("  - address: 详细地址")
            print("  - poi_id: POI ID")
            print("  - city: 城市")
            print("  - district: 区域")
            print("  - province: 省份")
            print("  - country: 国家（默认：中国）")
            print("  - accuracy: 定位精度（米）")
            print("  - altitude: 海拔高度（米）")
            print("  - speed: 速度（米/秒）")
            print("  - status: 状态（默认：active）")
            print("  - created_at: 创建时间")
            print("  - updated_at: 更新时间")
            
    except Exception as e:
        print(f"❌ 创建定位消息表失败: {e}")
        return False
    
    return True

def check_table_exists():
    """检查表是否已存在"""
    try:
        with app.app_context():
            # 检查表是否存在
            result = db.engine.execute("SHOW TABLES LIKE 'location_messages'")
            exists = result.fetchone() is not None
            
            if exists:
                print("📋 location_messages 表已存在")
                
                # 显示表结构
                result = db.engine.execute("DESCRIBE location_messages")
                columns = result.fetchall()
                
                print("\n🔍 当前表结构：")
                for column in columns:
                    print(f"  - {column[0]}: {column[1]} {column[2]} {column[3]} {column[4]} {column[5]}")
                    
                return True
            else:
                print("📋 location_messages 表不存在，需要创建")
                return False
                
    except Exception as e:
        print(f"❌ 检查表失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始创建定位消息表...")
    print("=" * 50)
    
    # 检查表是否已存在
    if check_table_exists():
        print("\n✅ 表已存在，无需重复创建")
        return
    
    # 创建表
    if create_location_table():
        print("\n🎉 定位消息表创建完成！")
        print("\n📱 现在可以在小程序中使用定位功能了：")
        print("  1. 在聊天界面点击 + 按钮")
        print("  2. 选择定位选项")
        print("  3. 选择要发送的位置")
        print("  4. 发送定位消息")
        print("  5. 点击定位消息可以打开地图查看")
    else:
        print("\n❌ 定位消息表创建失败！")

if __name__ == '__main__':
    main()

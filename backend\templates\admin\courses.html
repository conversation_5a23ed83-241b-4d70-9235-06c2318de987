{% extends "admin/base.html" %}

{% block title %}课程管理 - 题库管理系统{% endblock %}

{% block header %}课程管理{% endblock %}

{% block header_buttons %}
<button type="button" class="btn btn-primary" id="openAddCourseModal">
    <i class="fas fa-plus me-1"></i>添加课程
</button>
{% endblock %}

{% block content %}
<!-- 课程统计 -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body stats-card">
                <div class="number">{{ courses|length }}</div>
                <div class="label">课程总数</div>
            </div>
        </div>
    </div>
</div>

<!-- 搜索栏 -->
<div class="card shadow-sm mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="input-group">
                    <input type="text" id="searchInput" class="form-control" placeholder="输入课程名称搜索...">
                    <button class="btn btn-primary" type="button" id="searchButton">
                        <i class="fas fa-search"></i> 搜索
                    </button>
                </div>
            </div>
            <div class="col-md-6 text-end">
                <button type="button" class="btn btn-primary me-2" id="openAddCourseModalBtn">
                    <i class="fas fa-plus me-1"></i>添加课程
                </button>
                <button class="btn btn-outline-secondary" type="button" id="resetButton">
                    <i class="fas fa-redo-alt"></i> 重置
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 课程列表 -->
<div class="card shadow-sm">
    <div class="card-header bg-white">
        <div class="d-flex align-items-center">
            <i class="fas fa-book me-2 text-primary"></i>
            <h5 class="mb-0 fw-bold">课程列表</h5>
        </div>
    </div>
    <div class="card-body position-relative">
        <div class="table-responsive">
            <table class="table table-hover" id="coursesTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>课程名称</th>
                        <th>所属班级</th>
                        <th>描述</th>
                        <th>题库数量</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for course in courses %}
                    <tr id="course-row-{{ course.id }}" data-id="{{ course.id }}">
                        <td>{{ course.id }}</td>
                        <td>{{ course.name }}</td>
                        <td>{{ course.class_.name }}</td>
                        <td>
                            <div class="description-text" title="{{ course.description }}">
                                {{ course.description or '无' }}
                            </div>
                        </td>
                        <td>{{ course.question_banks|length }}</td>
                        <td>{{ course.created_at.strftime('%Y-%m-%d %H:%M:%S') if course.created_at else '-' }}</td>
                        <td>
                            <a href="{{ url_for('edit_course', course_id=course.id) }}" class="btn btn-sm btn-outline-primary me-2">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button type="button" class="btn btn-sm btn-outline-danger delete-btn" data-id="{{ course.id }}" data-name="{{ course.name }}" data-bank-count="{{ course.question_banks|length }}" data-bs-toggle="tooltip" title="删除课程">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- 删除确认面板 -->
        <div id="delete-confirm-panel" class="delete-confirm-panel" style="display: none; position: absolute; z-index: 1000;">
            <div class="p-3">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="fas fa-exclamation-triangle text-warning"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="mb-2">
                            <strong>确定要删除课程: <span id="delete-course-name" class="text-danger"></span>?</strong>
                        </div>
                        <div id="delete-warning" class="alert alert-warning py-2 mb-3" style="display: none;">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-info-circle text-warning me-2"></i>
                                <div>
                                    <small>此课程有关联题库，删除将会检验关联关系。</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="ms-auto">
                        <button type="button" class="btn btn-sm btn-secondary cancel-delete-btn me-2">
                            <i class="fas fa-times me-1"></i>取消
                        </button>
                        <form id="delete-form" action="" method="post" class="d-inline delete-form">
                            <button type="submit" id="confirm-delete-btn" class="btn btn-sm btn-danger">
                                <i class="fas fa-trash me-1"></i>确认删除
                            </button>
                        </form>
                        <button type="button" id="disabled-delete-btn" class="btn btn-sm btn-danger" style="display: none;" disabled>
                            <i class="fas fa-trash me-1"></i>确认删除
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加课程模态框 -->
<div class="modal fade" id="addCourseModal" tabindex="-1" aria-labelledby="addCourseModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addCourseModalLabel">
                    <i class="fas fa-plus text-primary me-2"></i>添加课程
                </h5>
                <button type="button" class="btn-close" id="closeModalBtn" aria-label="Close"></button>
            </div>
            <form id="addCourseForm" method="post" action="/admin/courses/add">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="courseName" class="form-label">课程名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="courseName" name="name" required>
                        <div class="form-text">例如：数据库原理、机器学习、Web开发等</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="courseClass" class="form-label">所属班级 <span class="text-danger">*</span></label>
                        <select class="form-select" id="courseClass" name="class_id" required>
                            <option value="">请选择班级</option>
                            {% for class_obj in classes %}
                            <option value="{{ class_obj.id }}">{{ class_obj.name }}</option>
                            {% endfor %}
                        </select>
                        <div class="form-text">选择该课程所属的班级</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="courseDescription" class="form-label">课程描述</label>
                        <textarea class="form-control" id="courseDescription" name="description" rows="3"></textarea>
                        <div class="form-text">可选：对课程的详细描述</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" id="cancelModalBtn">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>添加课程
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // 初始化数据表格 - 使用内联中文配置
        const dataTable = $('#coursesTable').DataTable({
            language: {
                "processing": "处理中...",
                "lengthMenu": "每页显示 _MENU_ 条记录",
                "zeroRecords": "没有找到匹配的记录",
                "info": "第 _PAGE_ 页 / 共 _PAGES_ 页 ( 共 _TOTAL_ 条记录 )",
                "infoEmpty": "没有记录",
                "infoFiltered": "(从 _MAX_ 条记录中过滤)",
                "search": "搜索:",
                "emptyTable": "表中没有数据",
                "paginate": {
                    "first": "首页",
                    "previous": "上一页",
                    "next": "下一页",
                    "last": "末页"
                },
                "aria": {
                    "sortAscending": ": 升序排列",
                    "sortDescending": ": 降序排列"
                },
                "autoFill": {
                    "cancel": "取消",
                    "fill": "填充所有单元格",
                    "fillHorizontal": "填充水平单元格",
                    "fillVertical": "填充垂直单元格"
                },
                "buttons": {
                    "collection": "集合",
                    "colvis": "列可见性",
                    "copy": "复制",
                    "copyTitle": "复制到剪贴板",
                    "csv": "CSV",
                    "excel": "Excel",
                    "pdf": "PDF",
                    "print": "打印"
                }
            },
            responsive: true,
            order: [[0, 'desc']],
            pageLength: 10,  // 每页显示10条记录
            lengthMenu: [
                [10, 25, 50, 100, -1],
                ['10条', '25条', '50条', '100条', '全部']
            ],
            pagingType: "full_numbers"  // 显示所有分页按钮
        });
        
        // 初始化工具提示
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
        
        // 模态框手动初始化和事件绑定
        const addCourseModal = document.getElementById('addCourseModal');
        let courseModal;
        
        if (addCourseModal) {
            courseModal = new bootstrap.Modal(addCourseModal, {
                backdrop: true,
                keyboard: true,
                focus: true
            });
            
            // 绑定打开模态框按钮
            $('#openAddCourseModal, #openAddCourseModalBtn').on('click', function() {
                courseModal.show();
            });
            
            // 绑定关闭模态框按钮
            $('#closeModalBtn, #cancelModalBtn').on('click', function() {
                courseModal.hide();
            });
        }

        // 处理删除按钮点击
        $(document).on('click', '.delete-btn', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            console.log('Delete button clicked');
            
            // 移除其他行的高亮并隐藏确认面板
            $('.selected-for-delete').removeClass('selected-for-delete');
            $('#delete-confirm-panel').hide();
            
            // 获取课程ID和名称
            const courseId = $(this).data('id');
            const courseName = $(this).data('name');
            const bankCount = $(this).data('bank-count');
            
            console.log('Course ID to delete:', courseId, 'Name:', courseName, 'Bank count:', bankCount);
            
            // 高亮当前行
            const currentRow = $(this).closest('tr');
            currentRow.addClass('selected-for-delete');
            
            // 更新确认面板
            $('#delete-course-name').text(courseName);
            $('#delete-form').attr('action', `/admin/courses/delete/${courseId}`);
            
            // 显示/隐藏警告和禁用按钮
            if (bankCount > 0) {
                $('#delete-warning').show();
                $('#confirm-delete-btn').hide();
                $('#disabled-delete-btn').show();
            } else {
                $('#delete-warning').hide();
                $('#confirm-delete-btn').show();
                $('#disabled-delete-btn').hide();
            }
            
            // 计算位置 - 在当前行下方
            const rowPos = currentRow.position();
            const rowHeight = currentRow.outerHeight();
            
            // 设置确认面板位置并显示
            $('#delete-confirm-panel').css({
                'top': (rowPos.top + rowHeight) + 'px'
            }).fadeIn(200);
        });
        
        // 处理取消按钮点击
        $(document).on('click', '.cancel-delete-btn', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            console.log('Cancel delete clicked');
            
            // 隐藏确认面板
            $('#delete-confirm-panel').fadeOut(200);
            
            // 移除高亮
            $('.selected-for-delete').removeClass('selected-for-delete');
        });
        
        // 点击页面其他区域关闭确认面板
        $(document).on('click', function(e) {
            if (!$(e.target).closest('#delete-confirm-panel').length && 
                !$(e.target).closest('.delete-btn').length) {
                $('#delete-confirm-panel').fadeOut(200);
                $('.selected-for-delete').removeClass('selected-for-delete');
            }
        });
        
        // 搜索功能
        $('#searchButton').on('click', function() {
            const searchValue = $('#searchInput').val().trim();
            if (searchValue) {
                $.ajax({
                    url: '/admin/courses/search',
                    type: 'GET',
                    data: { query: searchValue },
                    success: function(response) {
                        // 清空表格
                        dataTable.clear();
                        
                        // 添加搜索结果到表格
                        if (response.courses && response.courses.length > 0) {
                            // 更新表格数据
                            response.courses.forEach(function(course) {
                                const bankCount = course.question_bank_count || 0;
                                const rowNode = dataTable.row.add([
                                    course.id,
                                    course.name,
                                    course.class_name,
                                    `<div class="description-text" title="${course.description || ''}">
                                        ${course.description || '无'}
                                    </div>`,
                                    bankCount,
                                    `<small class="text-muted">
                                        <i class="far fa-calendar-alt me-1"></i>
                                        ${course.created_at}
                                    </small>`,
                                    `<div class="d-flex">
                                        <a href="/admin/courses/edit/${course.id}" class="btn btn-sm btn-outline-primary me-2" data-bs-toggle="tooltip" title="编辑课程">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger delete-btn" data-id="${course.id}" data-name="${course.name}" data-bank-count="${bankCount}" data-bs-toggle="tooltip" title="删除课程">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>`
                                ]).draw().node();
                            });
                        } else {
                            // 如果没有搜索结果
                            alert('没有找到匹配的课程！');
                            // 重新加载页面以显示所有课程
                            window.location.reload();
                        }
                    },
                    error: function() {
                        alert('搜索失败，请稍后再试！');
                    }
                });
            } else {
                // 如果搜索框为空，刷新页面
                window.location.reload();
            }
        });

        // 重置按钮
        $('#resetButton').on('click', function() {
            $('#searchInput').val('');
            window.location.reload();
        });

        // 回车键搜索
        $('#searchInput').on('keypress', function(e) {
            if (e.which === 13) {
                $('#searchButton').click();
                e.preventDefault();
            }
        });

        // 添加课程表单提交
        $('#addCourseForm').on('submit', function(e) {
            e.preventDefault();
            
            // 验证表单数据
            const courseName = $('#courseName').val().trim();
            const classId = $('#courseClass').val();
            
            if (!courseName) {
                alert('请输入课程名称');
                return false;
            }
            
            if (!classId) {
                alert('请选择班级');
                return false;
            }
            
            const formData = $(this).serialize();
            
            $.ajax({
                url: '/admin/courses/add',
                type: 'POST',
                data: formData,
                success: function(response) {
                    // 关闭模态框
                    if (courseModal) {
                        courseModal.hide();
                    }
                    
                    // 显示成功消息并刷新页面
                    setTimeout(function() {
                        window.location.reload();
                    }, 300);
                },
                error: function(xhr) {
                    let errorMessage = '添加课程失败，请稍后再试！';
                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        errorMessage = xhr.responseJSON.error;
                    }
                    alert(errorMessage);
                }
            });
        });
    });
</script>
{% endblock %}

{% block extra_css %}
<style>
    /* 描述文本超长省略 */
    .description-text {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 250px;
    }
    
    /* 删除确认面板样式 */
    .delete-confirm-panel {
        background-color: #fff2f2;
        border: 1px solid #ffdbdb;
        border-left: 4px solid #f56c6c;
        border-radius: 4px;
        margin: 0;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        width: 100%;
        left: 0;
        z-index: 100;
    }
    
    /* 高亮被选中的行 */
    .selected-for-delete {
        background-color: #fff9f9 !important;
    }
    
    .delete-confirm-panel .text-warning {
        color: #e6a23c !important;
    }
    
    .delete-confirm-panel .text-danger {
        color: #f56c6c !important;
    }
    
    .delete-confirm-panel .alert {
        background-color: #fdf6ec;
        border-color: #faecd8;
        color: #e6a23c;
        padding: 8px 12px;
        margin-bottom: 10px;
        font-size: 13px;
    }
    
    .delete-confirm-panel .btn-secondary {
        background-color: #909399;
        border-color: #909399;
    }
    
    .delete-confirm-panel .btn-danger {
        background-color: #f56c6c;
        border-color: #f56c6c;
    }
</style>
{% endblock %} 
// course.js
const app = getApp();

// 生成颜色的深色版本
function getDarkerColor(hexColor) {
  // 如果没有提供颜色或格式不正确，返回默认深色
  if (!hexColor || hexColor.length !== 7 || hexColor[0] !== '#') {
    return '#3c78e0';
  }
  
  // 将hex转换为RGB
  let r = parseInt(hexColor.substring(1, 3), 16);
  let g = parseInt(hexColor.substring(3, 5), 16);
  let b = parseInt(hexColor.substring(5, 7), 16);
  
  // 将每个颜色通道减暗约20%
  r = Math.max(0, Math.floor(r * 0.8));
  g = Math.max(0, Math.floor(g * 0.8));
  b = Math.max(0, Math.floor(b * 0.8));
  
  // 转回hex格式
  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
}

// 洗牌算法，用于随机打乱数组
function shuffleArray(array) {
  // 创建副本避免修改原数组
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

Page({
  data: {
    courseId: null,
    courseName: '',
    courseColor: '#4e8df7',
    courseColorDarker: '#3c78e0',
    singleQuestions: [],
    multipleQuestions: [],
    judgmentQuestions: [],
    fillBlankQuestions: [],
    singleCount: 0,
    multipleCount: 0,
    judgmentCount: 0,
    fillBlankCount: 0,
    isLoading: true,
    statusBarHeight: 0,
    isRandomMode: false // 默认为顺序模式
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    // 获取系统信息，包括状态栏高度
    wx.getSystemInfo({
      success: (res) => {
        // 保存状态栏高度，用于样式设置
        this.setData({
          statusBarHeight: res.statusBarHeight
        });
        
        console.log('系统信息:', res.statusBarHeight, res.windowWidth, res.windowHeight);
      }
    });
    
    // 获取传递的课程ID和名称
    const courseId = options.id;
    const courseName = options.name || '课程';
    
    // 获取存储的课程信息，包括颜色
    const selectedCourse = wx.getStorageSync('selectedCourse') || {};
    const courseColor = selectedCourse.color || '#4e8df7';
    const courseColorDarker = getDarkerColor(courseColor);
    
    // 尝试从缓存获取随机/顺序模式设置
    const isRandomMode = wx.getStorageSync('questionOrderMode') === 'random';
    
    this.setData({
      courseId: courseId,
      courseName: decodeURIComponent(courseName),
      courseColor: courseColor,
      courseColorDarker: courseColorDarker,
      isLoading: true,
      isRandomMode: isRandomMode
    });
    
    console.log('加载课程:', courseId, courseName, courseColor, '模式:', isRandomMode ? '随机' : '顺序');
    
    // 获取课程题目
    this.fetchCourseQuestions();
    
    // 设置导航栏标题和样式
    wx.setNavigationBarTitle({
      title: decodeURIComponent(courseName)
    });
    
    // 设置状态栏样式为白色文字
    wx.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: courseColor
    });
  },
  
  onShow: function() {
    // 如果有课程ID，刷新题目数据
    if (this.data.courseId) {
      this.fetchCourseQuestions();
    }
  },
  
  // 获取课程题目
  fetchCourseQuestions: function() {
    const courseId = this.data.courseId;
    if (!courseId) {
      console.error('缺少课程ID');
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
      return;
    }
    
    this.setData({ isLoading: true });
    
    this.fetchSingleQuestions()
      .then(() => this.fetchMultipleQuestions())
      .then(() => this.fetchJudgmentQuestions())
      .then(() => this.fetchFillBlankQuestions())
      .catch(err => {
        console.error('获取课程题目失败:', err);
        this.setData({ isLoading: false });
        wx.showToast({
          title: '获取题目失败',
          icon: 'none'
        });
      });
  },
  
  // 获取单选题
  fetchSingleQuestions: function() {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${app.globalData.baseUrl}/course/${this.data.courseId}/questions/single`,
        method: 'GET',
        header: {
          'Authorization': app.globalData.token
        },
        success: (res) => {
          console.log('获取课程单选题:', res.data);
          
          if (Array.isArray(res.data)) {
            this.setData({
              singleQuestions: res.data,
              singleCount: res.data.length
            });
            
            // 缓存题目数据
            wx.setStorageSync('currentSingleQuestions', res.data);
            
            resolve(res.data);
          } else {
            console.error('获取单选题失败:', res.data);
            reject(new Error('获取单选题失败'));
          }
        },
        fail: (err) => {
          console.error('获取单选题请求失败:', err);
          reject(err);
        }
      });
    });
  },
  
  // 获取多选题
  fetchMultipleQuestions: function() {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${app.globalData.baseUrl}/course/${this.data.courseId}/questions/multiple`,
        method: 'GET',
        header: {
          'Authorization': app.globalData.token
        },
        success: (res) => {
          console.log('获取课程多选题:', res.data);
          
          if (Array.isArray(res.data)) {
            this.setData({
              multipleQuestions: res.data,
              multipleCount: res.data.length,
              isLoading: false
            });
            
            // 缓存题目数据
            wx.setStorageSync('currentMultipleQuestions', res.data);
            
            resolve(res.data);
          } else {
            console.error('获取多选题失败:', res.data);
            this.setData({ isLoading: false });
            reject(new Error('获取多选题失败'));
          }
        },
        fail: (err) => {
          console.error('获取多选题请求失败:', err);
          this.setData({ isLoading: false });
          reject(err);
        }
      });
    });
  },
  
  // 获取判断题
  fetchJudgmentQuestions: function() {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${app.globalData.baseUrl}/course/${this.data.courseId}/questions/judgment`,
        method: 'GET',
        header: {
          'Authorization': app.globalData.token
        },
        success: (res) => {
          console.log('获取课程判断题:', res.data);
          
          if (Array.isArray(res.data)) {
            this.setData({
              judgmentQuestions: res.data,
              judgmentCount: res.data.length
            });
            
            // 缓存题目数据
            wx.setStorageSync('currentJudgmentQuestions', res.data);
            
            resolve(res.data);
          } else {
            console.error('获取判断题失败:', res.data);
            reject(new Error('获取判断题失败'));
          }
        },
        fail: (err) => {
          console.error('获取判断题请求失败:', err);
          reject(err);
        }
      });
    });
  },
  
  // 获取填空题
  fetchFillBlankQuestions: function() {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${app.globalData.baseUrl}/course/${this.data.courseId}/questions/fillblank`,
        method: 'GET',
        header: {
          'Authorization': app.globalData.token
        },
        success: (res) => {
          console.log('获取课程填空题:', res.data);
          
          if (Array.isArray(res.data)) {
            this.setData({
              fillBlankQuestions: res.data,
              fillBlankCount: res.data.length,
              isLoading: false
            });
            
            // 缓存题目数据
            wx.setStorageSync('currentFillBlankQuestions', res.data);
            
            resolve(res.data);
          } else {
            console.error('获取填空题失败:', res.data);
            this.setData({ isLoading: false });
            reject(new Error('获取填空题失败'));
          }
        },
        fail: (err) => {
          console.error('获取填空题请求失败:', err);
          this.setData({ isLoading: false });
          reject(err);
        }
      });
    });
  },
  
  // 切换题目顺序模式
  toggleQuestionOrder: function() {
    const newMode = !this.data.isRandomMode;
    this.setData({
      isRandomMode: newMode
    });
    
    // 保存设置到缓存
    wx.setStorageSync('questionOrderMode', newMode ? 'random' : 'order');
    
    // 显示提示
    wx.showToast({
      title: newMode ? '已切换到随机模式' : '已切换到顺序模式',
      icon: 'none',
      duration: 1500
    });
    
    console.log('切换题目顺序模式:', newMode ? '随机' : '顺序');
  },
  
  // 开始背题模式
  startReviewMode: function() {
    if (this.data.singleCount === 0 && this.data.multipleCount === 0 && 
        this.data.judgmentCount === 0 && this.data.fillBlankCount === 0) {
      wx.showToast({
        title: '暂无题目可背诵',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '准备中...',
    });
    
    // 保存课程信息
    wx.setStorageSync('currentCourse', {
      id: this.data.courseId,
      name: this.data.courseName,
      color: this.data.courseColor
    });
    
    // 导航到背题模式页面
    wx.navigateTo({
      url: `/pages/quiz/review/review?id=${this.data.courseId}&name=${encodeURIComponent(this.data.courseName)}`,
      success: () => {
        wx.hideLoading();
      },
      fail: (err) => {
        console.error('跳转失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },
  
  // 开始单选题
  startSingleQuiz: function() {
    if (this.data.singleQuestions.length === 0) {
      wx.showToast({
        title: '暂无单选题',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '准备中...',
    });
    
    // 根据当前模式决定是否打乱题目顺序
    let questions = this.data.singleQuestions;
    if (this.data.isRandomMode) {
      questions = shuffleArray(questions);
      console.log('随机模式：已打乱单选题顺序');
    }
    
    // 保存课程信息和题目
    wx.setStorageSync('currentQuestions', questions);
    wx.setStorageSync('currentCourse', {
      id: this.data.courseId,
      name: this.data.courseName,
      color: this.data.courseColor
    });
    
    // 导航到单选题页面并传递课程ID和标题
    wx.navigateTo({
      url: `/pages/quiz/single/single?mode=course&courseId=${this.data.courseId}&courseTitle=${encodeURIComponent(this.data.courseName)}`,
      success: () => {
        wx.hideLoading();
      },
      fail: (err) => {
        console.error('跳转失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },
  
  // 开始多选题
  startMultipleQuiz: function() {
    if (this.data.multipleQuestions.length === 0) {
      wx.showToast({
        title: '暂无多选题',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '准备中...',
    });
    
    // 根据当前模式决定是否打乱题目顺序
    let questions = this.data.multipleQuestions;
    if (this.data.isRandomMode) {
      questions = shuffleArray(questions);
      console.log('随机模式：已打乱多选题顺序');
    }
    
    // 保存课程信息和题目
    wx.setStorageSync('currentQuestions', questions);
    wx.setStorageSync('currentCourse', {
      id: this.data.courseId,
      name: this.data.courseName,
      color: this.data.courseColor
    });
    
    // 导航到多选题页面并传递课程ID和标题
    wx.navigateTo({
      url: `/pages/quiz/multiple/multiple?mode=course&courseId=${this.data.courseId}&courseTitle=${encodeURIComponent(this.data.courseName)}`,
      success: () => {
        wx.hideLoading();
      },
      fail: (err) => {
        console.error('跳转失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },
  
  // 开始判断题
  startJudgmentQuiz: function() {
    if (this.data.judgmentQuestions.length === 0) {
      wx.showToast({
        title: '暂无判断题',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '准备中...',
    });
    
    // 根据当前模式决定是否打乱题目顺序
    let questions = this.data.judgmentQuestions;
    if (this.data.isRandomMode) {
      questions = shuffleArray(questions);
      console.log('随机模式：已打乱判断题顺序');
    }
    
    // 保存课程信息和题目
    wx.setStorageSync('currentQuestions', questions);
    wx.setStorageSync('currentCourse', {
      id: this.data.courseId,
      name: this.data.courseName,
      color: this.data.courseColor
    });
    
    // 导航到判断题页面并传递课程ID和标题
    wx.navigateTo({
      url: `/pages/quiz/judgment/judgment?mode=course&courseId=${this.data.courseId}&courseTitle=${encodeURIComponent(this.data.courseName)}`,
      success: () => {
        wx.hideLoading();
      },
      fail: (err) => {
        console.error('跳转失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },
  
  // 开始填空题
  startFillBlankQuiz: function() {
    if (this.data.fillBlankQuestions.length === 0) {
      wx.showToast({
        title: '暂无填空题',
        icon: 'none'
      });
      return;
    }
    
    wx.showLoading({
      title: '准备中...',
    });
    
    // 根据当前模式决定是否打乱题目顺序
    let questions = this.data.fillBlankQuestions;
    if (this.data.isRandomMode) {
      questions = shuffleArray(questions);
      console.log('随机模式：已打乱填空题顺序');
    }
    
    // 保存课程信息和题目
    wx.setStorageSync('currentQuestions', questions);
    wx.setStorageSync('currentCourse', {
      id: this.data.courseId,
      name: this.data.courseName,
      color: this.data.courseColor
    });
    
    // 导航到填空题页面并传递课程ID和标题
    wx.navigateTo({
      url: `/pages/quiz/fillblank/fillblank?mode=course&courseId=${this.data.courseId}&courseTitle=${encodeURIComponent(this.data.courseName)}`,
      success: () => {
        wx.hideLoading();
      },
      fail: (err) => {
        console.error('跳转失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },
  
  // 开始考试模式
  startExamMode: function() {
    const singleQuestions = this.data.singleQuestions;
    const multipleQuestions = this.data.multipleQuestions;
    const judgmentQuestions = this.data.judgmentQuestions;
    const fillBlankQuestions = this.data.fillBlankQuestions;
    
    const totalQuestions = [
      ...singleQuestions,
      ...multipleQuestions,
      ...judgmentQuestions,
      ...fillBlankQuestions
    ];
    
    if (totalQuestions.length === 0) {
      wx.showToast({
        title: '暂无题目',
        icon: 'none'
      });
      return;
    }
    
    // 移除所有题型必须存在的检查，改为至少有一种题型即可
    if (singleQuestions.length === 0 && multipleQuestions.length === 0 && 
        judgmentQuestions.length === 0 && fillBlankQuestions.length === 0) {
      wx.showToast({
        title: '至少需要一种题型才能考试',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    wx.showLoading({
      title: '准备中...',
    });
    
    // 存储所有题目数据到缓存
    wx.setStorageSync('currentSingleQuestions', singleQuestions);
    wx.setStorageSync('currentMultipleQuestions', multipleQuestions);
    wx.setStorageSync('currentJudgmentQuestions', judgmentQuestions);
    wx.setStorageSync('currentFillBlankQuestions', fillBlankQuestions);
    
    // 保存课程信息
    wx.setStorageSync('currentCourse', {
      id: this.data.courseId,
      name: this.data.courseName,
      color: this.data.courseColor
    });
    
    // 导航到考试模式页面
    wx.navigateTo({
      url: `/pages/quiz/exam/exam?courseId=${this.data.courseId}&courseTitle=${encodeURIComponent(this.data.courseName)}`,
      success: () => {
        wx.hideLoading();
      },
      fail: (err) => {
        console.error('跳转失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },
  
  // 导航到错题本
  goToWrongQuestions: function() {
    wx.switchTab({
      url: '/pages/wrong-questions/wrong-questions'
    });
  },
  
  // 导航到答题统计
  goToStatistics: function() {
    wx.switchTab({
      url: '/pages/statistics/statistics'
    });
  },
  
  // 返回上一页
  navigateBack: function() {
    wx.navigateBack();
  }
}); 
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash, session
from functools import wraps
import datetime
import json
from sqlalchemy import func, desc

# 创建蓝图
cat_circle_admin_bp = Blueprint('cat_circle_admin', __name__, url_prefix='/admin/cat-circle')

# 全局变量，将在初始化时设置
db = None
Admin = None
User = None
SensitiveWord = None
CatCirclePost = None
CatCircleLike = None
CatCircleCollection = None
CatCircleComment = None

def init_cat_circle_admin(database, admin_model, user_model):
    """初始化猫友圈管理模块"""
    global db, Admin, User, SensitiveWord, CatCirclePost, CatCircleLike, CatCircleCollection, CatCircleComment
    db = database
    Admin = admin_model
    User = user_model
    
    # 导入猫友圈模型
    try:
        from cat_circle_api import SensitiveWord as SW, CatCirclePost as CCP, CatCircleLike as CCL, CatCircleCollection as CCC, CatCircleComment as CCComment
        SensitiveWord = SW
        CatCirclePost = CCP
        CatCircleLike = CCL
        CatCircleCollection = CCC
        CatCircleComment = CCComment
        print("猫友圈管理模块初始化成功")
    except Exception as e:
        print(f"猫友圈管理模块初始化失败: {e}")

def admin_required(f):
    """管理员权限验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'admin_id' not in session:
            return redirect('/admin/login')
        return f(*args, **kwargs)
    return decorated_function

def get_post_type_label(post_type):
    """获取动态类型的中文标签"""
    type_labels = {
        'dynamic': '动态',
        'secondhand': '二手交易',
        'help': '求助',
        'lost_found': '失物招领',
        'cat_friend': '猫友',  # 注意这里是单数形式
        'cat_friends': '猫友',  # 兼容复数形式
        'campus_run': '校园跑'
    }
    return type_labels.get(post_type, post_type)

def get_post_type_badge_class(post_type):
    """获取动态类型的徽章样式类"""
    badge_classes = {
        'dynamic': 'bg-primary',
        'secondhand': 'bg-success',
        'help': 'bg-info',
        'lost_found': 'bg-warning',
        'cat_friend': 'badge-purple',  # 使用自定义紫色
        'cat_friends': 'badge-purple',  # 使用自定义紫色
        'campus_run': 'bg-secondary'
    }
    return badge_classes.get(post_type, 'bg-light text-dark')

# 猫友圈管理主页
@cat_circle_admin_bp.route('/')
@cat_circle_admin_bp.route('/dashboard')
@admin_required
def cat_circle_dashboard():
    """猫友圈管理仪表盘"""
    try:
        # 统计数据
        total_posts = CatCirclePost.query.count() if CatCirclePost else 0
        total_comments = CatCircleComment.query.count() if CatCircleComment else 0
        total_likes = CatCircleLike.query.count() if CatCircleLike else 0
        total_collections = CatCircleCollection.query.count() if CatCircleCollection else 0
        total_sensitive_words = SensitiveWord.query.count() if SensitiveWord else 0
        
        # 今日数据
        today = datetime.datetime.now().date()
        today_start = datetime.datetime.combine(today, datetime.time.min)
        today_end = datetime.datetime.combine(today, datetime.time.max)
        
        today_posts = CatCirclePost.query.filter(
            CatCirclePost.created_at.between(today_start, today_end)
        ).count() if CatCirclePost else 0
        
        today_comments = CatCircleComment.query.filter(
            CatCircleComment.created_at.between(today_start, today_end)
        ).count() if CatCircleComment else 0
        
        # 按类型统计动态
        post_types = {}
        if CatCirclePost:
            types = ['dynamic', 'secondhand', 'help', 'lost_found', 'cat_friends', 'campus_run']
            for post_type in types:
                count = CatCirclePost.query.filter_by(type=post_type).count()
                post_types[post_type] = count
        
        stats = {
            'total_posts': total_posts,
            'total_comments': total_comments,
            'total_likes': total_likes,
            'total_collections': total_collections,
            'total_sensitive_words': total_sensitive_words,
            'today_posts': today_posts,
            'today_comments': today_comments,
            'post_types': post_types
        }
        
        return render_template('admin/cat_circle/dashboard.html', stats=stats)
    except Exception as e:
        flash(f'获取统计数据失败: {str(e)}', 'danger')
        return render_template('admin/cat_circle/dashboard.html', stats={})

# 动态管理
@cat_circle_admin_bp.route('/posts')
@admin_required
def manage_posts():
    """动态管理页面"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        post_type = request.args.get('type', '')
        status = request.args.get('status', '')
        search = request.args.get('search', '')
        sort_by = request.args.get('sort', 'created_at_desc')

        # 限制每页条数范围
        if per_page not in [5, 10, 20, 50, 100]:
            per_page = 10

        # 构建查询
        query = CatCirclePost.query if CatCirclePost else None
        if not query:
            return render_template('admin/cat_circle/posts.html',
                                 posts=[],
                                 pagination=None,
                                 current_type='',
                                 current_status='',
                                 current_search='',
                                 current_sort='created_at_desc',
                                 current_per_page=10)

        # 筛选条件
        if post_type:
            query = query.filter(CatCirclePost.type == post_type)
        if status:
            query = query.filter(CatCirclePost.status == status)
        if search:
            query = query.filter(CatCirclePost.content.contains(search))

        # 排序
        if sort_by == 'created_at_desc':
            query = query.order_by(CatCirclePost.created_at.desc())
        elif sort_by == 'created_at_asc':
            query = query.order_by(CatCirclePost.created_at.asc())
        elif sort_by == 'like_count_desc':
            query = query.order_by(CatCirclePost.like_count.desc())
        elif sort_by == 'like_count_asc':
            query = query.order_by(CatCirclePost.like_count.asc())
        elif sort_by == 'comment_count_desc':
            query = query.order_by(CatCirclePost.comment_count.desc())
        elif sort_by == 'comment_count_asc':
            query = query.order_by(CatCirclePost.comment_count.asc())
        elif sort_by == 'view_count_desc':
            query = query.order_by(CatCirclePost.view_count.desc())
        elif sort_by == 'view_count_asc':
            query = query.order_by(CatCirclePost.view_count.asc())
        else:
            query = query.order_by(CatCirclePost.created_at.desc())

        # 分页
        pagination = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        posts = pagination.items

        # 获取用户信息
        for post in posts:
            if User:
                user = User.query.get(post.user_id)
                post.user = user
            else:
                post.user = None

        return render_template('admin/cat_circle/posts.html',
                             posts=posts,
                             pagination=pagination,
                             current_type=post_type,
                             current_status=status,
                             current_search=search,
                             current_sort=sort_by,
                             current_per_page=per_page)
    except Exception as e:
        flash(f'获取动态列表失败: {str(e)}', 'danger')
        return render_template('admin/cat_circle/posts.html',
                             posts=[],
                             pagination=None,
                             current_type='',
                             current_status='',
                             current_search='',
                             current_sort='created_at_desc',
                             current_per_page=10)

# 评论管理
@cat_circle_admin_bp.route('/comments')
@admin_required
def manage_comments():
    """评论管理页面"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        search = request.args.get('search', '')
        sort_by = request.args.get('sort', 'created_at_desc')
        post_type = request.args.get('post_type', '')

        # 限制每页条数范围
        if per_page not in [5, 10, 20, 50, 100]:
            per_page = 10

        # 构建查询
        query = CatCircleComment.query if CatCircleComment else None
        if not query:
            return render_template('admin/cat_circle/comments.html',
                                 comments=[],
                                 pagination=None,
                                 current_search='',
                                 current_sort='created_at_desc',
                                 current_per_page=10,
                                 current_post_type='')

        # 筛选条件
        if search:
            query = query.filter(CatCircleComment.content.contains(search))

        # 按动态类型筛选
        if post_type and CatCirclePost:
            post_ids = CatCirclePost.query.filter(CatCirclePost.type == post_type).with_entities(CatCirclePost.id).all()
            post_ids = [pid[0] for pid in post_ids]
            if post_ids:
                query = query.filter(CatCircleComment.post_id.in_(post_ids))

        # 排序
        if sort_by == 'created_at_desc':
            query = query.order_by(CatCircleComment.created_at.desc())
        elif sort_by == 'created_at_asc':
            query = query.order_by(CatCircleComment.created_at.asc())
        else:
            query = query.order_by(CatCircleComment.created_at.desc())

        # 分页
        pagination = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        comments = pagination.items

        # 获取关联信息
        for comment in comments:
            if User:
                user = User.query.get(comment.user_id)
                comment.user = user
            else:
                comment.user = None

            if CatCirclePost:
                post = CatCirclePost.query.get(comment.post_id)
                comment.post = post
            else:
                comment.post = None

        return render_template('admin/cat_circle/comments.html',
                             comments=comments,
                             pagination=pagination,
                             current_search=search,
                             current_sort=sort_by,
                             current_per_page=per_page,
                             current_post_type=post_type)
    except Exception as e:
        flash(f'获取评论列表失败: {str(e)}', 'danger')
        return render_template('admin/cat_circle/comments.html',
                             comments=[],
                             pagination=None,
                             current_search='',
                             current_sort='created_at_desc',
                             current_per_page=10,
                             current_post_type='')

# 点赞管理
@cat_circle_admin_bp.route('/likes')
@admin_required
def manage_likes():
    """点赞管理页面"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        search = request.args.get('search', '')
        sort_by = request.args.get('sort', 'created_at_desc')
        post_type = request.args.get('post_type', '')

        # 限制每页条数范围
        if per_page not in [5, 10, 20, 50, 100]:
            per_page = 10

        # 构建查询
        query = CatCircleLike.query if CatCircleLike else None
        if not query:
            return render_template('admin/cat_circle/likes.html',
                                 likes=[],
                                 pagination=None,
                                 current_search='',
                                 current_sort='created_at_desc',
                                 current_per_page=10,
                                 current_post_type='')

        # 筛选条件
        if search and User:
            # 搜索用户昵称
            user_ids = User.query.filter(User.nickname.contains(search)).with_entities(User.id).all()
            user_ids = [uid[0] for uid in user_ids]
            if user_ids:
                query = query.filter(CatCircleLike.user_id.in_(user_ids))

        # 按动态类型筛选
        if post_type and CatCirclePost:
            post_ids = CatCirclePost.query.filter(CatCirclePost.type == post_type).with_entities(CatCirclePost.id).all()
            post_ids = [pid[0] for pid in post_ids]
            if post_ids:
                query = query.filter(CatCircleLike.post_id.in_(post_ids))

        # 排序
        if sort_by == 'created_at_desc':
            query = query.order_by(CatCircleLike.created_at.desc())
        elif sort_by == 'created_at_asc':
            query = query.order_by(CatCircleLike.created_at.asc())
        else:
            query = query.order_by(CatCircleLike.created_at.desc())

        # 分页
        pagination = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        likes = pagination.items

        # 获取关联信息
        for like in likes:
            if User:
                user = User.query.get(like.user_id)
                like.user = user
            else:
                like.user = None

            if CatCirclePost:
                post = CatCirclePost.query.get(like.post_id)
                like.post = post
            else:
                like.post = None

        return render_template('admin/cat_circle/likes.html',
                             likes=likes,
                             pagination=pagination,
                             current_search=search,
                             current_sort=sort_by,
                             current_per_page=per_page,
                             current_post_type=post_type)
    except Exception as e:
        flash(f'获取点赞列表失败: {str(e)}', 'danger')
        return render_template('admin/cat_circle/likes.html',
                             likes=[],
                             pagination=None,
                             current_search='',
                             current_sort='created_at_desc',
                             current_per_page=10,
                             current_post_type='')

# 收藏管理
@cat_circle_admin_bp.route('/collections')
@admin_required
def manage_collections():
    """收藏管理页面"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        search = request.args.get('search', '')
        sort_by = request.args.get('sort', 'created_at_desc')
        post_type = request.args.get('post_type', '')

        # 限制每页条数范围
        if per_page not in [5, 10, 20, 50, 100]:
            per_page = 10

        # 构建查询
        query = CatCircleCollection.query if CatCircleCollection else None
        if not query:
            return render_template('admin/cat_circle/collections.html',
                                 collections=[],
                                 pagination=None,
                                 current_search='',
                                 current_sort='created_at_desc',
                                 current_per_page=10,
                                 current_post_type='')

        # 筛选条件
        if search and User:
            # 搜索用户昵称
            user_ids = User.query.filter(User.nickname.contains(search)).with_entities(User.id).all()
            user_ids = [uid[0] for uid in user_ids]
            if user_ids:
                query = query.filter(CatCircleCollection.user_id.in_(user_ids))

        # 按动态类型筛选
        if post_type and CatCirclePost:
            post_ids = CatCirclePost.query.filter(CatCirclePost.type == post_type).with_entities(CatCirclePost.id).all()
            post_ids = [pid[0] for pid in post_ids]
            if post_ids:
                query = query.filter(CatCircleCollection.post_id.in_(post_ids))

        # 排序
        if sort_by == 'created_at_desc':
            query = query.order_by(CatCircleCollection.created_at.desc())
        elif sort_by == 'created_at_asc':
            query = query.order_by(CatCircleCollection.created_at.asc())
        else:
            query = query.order_by(CatCircleCollection.created_at.desc())

        # 分页
        pagination = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        collections = pagination.items

        # 获取关联信息
        for collection in collections:
            if User:
                user = User.query.get(collection.user_id)
                collection.user = user
            else:
                collection.user = None

            if CatCirclePost:
                post = CatCirclePost.query.get(collection.post_id)
                collection.post = post
            else:
                collection.post = None

        return render_template('admin/cat_circle/collections.html',
                             collections=collections,
                             pagination=pagination,
                             current_search=search,
                             current_sort=sort_by,
                             current_per_page=per_page,
                             current_post_type=post_type)
    except Exception as e:
        flash(f'获取收藏列表失败: {str(e)}', 'danger')
        return render_template('admin/cat_circle/collections.html',
                             collections=[],
                             pagination=None,
                             current_search='',
                             current_sort='created_at_desc',
                             current_per_page=10,
                             current_post_type='')

# 关键词管理
@cat_circle_admin_bp.route('/keywords')
@admin_required
def manage_keywords():
    """关键词管理页面"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        search = request.args.get('search', '')
        
        # 构建查询
        query = SensitiveWord.query if SensitiveWord else None
        if not query:
            return render_template('admin/cat_circle/keywords.html', keywords=[], pagination=None)
        
        if search:
            query = query.filter(SensitiveWord.word.contains(search))
        
        # 分页
        pagination = query.order_by(SensitiveWord.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        keywords = pagination.items
        
        return render_template('admin/cat_circle/keywords.html',
                             keywords=keywords,
                             pagination=pagination,
                             current_search=search)
    except Exception as e:
        flash(f'获取关键词列表失败: {str(e)}', 'danger')
        return render_template('admin/cat_circle/keywords.html', keywords=[], pagination=None)

# API接口 - 动态操作
@cat_circle_admin_bp.route('/api/posts/<int:post_id>', methods=['GET'])
@admin_required
def get_post_detail(post_id):
    """获取动态详情"""
    try:
        if not CatCirclePost:
            return jsonify({'success': False, 'message': '模型未初始化'})

        post = CatCirclePost.query.get_or_404(post_id)

        # 获取用户信息
        user_info = None
        if User:
            user = User.query.get(post.user_id)
            if user:
                user_info = {
                    'id': user.id,
                    'nickname': user.nickname,
                    'avatar': user.avatar
                }

        # 获取评论列表
        comments = []
        if CatCircleComment:
            comment_list = CatCircleComment.query.filter_by(post_id=post_id).order_by(CatCircleComment.created_at.desc()).limit(10).all()
            for comment in comment_list:
                comment_user = None
                if User:
                    comment_user = User.query.get(comment.user_id)

                comments.append({
                    'id': comment.id,
                    'content': comment.content,
                    'created_at': comment.created_at.strftime('%Y-%m-%d %H:%M:%S') if comment.created_at else '',
                    'user': {
                        'id': comment.user_id,
                        'nickname': comment_user.nickname if comment_user else '未知用户',
                        'avatar': comment_user.avatar if comment_user else None
                    }
                })

        # 处理图片
        images = []
        if post.images:
            try:
                images = json.loads(post.images) if isinstance(post.images, str) else post.images
            except:
                images = []

        post_detail = {
            'id': post.id,
            'content': post.content,
            'images': images,
            'type': post.type,
            'contact_info': post.contact_info,
            'gender': post.gender,
            'status': post.status,
            'view_count': post.view_count,
            'like_count': post.like_count,
            'comment_count': post.comment_count,
            'collect_count': post.collect_count,
            'created_at': post.created_at.strftime('%Y-%m-%d %H:%M:%S') if post.created_at else '',
            'updated_at': post.updated_at.strftime('%Y-%m-%d %H:%M:%S') if post.updated_at else '',
            'user': user_info,
            'comments': comments
        }

        return jsonify({'success': True, 'data': post_detail})
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取详情失败: {str(e)}'})

@cat_circle_admin_bp.route('/api/posts/<int:post_id>/status', methods=['POST'])
@admin_required
def update_post_status(post_id):
    """更新动态状态"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': '请求数据为空'})

        status = data.get('status')
        if not status:
            return jsonify({'success': False, 'message': '状态参数缺失'})

        if not CatCirclePost:
            return jsonify({'success': False, 'message': '模型未初始化'})

        post = CatCirclePost.query.get_or_404(post_id)
        post.status = status
        post.updated_at = datetime.datetime.now()

        db.session.commit()

        return jsonify({'success': True, 'message': '状态更新成功'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'更新失败: {str(e)}'})

@cat_circle_admin_bp.route('/api/posts/<int:post_id>', methods=['DELETE'])
@admin_required
def delete_post(post_id):
    """删除动态"""
    try:
        if not CatCirclePost:
            return jsonify({'success': False, 'message': '模型未初始化'})

        post = CatCirclePost.query.get_or_404(post_id)

        # 删除相关的点赞、收藏、评论
        if CatCircleLike:
            CatCircleLike.query.filter_by(post_id=post_id).delete()
        if CatCircleCollection:
            CatCircleCollection.query.filter_by(post_id=post_id).delete()
        if CatCircleComment:
            CatCircleComment.query.filter_by(post_id=post_id).delete()

        db.session.delete(post)
        db.session.commit()

        return jsonify({'success': True, 'message': '动态删除成功'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'删除失败: {str(e)}'})

# API接口 - 评论操作
@cat_circle_admin_bp.route('/api/comments/<int:comment_id>', methods=['GET'])
@admin_required
def get_comment_detail(comment_id):
    """获取评论详情"""
    try:
        if not CatCircleComment:
            return jsonify({'success': False, 'message': '模型未初始化'})

        comment = CatCircleComment.query.get_or_404(comment_id)

        # 获取评论用户信息
        comment_user = None
        if User:
            user = User.query.get(comment.user_id)
            if user:
                comment_user = {
                    'id': user.id,
                    'nickname': user.nickname,
                    'avatar': user.avatar
                }

        # 获取所属动态信息
        post_info = None
        post_user = None
        if CatCirclePost:
            post = CatCirclePost.query.get(comment.post_id)
            if post:
                # 获取动态作者信息
                if User:
                    post_author = User.query.get(post.user_id)
                    if post_author:
                        post_user = {
                            'id': post_author.id,
                            'nickname': post_author.nickname,
                            'avatar': post_author.avatar
                        }

                # 处理图片
                images = []
                if post.images:
                    try:
                        images = json.loads(post.images) if isinstance(post.images, str) else post.images
                    except:
                        images = []

                post_info = {
                    'id': post.id,
                    'content': post.content,
                    'images': images,
                    'type': post.type,
                    'status': post.status,
                    'view_count': post.view_count,
                    'like_count': post.like_count,
                    'comment_count': post.comment_count,
                    'collect_count': post.collect_count,
                    'created_at': post.created_at.strftime('%Y-%m-%d %H:%M:%S') if post.created_at else '',
                    'user': post_user
                }

        comment_detail = {
            'id': comment.id,
            'content': comment.content,
            'created_at': comment.created_at.strftime('%Y-%m-%d %H:%M:%S') if comment.created_at else '',
            'user': comment_user,
            'post': post_info
        }

        return jsonify({'success': True, 'data': comment_detail})
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取详情失败: {str(e)}'})

@cat_circle_admin_bp.route('/api/comments/<int:comment_id>', methods=['DELETE'])
@admin_required
def delete_comment(comment_id):
    """删除评论"""
    try:
        if not CatCircleComment:
            return jsonify({'success': False, 'message': '模型未初始化'})

        comment = CatCircleComment.query.get_or_404(comment_id)

        # 更新动态的评论数量
        if CatCirclePost:
            post = CatCirclePost.query.get(comment.post_id)
            if post:
                post.comment_count = max(0, post.comment_count - 1)

        db.session.delete(comment)
        db.session.commit()

        return jsonify({'success': True, 'message': '评论删除成功'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'删除失败: {str(e)}'})

# API接口 - 点赞操作
@cat_circle_admin_bp.route('/api/likes/<int:like_id>', methods=['GET'])
@admin_required
def get_like_detail(like_id):
    """获取点赞详情"""
    try:
        if not CatCircleLike:
            return jsonify({'success': False, 'message': '模型未初始化'})

        like = CatCircleLike.query.get_or_404(like_id)

        # 获取点赞用户信息
        like_user = None
        if User:
            user = User.query.get(like.user_id)
            if user:
                like_user = {
                    'id': user.id,
                    'nickname': user.nickname,
                    'avatar': user.avatar
                }

        # 获取被点赞动态信息
        post_info = None
        post_user = None
        if CatCirclePost:
            post = CatCirclePost.query.get(like.post_id)
            if post:
                # 获取动态作者信息
                if User:
                    post_author = User.query.get(post.user_id)
                    if post_author:
                        post_user = {
                            'id': post_author.id,
                            'nickname': post_author.nickname,
                            'avatar': post_author.avatar
                        }

                # 处理图片
                images = []
                if post.images:
                    try:
                        images = json.loads(post.images) if isinstance(post.images, str) else post.images
                    except:
                        images = []

                post_info = {
                    'id': post.id,
                    'content': post.content,
                    'images': images,
                    'type': post.type,
                    'status': post.status,
                    'view_count': post.view_count,
                    'like_count': post.like_count,
                    'comment_count': post.comment_count,
                    'collect_count': post.collect_count,
                    'created_at': post.created_at.strftime('%Y-%m-%d %H:%M:%S') if post.created_at else '',
                    'user': post_user
                }

        like_detail = {
            'id': like.id,
            'created_at': like.created_at.strftime('%Y-%m-%d %H:%M:%S') if like.created_at else '',
            'user': like_user,
            'post': post_info
        }

        return jsonify({'success': True, 'data': like_detail})
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取详情失败: {str(e)}'})

# API接口 - 收藏操作
@cat_circle_admin_bp.route('/api/collections/<int:collection_id>', methods=['GET'])
@admin_required
def get_collection_detail(collection_id):
    """获取收藏详情"""
    try:
        if not CatCircleCollection:
            return jsonify({'success': False, 'message': '模型未初始化'})

        collection = CatCircleCollection.query.get_or_404(collection_id)

        # 获取收藏用户信息
        collection_user = None
        if User:
            user = User.query.get(collection.user_id)
            if user:
                collection_user = {
                    'id': user.id,
                    'nickname': user.nickname,
                    'avatar': user.avatar
                }

        # 获取被收藏动态信息
        post_info = None
        post_user = None
        if CatCirclePost:
            post = CatCirclePost.query.get(collection.post_id)
            if post:
                # 获取动态作者信息
                if User:
                    post_author = User.query.get(post.user_id)
                    if post_author:
                        post_user = {
                            'id': post_author.id,
                            'nickname': post_author.nickname,
                            'avatar': post_author.avatar
                        }

                # 处理图片
                images = []
                if post.images:
                    try:
                        images = json.loads(post.images) if isinstance(post.images, str) else post.images
                    except:
                        images = []

                post_info = {
                    'id': post.id,
                    'content': post.content,
                    'images': images,
                    'type': post.type,
                    'status': post.status,
                    'view_count': post.view_count,
                    'like_count': post.like_count,
                    'comment_count': post.comment_count,
                    'collect_count': post.collect_count,
                    'created_at': post.created_at.strftime('%Y-%m-%d %H:%M:%S') if post.created_at else '',
                    'user': post_user
                }

        collection_detail = {
            'id': collection.id,
            'created_at': collection.created_at.strftime('%Y-%m-%d %H:%M:%S') if collection.created_at else '',
            'user': collection_user,
            'post': post_info
        }

        return jsonify({'success': True, 'data': collection_detail})
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取详情失败: {str(e)}'})

# API接口 - 关键词操作
@cat_circle_admin_bp.route('/api/keywords', methods=['POST'])
@admin_required
def add_keyword():
    """添加敏感词"""
    try:
        data = request.get_json()
        word = data.get('word', '').strip()
        word_type = data.get('type', 'normal')

        if not word:
            return jsonify({'success': False, 'message': '关键词不能为空'})

        if not SensitiveWord:
            return jsonify({'success': False, 'message': '模型未初始化'})

        # 检查是否已存在
        existing = SensitiveWord.query.filter_by(word=word).first()
        if existing:
            return jsonify({'success': False, 'message': '该关键词已存在'})

        # 添加新关键词
        keyword = SensitiveWord(
            word=word,
            type=word_type,
            created_at=datetime.datetime.now()
        )

        db.session.add(keyword)
        db.session.commit()

        return jsonify({'success': True, 'message': '关键词添加成功'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'添加失败: {str(e)}'})

@cat_circle_admin_bp.route('/api/keywords/<int:keyword_id>', methods=['PUT'])
@admin_required
def update_keyword(keyword_id):
    """更新敏感词"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': '请求数据为空'})

        word = data.get('word', '').strip()
        word_type = data.get('type', 'normal')

        if not word:
            return jsonify({'success': False, 'message': '关键词不能为空'})

        if not SensitiveWord:
            return jsonify({'success': False, 'message': '模型未初始化'})

        keyword = SensitiveWord.query.get_or_404(keyword_id)

        # 检查是否与其他关键词重复
        existing = SensitiveWord.query.filter(
            SensitiveWord.word == word,
            SensitiveWord.id != keyword_id
        ).first()
        if existing:
            return jsonify({'success': False, 'message': '该关键词已存在'})

        keyword.word = word
        keyword.type = word_type
        keyword.updated_at = datetime.datetime.now()

        db.session.commit()

        return jsonify({'success': True, 'message': '关键词更新成功'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'更新失败: {str(e)}'})

@cat_circle_admin_bp.route('/api/keywords/<int:keyword_id>', methods=['DELETE'])
@admin_required
def delete_keyword(keyword_id):
    """删除敏感词"""
    try:
        if not SensitiveWord:
            return jsonify({'success': False, 'message': '模型未初始化'})

        keyword = SensitiveWord.query.get_or_404(keyword_id)

        db.session.delete(keyword)
        db.session.commit()

        return jsonify({'success': True, 'message': '关键词删除成功'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'删除失败: {str(e)}'})

# 批量操作
@cat_circle_admin_bp.route('/api/keywords/batch', methods=['POST'])
@admin_required
def batch_add_keywords():
    """批量添加敏感词"""
    try:
        data = request.get_json()
        words = data.get('words', [])
        word_type = data.get('type', 'normal')

        if not words:
            return jsonify({'success': False, 'message': '关键词列表不能为空'})

        if not SensitiveWord:
            return jsonify({'success': False, 'message': '模型未初始化'})

        added_count = 0
        skipped_count = 0

        for word in words:
            word = word.strip()
            if not word:
                continue

            # 检查是否已存在
            existing = SensitiveWord.query.filter_by(word=word).first()
            if existing:
                skipped_count += 1
                continue

            # 添加新关键词
            keyword = SensitiveWord(
                word=word,
                type=word_type,
                created_at=datetime.datetime.now()
            )

            db.session.add(keyword)
            added_count += 1

        db.session.commit()

        message = f'成功添加 {added_count} 个关键词'
        if skipped_count > 0:
            message += f'，跳过 {skipped_count} 个重复关键词'

        return jsonify({'success': True, 'message': message})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'批量添加失败: {str(e)}'})

# 可视化大屏
@cat_circle_admin_bp.route('/analytics')
@admin_required
def analytics():
    """可视化大屏页面"""
    try:
        # 基础统计数据
        total_posts = CatCirclePost.query.count() if CatCirclePost else 0
        total_comments = CatCircleComment.query.count() if CatCircleComment else 0
        total_likes = CatCircleLike.query.count() if CatCircleLike else 0
        total_collections = CatCircleCollection.query.count() if CatCircleCollection else 0

        # 活跃用户数（最近7天有动态的用户）
        seven_days_ago = datetime.datetime.now() - datetime.timedelta(days=7)
        active_users = 0
        if CatCirclePost and User:
            active_users = db.session.query(CatCirclePost.user_id).filter(
                CatCirclePost.created_at >= seven_days_ago
            ).distinct().count()

        # 计算增长率（本周vs上周）
        this_week_start = datetime.datetime.now() - datetime.timedelta(days=7)
        last_week_start = datetime.datetime.now() - datetime.timedelta(days=14)

        posts_growth = 0
        users_growth = 0

        if CatCirclePost:
            this_week_posts = CatCirclePost.query.filter(
                CatCirclePost.created_at >= this_week_start
            ).count()
            last_week_posts = CatCirclePost.query.filter(
                CatCirclePost.created_at >= last_week_start,
                CatCirclePost.created_at < this_week_start
            ).count()

            if last_week_posts > 0:
                posts_growth = round(((this_week_posts - last_week_posts) / last_week_posts) * 100, 1)

        if CatCirclePost and User:
            this_week_users = db.session.query(CatCirclePost.user_id).filter(
                CatCirclePost.created_at >= this_week_start
            ).distinct().count()
            last_week_users = db.session.query(CatCirclePost.user_id).filter(
                CatCirclePost.created_at >= last_week_start,
                CatCirclePost.created_at < this_week_start
            ).distinct().count()

            if last_week_users > 0:
                users_growth = round(((this_week_users - last_week_users) / last_week_users) * 100, 1)

        # 图表数据 - 最近7天的动态发布趋势
        dates = []
        posts_count = []

        for i in range(6, -1, -1):
            date = datetime.datetime.now() - datetime.timedelta(days=i)
            dates.append(date.strftime('%m-%d'))

            day_start = datetime.datetime.combine(date.date(), datetime.time.min)
            day_end = datetime.datetime.combine(date.date(), datetime.time.max)

            if CatCirclePost:
                count = CatCirclePost.query.filter(
                    CatCirclePost.created_at >= day_start,
                    CatCirclePost.created_at <= day_end
                ).count()
            else:
                count = 0
            posts_count.append(count)

        # 动态类型分布
        categories = []
        category_counts = []

        if CatCirclePost:
            type_stats = db.session.query(
                CatCirclePost.type,
                func.count(CatCirclePost.id).label('count')
            ).group_by(CatCirclePost.type).all()

            for type_stat in type_stats:
                categories.append(get_post_type_label(type_stat.type))
                category_counts.append(type_stat.count)

        # 最近活动
        recent_activities = []
        if CatCirclePost and User:
            recent_posts = db.session.query(CatCirclePost, User).join(
                User, CatCirclePost.user_id == User.id
            ).order_by(desc(CatCirclePost.created_at)).limit(10).all()

            for post, user in recent_posts:
                time_diff = datetime.datetime.now() - post.created_at
                if time_diff.days > 0:
                    time_ago = f"{time_diff.days}天前"
                elif time_diff.seconds > 3600:
                    time_ago = f"{time_diff.seconds // 3600}小时前"
                elif time_diff.seconds > 60:
                    time_ago = f"{time_diff.seconds // 60}分钟前"
                else:
                    time_ago = "刚刚"

                recent_activities.append({
                    'user_name': user.nickname or user.username,
                    'action_text': f"发布了{get_post_type_label(post.type)}",
                    'time_ago': time_ago
                })

        stats = {
            'total_posts': total_posts,
            'total_comments': total_comments,
            'total_likes': total_likes,
            'total_collections': total_collections,
            'active_users': active_users,
            'posts_growth': posts_growth,
            'users_growth': users_growth
        }

        chart_data = {
            'dates': dates,
            'posts_count': posts_count,
            'categories': categories,
            'category_counts': category_counts
        }

        return render_template('admin/cat_circle/analytics.html',
                             stats=stats,
                             chart_data=chart_data,
                             recent_activities=recent_activities)
    except Exception as e:
        flash(f'获取可视化数据失败: {str(e)}', 'danger')
        return render_template('admin/cat_circle/analytics.html',
                             stats={},
                             chart_data={},
                             recent_activities=[])

# 可视化大屏数据API
@cat_circle_admin_bp.route('/analytics/data')
@admin_required
def analytics_data():
    """获取可视化大屏实时数据"""
    try:
        # 基础统计数据
        total_posts = CatCirclePost.query.count() if CatCirclePost else 0
        total_comments = CatCircleComment.query.count() if CatCircleComment else 0
        total_likes = CatCircleLike.query.count() if CatCircleLike else 0
        total_collections = CatCircleCollection.query.count() if CatCircleCollection else 0

        # 活跃用户数（最近7天有动态的用户）
        seven_days_ago = datetime.datetime.now() - datetime.timedelta(days=7)
        active_users = 0
        if CatCirclePost and User:
            active_users = db.session.query(CatCirclePost.user_id).filter(
                CatCirclePost.created_at >= seven_days_ago
            ).distinct().count()

        # 计算增长率（本周vs上周）
        this_week_start = datetime.datetime.now() - datetime.timedelta(days=7)
        last_week_start = datetime.datetime.now() - datetime.timedelta(days=14)

        posts_growth = 0
        users_growth = 0

        if CatCirclePost:
            this_week_posts = CatCirclePost.query.filter(
                CatCirclePost.created_at >= this_week_start
            ).count()
            last_week_posts = CatCirclePost.query.filter(
                CatCirclePost.created_at >= last_week_start,
                CatCirclePost.created_at < this_week_start
            ).count()

            if last_week_posts > 0:
                posts_growth = round(((this_week_posts - last_week_posts) / last_week_posts) * 100, 1)

        if CatCirclePost and User:
            this_week_users = db.session.query(CatCirclePost.user_id).filter(
                CatCirclePost.created_at >= this_week_start
            ).distinct().count()
            last_week_users = db.session.query(CatCirclePost.user_id).filter(
                CatCirclePost.created_at >= last_week_start,
                CatCirclePost.created_at < this_week_start
            ).distinct().count()

            if last_week_users > 0:
                users_growth = round(((this_week_users - last_week_users) / last_week_users) * 100, 1)

        # 图表数据 - 最近7天的动态发布趋势
        dates = []
        posts_count = []
        active_users_count = []

        for i in range(6, -1, -1):
            date = datetime.datetime.now() - datetime.timedelta(days=i)
            dates.append(date.strftime('%m-%d'))

            day_start = datetime.datetime.combine(date.date(), datetime.time.min)
            day_end = datetime.datetime.combine(date.date(), datetime.time.max)

            # 每日动态数
            if CatCirclePost:
                count = CatCirclePost.query.filter(
                    CatCirclePost.created_at >= day_start,
                    CatCirclePost.created_at <= day_end
                ).count()
            else:
                count = 0
            posts_count.append(count)

            # 每日活跃用户数
            if CatCirclePost and User:
                daily_active = db.session.query(CatCirclePost.user_id).filter(
                    CatCirclePost.created_at >= day_start,
                    CatCirclePost.created_at <= day_end
                ).distinct().count()
            else:
                daily_active = 0
            active_users_count.append(daily_active)

        # 动态类型分布
        categories = []
        category_counts = []

        if CatCirclePost:
            type_stats = db.session.query(
                CatCirclePost.type,
                func.count(CatCirclePost.id).label('count')
            ).group_by(CatCirclePost.type).all()

            for type_stat in type_stats:
                categories.append(get_post_type_label(type_stat.type))
                category_counts.append(type_stat.count)

        # 如果没有数据，提供默认数据
        if not categories:
            categories = ['动态', '二手交易', '求助', '失物招领']
            category_counts = [0, 0, 0, 0]

        # 用户活跃度分析（最近30天）
        thirty_days_ago = datetime.datetime.now() - datetime.timedelta(days=30)
        user_activity = []

        if CatCirclePost and User:
            # 获取最活跃的用户
            top_users = db.session.query(
                User.nickname,
                User.username,
                func.count(CatCirclePost.id).label('post_count')
            ).join(CatCirclePost, User.id == CatCirclePost.user_id).filter(
                CatCirclePost.created_at >= thirty_days_ago
            ).group_by(User.id).order_by(desc('post_count')).limit(5).all()

            for user in top_users:
                user_activity.append({
                    'name': user.nickname or user.username,
                    'posts': user.post_count
                })

        # 最近活动
        recent_activities = []
        if CatCirclePost and User:
            recent_posts = db.session.query(CatCirclePost, User).join(
                User, CatCirclePost.user_id == User.id
            ).order_by(desc(CatCirclePost.created_at)).limit(10).all()

            for post, user in recent_posts:
                time_diff = datetime.datetime.now() - post.created_at
                if time_diff.days > 0:
                    time_ago = f"{time_diff.days}天前"
                elif time_diff.seconds > 3600:
                    time_ago = f"{time_diff.seconds // 3600}小时前"
                elif time_diff.seconds > 60:
                    time_ago = f"{time_diff.seconds // 60}分钟前"
                else:
                    time_ago = "刚刚"

                recent_activities.append({
                    'user_name': user.nickname or user.username,
                    'action_text': f"发布了{get_post_type_label(post.type)}",
                    'time_ago': time_ago
                })

        # 返回完整的数据
        return jsonify({
            'total_posts': total_posts,
            'total_comments': total_comments,
            'total_likes': total_likes,
            'total_collections': total_collections,
            'active_users': active_users,
            'posts_growth': posts_growth,
            'users_growth': users_growth,
            'chart_data': {
                'dates': dates,
                'posts_count': posts_count,
                'active_users_count': active_users_count,
                'categories': categories,
                'category_counts': category_counts
            },
            'user_activity': user_activity,
            'recent_activities': recent_activities
        })
    except Exception as e:
        return jsonify({'error': f'获取数据失败: {str(e)}'})

# 导出蓝图
__all__ = ['cat_circle_admin_bp', 'init_cat_circle_admin', 'get_post_type_label', 'get_post_type_badge_class']

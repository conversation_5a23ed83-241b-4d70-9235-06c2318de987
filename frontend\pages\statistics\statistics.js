// pages/statistics/statistics.js
const app = getApp();
const wxCharts = require('../../utils/wxcharts.js');
const globalNotificationMixin = require('../../utils/global-notification-mixin')

Page(Object.assign({}, globalNotificationMixin, {

  /**
   * 页面的初始数据
   */
  data: Object.assign({}, globalNotificationMixin.data, {
    statistics: null,
    loading: true,
    isEmpty: false,
    refreshing: false, // Track if refresh button is spinning
    canvasWidth: 300, // 默认宽度，将在onLoad中更新
    charts: {
      accuracyPieChart: null,
      questionTypesChart: null,
      progressLineChart: null
    },
    formattedStats: {}, // 格式化后的统计数据
    // 添加显示图例的文本
    accuracyLegendText: '正确 | 错误',
    typeDistributionLegendText: '单选题 | 多选题 | 判断题 | 填空题',
    questionDetailsLegendText1: '单选题正确 | 单选题错误',
    questionDetailsLegendText2: '多选题正确 | 多选题错误',
    questionDetailsLegendText3: '判断题正确 | 判断题错误',
    questionDetailsLegendText4: '填空题正确 | 填空题错误',
    progressLegendText: '已完成题目数',
    // 添加示例数据以防API不返回数据
    sampleData: {
      totalQuestions: 56,
      correctCount: 42,
      wrongCount: 14,
      accuracy: 0.75,
      singleChoiceAccuracy: 0.82,
      multipleChoiceAccuracy: 0.64,
      judgmentAccuracy: 0.78,
      fillBlankAccuracy: 0.70,
      singleChoiceCorrect: 28,
      singleChoiceWrong: 6,
      multipleChoiceCorrect: 14,
      multipleChoiceWrong: 8,
      judgmentCorrect: 12,
      judgmentWrong: 3,
      fillBlankCorrect: 8,
      fillBlankWrong: 3,
      dailyProgress: [
        { date: '2023-04-15', total: 12, correct: 10, accuracy: 0.83 },
        { date: '2023-04-16', total: 8, correct: 5, accuracy: 0.63 },
        { date: '2023-04-17', total: 15, correct: 11, accuracy: 0.73 },
        { date: '2023-04-18', total: 10, correct: 9, accuracy: 0.90 },
        { date: '2023-04-19', total: 6, correct: 4, accuracy: 0.67 },
        { date: '2023-04-20', total: 5, correct: 3, accuracy: 0.60 }
      ],
      // 新增学习概览相关的数据
      dailyAvg: 9.3,
      avgTimePerQuestion: "45秒",
      activeDays: 6,
      streakDays: 3
    }
  }),

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取屏幕宽度
    const systemInfo = wx.getSystemInfoSync();
    const screenWidth = systemInfo.windowWidth;
    
    // 计算图表宽度 (留出两侧边距)
    const canvasWidth = screenWidth - 40;
    
    this.setData({ canvasWidth });
    console.log('Canvas宽度设置为:', canvasWidth);
    
    // 强制刷新获取真实数据
    this.fetchRealData();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    const query = wx.createSelectorQuery();
    query.select('.statistics-canvas').boundingClientRect(rect => {
      // Only set canvasWidth if rect exists
      if (rect) {
        this.setData({
          canvasWidth: rect.width || 300
        });
        
        if (this.data.statistics && !this.data.isEmpty) {
          this.initCharts(this.data.statistics);
        }
      } else {
        console.log('Canvas element not found yet, using default width');
        // Use system info to get a reasonable default width
        const systemInfo = wx.getSystemInfoSync();
        const screenWidth = systemInfo.windowWidth;
        this.setData({
          canvasWidth: screenWidth - 40 // Account for container padding
        });
      }
    }).exec();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 更新自定义tabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 3
      });
    }

    // 每次显示页面时刷新数据
    this.fetchStatistics();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('下拉刷新，重新获取真实数据');
    wx.showToast({
      title: '刷新中...',
      icon: 'loading'
    });
    
    // 强制刷新，获取最新实时数据
    this.fetchRealData();
    
    // 刷新完成后停止下拉动画
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 强制从服务器获取真实数据
  fetchRealData() {
    wx.showLoading({
      title: '数据获取中...',
    });

    this.setData({ 
      loading: true,
      refreshing: true // Set refreshing to true when starting
    });

    // 添加时间戳强制刷新
    const timestamp = new Date().getTime();
    
    // 直接使用wx.request而不是app.request来排除中间层问题
    wx.request({
      url: `${app.globalData.baseUrl}/statistics?t=${timestamp}`,
      method: 'GET',
      header: {
        'content-type': 'application/json',
        'Authorization': app.globalData.token || ''
      },
      success: (res) => {
        wx.hideLoading();
        console.log('获取真实统计数据:', res.data);
        
        if (res.statusCode === 200 && res.data) {
          let statsData = res.data;
          
          // 检查返回数据有效性
          if (!statsData.dailyProgress || statsData.dailyProgress.length === 0) {
            console.warn('API返回数据缺少每日进度，生成测试数据');
            // 生成测试数据
            statsData.dailyProgress = this.generateTestDailyData();
          }
          
          // 确保数据格式正确
          this.processApiData(statsData);
          
          // 处理格式化数据
          const formattedStats = this.formatStatistics(statsData);
          
          this.setData({
            statistics: statsData,
            formattedStats: formattedStats,
            loading: false,
            isEmpty: false,
            refreshing: false // Set refreshing to false when done
          });
          
          console.log('真实统计数据已更新:', statsData);
          console.log('每日进度真实数据:', statsData.dailyProgress);
          
          // 确保UI更新后再初始化图表
          setTimeout(() => {
            this.initCharts(statsData);
          }, 500);
        } else {
          console.error('获取真实数据失败:', res);
          this.setData({ refreshing: false }); // Set refreshing to false on error
          this.fallbackToTestData();
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('请求真实数据失败:', err);
        this.setData({ refreshing: false }); // Set refreshing to false on error
        this.fallbackToTestData();
      }
    });
  },
  
  // 处理API返回的数据
  processApiData(statsData) {
    // 如果没有dailyProgress数据，或数据为空，则使用示例数据
    if (!statsData.dailyProgress || statsData.dailyProgress.length === 0) {
      statsData.dailyProgress = this.data.sampleData.dailyProgress;
    }
    
    // 确保单选题数据存在
    if (typeof statsData.singleChoiceAccuracy === 'undefined') {
      statsData.singleChoiceAccuracy = this.data.sampleData.singleChoiceAccuracy;
    }
    
    // 确保多选题数据存在
    if (typeof statsData.multipleChoiceAccuracy === 'undefined') {
      statsData.multipleChoiceAccuracy = this.data.sampleData.multipleChoiceAccuracy;
    }
    
    // 确保判断题数据存在
    if (typeof statsData.judgmentAccuracy === 'undefined') {
      statsData.judgmentAccuracy = this.data.sampleData.judgmentAccuracy;
    }
    
    // 确保填空题数据存在
    if (typeof statsData.fillBlankAccuracy === 'undefined') {
      statsData.fillBlankAccuracy = this.data.sampleData.fillBlankAccuracy;
    }
    
    // 确保题型数量计算正确 - 分配各题型的比例
    const singleTotal = Math.floor((statsData.totalQuestions || 0) * 0.4); // 假设40%是单选
    const multipleTotal = Math.floor((statsData.totalQuestions || 0) * 0.3); // 假设30%是多选
    const judgmentTotal = Math.floor((statsData.totalQuestions || 0) * 0.2); // 假设20%是判断
    const fillBlankTotal = (statsData.totalQuestions || 0) - singleTotal - multipleTotal - judgmentTotal; // 剩余的是填空
    
    // 计算正确和错误的数量
    statsData.singleChoiceCorrect = Math.floor(singleTotal * statsData.singleChoiceAccuracy);
    statsData.singleChoiceWrong = singleTotal - statsData.singleChoiceCorrect;
    statsData.multipleChoiceCorrect = Math.floor(multipleTotal * statsData.multipleChoiceAccuracy);
    statsData.multipleChoiceWrong = multipleTotal - statsData.multipleChoiceCorrect;
    statsData.judgmentCorrect = Math.floor(judgmentTotal * statsData.judgmentAccuracy);
    statsData.judgmentWrong = judgmentTotal - statsData.judgmentCorrect;
    statsData.fillBlankCorrect = Math.floor(fillBlankTotal * statsData.fillBlankAccuracy);
    statsData.fillBlankWrong = fillBlankTotal - statsData.fillBlankCorrect;
    
    // 确保学习概览数据存在
    if (typeof statsData.dailyAvg === 'undefined') {
      statsData.dailyAvg = this.data.sampleData.dailyAvg;
    }
    
    if (typeof statsData.avgTimePerQuestion === 'undefined') {
      statsData.avgTimePerQuestion = this.data.sampleData.avgTimePerQuestion;
    }
    
    if (typeof statsData.activeDays === 'undefined') {
      statsData.activeDays = this.data.sampleData.activeDays;
    }
    
    if (typeof statsData.streakDays === 'undefined') {
      statsData.streakDays = this.data.sampleData.streakDays;
    }
    
    return statsData;
  },
  
  // 生成测试每日数据
  generateTestDailyData() {
    const dailyData = [];
    const today = new Date();
    
    // 生成最近7天的数据，确保有上升和下降的趋势
    const trends = [
      { total: 8, accuracy: 0.65 },  // 第1天
      { total: 12, accuracy: 0.75 }, // 第2天
      { total: 7, accuracy: 0.71 },  // 第3天
      { total: 15, accuracy: 0.80 }, // 第4天
      { total: 10, accuracy: 0.90 }, // 第5天
      { total: 20, accuracy: 0.75 }, // 第6天
      { total: 18, accuracy: 0.83 }  // 第7天
    ];
    
    // 生成最近7天的数据
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const formattedDate = date.getFullYear() + '-' + 
                           String(date.getMonth() + 1).padStart(2, '0') + '-' + 
                           String(date.getDate()).padStart(2, '0');
      
      // 使用预设趋势数据
      const trend = trends[6-i];
      // 添加少量随机变化
      const variation = 0.05; // 5%的随机变化
      const total = trend.total + Math.floor(Math.random() * 3) - 1; // -1到+1的变化
      const accuracy = Math.min(1, Math.max(0, trend.accuracy + (Math.random() * variation * 2 - variation))); // 变化不超过5%
      const correct = Math.round(total * accuracy);
      
      dailyData.push({
        date: formattedDate,
        total: total,
        correct: correct,
        accuracy: accuracy
      });
    }
    
    return dailyData;
  },
  
  // 当API请求失败时回退到测试数据
  fallbackToTestData() {
    console.log('回退到测试数据...');
    
    const statsData = {
      totalQuestions: Math.floor(Math.random() * 50) + 30,
      correctCount: 0,
      wrongCount: 0,
      singleChoiceAccuracy: Math.random() * 0.3 + 0.6, // 60%-90%
      multipleChoiceAccuracy: Math.random() * 0.3 + 0.5, // 50%-80%
      judgmentAccuracy: Math.random() * 0.3 + 0.6, // 60%-90%
      fillBlankAccuracy: Math.random() * 0.3 + 0.5, // 50%-80%
      dailyProgress: this.generateTestDailyData()
    };
    
    // 计算其他属性
    statsData.correctCount = Math.floor(statsData.totalQuestions * 0.7); // 假设70%正确率
    statsData.wrongCount = statsData.totalQuestions - statsData.correctCount;
    statsData.accuracy = statsData.correctCount / statsData.totalQuestions;
    
    // 题型数量分布
    const singleChoiceRatio = 0.4; // 假设40%是单选题
    const multipleChoiceRatio = 0.3; // 假设30%是多选题
    const judgmentRatio = 0.2; // 假设20%是判断题
    const fillBlankRatio = 0.1; // 假设10%是填空题
    
    const singleChoiceTotal = Math.floor(statsData.totalQuestions * singleChoiceRatio);
    const multipleChoiceTotal = Math.floor(statsData.totalQuestions * multipleChoiceRatio);
    const judgmentTotal = Math.floor(statsData.totalQuestions * judgmentRatio);
    const fillBlankTotal = statsData.totalQuestions - singleChoiceTotal - multipleChoiceTotal - judgmentTotal;
    
    statsData.singleChoiceCorrect = Math.floor(singleChoiceTotal * statsData.singleChoiceAccuracy);
    statsData.singleChoiceWrong = singleChoiceTotal - statsData.singleChoiceCorrect;
    
    statsData.multipleChoiceCorrect = Math.floor(multipleChoiceTotal * statsData.multipleChoiceAccuracy);
    statsData.multipleChoiceWrong = multipleChoiceTotal - statsData.multipleChoiceCorrect;
    
    statsData.judgmentCorrect = Math.floor(judgmentTotal * statsData.judgmentAccuracy);
    statsData.judgmentWrong = judgmentTotal - statsData.judgmentCorrect;
    
    statsData.fillBlankCorrect = Math.floor(fillBlankTotal * statsData.fillBlankAccuracy);
    statsData.fillBlankWrong = fillBlankTotal - statsData.fillBlankCorrect;
    
    // 处理格式化数据
    const formattedStats = this.formatStatistics(statsData);
    
    this.setData({
      statistics: statsData,
      formattedStats: formattedStats,
      loading: false,
      isEmpty: false,
      refreshing: false // Set refreshing to false when using test data
    });
    
    console.log('使用测试数据:', statsData);
    console.log('每日进度测试数据:', statsData.dailyProgress);
    
    wx.showToast({
      title: '使用随机测试数据',
      icon: 'none',
      duration: 2000
    });
    
    // 确保UI更新后再初始化图表
    setTimeout(() => {
      this.initCharts(statsData);
    }, 500);
  },

  // 原有的fetchStatistics方法保留为备用
  fetchStatistics(callback) {
    // 直接调用新的fetchRealData
    this.fetchRealData();
    if (callback) callback();
  },
  
  // 处理数据格式化
  formatStatistics(stats) {
    if (!stats) return {};
    
    // 计算各题型的正确数和错误数
    const singleChoiceTotal = stats.singleChoiceCorrect + stats.singleChoiceWrong || 
                             (stats.totalQuestions * 0.4);
    const multipleChoiceTotal = stats.multipleChoiceCorrect + stats.multipleChoiceWrong || 
                               (stats.totalQuestions * 0.3);
    const judgmentTotal = stats.judgmentCorrect + stats.judgmentWrong || 
                         (stats.totalQuestions * 0.2);
    const fillBlankTotal = stats.fillBlankCorrect + stats.fillBlankWrong || 
                          (stats.totalQuestions * 0.1);
    
    const singleCorrect = stats.singleChoiceCorrect || Math.round(singleChoiceTotal * stats.singleChoiceAccuracy);
    const multipleCorrect = stats.multipleChoiceCorrect || Math.round(multipleChoiceTotal * stats.multipleChoiceAccuracy);
    const judgmentCorrect = stats.judgmentCorrect || Math.round(judgmentTotal * stats.judgmentAccuracy);
    const fillBlankCorrect = stats.fillBlankCorrect || Math.round(fillBlankTotal * stats.fillBlankAccuracy);
    
    return {
      accuracyPercent: Math.round(stats.accuracy * 100) + '%',
      singleChoicePercent: Math.round(stats.singleChoiceAccuracy * 100) + '%',
      multipleChoicePercent: Math.round(stats.multipleChoiceAccuracy * 100) + '%',
      judgmentPercent: Math.round(stats.judgmentAccuracy * 100) + '%',
      fillBlankPercent: Math.round(stats.fillBlankAccuracy * 100) + '%',
      singleChoiceWidth: Math.round(stats.singleChoiceAccuracy * 100) + '%',
      multipleChoiceWidth: Math.round(stats.multipleChoiceAccuracy * 100) + '%',
      judgmentWidth: Math.round(stats.judgmentAccuracy * 100) + '%',
      fillBlankWidth: Math.round(stats.fillBlankAccuracy * 100) + '%',
      
      // 各题型统计
      singleChoiceTotal: singleChoiceTotal,
      multipleChoiceTotal: multipleChoiceTotal,
      judgmentTotal: judgmentTotal,
      fillBlankTotal: fillBlankTotal,
      singleChoiceCorrect: singleCorrect,
      singleChoiceWrong: Math.round(singleChoiceTotal - singleCorrect),
      multipleChoiceCorrect: multipleCorrect,
      multipleChoiceWrong: Math.round(multipleChoiceTotal - multipleCorrect),
      judgmentCorrect: judgmentCorrect,
      judgmentWrong: Math.round(judgmentTotal - judgmentCorrect),
      fillBlankCorrect: fillBlankCorrect,
      fillBlankWrong: Math.round(fillBlankTotal - fillBlankCorrect),
      
      // 格式化每日进度数据
      dailyProgress: stats.dailyProgress ? stats.dailyProgress.map(item => {
        return {
          ...item,
          accuracyPercent: Math.round(item.accuracy * 100) + '%',
          accuracyColor: item.accuracy >= 0.8 ? '#4CAF50' : 
                         item.accuracy >= 0.6 ? '#8BC34A' : 
                         item.accuracy >= 0.4 ? '#FF9800' : '#F44336'
        };
      }) : []
    };
  },
  
  // 初始化图表
  initCharts(data) {
    try {
      console.log('开始初始化图表，设置延时确保DOM已加载...');
      
      // 确保canvas元素已经准备好
      const initialDelay = 800; // 初始化延时
      
      setTimeout(() => {
        console.log('开始绘制准确率饼图...');
        this.drawAccuracyPieChart(data);
        
        setTimeout(() => {
          console.log('开始绘制题型分布饼图...');
          this.drawQuestionTypesChart(data);
          
          // 最后绘制每日进度折线图，因为这个最复杂
          setTimeout(() => {
            console.log('开始绘制每日进度折线图...');
            
            // 确保数据中有每日进度
            if (data.dailyProgress && data.dailyProgress.length > 0) {
              // 强制刷新canvas大小
              const query = wx.createSelectorQuery();
              query.select('#progressCanvas').boundingClientRect(rect => {
                if (rect) {
                  console.log('进度图表canvas大小:', rect.width, 'x', rect.height);
                }
                this.drawProgressLineChart(data);
              }).exec();
            } else {
              console.warn('没有每日进度数据，无法绘制折线图');
            }
          }, 500);
        }, 400);
      }, initialDelay);
    } catch (error) {
      console.error('初始化图表出错:', error);
    }
  },
  
  // 绘制准确率饼图
  drawAccuracyPieChart(data) {
    try {
      const { canvasWidth } = this.data;
      
      if (!data || (data.correctCount === 0 && data.wrongCount === 0)) {
        console.log('准确率数据不完整，无法绘制饼图');
        return;
      }
      
      console.log('绘制准确率饼图...');
      
      const series = [{
        name: '正确',
        data: data.correctCount || 1,
        color: '#4CAF50',
        format: function(val) {
          return val + '题 (' + Math.round(val / (data.totalQuestions) * 100) + '%)';
        }
      }, {
        name: '错误',
        data: data.wrongCount || 1,
        color: '#F44336',
        format: function(val) {
          return val + '题 (' + Math.round(val / (data.totalQuestions) * 100) + '%)';
        }
      }];
      
      // 直接添加图例文本到图表下方
      const chartCanvas = new wxCharts({
        canvasId: 'accuracyCanvas',
        type: 'pie',
        series: series,
        width: canvasWidth,
        height: 280, // 增加高度为图例留出空间
        dataLabel: true,
        legend: false, // 关闭内置图例，改用自定义文本显示
        title: {
          name: Math.round(data.accuracy * 100) + '%',
          color: '#4C84FF',
          fontSize: 25
        },
        subtitle: {
          name: '正确率',
          color: '#666666',
          fontSize: 15
        },
        extra: {
          pie: {
            offsetAngle: -90
          }
        }
      });
      
      // 添加注释到WXML中显示图例
      this.setData({
        accuracyLegendText: `● 正确(${data.correctCount}题) ○ 错误(${data.wrongCount}题)`
      });
    } catch (error) {
      console.error('绘制准确率饼图出错:', error);
    }
  },
  
  // 绘制题型分布饼图
  drawQuestionTypesChart(data) {
    try {
      const { canvasWidth } = this.data;
      const { formattedStats } = this.data;
      
      if (!data) {
        console.log('题型分布数据不完整，无法绘制饼图');
        return;
      }
      
      console.log('绘制题型分布饼图...');
      
      // 确保数据至少为1，避免图表不显示
      const singleCount = formattedStats.singleChoiceTotal || 1;
      const multipleCount = formattedStats.multipleChoiceTotal || 1;
      const judgmentCount = formattedStats.judgmentTotal || 1;
      const fillBlankCount = formattedStats.fillBlankTotal || 1;
      
      const series = [{
        name: '单选题',
        data: singleCount,
        color: '#2196F3',
        format: function(val) {
          return val + '题';
        }
      }, {
        name: '多选题',
        data: multipleCount,
        color: '#FF9800',
        format: function(val) {
          return val + '题';
        }
      }, {
        name: '判断题',
        data: judgmentCount,
        color: '#9C27B0',
        format: function(val) {
          return val + '题';
        }
      }, {
        name: '填空题',
        data: fillBlankCount,
        color: '#009688',
        format: function(val) {
          return val + '题';
        }
      }];
      
      new wxCharts({
        canvasId: 'questionTypesCanvas',
        type: 'pie',
        series: series,
        width: canvasWidth,
        height: 280, // 增加高度为图例留出空间
        dataLabel: true,
        legend: false, // 关闭内置图例，改用自定义文本显示
        title: {
          name: '题型分布',
          color: '#333333',
          fontSize: 15
        },
        subtitle: {
          name: '总计: ' + (singleCount + multipleCount + judgmentCount + fillBlankCount) + '题',
          color: '#666666',
          fontSize: 12
        },
        extra: {
          pie: {
            offsetAngle: -90
          }
        },
        disablePieStroke: false,
        animation: true
      });
      
      // 添加注释到WXML中显示图例
      this.setData({
        typeDistributionLegendText: `● 单选题(${singleCount}题) ● 多选题(${multipleCount}题) ● 判断题(${judgmentCount}题) ● 填空题(${fillBlankCount}题)`
      });
      
      // 绘制题型详细数据饼图
      this.drawQuestionDetailsChart();
    } catch (error) {
      console.error('绘制题型分布饼图出错:', error);
    }
  },
  
  // 绘制题型详细数据
  drawQuestionDetailsChart() {
    try {
      const { canvasWidth, formattedStats } = this.data;
      
      if (!formattedStats) {
        return;
      }
      
      // 将柱状图改为饼图展示
      const total = formattedStats.singleChoiceCorrect + formattedStats.singleChoiceWrong +
                   formattedStats.multipleChoiceCorrect + formattedStats.multipleChoiceWrong +
                   formattedStats.judgmentCorrect + formattedStats.judgmentWrong +
                   formattedStats.fillBlankCorrect + formattedStats.fillBlankWrong;
      
      const sCorrect = formattedStats.singleChoiceCorrect || 1;
      const sWrong = formattedStats.singleChoiceWrong || 1;
      const mCorrect = formattedStats.multipleChoiceCorrect || 1;
      const mWrong = formattedStats.multipleChoiceWrong || 1;
      const jCorrect = formattedStats.judgmentCorrect || 1;
      const jWrong = formattedStats.judgmentWrong || 1;
      const fCorrect = formattedStats.fillBlankCorrect || 1;
      const fWrong = formattedStats.fillBlankWrong || 1;
      
      const series = [
        {
          name: '单选题正确',
          data: sCorrect,
          color: '#4CAF50',
          format: function(val) {
            return val + '题';
          }
        },
        {
          name: '单选题错误',
          data: sWrong,
          color: '#F44336',
          format: function(val) {
            return val + '题';
          }
        },
        {
          name: '多选题正确',
          data: mCorrect,
          color: '#8BC34A',
          format: function(val) {
            return val + '题';
          }
        },
        {
          name: '多选题错误',
          data: mWrong,
          color: '#FF5722',
          format: function(val) {
            return val + '题';
          }
        },
        {
          name: '判断题正确',
          data: jCorrect,
          color: '#BA68C8',
          format: function(val) {
            return val + '题';
          }
        },
        {
          name: '判断题错误',
          data: jWrong,
          color: '#7B1FA2',
          format: function(val) {
            return val + '题';
          }
        },
        {
          name: '填空题正确',
          data: fCorrect,
          color: '#4DB6AC',
          format: function(val) {
            return val + '题';
          }
        },
        {
          name: '填空题错误',
          data: fWrong,
          color: '#00695C',
          format: function(val) {
            return val + '题';
          }
        }
      ];
      
      new wxCharts({
        canvasId: 'questionDetailsCanvas',
        type: 'pie',
        series: series,
        width: canvasWidth,
        height: 280, // 增加高度为图例留出空间
        dataLabel: true,
        legend: false, // 关闭内置图例，改用自定义文本显示
        title: {
          name: '答题详情',
          color: '#333333',
          fontSize: 15
        },
        subtitle: {
          name: '总计: ' + total + '题',
          color: '#666666',
          fontSize: 12
        },
        animation: true,
        disablePieStroke: false,
        extra: {
          pie: {
            labelWidth: 13,
            offsetAngle: -90
          }
        }
      });
      
      // 添加注释到WXML中显示图例
      this.setData({
        questionDetailsLegendText1: `● 单选题正确(${sCorrect}题) ● 单选题错误(${sWrong}题)`,
        questionDetailsLegendText2: `● 多选题正确(${mCorrect}题) ● 多选题错误(${mWrong}题)`,
        questionDetailsLegendText3: `● 判断题正确(${jCorrect}题) ● 判断题错误(${jWrong}题)`,
        questionDetailsLegendText4: `● 填空题正确(${fCorrect}题) ● 填空题错误(${fWrong}题)`
      });
    } catch (error) {
      console.error('绘制题型详细数据图表出错:', error);
    }
  },
  
  // 绘制每日进度折线图
  drawProgressLineChart(data) {
    try {
      const { canvasWidth } = this.data;
      
      if (!data || !data.dailyProgress || data.dailyProgress.length === 0) {
        console.log('每日进度数据不完整，无法绘制折线图');
        return;
      }
      
      console.log('重新绘制每日进度折线图...', data.dailyProgress);
      
      // 确保有数据
      if (data.dailyProgress.length > 0) {
        // 提取数据
        let progressData = data.dailyProgress;
        console.log('原始数据:', progressData);
        
        // 确保数据按日期排序
        progressData.sort((a, b) => new Date(a.date) - new Date(b.date));
        
        // 处理日期和数据
        const categories = progressData.map(item => item.date.substring(5)); // 只显示月-日
        const accuracyData = progressData.map(item => {
          let accuracy = parseFloat(item.accuracy);
          if (isNaN(accuracy)) accuracy = 0;
          if (accuracy > 1) accuracy = accuracy / 100;
          return Math.round(accuracy * 100);
        });
        const totalQuestions = progressData.map(item => parseInt(item.total) || 0);
        
        console.log('处理后的数据:');
        console.log('- 日期:', categories);
        console.log('- 正确率:', accuracyData);
        console.log('- 题目数量:', totalQuestions);
        
        // 直接使用原生Canvas API绘制
        const ctx = wx.createCanvasContext('progressCanvas');
        
        // 设置画布尺寸和边距 - 减小左侧边距，增加右侧边距
        const width = canvasWidth;
        const height = 300;
        const padding = { top: 40, right: 40, bottom: 70, left: 40 }; // 底部增加到70px，为倾斜标签留出空间
        const chartWidth = width - padding.left - padding.right;
        const chartHeight = height - padding.top - padding.bottom;
        
        // 清空画布
        ctx.clearRect(0, 0, width, height);
        
        // 找出最大值，以便缩放
        const maxAccuracy = 100; // 正确率最大100%
        const maxTotal = Math.max(...totalQuestions, 10); // 至少10的最大值
        
        // 绘制标题 - 向左移动
        ctx.setFontSize(14);
        ctx.setFillStyle('#333333');
        ctx.setTextAlign('center');
        ctx.fillText('每日答题进度', width / 2 - 20, 20); // 标题左移20px
        
        // 绘制坐标轴
        ctx.beginPath();
        ctx.setLineWidth(1);
        ctx.setStrokeStyle('#cccccc');
        
        // X轴
        ctx.moveTo(padding.left, height - padding.bottom);
        ctx.lineTo(width - padding.right, height - padding.bottom);
        
        // Y轴
        ctx.moveTo(padding.left, padding.top);
        ctx.lineTo(padding.left, height - padding.bottom);
        ctx.stroke();
        
        // 绘制X轴标签 - 改为向右下角倾斜显示
        ctx.setFontSize(10);
        const xStep = chartWidth / (categories.length > 1 ? categories.length - 1 : 1);
        
        categories.forEach((category, index) => {
          const x = padding.left + index * xStep;
          const y = height - padding.bottom + 8; // 调整与轴线的距离
          
          // 保存当前画布状态
          ctx.save();
          // 移动原点到标签位置
          ctx.translate(x, y);
          // 旋转画布为正角度，使文本向右下角倾斜
          ctx.rotate(Math.PI / 3); // 约60度角，向右下倾斜更明显
          // 设置文本对齐方式
          ctx.setTextAlign('left');
          // 绘制旋转后的文本
          ctx.fillText(category, 0, 0);
          // 恢复画布状态
          ctx.restore();
        });
        
        // 绘制Y轴标签和网格线 - 左侧为正确率刻度
        ctx.setTextAlign('right');
        const ySteps = 5;
        for(let i = 0; i <= ySteps; i++) {
          const y = padding.top + (chartHeight / ySteps) * i;
          const value = maxAccuracy - (maxAccuracy / ySteps) * i;
          
          // 绘制刻度
          ctx.fillText(value + '%', padding.left - 5, y + 3);
          
          // 绘制网格线
          ctx.beginPath();
          ctx.setLineWidth(0.5);
          ctx.setLineDash([2, 2]);
          ctx.moveTo(padding.left, y);
          ctx.lineTo(width - padding.right, y);
          ctx.stroke();
          ctx.setLineDash([]);
        }
        
        // 绘制右侧Y轴标签（题目数量）- 减小字体以节省空间
        ctx.setTextAlign('left');
        ctx.setFontSize(9); // 减小字体
        for(let i = 0; i <= ySteps; i++) {
          const y = padding.top + (chartHeight / ySteps) * i;
          const value = maxTotal - (maxTotal / ySteps) * i;
          
          ctx.fillText(Math.round(value) + '题', width - padding.right + 8, y + 3); // 右侧标签向右移动到+8
        }
        
        // 绘制正确率折线 - 蓝色
        ctx.beginPath();
        ctx.setLineWidth(2);
        ctx.setStrokeStyle('#4C84FF');
        
        accuracyData.forEach((value, index) => {
          const x = padding.left + index * xStep;
          const y = padding.top + chartHeight - (value / maxAccuracy) * chartHeight;
          
          if(index === 0) {
            ctx.moveTo(x, y);
          } else {
            ctx.lineTo(x, y);
          }
          
          // 在每个数据点上添加一个圆点
          ctx.stroke();
          ctx.beginPath();
          ctx.setFillStyle('#4C84FF');
          ctx.arc(x, y, 3, 0, Math.PI * 2);
          ctx.fill();
          
          // 添加数值标签 - 标签更紧凑
          ctx.setTextAlign('center');
          ctx.setFontSize(9); // 减小字体
          ctx.fillText(Math.round(value) + '%', x, y - 8); // 减小垂直间距，使用Math.round确保值为整数
          
          ctx.beginPath();
          ctx.setStrokeStyle('#4C84FF');
          if(index === 0) {
            ctx.moveTo(x, y);
          } else {
            ctx.lineTo(x, y);
          }
        });
        ctx.stroke();
        
        // 绘制题目数量折线 - 橙色
        ctx.beginPath();
        ctx.setLineWidth(2);
        ctx.setStrokeStyle('#FF9800');
        
        totalQuestions.forEach((value, index) => {
          const x = padding.left + index * xStep;
          const y = padding.top + chartHeight - (value / maxTotal) * chartHeight;
          
          if(index === 0) {
            ctx.moveTo(x, y);
          } else {
            ctx.lineTo(x, y);
          }
          
          // 在每个数据点上添加一个圆点
          ctx.stroke();
          ctx.beginPath();
          ctx.setFillStyle('#FF9800');
          ctx.arc(x, y, 3, 0, Math.PI * 2);
          ctx.fill();
          
          // 添加数值标签 - 标签更紧凑
          ctx.setTextAlign('center');
          ctx.setFontSize(9); // 减小字体
          ctx.fillText(Math.round(value) + '题', x, y - 8); // 减小垂直间距，使用Math.round确保值为整数
          
          ctx.beginPath();
          ctx.setStrokeStyle('#FF9800');
          if(index === 0) {
            ctx.moveTo(x, y);
          } else {
            ctx.lineTo(x, y);
          }
        });
        ctx.stroke();
        
        // 绘制图例 - 左移
        ctx.beginPath();
        const legendY = height - 30;
        
        // 正确率图例 - 整体左移
        ctx.setFillStyle('#4C84FF');
        ctx.fillRect(width / 2 - 90, legendY, 10, 10);
        ctx.setTextAlign('left');
        ctx.setFontSize(10); // 保持清晰
        ctx.fillText('正确率(%)', width / 2 - 75, legendY + 7);
        
        // 题目数量图例 - 整体左移
        ctx.setFillStyle('#FF9800');
        ctx.fillRect(width / 2 - 10, legendY, 10, 10);
        ctx.fillText('题目数量(题)', width / 2 + 5, legendY + 7);
        
        // 绘制到Canvas
        ctx.draw(true);
        console.log('绘制完成');
        
        // 更新图例文本
        this.setData({
          progressLegendText: `● 正确率(%) ● 题目数量(题)`
        });
      }
    } catch (error) {
      console.error('绘制每日进度折线图出错:', error);
    }
  }
}))
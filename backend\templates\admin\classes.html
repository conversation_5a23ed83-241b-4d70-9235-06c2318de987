{% extends "admin/base.html" %}

{% block title %}班级管理 - 题库管理系统{% endblock %}

{% block header %}班级管理{% endblock %}

{% block header_buttons %}
<button type="button" class="btn btn-primary" id="openAddClassModal">
    <i class="fas fa-plus me-1"></i>添加班级
</button>
{% endblock %}

{% block content %}
<!-- 班级统计卡片 -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card h-100">
            <div class="card-body stats-card">
                <div class="number">{{ classes|length }}</div>
                <div class="label">班级总数</div>
            </div>
        </div>
    </div>
    <div class="col-md-8">
        <div class="card h-100">
            <div class="card-header">
                <i class="fas fa-info-circle me-2"></i>班级管理指南
            </div>
            <div class="card-body">
                <p class="mb-0">您可以在此页面管理所有班级信息，包括创建新班级、编辑现有班级信息以及删除不需要的班级。每个班级可以关联多个课程。</p>
            </div>
        </div>
    </div>
</div>

<!-- 搜索栏 -->
<div class="card shadow-sm mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="input-group">
                    <input type="text" id="searchInput" class="form-control" placeholder="输入班级名称搜索...">
                    <button class="btn btn-primary" type="button" id="searchButton">
                        <i class="fas fa-search"></i> 搜索
                    </button>
                </div>
            </div>
            <div class="col-md-6 text-end">
                <button type="button" class="btn btn-primary me-2" id="openAddClassModalBtn">
                    <i class="fas fa-plus me-1"></i>添加班级
                </button>
                <button class="btn btn-outline-secondary" type="button" id="resetButton">
                    <i class="fas fa-redo-alt"></i> 重置
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 班级列表卡片 -->
<div class="card shadow-sm">
    <div class="card-header bg-white">
        <div class="d-flex align-items-center">
            <i class="fas fa-users me-2 text-primary"></i>
            <h5 class="mb-0 fw-bold">班级列表</h5>
        </div>
    </div>
    <div class="card-body position-relative">
        <div class="table-responsive">
            <table class="table table-hover" id="classesTable">
                <thead>
                    <tr>
                        <th width="5%">ID</th>
                        <th width="20%">班级名称</th>
                        <th width="35%">描述</th>
                        <th width="10%">课程数量</th>
                        <th width="15%">创建时间</th>
                        <th width="15%">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for class_obj in classes %}
                    <tr id="class-row-{{ class_obj.id }}" data-id="{{ class_obj.id }}">
                        <td>{{ class_obj.id }}</td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-circle bg-primary me-2">
                                    <span>{{ class_obj.name[:1] }}</span>
                                </div>
                                <span>{{ class_obj.name }}</span>
                            </div>
                        </td>
                        <td>
                            <div class="description-text" title="{{ class_obj.description }}">
                                {{ class_obj.description or '无' }}
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-primary rounded-pill">
                                {{ class_obj.courses|length }}
                            </span>
                        </td>
                        <td>
                            <small class="text-muted">
                                <i class="far fa-calendar-alt me-1"></i>
                                {{ class_obj.created_at.strftime('%Y-%m-%d %H:%M') if class_obj.created_at else '' }}
                            </small>
                        </td>
                        <td>
                            <div class="d-flex">
                                <a href="/admin/classes/edit/{{ class_obj.id }}" class="btn btn-sm btn-outline-primary me-2" data-bs-toggle="tooltip" title="编辑班级">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-outline-danger delete-btn" data-id="{{ class_obj.id }}" data-name="{{ class_obj.name }}" data-course-count="{{ class_obj.courses|length }}" data-bs-toggle="tooltip" title="删除班级">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- 删除确认面板 -->
        <div id="delete-confirm-panel" class="delete-confirm-panel" style="display: none; position: absolute; z-index: 1000;">
            <div class="p-3">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="fas fa-exclamation-triangle text-warning"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="mb-2">
                            <strong>确定要删除班级: <span id="delete-class-name" class="text-danger"></span>?</strong>
                        </div>
                        <div id="delete-warning" class="alert alert-warning py-2 mb-3" style="display: none;">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-info-circle text-warning me-2"></i>
                                <div>
                                    <small>删除操作将会检验班级是否含有关联的课程（包括本身会随之删除的课程）。</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="ms-auto">
                        <button type="button" class="btn btn-sm btn-secondary cancel-delete-btn me-2">
                            <i class="fas fa-times me-1"></i>取消
                        </button>
                        <form id="delete-form" action="" method="post" class="d-inline delete-form">
                            <button type="submit" id="confirm-delete-btn" class="btn btn-sm btn-danger">
                                <i class="fas fa-trash me-1"></i>确认删除
                            </button>
                        </form>
                        <button type="button" id="disabled-delete-btn" class="btn btn-sm btn-danger" style="display: none;" disabled>
                            <i class="fas fa-trash me-1"></i>确认删除
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加班级模态框 -->
<div class="modal fade" id="addClassModal" tabindex="-1" aria-labelledby="addClassModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addClassModalLabel">
                    <i class="fas fa-plus text-primary me-2"></i>添加班级
                </h5>
                <button type="button" class="btn-close" id="closeModalBtn" aria-label="Close"></button>
            </div>
            <form id="addClassForm" method="post" action="/admin/classes/add">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="className" class="form-label">班级名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="className" name="name" required>
                        <div class="form-text">例如：计算机科学2023级、软件工程2022级等</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="classDescription" class="form-label">班级描述</label>
                        <textarea class="form-control" id="classDescription" name="description" rows="3"></textarea>
                        <div class="form-text">可选：对班级的详细描述</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" id="cancelModalBtn">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>添加班级
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .avatar-circle {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
    }
    
    .bg-primary {
        background-color: #4c51bf !important;
    }
    
    .badge {
        font-weight: 500;
        padding: 0.5em 0.8em;
    }

    .description-text {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        max-height: 3em;
    }
    
    /* 删除确认面板样式 */
    .delete-confirm-panel {
        background-color: #fff2f2;
        border: 1px solid #ffdbdb;
        border-left: 4px solid #f56c6c;
        border-radius: 4px;
        margin: 0;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        width: 100%;
        left: 0;
    }
    
    /* 高亮被选中的行 */
    .selected-for-delete {
        background-color: #fff9f9 !important;
    }
    
    .delete-confirm-panel .text-warning {
        color: #e6a23c !important;
    }
    
    .delete-confirm-panel .text-danger {
        color: #f56c6c !important;
    }
    
    .delete-confirm-panel .alert {
        background-color: #fdf6ec;
        border-color: #faecd8;
        color: #e6a23c;
        padding: 8px 12px;
        margin-bottom: 10px;
        font-size: 13px;
    }
    
    .delete-confirm-panel .btn-secondary {
        background-color: #909399;
        border-color: #909399;
    }
    
    .delete-confirm-panel .btn-danger {
        background-color: #f56c6c;
        border-color: #f56c6c;
    }
</style>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // 初始化数据表格
        const dataTable = $('#classesTable').DataTable({
            language: {
                "processing": "处理中...",
                "lengthMenu": "每页显示 _MENU_ 条记录",
                "zeroRecords": "没有找到匹配的记录",
                "info": "第 _PAGE_ 页 / 共 _PAGES_ 页 ( 共 _TOTAL_ 条记录 )",
                "infoEmpty": "没有记录",
                "infoFiltered": "(从 _MAX_ 条记录中过滤)",
                "search": "搜索:",
                "emptyTable": "表中没有数据",
                "paginate": {
                    "first": "首页",
                    "previous": "上一页",
                    "next": "下一页",
                    "last": "末页"
                },
                "aria": {
                    "sortAscending": ": 升序排列",
                    "sortDescending": ": 降序排列"
                },
                "autoFill": {
                    "cancel": "取消",
                    "fill": "填充所有单元格",
                    "fillHorizontal": "填充水平单元格",
                    "fillVertical": "填充垂直单元格"
                },
                "buttons": {
                    "collection": "集合",
                    "colvis": "列可见性",
                    "copy": "复制",
                    "copyTitle": "复制到剪贴板",
                    "csv": "CSV",
                    "excel": "Excel",
                    "pdf": "PDF",
                    "print": "打印"
                }
            },
            responsive: true,
            order: [[0, 'desc']],
            pageLength: 10,  // 每页显示10条记录
            lengthMenu: [
                [10, 25, 50, 100, -1],
                ['10条', '25条', '50条', '100条', '全部']
            ],
            pagingType: "full_numbers"  // 显示所有分页按钮
        });
        
        // 初始化工具提示
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
        
        // 模态框手动初始化和事件绑定
        const addClassModal = document.getElementById('addClassModal');
        let classModal;
        
        if (addClassModal) {
            classModal = new bootstrap.Modal(addClassModal, {
                backdrop: true,
                keyboard: true,
                focus: true
            });
            
            // 绑定打开模态框按钮
            $('#openAddClassModal, #openAddClassModalBtn').on('click', function() {
                classModal.show();
            });
            
            // 绑定关闭模态框按钮
            $('#closeModalBtn, #cancelModalBtn').on('click', function() {
                classModal.hide();
            });
        }

        // 处理删除按钮点击
        $(document).on('click', '.delete-btn', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // 移除其他行的高亮并隐藏确认面板
            $('.selected-for-delete').removeClass('selected-for-delete');
            $('#delete-confirm-panel').hide();
            
            // 获取班级ID和名称
            const classId = $(this).data('id');
            const className = $(this).data('name');
            const courseCount = $(this).data('course-count');
            
            // 高亮当前行
            const currentRow = $(this).closest('tr');
            currentRow.addClass('selected-for-delete');
            
            // 更新确认面板
            $('#delete-class-name').text(className);
            $('#delete-form').attr('action', `/admin/classes/delete/${classId}`);
            
            // 显示/隐藏警告和禁用按钮
            if (courseCount > 0) {
                $('#delete-warning').show();
                $('#confirm-delete-btn').hide();
                $('#disabled-delete-btn').show();
            } else {
                $('#delete-warning').hide();
                $('#confirm-delete-btn').show();
                $('#disabled-delete-btn').hide();
            }
            
            // 计算位置 - 在当前行下方
            const rowPos = currentRow.position();
            const rowHeight = currentRow.outerHeight();
            
            // 设置确认面板位置并显示
            $('#delete-confirm-panel').css({
                'top': (rowPos.top + rowHeight) + 'px'
            }).fadeIn(200);
        });
        
        // 处理取消按钮点击
        $(document).on('click', '.cancel-delete-btn', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // 隐藏确认面板
            $('#delete-confirm-panel').fadeOut(200);
            
            // 移除高亮
            $('.selected-for-delete').removeClass('selected-for-delete');
        });
        
        // 点击页面其他区域关闭确认面板
        $(document).on('click', function(e) {
            if (!$(e.target).closest('#delete-confirm-panel').length && 
                !$(e.target).closest('.delete-btn').length) {
                $('#delete-confirm-panel').fadeOut(200);
                $('.selected-for-delete').removeClass('selected-for-delete');
            }
        });
        
        // 搜索功能
        $('#searchButton').on('click', function() {
            const searchValue = $('#searchInput').val().trim();
            if (searchValue) {
                $.ajax({
                    url: '/admin/classes/search',
                    type: 'GET',
                    data: { query: searchValue },
                    success: function(response) {
                        // 清空表格
                        dataTable.clear();
                        
                        // 添加搜索结果到表格
                        if (response.classes && response.classes.length > 0) {
                            response.classes.forEach(function(class_obj) {
                                const courseCount = class_obj.course_count || 0;
                                const rowNode = dataTable.row.add([
                                    class_obj.id,
                                    `<div class="d-flex align-items-center">
                                        <div class="avatar-circle bg-primary me-2">
                                            <span>${class_obj.name.charAt(0)}</span>
                                        </div>
                                        <span>${class_obj.name}</span>
                                    </div>`,
                                    `<div class="description-text" title="${class_obj.description || ''}">
                                        ${class_obj.description || '无'}
                                    </div>`,
                                    `<span class="badge bg-primary rounded-pill">
                                        ${courseCount}
                                    </span>`,
                                    `<small class="text-muted">
                                        <i class="far fa-calendar-alt me-1"></i>
                                        ${class_obj.created_at}
                                    </small>`,
                                    `<div class="d-flex">
                                        <a href="/admin/classes/edit/${class_obj.id}" class="btn btn-sm btn-outline-primary me-2" data-bs-toggle="tooltip" title="编辑班级">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger delete-btn" data-id="${class_obj.id}" data-name="${class_obj.name}" data-course-count="${courseCount}" data-bs-toggle="tooltip" title="删除班级">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>`
                                ]).draw().node();
                            });
                        } else {
                            alert('没有找到匹配的班级！');
                            window.location.reload();
                        }
                    },
                    error: function() {
                        alert('搜索失败，请稍后再试！');
                    }
                });
            } else {
                window.location.reload();
            }
        });

        // 重置按钮
        $('#resetButton').on('click', function() {
            $('#searchInput').val('');
            window.location.reload();
        });

        // 回车键搜索
        $('#searchInput').on('keypress', function(e) {
            if (e.which === 13) {
                $('#searchButton').click();
                e.preventDefault();
            }
        });

        // 添加班级表单提交
        $('#addClassForm').on('submit', function(e) {
            e.preventDefault();
            
            // 验证表单数据
            const className = $('#className').val().trim();
            
            if (!className) {
                alert('请输入班级名称');
                return false;
            }
            
            const formData = $(this).serialize();
            
            $.ajax({
                url: '/admin/classes/add',
                type: 'POST',
                data: formData,
                success: function(response) {
                    // 关闭模态框
                    if (classModal) {
                        classModal.hide();
                    }
                    
                    // 显示成功消息并刷新页面
                    setTimeout(function() {
                        window.location.reload();
                    }, 300);
                },
                error: function(xhr) {
                    let errorMessage = '添加班级失败，请稍后再试！';
                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        errorMessage = xhr.responseJSON.error;
                    }
                    alert(errorMessage);
                }
            });
        });
    });
</script>
{% endblock %} 
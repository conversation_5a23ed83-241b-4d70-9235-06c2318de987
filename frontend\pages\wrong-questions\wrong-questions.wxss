/* pages/wrong-questions/wrong-questions.wxss */
.container {
  min-height: 100vh;
  padding: 30rpx;
  padding-bottom: 140rpx; /* 为底部tabBar留出足够空间 */
  background-color: #f5f7fa;
}

/* 页面标题 */
.header {
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
}

.subtitle {
  font-size: 28rpx;
  color: #999;
  margin-top: 10rpx;
  display: block;
}

/* 选项卡 */
.tabs {
  display: flex;
  background-color: #fff;
  border-radius: 10rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.tab {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab.active {
  color: #4C84FF;
  font-weight: bold;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 25%;
  width: 50%;
  height: 6rpx;
  background-color: #4C84FF;
  border-radius: 3rpx;
}

/* 课程筛选 */
.course-filter {
  margin-bottom: 30rpx;
  background-color: #fff;
  padding: 20rpx 0;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-top: 10rpx;
}

.course-scroll {
  width: 100%;
  white-space: nowrap;
}

.course-tabs {
  padding: 0 20rpx;
  display: flex;
}

.course-tab {
  display: inline-flex;
  align-items: center;
  padding: 12rpx 24rpx;
  margin-right: 16rpx;
  font-size: 26rpx;
  color: #666;
  background-color: #f5f7fa;
  border-radius: 30rpx;
  position: relative;
  transition: all 0.3s;
}

.course-tab.active {
  color: #fff;
  background-color: #4C84FF;
  font-weight: 500;
}

.course-count {
  margin-left: 8rpx;
  font-size: 22rpx;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 2rpx 10rpx;
  border-radius: 20rpx;
}

.course-tab.active .course-count {
  background-color: rgba(255, 255, 255, 0.3);
  color: #fff;
}

/* 开始练习按钮 */
.action-buttons {
  margin-bottom: 30rpx;
}

.practice-all-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 45rpx;
  background-color: #4C84FF;
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}

/* 加载中 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
}

.loading {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #4C84FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 500rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.empty-hint {
  font-size: 28rpx;
  color: #999;
}

/* 错题列表 */
.question-list {
  padding-bottom: 50rpx;
}

.question-card {
  background-color: white;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.question-type {
  display: flex;
  align-items: center;
}

.type-tag {
  display: inline-block;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  margin-right: 10rpx;
}

.type-tag.single {
  background-color: #e8f0ff;
  color: #4C84FF;
}

.type-tag.multiple {
  background-color: #fff0e5;
  color: #ff9800;
}

.type-tag.judgment {
  background-color: #f5fff7;
  color: #4caf50;
}

.type-tag.fillblank {
  background-color: #fff5f2;
  color: #ff7043;
}

.course-name {
  font-size: 24rpx;
  color: #666;
  background-color: #f0f0f0;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  margin-left: 10rpx;
}

.wrong-count {
  font-size: 24rpx;
  color: #ff4d4f;
}

.last-wrong-time {
  font-size: 24rpx;
  color: #999;
}

.question-content {
  margin-bottom: 20rpx;
}

.question-text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.5;
}

/* 选项样式 */
.options-list {
  margin-bottom: 20rpx;
}

.option {
  display: flex;
  align-items: center;
  padding: 15rpx;
  border-radius: 8rpx;
  margin-bottom: 15rpx;
  background-color: #f9f9f9;
  position: relative;
}

.option.correct {
  background-color: #e3ffe3;
}

.option-letter {
  width: 40rpx;
  height: 40rpx;
  line-height: 40rpx;
  text-align: center;
  border-radius: 50%;
  background-color: #eee;
  font-size: 24rpx;
  margin-right: 15rpx;
  color: #666;
}

.option.correct .option-letter {
  background-color: #4CAF50;
  color: white;
}

.option-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.correct-mark {
  color: #4CAF50;
  font-size: 32rpx;
  font-weight: bold;
  margin-left: 10rpx;
}

/* 卡片操作按钮 */
.card-actions {
  display: flex;
  justify-content: flex-end;
}

.action-btn {
  min-width: 160rpx;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 28rpx;
  margin-left: 20rpx;
  padding: 0 30rpx;
}

.practice-btn {
  background-color: #4C84FF;
  color: white;
}

.remove-btn {
  background-color: #f5f5f5;
  color: #666;
} 
/* pages/result/result.wxss */
.container {
  padding: 30rpx;
  min-height: 100vh;
  background-color: #f5f7fa;
}

.result-header {
  margin-bottom: 40rpx;
  text-align: center;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 32rpx;
  color: #666;
}

.stats-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.stats-row {
  display: flex;
  justify-content: space-around;
  margin-bottom: 30rpx;
}

.stats-row:last-child {
  margin-bottom: 0;
}

.stats-item {
  text-align: center;
  flex: 1;
}

.stats-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #4C84FF;
  display: block;
  margin-bottom: 10rpx;
}

.stats-label {
  font-size: 28rpx;
  color: #666;
}

.buttons-container {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.primary-button {
  background-color: #4C84FF;
  color: #fff;
  border-radius: 45rpx;
  height: 90rpx;
  line-height: 90rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.secondary-button {
  background-color: #f5f5f5;
  color: #666;
  border-radius: 45rpx;
  height: 90rpx;
  line-height: 90rpx;
  font-size: 32rpx;
} 
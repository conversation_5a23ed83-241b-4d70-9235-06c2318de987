.cat-container {
  position: relative;
  z-index: 9999;
  pointer-events: none;
  width: auto;
  height: auto;
}

.animation-container {
  position: relative;
  width: 80px;
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  transform-origin: center center;
  transform: translateZ(0);
  will-change: transform;
}

.animation-container.meowing {
  animation: meow 1s ease-in-out;
}

.cat-image {
  position: relative;
  width: 80px;
  height: 80px;
  z-index: 10000;
  background: none;
  border: none;
  box-shadow: none;
  border-radius: 0;
  object-fit: contain;
  pointer-events: auto;
  opacity: 1;
  animation: catAnimate 4s ease-in-out infinite;
  transform-origin: bottom center;
  will-change: transform, filter;
}

.cat-image:active {
  transform: scale(0.95);
}

.cat-image.visible {
  transform: scale(1.05);
}

/* 不再给图片本身添加动画，而是放在容器上 */

.chat-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 300px;
  height: 400px;
  background-color: #fff;
  border-radius: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: 999;
  pointer-events: auto;
  transition: opacity 0.3s ease, transform 0.3s ease, width 0.3s ease, height 0.3s ease;
  will-change: transform, left, top;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}

/* 最小化状态 */
.chat-container.minimized {
  transform: scale(0.1) translateZ(0);
  opacity: 0;
  pointer-events: none;
}

/* 放大状态 */
.chat-container.expanded {
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  border-radius: 0;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #4C84FF;
  color: white;
  cursor: move;
  user-select: none;
  -webkit-user-select: none;
  touch-action: none;
}

.header-controls {
  display: flex;
  align-items: center;
}

.control-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  margin-left: 12px;
  color: white;
  font-size: 18px;
  opacity: 0.8;
  transition: all 0.2s ease;
}

.control-icon:hover, 
.control-icon:active {
  opacity: 1;
  transform: scale(1.1);
}

.close-icon {
  font-size: 22px;
}

/* AI服务选择器 */
.ai-service-selector {
  padding: 8px 12px;
  background-color: #f7f7f7;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.service-info {
  font-size: 13px;
  color: #4C84FF;
  font-weight: 500;
}

.reset-button {
  font-size: 12px;
  color: #666;
  background: #e8e8e8;
  padding: 3px 8px;
  border-radius: 10px;
}

.reset-button:active {
  background: #ddd;
}

.ai-model-picker {
  margin-top: 6px;
  padding-top: 6px;
  border-top: 1px dashed #e0e0e0;
}

.picker-arrow {
  font-size: 10px;
  margin-left: 5px;
}

/* 确保滚动条可用 */
.chat-messages {
  flex: 1;
  padding: 18px 0;
  background-color: #f7f7f7;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 提升iOS滑动性能 */
}

/* 消息行样式 */
.message-row {
  display: flex;
  margin-bottom: 16px;
  align-items: flex-start;
}

/* 用户消息行 - 反向排列，用户消息和头像在右侧 */
.user-row {
  flex-direction: row-reverse;
}

/* 助手消息行 */
.assistant-row {
  flex-direction: row;
}

/* 头像样式 */
.avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  flex-shrink: 0;
  border: 1px solid #efefef;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.ai-avatar {
  margin-right: 8px;
}

.user-avatar {
  margin-left: 8px;
}

/* 消息气泡样式 */
.message {
  max-width: 70%;
  padding: 10px 15px;
  border-radius: 15px;
  word-wrap: break-word;
  position: relative;
}

.user-message {
  background-color: #4C84FF;
  color: white;
  border-top-right-radius: 4px;
}

.bot-message {
  background-color: #e6e6e6;
  color: #333;
  border-top-left-radius: 4px;
}

.loading {
  display: flex;
  justify-content: center;
  margin: 10px 0;
  color: #888;
}

/* 打字指示器样式 */
.typing-indicator {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #e6e6e6;
  border-radius: 15px;
}

.dot {
  height: 8px;
  width: 8px;
  border-radius: 50%;
  background-color: #4C84FF;
  margin: 0 2px;
  animation: typing 1.4s infinite ease-in-out;
  opacity: 0.6;
}

.dot:nth-child(1) {
  animation-delay: 0s;
}

.dot:nth-child(2) {
  animation-delay: 0.2s;
}

.dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-4px);
  }
}

.chat-input {
  display: flex;
  padding: 10px;
  border-top: 1px solid #eee;
  background-color: white;
}

.chat-input input {
  flex: 1;
  height: 36px;
  border: 1px solid #ddd;
  border-radius: 18px;
  padding: 0 15px;
  margin-right: 10px;
}

.send-btn {
  background-color: #4C84FF;
  color: white;
  border-radius: 18px;
  font-size: 14px;
  padding: 0 15px;
  height: 36px;
  line-height: 36px;
  border: none;
}

/* 最小化状态 */
.chat-container.minimized .chat-messages,
.chat-container.minimized .chat-input {
  display: none;
}

.chat-container.minimized .chat-header {
  border-radius: 0 0 15px 15px;
}

/* 扩展状态下的消息区域 */
.chat-container.expanded .chat-messages {
  max-height: calc(100vh - 120px);
} 

/* 自定义粉色猫爪图标 */
.pink-paw {
  display: inline-block;
  color: #ff6b9d;
  font-size: 16px;
  position: relative;
  top: 2px;
}

/* 使消息文本支持自定义样式 */
.bot-message text {
  word-break: break-word;
  white-space: pre-wrap;
}

/* 猫咪呼吸动画 */
@keyframes breathe {
  0% { transform: scale(1); }
  50% { transform: scale(1.03); }
  100% { transform: scale(1); }
}

/* 猫咪摇晃动画 */
@keyframes wiggle {
  0% { transform: rotate(0deg); }
  25% { transform: rotate(-2deg); }
  50% { transform: rotate(0deg); }
  75% { transform: rotate(2deg); }
  100% { transform: rotate(0deg); }
}

/* 猫咪上下浮动动画 */
@keyframes float {
  0% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
  100% { transform: translateY(0); }
}

/* 组合动画 */
@keyframes catAnimate {
  0% { 
    transform: translateY(0) scale(1) rotate(0deg);
    filter: brightness(1);
  }
  25% { 
    transform: translateY(-2px) scale(1.01) rotate(-1deg);
    filter: brightness(1.02); 
  }
  50% { 
    transform: translateY(-4px) scale(1.02) rotate(0deg);
    filter: brightness(1.03);
  }
  75% { 
    transform: translateY(-2px) scale(1.01) rotate(1deg);
    filter: brightness(1.02);
  }
  100% { 
    transform: translateY(0) scale(1) rotate(0deg);
    filter: brightness(1);
  }
}

/* 猫咪喵喵叫动画 - 减弱动画效果，防止窗口抖动 */
@keyframes meow {
  0% { transform: scale(1) rotate(0deg); }
  20% { transform: scale(1.02) rotate(-0.2deg); }
  40% { transform: scale(1.01) rotate(0.2deg); }
  60% { transform: scale(1.02) rotate(-0.1deg); }
  80% { transform: scale(1.01) rotate(0.1deg); }
  100% { transform: scale(1) rotate(0deg); }
}

/* 喵喵叫气泡动画 */
@keyframes showMeowBubble {
  0% { 
    opacity: 0;
    transform: scale(0.5) translateY(10px);
  }
  20% { 
    opacity: 1;
    transform: scale(1.1) translateY(-5px);
  }
  80% { 
    opacity: 1;
    transform: scale(1) translateY(-10px);
  }
  100% { 
    opacity: 0;
    transform: scale(0.8) translateY(-15px);
  }
}

/* 喵喵叫气泡样式 */
.meow-bubble {
  position: absolute;
  top: -30px;
  right: -15px;
  background-color: white;
  border-radius: 15px;
  padding: 5px 10px;
  font-size: 14px;
  color: #333;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  opacity: 0;
  pointer-events: none;
  transform-origin: bottom center;
  z-index: 10001;
  min-width: 40px;
  text-align: center;
}

.meow-bubble:after {
  content: "";
  position: absolute;
  bottom: -6px;
  right: 20px;
  width: 12px;
  height: 12px;
  background-color: white;
  transform: rotate(45deg);
  box-shadow: 2px 2px 2px rgba(0,0,0,0.05);
}

.meow-bubble.show {
  animation: showMeowBubble 2s ease-in-out forwards;
}

.cat-wrapper {
  position: fixed;
  width: 100px;
  height: 100px;
  z-index: 10000;
  pointer-events: auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 猫咪提示气泡样式 */
.cat-tooltip {
  position: absolute;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(76, 132, 255, 0.9);
  color: white;
  padding: 6px 10px;
  border-radius: 15px;
  font-size: 12px;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  opacity: 0;
  z-index: 10001;
  animation: cat-tooltip-pulse 6s ease-in-out infinite;
  pointer-events: none; /* 确保不影响点击 */
  border: 1px solid rgba(255, 255, 255, 0.6);
}

/* 气泡尾巴 */
.cat-tooltip::after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid rgba(76, 132, 255, 0.9);
}

/* 气泡淡入淡出动画 */
@keyframes cat-tooltip-pulse {
  0%, 100% {
    opacity: 0;
    transform: translateX(-50%) translateY(5px);
  }
  15%, 25% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
  40% {
    opacity: 0;
    transform: translateX(-50%) translateY(-5px);
  }
} 
{% extends "admin/base.html" %}

{% block title %}用户详情 - 大数据题库管理后台{% endblock %}

{% block additional_head %}
<link rel="stylesheet" href="/static/css/admin.css">
<style>
    /* 记录高亮样式 */
    .highlight-record {
        background-color: rgba(13, 110, 253, 0.1);
        transition: background-color 0.5s ease;
    }
    
    /* 记录项悬停效果 */
    .record-item:hover {
        background-color: rgba(0, 0, 0, 0.02);
    }
    
    /* 记录头部悬停效果 */
    .record-header:hover {
        background-color: rgba(0, 0, 0, 0.03);
    }
    
    /* 搜索高亮样式 */
    mark {
        background-color: #ffeb3b;
        padding: 0 2px;
        border-radius: 2px;
    }
    
    /* 隐藏由搜索过滤的元素 */
    .hidden-by-search {
        display: none !important;
    }
    
    /* 改进筛选按钮样式 */
    .filter-btn.active {
        font-weight: bold;
    }
    
    /* 搜索框样式优化 */
    #quickSearchInput {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }
    
    #quickSearchBtn {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }

    /* 用户卡片样式优化 */
    .user-profile-card {
        border-radius: 10px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    
    /* 学习概览图表样式 */
    .learning-overview .card {
        border-radius: 12px;
    }
    
    .overview-item {
        padding: 12px 8px;
        border-radius: 10px;
        transition: all 0.3s ease;
    }
    
    .overview-item:hover {
        background-color: rgba(248, 249, 250, 0.9);
        transform: translateY(-3px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    }
    
    .overview-icon {
        font-size: 1.5rem;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .overview-value {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 4px;
    }
    
    .overview-label {
        font-size: 0.8rem;
        color: #6c757d;
    }

    .user-avatar-wrapper {
        position: relative;
    }

    .user-avatar {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid #f8f9fa;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .user-avatar-placeholder {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        background-color: #e9ecef;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2.5rem;
        color: #6c757d;
        margin: 0 auto;
    }

    .admin-badge {
        top: 0;
        right: calc(50% - 60px);
        font-size: 0.7rem;
    }

    .user-id {
        color: #6c757d;
        font-size: 0.9rem;
    }

    .user-stats {
        border-top: 1px solid #eee;
        border-bottom: 1px solid #eee;
        padding: 10px 0;
    }

    .stat-item {
        flex: 1;
        text-align: center;
    }

    .stat-value {
        font-weight: bold;
        font-size: 1.2rem;
        color: #495057;
    }

    .stat-label {
        font-size: 0.8rem;
        color: #6c757d;
    }

    .stat-divider {
        width: 1px;
        background-color: #eee;
        margin: 0 10px;
    }

    .user-contact-info {
        color: #6c757d;
    }

    /* 班级信息样式 */
    .user-class {
        background-color: #f8f9fa !important;
        border-color: #dee2e6 !important;
        border-left: 4px solid #0d6efd !important;
        transition: all 0.3s ease;
    }

    .user-class:hover {
        background-color: #f1f4f7 !important;
        transform: translateY(-2px);
        box-shadow: 0 3px 5px rgba(0, 0, 0, 0.05);
    }

    .user-class .fa-users {
        color: #0d6efd;
        font-size: 1.2rem;
    }

    /* 联系信息卡片样式 */
    .user-contact-info .card {
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .user-contact-info .card:hover {
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.06);
        transform: translateY(-2px);
    }

    .user-contact-info .card-title {
        color: #495057;
        font-size: 0.95rem;
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 8px;
    }

    .user-contact-info .text-muted {
        font-size: 0.8rem;
    }

    /* 题型统计表格样式 */
    .table-question-stats {
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        border-radius: 5px;
    }

    .table-question-stats thead th {
        background-color: #f8f9fa;
        border-bottom: 2px solid #e3e6f0;
        font-weight: 600;
        text-align: center;
    }

    .table-question-stats tbody td {
        vertical-align: middle;
        text-align: center;
    }
    
    /* 能力水平图表样式 */
    .skill-progress-container {
        padding: 10px;
        background: #f8f9fa;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }
    
    .skill-progress {
        margin-bottom: 15px !important;
    }
    
    .skill-progress:last-child {
        margin-bottom: 0 !important;
    }
    
    .skill-label {
        font-size: 0.9rem;
        font-weight: 500;
        color: #495057;
    }
    
    .skill-value {
        font-size: 0.9rem;
        font-weight: 600;
        color: #495057;
    }
    
    .progress {
        height: 12px;
        border-radius: 6px;
        background-color: rgba(0, 0, 0, 0.05);
        overflow: visible;
    }
    
    .progress-bar {
        border-radius: 6px;
        position: relative;
        transition: width 1s ease;
    }
    
    /* 自定义进度条颜色 */
    .progress-bar.bg-primary {
        background-color: #4e73df !important;
    }
    
    .progress-bar.bg-success {
        background-color: #1cc88a !important;
    }
    
    .progress-bar.bg-info {
        background-color: #36b9cc !important;
    }
    
    .progress-bar.bg-warning {
        background-color: #f6c23e !important;
    }
    
    .progress-bar.bg-danger {
        background-color: #e74a3b !important;
    }
    
    .progress-bar.bg-secondary {
        background-color: #858796 !important;
    }

    /* 自定义题型颜色 */
    .bg-success {
        background-color: #6f42c1;
    }

    /* 高亮行 */
    .table-question-stats tbody tr:hover {
        background-color: rgba(0, 0, 0, 0.02);
    }

    /* 正确率颜色样式 */
    .accuracy-high {
        color: #28a745;
        font-weight: 600;
    }

    .accuracy-medium {
        color: #fd7e14;
        font-weight: 600;
    }

    .accuracy-low {
        color: #dc3545;
        font-weight: 600;
    }
    

    /* 成就卡片样式 */
    .achievement-card {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .achievement-unlocked {
        background: linear-gradient(45deg, rgba(255, 255, 255, 1) 0%, rgba(240, 249, 255, 1) 100%);
        border: 1px solid #b3d7ff;
        box-shadow: 0 3px 10px rgba(0, 123, 255, 0.1);
    }

    .achievement-locked {
        opacity: 0.7;
        filter: grayscale(1);
    }

    .achievement-icon {
        font-size: 1.75rem;
        color: #6c757d;
    }

    .achievement-unlocked .achievement-icon {
        color: #0d6efd;
    }

    .achievement-title {
        font-size: 0.85rem;
        font-weight: 600;
    }

    .achievement-progress {
        font-size: 0.7rem;
    }

    .achievement-unlocked .progress-bar {
        background-color: #0d6efd;
    }

    .achievement-locked .progress-bar {
        background-color: #6c757d;
    }

    /* 统计卡片样式 */
    .stat-card {
        border: 1px solid #e9ecef;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .stat-card h2 {
        font-size: 1.75rem;
        color: #343a40;
    }

    .time-distribution .progress {
        background-color: #e9ecef;
        border-radius: 4px;
    }

    /* 活跃度热图样式 */
    .heatmap-scale {
        display: flex;
        align-items: center;
    }

    .heatmap-level {
        width: 12px;
        height: 12px;
        margin: 0 1px;
    }

    .heatmap-container {
        display: flex;
        margin-top: 10px;
    }

    .heatmap-labels {
        display: flex;
        flex-direction: column;
        margin-right: 5px;
        justify-content: space-around;
    }

    .heatmap-day-label {
        font-size: 12px;
        color: #6c757d;
        height: 15px;
        margin-bottom: 5px;
    }

    .heatmap-grid {
        display: grid;
        grid-template-columns: repeat(35, 15px);
        grid-template-rows: repeat(7, 15px);
        grid-gap: 3px;
    }

    .heatmap-cell {
        width: 15px;
        height: 15px;
        border-radius: 2px;
        position: relative;
    }

    .heatmap-cell.has-tooltip:hover .heatmap-tooltip {
        display: block;
    }

    .heatmap-tooltip {
        display: none;
        position: absolute;
        background-color: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 5px 8px;
        border-radius: 4px;
        font-size: 12px;
        bottom: 120%;
        left: 50%;
        transform: translateX(-50%);
        white-space: nowrap;
        z-index: 10;
    }

    .heatmap-tooltip:after {
        content: '';
        position: absolute;
        top: 100%;
        left: 50%;
        margin-left: -5px;
        border-width: 5px;
        border-style: solid;
        border-color: rgba(0, 0, 0, 0.8) transparent transparent transparent;
    }

    /* 活跃时间图表样式 */
    .hour-chart {
        display: flex;
        height: 200px;
        align-items: flex-end;
        margin-top: 10px;
        padding-bottom: 20px;
        border-bottom: 1px solid #dee2e6;
        position: relative;
    }

    .hour-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        height: 100%;
    }

    .hour-bar {
        width: 80%;
        background-color: #4c84ff;
        border-radius: 2px 2px 0 0;
        position: relative;
    }

    .hour-bar:hover .hour-tooltip {
        display: block;
    }

    .hour-tooltip {
        display: none;
        position: absolute;
        background-color: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 5px 8px;
        border-radius: 4px;
        font-size: 12px;
        bottom: 105%;
        left: 50%;
        transform: translateX(-50%);
        white-space: nowrap;
        z-index: 10;
    }

    .hour-label {
        font-size: 10px;
        color: #6c757d;
        margin-top: 5px;
    }

    /* 每3小时显示一次标签 */
    .hour-column:nth-child(3n) .hour-label {
        font-weight: bold;
        color: #495057;
    }

    .hour-column:not(:nth-child(3n)) .hour-label {
        visibility: hidden;
    }

    /* 学习连续性样式 */
    .streak-badges {
        display: flex;
        margin-top: 10px;
    }

    .streak-badge {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #e9ecef;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6c757d;
    }

    .streak-badge.active {
        background-color: #28a745;
        color: white;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .weekly-stats .stat-column {
        padding: 10px;
        width: 23%;
        border-radius: 5px;
        background-color: #f8f9fa;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    }

    .weekly-stats .stat-value {
        font-size: 1.5rem;
        font-weight: bold;
        color: #4c84ff;
    }
</style>
{% endblock %}

{% block header %}
用户详情: {{ user.nickname or "用户" + user.id|string }}
{% endblock %}

{% block custom_head %}
<style>
    /* 题型统计表格样式 */
    .table-question-stats {
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        border-radius: 5px;
    }

    .table-question-stats thead th {
        background-color: #f8f9fa;
        border-bottom: 2px solid #e3e6f0;
        font-weight: 600;
        text-align: center;
    }

    .table-question-stats tbody td {
        vertical-align: middle;
        text-align: center;
    }
    
    /* 能力水平图表样式 */
    .skill-progress-container {
        padding: 10px;
        background: #f8f9fa;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }
    
    .skill-progress {
        margin-bottom: 15px !important;
    }
    
    .skill-progress:last-child {
        margin-bottom: 0 !important;
    }
    
    .skill-label {
        font-size: 0.9rem;
        font-weight: 500;
        color: #495057;
    }
    
    .skill-value {
        font-size: 0.9rem;
        font-weight: 600;
        color: #495057;
    }
    
    .progress {
        height: 12px;
        border-radius: 6px;
        background-color: rgba(0, 0, 0, 0.05);
        overflow: visible;
    }
    
    .progress-bar {
        border-radius: 6px;
        position: relative;
        transition: width 1s ease;
    }
    
    /* 自定义进度条颜色 */
    .progress-bar.bg-primary {
        background-color: #4e73df !important;
    }
    
    .progress-bar.bg-success {
        background-color: #1cc88a !important;
    }
    
    .progress-bar.bg-info {
        background-color: #36b9cc !important;
    }
    
    .progress-bar.bg-warning {
        background-color: #f6c23e !important;
    }
    
    .progress-bar.bg-danger {
        background-color: #e74a3b !important;
    }
    
    .progress-bar.bg-secondary {
        background-color: #858796 !important;
    }

    /* 自定义题型颜色 */
    .bg-success {
        background-color: #6f42c1;
    }

    /* 高亮行 */
    .table-question-stats tbody tr:hover {
        background-color: rgba(0, 0, 0, 0.02);
    }

    /* 正确率颜色样式 */
    .accuracy-high {
        color: #28a745;
        font-weight: 600;
    }

    .accuracy-medium {
        color: #fd7e14;
        font-weight: 600;
    }

    .accuracy-low {
        color: #dc3545;
        font-weight: 600;
    }
    

    /* 成就卡片样式 */
    .achievement-card {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .achievement-unlocked {
        background: linear-gradient(45deg, rgba(255, 255, 255, 1) 0%, rgba(240, 249, 255, 1) 100%);
        border: 1px solid #b3d7ff;
        box-shadow: 0 3px 10px rgba(0, 123, 255, 0.1);
    }

    .achievement-locked {
        opacity: 0.7;
        filter: grayscale(1);
    }

    .achievement-icon {
        font-size: 1.75rem;
        color: #6c757d;
    }

    .achievement-unlocked .achievement-icon {
        color: #0d6efd;
    }

    .achievement-title {
        font-size: 0.85rem;
        font-weight: 600;
    }

    .achievement-progress {
        font-size: 0.7rem;
    }

    .achievement-unlocked .progress-bar {
        background-color: #0d6efd;
    }

    .achievement-locked .progress-bar {
        background-color: #6c757d;
    }

    /* 统计卡片样式 */
    .stat-card {
        border: 1px solid #e9ecef;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .stat-card h2 {
        font-size: 1.75rem;
        color: #343a40;
    }

    .time-distribution .progress {
        background-color: #e9ecef;
        border-radius: 4px;
    }

    /* 活跃度热图样式 */
    .heatmap-scale {
        display: flex;
        align-items: center;
    }

    .heatmap-level {
        width: 12px;
        height: 12px;
        margin: 0 1px;
    }

    .heatmap-container {
        display: flex;
        margin-top: 10px;
    }

    .heatmap-labels {
        display: flex;
        flex-direction: column;
        margin-right: 5px;
        justify-content: space-around;
    }

    .heatmap-day-label {
        font-size: 12px;
        color: #6c757d;
        height: 15px;
        margin-bottom: 5px;
    }

    .heatmap-grid {
        display: grid;
        grid-template-columns: repeat(35, 15px);
        grid-template-rows: repeat(7, 15px);
        grid-gap: 3px;
    }

    .heatmap-cell {
        width: 15px;
        height: 15px;
        border-radius: 2px;
        position: relative;
    }

    .heatmap-cell.has-tooltip:hover .heatmap-tooltip {
        display: block;
    }

    .heatmap-tooltip {
        display: none;
        position: absolute;
        background-color: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 5px 8px;
        border-radius: 4px;
        font-size: 12px;
        bottom: 120%;
        left: 50%;
        transform: translateX(-50%);
        white-space: nowrap;
        z-index: 10;
    }

    .heatmap-tooltip:after {
        content: '';
        position: absolute;
        top: 100%;
        left: 50%;
        margin-left: -5px;
        border-width: 5px;
        border-style: solid;
        border-color: rgba(0, 0, 0, 0.8) transparent transparent transparent;
    }

    /* 活跃时间图表样式 */
    .hour-chart {
        display: flex;
        height: 200px;
        align-items: flex-end;
        margin-top: 10px;
        padding-bottom: 20px;
        border-bottom: 1px solid #dee2e6;
        position: relative;
    }

    .hour-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        height: 100%;
    }

    .hour-bar {
        width: 80%;
        background-color: #4c84ff;
        border-radius: 2px 2px 0 0;
        position: relative;
    }

    .hour-bar:hover .hour-tooltip {
        display: block;
    }

    .hour-tooltip {
        display: none;
        position: absolute;
        background-color: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 5px 8px;
        border-radius: 4px;
        font-size: 12px;
        bottom: 105%;
        left: 50%;
        transform: translateX(-50%);
        white-space: nowrap;
        z-index: 10;
    }

    .hour-label {
        font-size: 10px;
        color: #6c757d;
        margin-top: 5px;
    }

    /* 每3小时显示一次标签 */
    .hour-column:nth-child(3n) .hour-label {
        font-weight: bold;
        color: #495057;
    }

    .hour-column:not(:nth-child(3n)) .hour-label {
        visibility: hidden;
    }

    /* 学习连续性样式 */
    .streak-badges {
        display: flex;
        margin-top: 10px;
    }

    .streak-badge {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #e9ecef;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6c757d;
    }

    .streak-badge.active {
        background-color: #28a745;
        color: white;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .weekly-stats .stat-column {
        padding: 10px;
        width: 23%;
        border-radius: 5px;
        background-color: #f8f9fa;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    }

    .weekly-stats .stat-value {
        font-size: 1.5rem;
        font-weight: bold;
        color: #4c84ff;
    }
</style>
{% endblock %}

{% block content %}
<!-- 顶部操作按钮 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="action-buttons">
            <a href="/admin/users" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> 返回用户列表
            </a>
            <button type="button" class="btn btn-primary ms-2" id="editUserInfoBtn">
                <i class="fas fa-user-edit me-1"></i> 编辑用户信息
            </button>
            <div class="btn-group ms-2">
                <button type="button" class="btn btn-outline-primary" id="messageUserBtn">
                    <i class="fas fa-envelope me-1"></i> 发送消息
                </button>
                <button type="button" class="btn btn-outline-primary" id="refreshDataBtn">
                    <i class="fas fa-sync-alt me-1"></i> 刷新数据
                </button>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown"
                        aria-expanded="false">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><button class="dropdown-item" id="exportDataBtn"><i class="fas fa-file-export me-2"></i>
                                导出数据</button></li>
                        <li><button class="dropdown-item" id="printReportBtn"><i class="fas fa-print me-2"></i>
                                打印报告</button></li>
                        <li>
                            <hr class="dropdown-divider">
                        </li>
                        {% if user.is_active %}
                        <li>
                            <button class="dropdown-item text-danger" id="disableUserBtn">
                                <i class="fas fa-ban me-2"></i> 禁用账户
                            </button>
                        </li>
                        {% else %}
                        <li>
                            <button class="dropdown-item text-success" id="enableUserBtn">
                                <i class="fas fa-check-circle me-2"></i> 启用账户
                            </button>
                        </li>
                        {% endif %}
                        <li>
                            <hr class="dropdown-divider">
                        </li>
                        <li>
                            <button class="dropdown-item text-danger" id="deleteUserBtn">
                                <i class="fas fa-trash-alt me-2"></i> 删除用户
                            </button>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 用户资料卡片 -->
<div class="row mb-4">
    <div class="col-md-4 mb-4 mb-md-0">
        <div class="card user-profile-card mb-4">
            <div class="card-body text-center">
                <div class="user-avatar-wrapper mb-3">
                    {% if user.avatar %}
                    <img src="{{ user.avatar }}" alt="头像" class="user-avatar">
                    {% else %}
                    <div class="user-avatar-placeholder">
                        <i class="fas fa-user"></i>
                    </div>
                    {% endif %}
                    {% if user.is_admin %}
                    <span class="badge bg-danger position-absolute admin-badge">管理员</span>
                    {% endif %}
                </div>

                <h4 class="mb-1">{{ user.nickname or "用户" + user.id|string }}</h4>
                <div class="user-id mb-3">ID: {{ user.id }}</div>

                <div class="user-stats d-flex justify-content-center mb-3">
                    <div class="stat-item">
                        <div class="stat-value">{{ total_questions }}</div>
                        <div class="stat-label">答题数</div>
                    </div>
                    <div class="stat-divider"></div>
                    <div class="stat-item">
                        <div class="stat-value">{{ "%.1f"|format(accuracy * 100) }}%</div>
                        <div class="stat-label">正确率</div>
                    </div>
                    <div class="stat-divider"></div>
                    <div class="stat-item">
                        <div class="stat-value">{{ user_rank or "N/A" }}</div>
                        <div class="stat-label">排名</div>
                    </div>
                </div>

                <div class="user-status mb-3">
                    {% if user.is_active %}
                    <span class="badge rounded-pill bg-success">活跃</span>
                    {% else %}
                    <span class="badge rounded-pill bg-danger">已禁用</span>
                    {% endif %}

                    {% set days_since = ((now - user.created_at).days)|default(0) %}
                    {% if days_since < 7 %} <span class="badge rounded-pill bg-primary">新用户</span>
                        {% elif days_since > 180 %}
                        <span class="badge rounded-pill bg-info">资深用户</span>
                        {% endif %}

                        {% if total_questions > 100 %}
                        <span class="badge rounded-pill bg-warning">答题达人</span>
                        {% endif %}
                </div>

                <!-- 班级信息 -->
                {% if user.class_id %}
                <div class="user-class mb-3 p-2 border rounded bg-light">
                    <div class="d-flex align-items-center mb-1">
                        <i class="fas fa-users text-primary me-2"></i>
                        <div>
                            <div class="small text-muted">所属班级</div>
                            <div class="fw-bold">{{ user.class_.name }}</div>
                        </div>
                        <button class="btn btn-sm btn-outline-primary ms-auto edit-class-btn" id="editClassBtn">
                            <i class="fas fa-edit"></i>
                        </button>
                    </div>
                    <div class="mt-2">
                        <div class="small text-muted mb-1">班级课程:</div>
                        <div class="d-flex flex-wrap gap-1">
                            {% if user.class_.courses %}
                                {% for course in user.class_.courses %}
                                <span class="badge bg-light text-primary border border-primary">{{ course.name }}</span>
                                {% endfor %}
                            {% else %}
                                <span class="text-muted small">暂无课程</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="user-class mb-3 p-2 border rounded bg-light border-warning">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                        <div>
                            <div class="small text-muted">所属班级</div>
                            <div class="fw-bold text-warning">未绑定班级</div>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- 学习概览图表 -->
                <div class="learning-overview mb-3">
                    <div class="card border-0 bg-light mb-3">
                        <div class="card-body p-3">
                            <h6 class="card-title mb-3">
                                <i class="fas fa-chart-line me-2 text-primary"></i>学习概览
                            </h6>
                            <div class="row row-cols-2 row-cols-md-4 g-3 text-center">
                                <!-- 日均答题 -->
                                <div class="col">
                                    <div class="overview-item">
                                        <div class="overview-icon mb-2">
                                            <i class="fas fa-calendar-day text-primary"></i>
                                        </div>
                                        <div class="overview-value fw-bold">{{ daily_avg|default('10.3') }}</div>
                                        <div class="overview-label small text-muted">日均答题</div>
                                    </div>
                                </div>
                                <!-- 平均用时 -->
                                <div class="col">
                                    <div class="overview-item">
                                        <div class="overview-icon mb-2">
                                            <i class="fas fa-stopwatch text-success"></i>
                                        </div>
                                        <div class="overview-value fw-bold">{{ avg_time|default('39') }}秒</div>
                                        <div class="overview-label small text-muted">平均用时</div>
                                    </div>
                                </div>
                                <!-- 活跃天数 -->
                                <div class="col">
                                    <div class="overview-item">
                                        <div class="overview-icon mb-2">
                                            <i class="fas fa-calendar-check text-info"></i>
                                        </div>
                                        <div class="overview-value fw-bold">{{ active_days|default('3') }}</div>
                                        <div class="overview-label small text-muted">活跃天数</div>
                                    </div>
                                </div>
                                <!-- 连续学习 -->
                                <div class="col">
                                    <div class="overview-item">
                                        <div class="overview-icon mb-2">
                                            <i class="fas fa-fire text-warning"></i>
                                        </div>
                                        <div class="overview-value fw-bold">{{ streak_days|default('7') }}</div>
                                        <div class="overview-label small text-muted">连续学习</div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 添加趋势小图表 -->
                            <div class="mt-3 pt-2 border-top">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="card-subtitle mb-0">学习趋势</h6>
                                    <small class="text-muted">最近7天</small>
                                </div>
                                <div style="height: 100px;">
                                    <canvas id="overviewMiniChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 用户联系信息紧凑布局 -->
                <div class="user-contact-info small text-start">
                    <div class="card border-0 bg-light mb-3">
                        <div class="card-body p-3">
                            <h6 class="card-title mb-3">
                                <i class="fas fa-id-card me-2 text-primary"></i>联系信息
                            </h6>
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-clock me-3 text-muted fa-sm"></i>
                                <div class="text-truncate">
                                    <span class="text-muted me-1">注册时间:</span>
                                    {{ user.created_at.strftime('%Y-%m-%d %H:%M:%S') if user.created_at else '-' }}
                                </div>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-envelope me-3 text-muted fa-sm"></i>
                                <div class="text-truncate">
                                    <span class="text-muted me-1">邮箱:</span>
                                    {{ user.email or "未设置邮箱" }}
                                </div>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-phone me-3 text-muted fa-sm"></i>
                                <div class="text-truncate">
                                    <span class="text-muted me-1">手机:</span>
                                    {{ user.phone or "未绑定手机" }}
                                </div>
                            </div>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-map-marker-alt me-3 text-muted fa-sm"></i>
                                <div class="text-truncate">
                                    <span class="text-muted me-1">地区:</span>
                                    {{ user.location or "未知地区" }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最近活动放到左侧下方 -->
        <div class="card h-98">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div><i class="fas fa-history me-2"></i> 最近活动</div>
                <div class="btn-group">
                    <button class="btn btn-sm btn-outline-primary" id="refreshRecords">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button class="btn btn-sm btn-primary" data-bs-toggle="modal"
                        data-bs-target="#searchRecordsModal">
                        <i class="fas fa-search me-1"></i> 高级搜索
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <!-- 添加搜索框 -->
                <div class="px-3 pt-3">
                    <div class="input-group mb-2">
                        <input type="text" class="form-control" id="quickSearchInput" placeholder="快速搜索题目内容..." style="border-radius: 4px 0 0 4px; border-right: none;">
                        <button class="btn btn-primary" id="quickSearchBtn" style="border-radius: 0 4px 4px 0; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                    </div>
                </div>
                
                <div class="activity-filter mb-2 px-3 pt-2">
                    <div class="d-flex align-items-center">
                        <div class="me-2">筛选:</div>
                        <div class="btn-group btn-group-sm me-2">
                            <button class="btn btn-outline-secondary active filter-btn result-filter" data-filter="all">全部</button>
                            <button class="btn btn-outline-success filter-btn result-filter" data-filter="correct">正确</button>
                            <button class="btn btn-outline-danger filter-btn result-filter" data-filter="wrong">错误</button>
                        </div>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary filter-btn type-filter" data-filter="single">单选</button>
                            <button class="btn btn-outline-warning filter-btn type-filter" data-filter="multiple">多选</button>
                            <button class="btn btn-outline-success filter-btn type-filter" data-filter="judgment">判断</button>
                            <button class="btn btn-outline-info filter-btn type-filter" data-filter="fillBlank">填空</button>
                        </div>
                    </div>
                </div>
                
                <!-- 搜索和记录数量计数器 -->
                <div class="px-3 pb-2 d-flex justify-content-between align-items-center">
                    <span class="text-muted small" id="recordsCounter">共 {{ records|length }} 条记录</span>
                </div>

                <div class="records-container" style="max-height: 1200px; overflow-y: auto;" id="recordsContainer">
                    <div class="list-group list-group-flush" id="activityRecordsList">
                        {% if records and records|length > 0 %}
                        {% for record in records %}
                        <div class="list-group-item record-item p-2" data-record-id="{{ record.id }}"
                            data-record-type="{{ record.question_type }}"
                            data-record-result="{% if record.is_correct %}correct{% else %}wrong{% endif %}">
                            <div class="d-flex justify-content-between align-items-center record-header" style="cursor: pointer;">
                                <div class="d-flex align-items-center">
                                    <span class="badge 
                                                {% if record.is_correct %}
                                                    bg-success
                                                {% else %}
                                                    bg-danger
                                                {% endif %}
                                                me-2">
                                        {% if record.is_correct %}
                                        <i class="fas fa-check"></i>
                                        {% else %}
                                        <i class="fas fa-times"></i>
                                        {% endif %}
                                    </span>
                                    <span class="badge 
                                                {% if record.question_type == 'single' %}
                                                    bg-primary
                                                {% elif record.question_type == 'multiple' %}
                                                    bg-warning
                                                {% elif record.question_type == 'judgment' %}
                                                    bg-success
                                                {% else %}
                                                    bg-info
                                                {% endif %}
                                                me-2">
                                        {% if record.question_type == 'single' %}
                                        单选题
                                        {% elif record.question_type == 'multiple' %}
                                        多选题
                                        {% elif record.question_type == 'judgment' %}
                                        判断题
                                        {% else %}
                                        填空题
                                        {% endif %}
                                    </span>
                                    <span class="small text-secondary">题#{{ record.id }}</span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <small class="text-muted me-2">{{ record.created_at }}</small>
                                    <div class="dropdown record-actions">
                                        <button class="btn btn-sm btn-link text-muted" type="button"
                                            data-bs-toggle="dropdown">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end">
                                            <li><a class="dropdown-item view-record" href="#"
                                                    data-record-id="{{ record.id }}"><i
                                                        class="fas fa-eye me-2"></i>查看详情</a></li>
                                            <li><a class="dropdown-item delete-record" href="#"
                                                    data-record-id="{{ record.id }}"><i
                                                        class="fas fa-trash-alt me-2"></i>删除记录</a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="record-detail mt-1 ps-4" style="display: none;">
                                <div class="small">
                                    <div class="mb-1"><strong>问题:</strong> {{ record.question_text|default('(题目内容未记录)') }}</div>
                                    <div class="mb-1"><strong>用户答案:</strong> <span
                                            class="text-{% if record.is_correct %}success{% else %}danger{% endif %}"
                                            data-answer="{{ record.user_answer|default({})|tojson }}">{{
                                            record.user_answer|default({}) }}</span></div>
                                    {% if not record.is_correct %}
                                    <div><strong>正确答案:</strong> <span class="text-success"
                                            data-answer="{{ record.correct_answer|default({})|tojson }}">{{
                                            record.correct_answer|default({}) }}</span></div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                        {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-info-circle text-muted mb-2" style="font-size: 2rem;"></i>
                            <p class="mb-0 text-muted">暂无记录</p>
                        </div>
                        {% endif %}
                    </div>
                    <div id="loadingIndicator" class="text-center py-3" style="display: none;">
                        <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                        <span class="ms-2">加载更多...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-chart-pie me-2"></i> 学习概览
                </div>
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-secondary active" data-time-range="week">周</button>
                    <button type="button" class="btn btn-outline-secondary" data-time-range="month">月</button>
                    <button type="button" class="btn btn-outline-secondary" data-time-range="year">年</button>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6 mb-4 mb-md-0">
                        <div class="chart-container">
                            <canvas id="accuracyDoughnutChart"></canvas>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="chart-container">
                            <canvas id="questionTypeChart"></canvas>
                        </div>
                    </div>
                </div>

                <div class="row learning-stats">
                    <div class="col-md-3 col-6 mb-3">
                        <div class="learning-stat-card">
                            <div class="stat-icon bg-primary-light">
                                <i class="fas fa-calendar-day text-primary"></i>
                            </div>
                            <div class="stat-details">
                                <div class="stat-value">{{ daily_avg or 0 }}</div>
                                <div class="stat-label">日均答题</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-6 mb-3">
                        <div class="learning-stat-card">
                            <div class="stat-icon bg-success-light">
                                <i class="fas fa-clock text-success"></i>
                            </div>
                            <div class="stat-details">
                                <div class="stat-value">{{ avg_time_per_question or "N/A" }}</div>
                                <div class="stat-label">平均用时</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-6 mb-3">
                        <div class="learning-stat-card">
                            <div class="stat-icon bg-info-light">
                                <i class="fas fa-calendar-check text-info"></i>
                            </div>
                            <div class="stat-details">
                                <div class="stat-value">{{ active_days or 0 }}</div>
                                <div class="stat-label">活跃天数</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-6 mb-3">
                        <div class="learning-stat-card">
                            <div class="stat-icon bg-warning-light">
                                <i class="fas fa-trophy text-warning"></i>
                            </div>
                            <div class="stat-details">
                                <div class="stat-value">{{ streak_days or 0 }}</div>
                                <div class="stat-label">连续学习</div>
                            </div>
                        </div>
                    </div>
                </div>

                <hr>

                <h5 class="card-title mb-3">题型答题情况</h5>
                <div class="row">
                    <div class="col-md-6">
                        <!-- 题型答题详情表格 -->
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover table-question-stats">
                                <thead class="table-light">
                                    <tr>
                                        <th>题型</th>
                                        <th>已答题数</th>
                                        <th>正确数</th>
                                        <th>正确率</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><span class="badge rounded-pill bg-primary">单选题</span></td>
                                        <td id="singleTotal">{{ question_type_stats.single.total }}</td>
                                        <td id="singleCorrect">{{ question_type_stats.single.correct }}</td>
                                        <td>
                                            <span id="singleAccuracy"
                                                class="{% if question_type_stats.single.accuracy >= 80 %}accuracy-high{% elif question_type_stats.single.accuracy >= 60 %}accuracy-medium{% else %}accuracy-low{% endif %}">{{
                                                question_type_stats.single.accuracy }}%</span>
                                            <div class="progress mt-1" style="height: 5px;">
                                                <div id="singleProgressBar" class="progress-bar bg-primary"
                                                    role="progressbar"
                                                    style="width: {{ question_type_stats.single.accuracy }}%"></div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><span class="badge rounded-pill bg-warning">多选题</span></td>
                                        <td id="multipleTotal">{{ question_type_stats.multiple.total }}</td>
                                        <td id="multipleCorrect">{{ question_type_stats.multiple.correct }}</td>
                                        <td>
                                            <span id="multipleAccuracy"
                                                class="{% if question_type_stats.multiple.accuracy >= 80 %}accuracy-high{% elif question_type_stats.multiple.accuracy >= 60 %}accuracy-medium{% else %}accuracy-low{% endif %}">{{
                                                question_type_stats.multiple.accuracy }}%</span>
                                            <div class="progress mt-1" style="height: 5px;">
                                                <div id="multipleProgressBar" class="progress-bar bg-warning"
                                                    role="progressbar"
                                                    style="width: {{ question_type_stats.multiple.accuracy }}%"></div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><span class="badge rounded-pill bg-success">判断题</span></td>
                                        <td id="judgmentTotal">{{ question_type_stats.judgment.total }}</td>
                                        <td id="judgmentCorrect">{{ question_type_stats.judgment.correct }}</td>
                                        <td>
                                            <span id="judgmentAccuracy"
                                                class="{% if question_type_stats.judgment.accuracy >= 80 %}accuracy-high{% elif question_type_stats.judgment.accuracy >= 60 %}accuracy-medium{% else %}accuracy-low{% endif %}">{{
                                                question_type_stats.judgment.accuracy }}%</span>
                                            <div class="progress mt-1" style="height: 5px;">
                                                <div id="judgmentProgressBar" class="progress-bar bg-success"
                                                    role="progressbar"
                                                    style="width: {{ question_type_stats.judgment.accuracy }}%"></div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><span class="badge rounded-pill bg-info">填空题</span></td>
                                        <td id="fillBlankTotal">{{ question_type_stats.fillBlank.total }}</td>
                                        <td id="fillBlankCorrect">{{ question_type_stats.fillBlank.correct }}</td>
                                        <td>
                                            <span id="fillBlankAccuracy"
                                                class="{% if question_type_stats.fillBlank.accuracy >= 80 %}accuracy-high{% elif question_type_stats.fillBlank.accuracy >= 60 %}accuracy-medium{% else %}accuracy-low{% endif %}">{{
                                                question_type_stats.fillBlank.accuracy }}%</span>
                                            <div class="progress mt-1" style="height: 5px;">
                                                <div id="fillBlankProgressBar" class="progress-bar bg-info"
                                                    role="progressbar"
                                                    style="width: {{ question_type_stats.fillBlank.accuracy }}%"></div>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <!-- 题型正确率对比图 -->
                        <div class="chart-container" style="height: 300px; position: relative;">
                            <canvas id="questionTypeAccuracyChart"></canvas>
                        </div>
                    </div>
                </div>

                <hr>

                <h5 class="card-title mb-3">能力雷达</h5>
                <div class="row">
                    <div class="col-md-5">
                        <!-- 能力水平横条图表 -->
                        <div class="skill-progress-container mb-4">
                            <h6 class="mb-3">课程能力水平</h6>
                        {% if course_performance %}
                        {% for course_id, data in course_performance.items() %}
                        <div class="skill-progress mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <div class="skill-label">{{ data.name }}</div>
                                <div class="skill-value">{{ "%.1f"|format(data.accuracy * 100) }}%</div>
                            </div>
                            <div class="progress">
                                <div class="progress-bar 
                                        {% if loop.index % 5 == 1 %}bg-primary
                                        {% elif loop.index % 5 == 2 %}bg-success
                                        {% elif loop.index % 5 == 3 %}bg-info
                                        {% elif loop.index % 5 == 4 %}bg-warning
                                        {% else %}bg-danger{% endif %}" role="progressbar"
                                    style="width: {{ data.accuracy * 100 }}%">
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                        {% else %}
                        <div class="skill-progress mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                    <div class="skill-label">大数据技术与应用</div>
                                    <div class="skill-value">{{ "%.1f"|format(skill_scores.basics|default(0) * 100) }}%</div>
                            </div>
                            <div class="progress">
                                <div class="progress-bar bg-primary" role="progressbar"
                                    style="width: {{ skill_scores.basics|default(0) * 100 }}%"></div>
                            </div>
                        </div>
                        <div class="skill-progress mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                    <div class="skill-label">可视化</div>
                                    <div class="skill-value">{{ "%.1f"|format(skill_scores.analysis|default(0) * 100) }}%</div>
                            </div>
                            <div class="progress">
                                <div class="progress-bar bg-success" role="progressbar"
                                    style="width: {{ skill_scores.analysis|default(0) * 100 }}%"></div>
                            </div>
                        </div>
                        <div class="skill-progress mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                    <div class="skill-label">机器学习</div>
                                    <div class="skill-value">{{ "%.1f"|format(skill_scores.visualization|default(0) * 100) }}%</div>
                            </div>
                            <div class="progress">
                                <div class="progress-bar bg-info" role="progressbar"
                                    style="width: {{ skill_scores.visualization|default(0) * 100 }}%"></div>
                            </div>
                        </div>
                        <div class="skill-progress mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                    <div class="skill-label">生活美学</div>
                                    <div class="skill-value">{{ "%.1f"|format(skill_scores.machine_learning|default(0) * 100) }}%</div>
                            </div>
                            <div class="progress">
                                <div class="progress-bar bg-warning" role="progressbar"
                                    style="width: {{ skill_scores.machine_learning|default(0) * 100 }}%"></div>
                            </div>
                        </div>
                            <div class="skill-progress mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                    <div class="skill-label">区块链</div>
                                    <div class="skill-value">{{ "%.1f"|format(skill_scores.tools|default(0) * 100) }}%</div>
                            </div>
                            <div class="progress">
                                <div class="progress-bar bg-danger" role="progressbar"
                                    style="width: {{ skill_scores.tools|default(0) * 100 }}%"></div>
                            </div>
                        </div>
                            <div class="skill-progress mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <div class="skill-label">数据描述</div>
                                    <div class="skill-value">0.0%</div>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-secondary" role="progressbar"
                                        style="width: 0%"></div>
                                </div>
                            </div>
                            <div class="skill-progress mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <div class="skill-label">大数据第1、2章</div>
                                    <div class="skill-value">59.3%</div>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-info" role="progressbar"
                                        style="width: 59.3%"></div>
                                </div>
                            </div>
                            <div class="skill-progress mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <div class="skill-label">大数据第3、4章</div>
                                    <div class="skill-value">50.0%</div>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-warning" role="progressbar"
                                        style="width: 50.0%"></div>
                                </div>
                            </div>
                            <div class="skill-progress">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <div class="skill-label">大数据第11、12章</div>
                                    <div class="skill-value">50.0%</div>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-primary" role="progressbar"
                                        style="width: 50.0%"></div>
                            </div>
                        </div>
                        {% endif %}
                        </div>
                    </div>
                    <div class="col-md-7 d-flex align-items-center">
                        <div class="chart-container" style="height: 450px; width: 100%; max-width: 100%; padding: 0 15px;">
                            <canvas id="skillRadarChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加学习活动图表 -->
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <div>
            <i class="fas fa-chart-line me-2"></i> 学习活动
        </div>
        <div class="btn-group btn-group-sm">
            <button type="button" class="btn btn-outline-primary active" data-chart="activity">活动量</button>
            <button type="button" class="btn btn-outline-primary" data-chart="accuracy">正确率</button>
        </div>
    </div>
    <div class="card-body">
        <div class="chart-container" style="height: 350px !important; max-height: 350px !important; overflow: hidden;">
            <canvas id="activityChart" height="350"></canvas>
        </div>
    </div>
</div>

{% if date_stats %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">用户答题趋势</h5>
            </div>
            <div class="card-body chart-container"
                style="height: 350px !important; max-height: 350px !important; overflow: hidden;">
                <canvas id="userProgressChart" style="max-height: 350px;"></canvas>
            </div>
        </div>
    </div>
</div>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        var userProgressCtx = document.getElementById('userProgressChart').getContext('2d');

        var userProgressDateStats = {{ date_stats| tojson }};
        var userProgressDates = userProgressDateStats.map(function (item) { return item.date.substring(5); }); // Only show MM-DD
        var userProgressAccuracies = userProgressDateStats.map(function (item) { return item.accuracy * 100; }); // 将小数转换为百分比
        var userProgressTotals = userProgressDateStats.map(function (item) { return item.total; });

        var userProgressChart = new Chart(userProgressCtx, {
            type: 'line',
            data: {
                labels: userProgressDates,
                datasets: [
                    {
                        label: '准确率 (%)',
                        yAxisID: 'y-accuracy',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
                        data: userProgressAccuracies,
                        fill: true,
                        pointRadius: 4,
                        pointHoverRadius: 6
                    },
                    {
                        label: '题目数量',
                        yAxisID: 'y-questions',
                        type: 'bar',
                        borderColor: 'rgba(255, 99, 132, 1)',
                        backgroundColor: 'rgba(255, 99, 132, 0.5)',
                        data: userProgressTotals
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                if (context.dataset.label === '准确率 (%)') {
                                    return '准确率: ' + context.raw.toFixed(1) + '%';
                                }
                                return context.dataset.label + ': ' + context.raw;
                            }
                        }
                    }
                },
                scales: {
                    'y-accuracy': {
                        type: 'linear',
                        position: 'left',
                        min: 0,
                        max: 100,
                        title: {
                            display: true,
                            text: '准确率 (%)'
                        },
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    },
                    'y-questions': {
                        type: 'linear',
                        position: 'right',
                        min: 0,
                        // 使用动态最大值替代固定的20
                        suggestedMax: Math.max(...userProgressTotals) * 1.2,
                        title: {
                            display: true,
                            text: '题目数量'
                        },
                        grid: {
                            drawOnChartArea: false
                        }
                    }
                }
            }
        });
    });
</script>
{% endif %}

<!-- 在线时间热图 -->
<div class="row mt-4 mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-calendar-alt me-2"></i> 活跃度热图
            </div>
            <div class="card-body">
                <div class="activity-heatmap mb-3">
                    <div class="d-flex justify-content-between mb-2">
                        <h6 class="fw-bold">近30天活跃记录</h6>
                        <div class="d-flex align-items-center">
                            <div class="me-1 small">低</div>
                            <div class="heatmap-scale d-flex">
                                <div class="heatmap-level" style="background-color: #ebedf0"></div>
                                <div class="heatmap-level" style="background-color: #c6e48b"></div>
                                <div class="heatmap-level" style="background-color: #7bc96f"></div>
                                <div class="heatmap-level" style="background-color: #239a3b"></div>
                                <div class="heatmap-level" style="background-color: #196127"></div>
                            </div>
                            <div class="ms-1 small">高</div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-3">
                                <h6 class="fw-bold mb-0 me-2">今日活跃时间</h6>
                                <span class="badge bg-success">上线</span>
                            </div>
                            <div class="daily-activity-chart-container shadow-sm rounded bg-white p-3">
                                <div class="hour-chart">
                                    {% for hour in range(24) %}
                                    {% if hour_activity_data is defined %}
                                        {% set hour_activity = hour_activity_data[hour]|default(0) %}
                                    {% else %}
                                        {% set hour_activity = [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100]|random if hour >= 8 and hour <= 22 else [0, 5, 10]|random %}
                                    {% endif %}
                                    <div class="hour-column">
                                        <div class="hour-bar {% if hour_activity > 70 %}high-activity{% elif hour_activity > 30 %}medium-activity{% elif hour_activity > 0 %}low-activity{% endif %}" style="height: {{ hour_activity }}%">
                                            <span class="hour-tooltip">{{ hour }}:00: {{ hour_activity }}% 活跃度</span>
                                        </div>
                                        <div class="hour-label {% if hour == now.hour %}current-hour{% endif %}">{{ hour }}</div>
                                    </div>
                                    {% endfor %}
                                </div>
                                <div class="hour-labels-container mt-1">
                                    <div class="d-flex justify-content-between text-muted">
                                        <small>凌晨</small>
                                        <small>上午</small>
                                        <small>下午</small>
                                        <small>晚上</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="weekly-stats mb-3">
                                <h6 class="fw-bold mb-3">本周活跃统计</h6>
                                <div class="weekly-stats-container d-flex justify-content-between">
                                    <div class="stat-column text-center">
                                        <div class="stat-value">{{ (5 + (user.id % 5))|int }}</div>
                                        <div class="stat-label small">活跃天数</div>
                                    </div>
                                    <div class="stat-column text-center">
                                        <div class="stat-value">{{ (20 + (user.id % 30))|int }}</div>
                                        <div class="stat-label small">答题次数</div>
                                    </div>
                                    <div class="stat-column text-center">
                                        <div class="stat-value">{{ (2 + (user.id % 3))|int }}h</div>
                                        <div class="stat-label small">使用时长</div>
                                    </div>
                                    <div class="stat-column text-center">
                                        <div class="stat-value">{{ (70 + (user.id % 20))|int }}%</div>
                                        <div class="stat-label small">正确率</div>
                                    </div>
                                </div>
                            </div>

                            <div class="learning-streaks">
                                <h6 class="fw-bold mb-3">学习连续性</h6>
                                <div class="streak-container shadow-sm rounded bg-white p-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div class="fw-bold">连续学习天数</div>
                                        <div class="streak-value">{{ streak_days }} 天</div>
                                    </div>
                                    <div class="progress mb-2" style="height: 10px;">
                                        <div class="progress-bar bg-success" role="progressbar"
                                            style="width: {{ (streak_days/7*100) if streak_days <= 7 else 100 }}%">
                                        </div>
                                    </div>
                                    <div class="streak-badges d-flex flex-wrap">
                                        {% for i in range(1, 8) %}
                                        <div class="streak-badge me-2 mb-2 {% if i <= streak_days %}active{% endif %}">
                                            <span class="small">{{ i }}</span>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <style>
                    /* 活跃度热图样式改进 */
                    .daily-activity-chart-container {
                        border: 1px solid rgba(0,0,0,0.05);
                    }
                    
                    .hour-chart {
                        display: flex;
                        height: 200px;
                        align-items: flex-end;
                        margin-top: 10px;
                        padding-bottom: 10px;
                        position: relative;
                    }
                    
                    .hour-column {
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        height: 100%;
                    }
                    
                    .hour-bar {
                        width: 70%;
                        background-color: #e9ecef;
                        border-radius: 2px 2px 0 0;
                        position: relative;
                        transition: all 0.3s ease;
                    }
                    
                    .hour-bar.low-activity {
                        background-color: #c6e48b;
                    }
                    
                    .hour-bar.medium-activity {
                        background-color: #7bc96f;
                    }
                    
                    .hour-bar.high-activity {
                        background-color: #239a3b;
                    }
                    
                    .hour-bar:hover {
                        width: 85%;
                        box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
                    }
                    
                    .hour-bar:hover .hour-tooltip {
                        display: block;
                    }
                    
                    .hour-tooltip {
                        display: none;
                        position: absolute;
                        background-color: rgba(0, 0, 0, 0.8);
                        color: white;
                        padding: 5px 8px;
                        border-radius: 4px;
                        font-size: 12px;
                        bottom: 105%;
                        left: 50%;
                        transform: translateX(-50%);
                        white-space: nowrap;
                        z-index: 10;
                    }
                    
                    .hour-label {
                        font-size: 10px;
                        color: #6c757d;
                        margin-top: 5px;
                    }
                    
                    .hour-label.current-hour {
                        font-weight: bold;
                        color: #0d6efd;
                    }
                    
                    /* 每3小时显示一次标签 */
                    .hour-column:not(:nth-child(3n)) .hour-label {
                        visibility: hidden;
                    }
                    
                    /* 学习连续性样式改进 */
                    .streak-container {
                        border: 1px solid rgba(0,0,0,0.05);
                    }
                    
                    .streak-value {
                        font-size: 1.2rem;
                        font-weight: bold;
                        color: #28a745;
                    }
                    
                    .streak-badges {
                        display: flex;
                        margin-top: 10px;
                    }
                    
                    .streak-badge {
                        width: 30px;
                        height: 30px;
                        border-radius: 50%;
                        background-color: #e9ecef;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: #6c757d;
                        transition: all 0.3s ease;
                    }
                    
                    .streak-badge.active {
                        background-color: #28a745;
                        color: white;
                        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
                    }
                    
                    /* 周统计样式改进 */
                    .weekly-stats-container {
                        background: white;
                        border-radius: 10px;
                        padding: 15px;
                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
                        margin-bottom: 20px;
                        border: 1px solid rgba(0,0,0,0.05);
                    }
                    
                    .weekly-stats .stat-column {
                        text-align: center;
                        transition: all 0.3s ease;
                    }
                    
                    .weekly-stats .stat-column:hover {
                        transform: translateY(-3px);
                    }
                    
                    .weekly-stats .stat-value {
                        font-size: 1.5rem;
                        font-weight: 700;
                        color: #4c84ff;
                        margin-bottom: 5px;
                    }
                    
                    .weekly-stats .stat-label {
                        font-size: 0.8rem;
                        color: #6c757d;
                        font-weight: 500;
                    }
                </style>
            </div>
        </div>
    </div>
</div>

<!-- 修改原来的最近活动和成就部分，仅保留成就-->
<div class="row mt-4">
    <!-- 用户成就与徽章 -->
    <div class="col-md-12 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <i class="fas fa-trophy me-2"></i> 成就与徽章
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <!-- 答题成就 -->
                    {% set total_questions = question_type_stats.single.total +
                    question_type_stats.multiple.total +
                    question_type_stats.judgment.total +
                    question_type_stats.fillBlank.total %}

                    <div class="col-md-4 col-6">
                        <div
                            class="achievement-card text-center p-3 rounded {% if total_questions >= 100 %}achievement-unlocked{% else %}achievement-locked{% endif %}">
                            <div class="achievement-icon mb-2">
                                <i class="fas fa-graduation-cap"></i>
                            </div>
                            <h6 class="achievement-title mb-1">答题能手</h6>
                            <div class="achievement-progress">
                                <small>{{ total_questions }}/100</small>
                                <div class="progress mt-1" style="height: 5px;">
                                    <div class="progress-bar" role="progressbar"
                                        style="width: {{ (total_questions / 100 * 100) if total_questions < 100 else 100 }}%">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 准确率成就 -->
                    {% set overall_accuracy = accuracy %}
                    <div class="col-md-4 col-6">
                        <div
                            class="achievement-card text-center p-3 rounded {% if overall_accuracy >= 80 %}achievement-unlocked{% else %}achievement-locked{% endif %}">
                            <div class="achievement-icon mb-2">
                                <i class="fas fa-bullseye"></i>
                            </div>
                            <h6 class="achievement-title mb-1">答题精确</h6>
                            <div class="achievement-progress">
                                <small>{{ overall_accuracy|round|int }}/80%</small>
                                <div class="progress mt-1" style="height: 5px;">
                                    <div class="progress-bar" role="progressbar"
                                        style="width: {{ (overall_accuracy / 80 * 100) if overall_accuracy < 80 else 100 }}%">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 连续学习成就 -->
                    <div class="col-md-4 col-6">
                        <div
                            class="achievement-card text-center p-3 rounded {% if streak_days >= 7 %}achievement-unlocked{% else %}achievement-locked{% endif %}">
                            <div class="achievement-icon mb-2">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <h6 class="achievement-title mb-1">持之以恒</h6>
                            <div class="achievement-progress">
                                <small>{{ streak_days }}/7</small>
                                <div class="progress mt-1" style="height: 5px;">
                                    <div class="progress-bar" role="progressbar"
                                        style="width: {{ (streak_days / 7 * 100) if streak_days < 7 else 100 }}%"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 知识广度成就 -->
                    {% set course_count = course_performance|length if course_performance else 0 %}
                    <div class="col-md-4 col-6">
                        <div
                            class="achievement-card text-center p-3 rounded {% if course_count >= 3 %}achievement-unlocked{% else %}achievement-locked{% endif %}">
                            <div class="achievement-icon mb-2">
                                <i class="fas fa-book"></i>
                            </div>
                            <h6 class="achievement-title mb-1">知识广博</h6>
                            <div class="achievement-progress">
                                <small>{{ course_count }}/3</small>
                                <div class="progress mt-1" style="height: 5px;">
                                    <div class="progress-bar" role="progressbar"
                                        style="width: {{ (course_count / 3 * 100) if course_count < 3 else 100 }}%">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 多选题专家 -->
                    {% set multiple_mastery = question_type_stats.multiple.accuracy %}
                    <div class="col-md-4 col-6">
                        <div
                            class="achievement-card text-center p-3 rounded {% if multiple_mastery >= 75 and question_type_stats.multiple.total >= 20 %}achievement-unlocked{% else %}achievement-locked{% endif %}">
                            <div class="achievement-icon mb-2">
                                <i class="fas fa-tasks"></i>
                            </div>
                            <h6 class="achievement-title mb-1">多选专家</h6>
                            <div class="achievement-progress">
                                <small>{{ multiple_mastery|round|int }}%/75%</small>
                                <div class="progress mt-1" style="height: 5px;">
                                    <div class="progress-bar" role="progressbar"
                                        style="width: {{ (multiple_mastery / 75 * 100) if multiple_mastery < 75 else 100 }}%">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 填空题达人 -->
                    {% set fill_blank_mastery = question_type_stats.fillBlank.accuracy %}
                    <div class="col-md-4 col-6">
                        <div
                            class="achievement-card text-center p-3 rounded {% if fill_blank_mastery >= 70 and question_type_stats.fillBlank.total >= 15 %}achievement-unlocked{% else %}achievement-locked{% endif %}">
                            <div class="achievement-icon mb-2">
                                <i class="fas fa-pencil-alt"></i>
                            </div>
                            <h6 class="achievement-title mb-1">填空达人</h6>
                            <div class="achievement-progress">
                                <small>{{ fill_blank_mastery|round|int }}%/70%</small>
                                <div class="progress mt-1" style="height: 5px;">
                                    <div class="progress-bar" role="progressbar"
                                        style="width: {{ (fill_blank_mastery / 70 * 100) if fill_blank_mastery < 70 else 100 }}%">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 学习时间分布 -->
<div class="row mt-4">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-user-clock me-2"></i> 用户学习习惯分析
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- 学习时间段分布 -->
                    <div class="col-md-6">
                        <h6 class="fw-bold mb-3">学习时间段分布</h6>
                        <div class="time-distribution mb-4">
                            <div class="row g-2">
                                {% set time_periods = time_periods or {
                                '早晨 (6:00-9:00)': 15,
                                '上午 (9:00-12:00)': 25,
                                '中午 (12:00-14:00)': 10,
                                '下午 (14:00-18:00)': 20,
                                '晚上 (18:00-22:00)': 25,
                                '深夜 (22:00-6:00)': 5
                                } %}

                                {% for period, percentage in time_periods.items() %}
                                <div class="col-md-6">
                                    <div class="d-flex justify-content-between mb-1">
                                        <small>{{ period }}</small>
                                        <small>{{ percentage }}%</small>
                                    </div>
                                    <div class="progress" style="height: 8px; margin-bottom: 10px;">
                                        <div class="progress-bar 
                                        {% if loop.index % 6 == 1 %}bg-info
                                        {% elif loop.index % 6 == 2 %}bg-primary
                                        {% elif loop.index % 6 == 3 %}bg-success
                                        {% elif loop.index % 6 == 4 %}bg-warning
                                        {% elif loop.index % 6 == 5 %}bg-danger
                                        {% else %}bg-secondary{% endif %}" role="progressbar"
                                            style="width: {{ percentage }}%"></div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>

                    <!-- 平均每次学习时长 -->
                    <div class="col-md-6">
                        <h6 class="fw-bold mb-3">学习习惯统计</h6>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="stat-card p-3 rounded bg-light">
                                    <h2 class="mb-0 fw-bold">{{ (15 + (user.id % 15))|int }}</h2>
                                    <small class="text-muted">平均每次学习时长（分钟）</small>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="stat-card p-3 rounded bg-light">
                                    <h2 class="mb-0 fw-bold">{{ (total_questions / active_days)|round(1) if active_days
                                        > 0 else 0 }}</h2>
                                    <small class="text-muted">平均每天答题数</small>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="stat-card p-3 rounded bg-light">
                                    <h2 class="mb-0 fw-bold">{{ (4 + (user.id % 4))|int }}</h2>
                                    <small class="text-muted">每周活跃天数</small>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="stat-card p-3 rounded bg-light">
                                    <h2 class="mb-0 fw-bold">{{ avg_time_per_question }}</h2>
                                    <small class="text-muted">平均答题时间</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 搜索记录模态框 -->
<div class="modal fade" id="searchRecordsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">搜索答题记录</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="recordSearchForm">
                    <div class="mb-3">
                        <label class="form-label">题目类型</label>
                        <div class="d-flex">
                            <div class="form-check me-3">
                                <input class="form-check-input" type="checkbox" value="single" id="typeSingle" checked>
                                <label class="form-check-label" for="typeSingle">单选题</label>
                            </div>
                            <div class="form-check me-3">
                                <input class="form-check-input" type="checkbox" value="multiple" id="typeMultiple"
                                    checked>
                                <label class="form-check-label" for="typeMultiple">多选题</label>
                            </div>
                            <div class="form-check me-3">
                                <input class="form-check-input" type="checkbox" value="judgment" id="typeJudgment"
                                    checked>
                                <label class="form-check-label" for="typeJudgment">判断题</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" value="fillBlank" id="typeFillBlank"
                                    checked>
                                <label class="form-check-label" for="typeFillBlank">填空题</label>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">答题结果</label>
                        <div class="d-flex">
                            <div class="form-check me-3">
                                <input class="form-check-input" type="radio" name="resultFilter" id="resultAll"
                                    value="all" checked>
                                <label class="form-check-label" for="resultAll">全部</label>
                            </div>
                            <div class="form-check me-3">
                                <input class="form-check-input" type="radio" name="resultFilter" id="resultCorrect"
                                    value="correct">
                                <label class="form-check-label" for="resultCorrect">正确</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="resultFilter" id="resultWrong"
                                    value="wrong">
                                <label class="form-check-label" for="resultWrong">错误</label>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">日期范围</label>
                        <div class="row">
                            <div class="col">
                                <input type="date" class="form-control" id="dateFrom">
                            </div>
                            <div class="col-auto pt-2">至</div>
                            <div class="col">
                                <input type="date" class="form-control" id="dateTo">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">关键词搜索</label>
                        <input type="text" class="form-control" id="keywordSearch" placeholder="题目内容、答案关键词">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="searchRecordsBtn">
                    <i class="fas fa-search me-1"></i> 搜索记录
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 记录详情模态框 -->
<div class="modal fade" id="recordDetailModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">答题详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="recordDetailContent">
                    <!-- 动态加载内容 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 删除用户确认模态框 -->
<div class="modal fade" id="deleteUserModal" tabindex="-1" aria-labelledby="deleteUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteUserModalLabel">确认删除用户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger mb-3">
                    <i class="fas fa-exclamation-triangle me-2"></i> 
                    <strong>警告:</strong> 删除用户将同时删除其所有答题记录和错题记录，此操作无法撤销!
                </div>
                <p class="mb-2">请确认是否要永久删除用户: <strong>{{ user.nickname or "用户" + user.id|string }}</strong>？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form action="{{ url_for('delete_user', user_id=user.id) }}" method="POST">
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 禁用/启用用户确认模态框 -->
<div class="modal fade" id="toggleUserModal" tabindex="-1" aria-labelledby="toggleUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="toggleUserModalLabel">
                    {% if user.is_active %}禁用用户{% else %}启用用户{% endif %}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert {% if user.is_active %}alert-warning{% else %}alert-info{% endif %} mb-3">
                    <i class="fas {% if user.is_active %}fa-ban{% else %}fa-check-circle{% endif %} me-2"></i> 
                    {% if user.is_active %}
                    <strong>注意:</strong> 禁用此用户后，该用户将无法登录系统。
                    {% else %}
                    <strong>注意:</strong> 启用此用户后，该用户将可以恢复正常使用系统。
                    {% endif %}
                </div>
                <p class="mb-2">
                    请确认是否要{% if user.is_active %}禁用{% else %}启用{% endif %}用户: 
                    <strong>{{ user.nickname or "用户" + user.id|string }}</strong>？
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form action="{{ url_for('toggle_user_status', user_id=user.id) }}" method="POST">
                    <button type="submit" class="btn {% if user.is_active %}btn-warning{% else %}btn-success{% endif %}">
                        {% if user.is_active %}确认禁用{% else %}确认启用{% endif %}
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 编辑用户信息模态框 -->
<div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editUserModalLabel">编辑用户信息</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('edit_user_info', user_id=user.id) }}" method="POST">
                <div class="modal-body">
                    <div class="row">
                        <!-- 左侧：基本信息和班级 -->
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header">
                                    <i class="fas fa-user me-2"></i> 基本信息
                                </div>
                                <div class="card-body">
                                    <!-- 用户昵称 -->
                                    <div class="mb-3">
                                        <label for="nickname" class="form-label">用户昵称</label>
                                        <input type="text" class="form-control" id="nickname" name="nickname" 
                                               value="{{ user.nickname or '' }}" placeholder="输入用户昵称">
                                    </div>
                                    
                                    <!-- 头像URL -->
                                    <div class="mb-3">
                                        <label for="avatar" class="form-label">头像URL</label>
                                        <input type="text" class="form-control" id="avatar" name="avatar" 
                                               value="{{ user.avatar or '' }}" placeholder="输入头像图片链接">
                                        <small class="text-muted">请输入有效的图片URL地址</small>
                                    </div>

                                    <!-- 班级选择 -->
                                    <div class="mb-3">
                                        <label for="class_id" class="form-label">所属班级</label>
                                        <select class="form-select" id="class_id" name="class_id">
                                            <option value="">-- 不绑定班级 --</option>
                                            {% for cls in classes %}
                                                <option value="{{ cls.id }}" {% if user.class_id == cls.id %}selected{% endif %}>
                                                    {{ cls.name }}
                                                </option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 右侧：联系信息 -->
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header">
                                    <i class="fas fa-address-card me-2"></i> 联系信息
                                </div>
                                <div class="card-body">
                                    <!-- 电子邮箱 -->
                                    <div class="mb-3">
                                        <label for="email" class="form-label">电子邮箱</label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="{{ user.email or '' }}" placeholder="输入电子邮箱">
                                    </div>
                                    
                                    <!-- 联系电话 -->
                                    <div class="mb-3">
                                        <label for="phone" class="form-label">联系电话</label>
                                        <input type="text" class="form-control" id="phone" name="phone" 
                                               value="{{ user.phone or '' }}" placeholder="输入联系电话">
                                    </div>
                                    
                                    <!-- 所在地区 -->
                                    <div class="mb-3">
                                        <label for="location" class="form-label">所在地区</label>
                                        <input type="text" class="form-control" id="location" name="location" 
                                               value="{{ user.location or '' }}" placeholder="输入所在地区">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存更改</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 导出数据模态框 -->
<div class="modal fade" id="exportDataModal" tabindex="-1" aria-labelledby="exportDataModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="exportDataModalLabel">
                    <i class="fas fa-file-export me-2"></i>导出用户数据
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="text-center mb-4">请选择要导出的数据类型：</p>
                
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="exportAnswerRecords" checked>
                            <label class="form-check-label" for="exportAnswerRecords">
                                <i class="fas fa-history text-primary me-2"></i>答题记录
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="exportWrongQuestions" checked>
                            <label class="form-check-label" for="exportWrongQuestions">
                                <i class="fas fa-times-circle text-danger me-2"></i>错题记录
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="exportStatistics" checked>
                            <label class="form-check-label" for="exportStatistics">
                                <i class="fas fa-chart-bar text-success me-2"></i>统计数据
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="exportUserInfo" checked>
                            <label class="form-check-label" for="exportUserInfo">
                                <i class="fas fa-user text-info me-2"></i>用户信息
                            </label>
                        </div>
                    </div>
                </div>
                
                <hr class="my-4">
                
                <div class="mb-3">
                    <label class="form-label">导出格式：</label>
                    <div class="d-flex">
                        <div class="form-check me-4">
                            <input class="form-check-input" type="radio" name="exportFormat" id="exportFormatExcel" value="excel" checked>
                            <label class="form-check-label" for="exportFormatExcel">
                                <i class="fas fa-file-excel text-success me-1"></i> Excel
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="exportFormat" id="exportFormatCSV" value="csv">
                            <label class="form-check-label" for="exportFormatCSV">
                                <i class="fas fa-file-csv text-primary me-1"></i> CSV
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmExportData">
                    <i class="fas fa-download me-1"></i>开始导出
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 删除记录确认模态框 -->
<div class="modal fade" id="deleteRecordModal" tabindex="-1" aria-labelledby="deleteRecordModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteRecordModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>删除确认
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <i class="fas fa-trash-alt text-danger" style="font-size: 3rem;"></i>
                </div>
                <p class="text-center">您确定要删除这条记录吗？</p>
                <p class="text-center text-danger"><strong>此操作不可恢复。</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteRecord">
                    <i class="fas fa-trash-alt me-1"></i>确认删除
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 添加一个将数字索引转换为选项字母的函数
    function formatAnswerOptions(answer) {
        // 处理判断题的true/false显示为对/错
        if (answer === true || answer === 'true') {
            return "对";
        }
        if (answer === false || answer === 'false') {
            return "错";
        }
        
        // 检查是否为数组形式的答案
        if (Array.isArray(answer) || (typeof answer === 'string' && answer.startsWith('[') && answer.endsWith(']'))) {
            try {
                // 如果是字符串形式的数组，转换为真正的数组
                const answerArray = Array.isArray(answer) ? answer : JSON.parse(answer);
                // 将数字索引转换为对应的字母选项
                return answerArray.map(index => String.fromCharCode(65 + parseInt(index))).join('');
            } catch (e) {
                // 转换失败就返回原始答案
                return answer;
            }
        } else if (typeof answer === 'number' || (typeof answer === 'string' && !isNaN(parseInt(answer)))) {
            // 单个数字答案，也转为字母
            return String.fromCharCode(65 + parseInt(answer));
        }
        return answer;
    }
    
    // 页面加载完成后处理答案显示
    document.addEventListener('DOMContentLoaded', function() {
        // 查找所有用户答案和正确答案的元素
        document.querySelectorAll('.record-detail').forEach(detail => {
            // 处理用户答案
            const userAnswerElement = detail.querySelector('div:nth-child(2) span');
            if (userAnswerElement) {
                const rawAnswer = userAnswerElement.dataset.answer || userAnswerElement.textContent.trim();
                try {
                    // 尝试将JSON字符串解析为对象
                    const answerObj = JSON.parse(rawAnswer);
                    userAnswerElement.textContent = formatAnswerOptions(answerObj);
                } catch (e) {
                    // 如果不能作为JSON解析，直接格式化
                    userAnswerElement.textContent = formatAnswerOptions(rawAnswer);
                }
            }
            
            // 处理正确答案
            const correctAnswerElement = detail.querySelector('div:nth-child(3) span');
            if (correctAnswerElement) {
                const rawAnswer = correctAnswerElement.dataset.answer || correctAnswerElement.textContent.trim();
                try {
                    // 尝试将JSON字符串解析为对象
                    const answerObj = JSON.parse(rawAnswer);
                    correctAnswerElement.textContent = formatAnswerOptions(answerObj);
                } catch (e) {
                    // 如果不能作为JSON解析，直接格式化
                    correctAnswerElement.textContent = formatAnswerOptions(rawAnswer);
                }
            }
        });
    });

    $(document).ready(function () {
        // 初始化环形图表：正确率
        const accuracyCtx = document.getElementById('accuracyDoughnutChart').getContext('2d');
        const accuracyChart = new Chart(accuracyCtx, {
            type: 'doughnut',
            data: {
                labels: ['正确', '错误'],
                datasets: [{
                    data: [{{ correct_answers }}, {{ total_answers - correct_answers }}],
                    backgroundColor: ['rgba(40, 167, 69, 0.8)', 'rgba(220, 53, 69, 0.8)'],
                    borderColor: ['rgba(40, 167, 69, 1)', 'rgba(220, 53, 69, 1)'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    title: {
                        display: true,
                        text: '答题正确率',
                        font: {
                            size: 16
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
        
        // 初始化饼图：题型分布
        const typeCtx = document.getElementById('questionTypeChart').getContext('2d');
        const questionTypeChart = new Chart(typeCtx, {
            type: 'pie',
            data: {
                labels: ['单选题', '多选题', '判断题', '填空题'],
                datasets: [{
                    data: [
                        {{ question_type_stats.single.total }}, 
                        {{ question_type_stats.multiple.total }},
                        {{ question_type_stats.judgment.total }},
                        {{ question_type_stats.fillBlank.total }}
                    ],
                    backgroundColor: [
                        'rgba(0, 123, 255, 0.8)',
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(40, 167, 69, 0.8)',
                        'rgba(23, 162, 184, 0.8)'
                    ],
                    borderColor: [
                        'rgba(0, 123, 255, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(40, 167, 69, 1)',
                        'rgba(23, 162, 184, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    title: {
                        display: true,
                        text: '题型分布',
                        font: {
                            size: 16
                        }
                    }
                }
            }
        });

        // 初始化学习概览趋势小图表
        const overviewCtx = document.getElementById('overviewMiniChart').getContext('2d');
        
        // 生成过去7天的日期标签
        const getLastDays = (days) => {
            const result = [];
            for (let i = days - 1; i >= 0; i--) {
                const d = new Date();
                d.setDate(d.getDate() - i);
                result.push((d.getMonth() + 1) + '/' + d.getDate());
            }
            return result;
        };
        
        // 模拟数据或使用已有数据
        let overviewTrendData = [];
        {% if daily_progress %}
        const overviewProgressData = {{ daily_progress|tojson }};
        overviewTrendData = overviewProgressData.slice(-7);
        if (overviewTrendData.length < 7) {
            const overviewBaseCount = {{ daily_avg|default(10)|int }};
            const overviewExistingDates = overviewTrendData.map(item => item.date);
            const overviewAllDates = getLastDays(7);
            
            for (let i = 0; i < overviewAllDates.length; i++) {
                if (!overviewExistingDates.includes(overviewAllDates[i])) {
                    overviewTrendData.push({
                        date: overviewAllDates[i],
                        total: Math.floor(overviewBaseCount * (0.7 + Math.random() * 0.6)),
                        accuracy: 0.6 + Math.random() * 0.3
                    });
                }
            }
        }
        {% else %}
        const overviewBaseCount = {{ daily_avg|default(10)|int }};
        const overviewDates = getLastDays(7);
        for (let i = 0; i < 7; i++) {
            overviewTrendData.push({
                date: overviewDates[i],
                total: Math.floor(overviewBaseCount * (0.7 + Math.random() * 0.6)),
                accuracy: 0.6 + Math.random() * 0.3
            });
        }
        {% endif %}
        
        // 提取数据
        const miniDates = overviewTrendData.map(item => item.date);
        const miniTotals = overviewTrendData.map(item => item.total);
        
        // 创建概览趋势小图表
        const overviewMiniChart = new Chart(overviewCtx, {
            type: 'bar',
            data: {
                labels: miniDates,
                datasets: [{
                    label: '答题数量',
                    data: miniTotals,
                    backgroundColor: 'rgba(54, 162, 235, 0.5)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return '答题数: ' + context.raw;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            display: false
                        },
                        ticks: {
                            display: false
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                size: 9
                            }
                        }
                    }
                }
            }
        });

        // 初始化横向条形图：题型正确率对比
        const accuracyCompareCtx = document.getElementById('questionTypeAccuracyChart').getContext('2d');
        const questionTypeAccuracyChart = new Chart(accuracyCompareCtx, {
            type: 'bar',
            data: {
                labels: ['单选题', '多选题', '判断题', '填空题'],
                datasets: [{
                    label: '正确率(%)',
                    data: [
                        {{ question_type_stats.single.accuracy }},
                        {{ question_type_stats.multiple.accuracy }},
                        {{ question_type_stats.judgment.accuracy }},
                        {{ question_type_stats.fillBlank.accuracy }}
                    ],
                    backgroundColor: [
                        'rgba(0, 123, 255, 0.7)',
                        'rgba(255, 193, 7, 0.7)',
                        'rgba(40, 167, 69, 0.7)',
                        'rgba(23, 162, 184, 0.7)'
                    ],
                    borderColor: [
                        'rgba(0, 123, 255, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(40, 167, 69, 1)',
                        'rgba(23, 162, 184, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                indexAxis: 'y',
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        beginAtZero: true,
                        max: 100
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: '题型正确率对比',
                        font: {
                            size: 14
                        }
                    }
                }
            }
        });
        
        // 初始化能力雷达图
        const radarCtx = document.getElementById('skillRadarChart').getContext('2d');
        
        // 准备雷达图数据
        {% if course_performance %}
        // 使用课程表现数据
        const courseLabels = [];
        const courseData = [];
        {% for course_id, data in course_performance.items() %}
        courseLabels.push("{{ data.name }}");
        courseData.push({{ data.accuracy * 100 }});
        {% endfor %}
        
        const skillRadarChart = new Chart(radarCtx, {
            type: 'radar',
            data: {
                labels: courseLabels,
                datasets: [{
                    label: '能力水平',
                    data: courseData,
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 2,
                    pointBackgroundColor: 'rgba(54, 162, 235, 1)',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: 'rgba(54, 162, 235, 1)'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '课程能力雷达图',
                        font: {
                            size: 16
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let value = context.raw || 0;
                                return `掌握率: ${value.toFixed(1)}%`;
                            }
                        }
                    }
                },
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            stepSize: 20,
                            callback: function(value) {
                                return value + '%';
                            }
                        },
                        pointLabels: {
                            font: {
                                size: 12
                            }
                        }
                    }
                }
            }
        });
        {% else %}
        // 使用默认技能数据
        const skillRadarChart = new Chart(radarCtx, {
            type: 'radar',
            data: {
                labels: ['大数据技术与应用', '可视化', '机器学习', '生活美学', '区块链', '数据描述'],
                datasets: [{
                    label: '能力水平',
                    data: [
                        {{ skill_scores.basics|default(0) * 100 }},
                        {{ skill_scores.analysis|default(0) * 100 }},
                        {{ skill_scores.visualization|default(0) * 100 }},
                        {{ skill_scores.machine_learning|default(0) * 100 }},
                        {{ skill_scores.tools|default(0) * 100 }},
                        0 // 数据描述默认为0
                    ],
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 2,
                    pointBackgroundColor: 'rgba(54, 162, 235, 1)',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: 'rgba(54, 162, 235, 1)'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '能力雷达图',
                        font: {
                            size: 16
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let value = context.raw || 0;
                                return `掌握率: ${value.toFixed(1)}%`;
                            }
                        }
                    }
                },
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            stepSize: 20,
                            callback: function(value) {
                                return value + '%';
                            }
                        },
                        pointLabels: {
                            font: {
                                size: 12
                            }
                        }
                    }
                }
            }
        });
        {% endif %}

        // 初始化活动图表：每日学习进度
        const activityCtx = document.getElementById('activityChart').getContext('2d');
        // 提取数据
        const activityDailyProgress = {{ daily_progress|tojson }};
        const activityDates = activityDailyProgress.map(item => item.date);
        const activityTotals = activityDailyProgress.map(item => item.total);
        const activityAccuracies = activityDailyProgress.map(item => (item.accuracy * 100).toFixed(1));

        const activityChart = new Chart(activityCtx, {
            type: 'line',
            data: {
                labels: activityDates,
                datasets: [
                    {
                        label: '答题数量',
                        data: activityTotals,
                        backgroundColor: 'rgba(255, 159, 64, 0.2)',
                        borderColor: 'rgba(255, 159, 64, 1)',
                        borderWidth: 2,
                        pointRadius: 4,
                        yAxisID: 'y1'
                    },
                    {
                        label: '正确率 (%)',
                        data: activityAccuracies,
                        backgroundColor: 'rgba(54, 162, 235, 0.2)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 2,
                        pointRadius: 4,
                        yAxisID: 'y'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        min: 0,
                        max: 100,
                        title: {
                            display: true,
                            text: '正确率 (%)'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        min: 0,
                        // 使用动态最大值，而不是固定的20
                        suggestedMax: Math.max(...activityTotals) * 1.2,
                        title: {
                            display: true,
                            text: '题目数量'
                        },
                        grid: {
                            drawOnChartArea: false
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '日期'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: '每日学习进度',
                        font: {
                            size: 16
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                let value = context.raw || 0;
                                return `${label}: ${value}`;
                            }
                        }
                    }
                }
            }
        });
        
        // 为学习活动图表的切换按钮添加事件处理
        document.querySelectorAll('[data-chart]').forEach(button => {
            button.addEventListener('click', function() {
                const chartType = this.dataset.chart;
                // 移除所有按钮的active类
                document.querySelectorAll('[data-chart]').forEach(btn => {
                    btn.classList.remove('active');
                });
                // 添加当前按钮的active类
                this.classList.add('active');
                
                // 更新图表数据
                if (chartType === 'activity') {
                    activityChart.data.datasets = [{
                        label: '每日活跃度',
                        data: activityTotals,
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 2,
                        pointRadius: 4,
                        yAxisID: 'y1'
                    }];
                    activityChart.options.scales.y1.display = true;
                    activityChart.options.scales.y.display = false;
                } else if (chartType === 'accuracy') {
                    activityChart.data.datasets = [{
                        label: '正确率 (%)',
                        data: activityAccuracies,
                        backgroundColor: 'rgba(54, 162, 235, 0.2)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 2,
                        pointRadius: 4,
                        yAxisID: 'y'
                    }];
                    activityChart.options.scales.y.display = true;
                    activityChart.options.scales.y1.display = false;
                }
                activityChart.update();
            });
        });
        
        // 删除用户按钮事件
        $("#deleteUserBtn").on("click", function() {
            $('#deleteUserModal').modal('show');
        });

        // 禁用用户按钮事件
        $("#disableUserBtn").on("click", function() {
            $('#toggleUserModal').modal('show');
        });
        
        // 刷新数据按钮事件
        $("#refreshDataBtn").on("click", function() {
            // 添加旋转动画到图标
            const icon = $(this).find('i');
            icon.addClass('fa-spin');
            $(this).prop('disabled', true);
            
            // 刷新页面
            setTimeout(function() {
                window.location.reload();
            }, 500);
        });
        
        // 导出数据按钮事件
        $("#exportDataBtn").on("click", function() {
            $('#exportDataModal').modal('show');
        });
        
        // 处理导出数据确认
        $("#confirmExportData").on("click", function() {
            // 获取选中的数据类型
            const dataTypes = [];
            if ($("#exportAnswerRecords").prop("checked")) dataTypes.push("answer_records");
            if ($("#exportWrongQuestions").prop("checked")) dataTypes.push("wrong_questions");
            if ($("#exportStatistics").prop("checked")) dataTypes.push("statistics");
            if ($("#exportUserInfo").prop("checked")) dataTypes.push("user_info");
            
            // 获取导出格式
            const format = $("input[name='exportFormat']:checked").val();
            
            // 创建一个临时按钮执行下载操作
            const userId = {{ user.id }};
            const downloadUrl = `/admin/export_user_data/${userId}?format=${format}&data_types=${dataTypes.join(",")}`;            
            
            // 更新按钮状态
            const btn = $(this);
            const originalText = btn.html();
            btn.html(`<i class="fas fa-spinner fa-spin me-2"></i>正在导出...`);
            btn.prop('disabled', true);
            
            // 创建一个隧道式 iframe 进行下载
            const iframe = $('<iframe/>')
                .attr('src', downloadUrl)
                .css({ width: 0, height: 0, position: 'absolute', visibility: 'hidden' })
                .appendTo('body');
            
            // 3 秒后恢复按钮状态
            setTimeout(function() {
                btn.html(originalText);
                btn.prop('disabled', false);
                $('#exportDataModal').modal('hide');
                
                // 显示成功消息
                const alertHTML = `
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>数据导出成功！
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;
                $('.user-management-container').before(alertHTML);
                
                // 5秒后自动关闭提示
                setTimeout(function() {
                    $('.alert').alert('close');
                }, 5000);
                
                // 删除 iframe
                setTimeout(function() {
                    iframe.remove();
                }, 1000);
            }, 3000);
        });

        // 启用用户按钮事件
        $("#enableUserBtn").on("click", function() {
            $('#toggleUserModal').modal('show');
        });
        
        // 编辑用户信息按钮事件 - 顶部按钮
        $("#editUserInfoBtn").on("click", function() {
            $('#editUserModal').modal('show');
        });
        
        // 编辑班级按钮事件 - 个人资料卡片按钮
        $("#editClassBtn").on("click", function() {
            $('#editUserModal').modal('show');
            // 将焦点放在班级选择框上
            setTimeout(function() {
                $('#class_id').focus();
            }, 500);
        });
        
        // 打印报告按钮事件
        $("#printReportBtn").on("click", function() {
            // 打印前的准备工作
            const printWindow = window.open('', '_blank');
            
            // 获取用户基本信息
            const userId = {{ user.id }};
            const nickname = "{{ user.nickname if user.nickname else '未命名用户' }}";
            const records = {{ records|tojson }};
            
            // 计算统计数据
            let totalRecords = records.length;
            let correctRecords = records.filter(record => record.is_correct).length;
            let wrongRecords = totalRecords - correctRecords;
            let accuracyRate = totalRecords > 0 ? (correctRecords / totalRecords * 100).toFixed(2) : 0;
            
            // 准备打印内容
            let printContent = `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>用户学习报告 - ${nickname}</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        margin: 0;
                        padding: 20px;
                        color: #333;
                    }
                    /* 其他样式... */
                </style>
            </head>
            <body>
                <div class="report-header">
                    <div class="report-title">用户学习报告</div>
                    <div class="report-date">生成时间: ${new Date().toLocaleString()}</div>
                </div>
                
                <!-- 用户信息、学习统计、答题记录等内容 -->
                
                <div class="footer">
                    <p>本报告由系统自动生成，仅供参考</p>
                </div>
                
                <div class="no-print" style="text-align: center; margin-top: 30px;">
                    <button onclick="window.print()" style="padding: 10px 20px; background-color: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        点击打印报告
                    </button>
                </div>
            </body>
            </html>
            `;
            
            // 写入打印窗口并触发打印
            printWindow.document.open();
            printWindow.document.write(printContent);
            printWindow.document.close();
        });
        
        // 定义筛选状态对象
        let activeFilters = {
            resultType: 'all',   // 正确/错误/全部
            questionType: 'all', // 单选/多选/判断/填空/全部
            searchTerm: ''       // 搜索关键词
        };
        
        // 添加结果筛选按钮点击事件处理 (全部/正确/错误)
        $(".result-filter").on("click", function() {
            const filterType = $(this).data("filter");
            const isActive = $(this).hasClass("active");
            
            if (filterType === 'all') {
                // 如果点击全部
                $(".result-filter").removeClass("active");
                $(this).addClass("active");
                activeFilters.resultType = 'all';
            } else {
                // 如果点击正确或错误
                $(".result-filter[data-filter='all']").removeClass("active");
                
                if (isActive) {
                    // 如果已经激活，则取消激活
                    $(this).removeClass("active");
                    // 检查是否没有激活的结果按钮，如果没有则激活"全部"
                    if ($(".result-filter.active").length === 0) {
                        $(".result-filter[data-filter='all']").addClass("active");
                        activeFilters.resultType = 'all';
                    } else {
                        // 更新筛选条件
                        updateResultFilter();
                    }
                } else {
                    // 如果未激活，则激活
                    $(this).addClass("active");
                    updateResultFilter();
                }
            }
            
            // 执行筛选
            applyFilters();
        });
        
        // 添加题型筛选按钮点击事件处理 (单选/多选/判断/填空)
        $(".type-filter").on("click", function() {
            const filterType = $(this).data("filter");
            const isActive = $(this).hasClass("active");
            
            if (filterType === 'all') {
                // 如果点击全部
                $(".type-filter").removeClass("active");
                $(this).addClass("active");
                activeFilters.questionType = 'all';
            } else {
                // 如果点击其他题型按钮
                $(".type-filter[data-filter='all']").removeClass("active");
                
                if (isActive) {
                    // 如果已经激活，则取消激活
                    $(this).removeClass("active");
                    // 检查是否没有激活的题型按钮，如果没有则激活"全部"
                    if ($(".type-filter.active").length === 0) {
                        $(".type-filter[data-filter='all']").addClass("active");
                        activeFilters.questionType = 'all';
                    } else {
                        // 更新筛选条件
                        updateTypeFilter();
                    }
                } else {
                    // 如果未激活，则激活
                    $(this).addClass("active");
                    updateTypeFilter();
                }
            }
            
            // 执行筛选
            applyFilters();
        });
        
        // 搜索框事件处理
        $("#quickSearchInput").on("keyup", function() {
            activeFilters.searchTerm = $(this).val().trim();
            applyFilters();
        });
        
        // 点击搜索按钮
        $("#quickSearchBtn").on("click", function() {
            activeFilters.searchTerm = $("#quickSearchInput").val().trim();
            applyFilters();
        });
        
        // 在搜索框中按ESC键清除搜索
        $("#quickSearchInput").on("keydown", function(e) {
            if (e.key === "Escape") {
                $(this).val('');
                activeFilters.searchTerm = '';
                applyFilters();
            }
        });
        
        // 更新结果筛选条件
        function updateResultFilter() {
            const activeResults = [];
            $(".result-filter.active").each(function() {
                activeResults.push($(this).data("filter"));
            });
            activeFilters.resultType = activeResults;
        }
        
        // 更新题型筛选条件
        function updateTypeFilter() {
            const activeTypes = [];
            $(".type-filter.active").each(function() {
                activeTypes.push($(this).data("filter"));
            });
            activeFilters.questionType = activeTypes;
        }
        
        // 执行多重筛选
        function applyFilters() {
            // 获取所有记录项
            const recordItems = $(".record-item");
            
            recordItems.each(function() {
                const $item = $(this);
                const recordResult = $item.data("record-result");
                const recordType = $item.data("record-type");
                
                // 获取记录的所有文本内容用于搜索
                const recordText = $item.text().toLowerCase();
                
                // 搜索匹配
                const searchTerm = activeFilters.searchTerm.toLowerCase();
                const matchesSearch = searchTerm === '' || 
                                       recordText.includes(searchTerm);
                
                // 结果筛选（正确/错误）
                let matchesResult = false;
                if (activeFilters.resultType === 'all') {
                    matchesResult = true;
                } else if (Array.isArray(activeFilters.resultType)) {
                    matchesResult = activeFilters.resultType.includes(recordResult);
                } else {
                    matchesResult = recordResult === activeFilters.resultType;
                }
                
                // 题型筛选（单选/多选/判断/填空）
                let matchesQuestionType = false;
                if (activeFilters.questionType === 'all') {
                    matchesQuestionType = true;
                } else if (Array.isArray(activeFilters.questionType)) {
                    matchesQuestionType = activeFilters.questionType.includes(recordType);
                } else {
                    matchesQuestionType = recordType === activeFilters.questionType;
                }
                
                // 综合判断，同时满足搜索、结果和题型的筛选条件
                if (matchesSearch && matchesResult && matchesQuestionType) {
                    $item.show();
                } else {
                    $item.hide();
                }
            });
            
            // 更新筛选结果计数
            updateFilterCounts();
        }
        
        // 更新筛选结果计数
        function updateFilterCounts() {
            const visibleItems = $(".record-item:visible").length;
            const totalItems = $(".record-item").length;
            
            if (activeFilters.resultType !== 'all' || activeFilters.questionType !== 'all' || activeFilters.searchTerm !== '') {
                $("#recordsCounter").text(`显示 ${visibleItems} 条记录（共 ${totalItems} 条）`);
            } else {
                $("#recordsCounter").text(`共 ${totalItems} 条记录`);
            }
        }
        
        // 实现点击记录头部打开/折叠详情
        $(document).on('click', '.record-header', function(e) {
            // 确保不是从下拉菜单或按钮点击时触发
            if (!$(e.target).closest('.record-actions').length) {
                const recordItem = $(this).closest('.record-item');
                const recordDetail = recordItem.find('.record-detail');
                recordDetail.slideToggle(200);
            }
        });
        
        // 实现查看详情功能
        $(document).on('click', '.view-record', function(e) {
            e.preventDefault();
            const recordId = $(this).data('record-id');
            const recordItem = $(`.record-item[data-record-id="${recordId}"]`);
            const recordDetail = recordItem.find('.record-detail');
            
            // 打开详情面板
            recordDetail.slideDown(200);
            
            // 滚动到该元素处
            $('html, body').animate({
                scrollTop: recordItem.offset().top - 100
            }, 300);
            
            // 高亮显示该条目
            recordItem.addClass('highlight-record');
            setTimeout(function() {
                recordItem.removeClass('highlight-record');
            }, 2000);
        });
        
        // 实现删除记录功能
        $(document).on('click', '.delete-record', function(e) {
            e.preventDefault();
            const recordId = $(this).data('record-id');
            
            // 设置模态框中的记录ID
            $('#deleteRecordModal').data('record-id', recordId);
            
            // 显示确认模态框
            $('#deleteRecordModal').modal('show');
        });
        
        // 处理删除记录确认
        $('#confirmDeleteRecord').on('click', function() {
            const recordId = $('#deleteRecordModal').data('record-id');
            const recordItem = $(`.record-item[data-record-id="${recordId}"]`);
            
            // 隐藏模态框
            $('#deleteRecordModal').modal('hide');
            
            // 显示加载指示器
            const loadingHTML = `
                <div class="alert alert-info alert-dismissible fade show" role="alert" id="deleteLoadingAlert">
                    <div class="d-flex align-items-center">
                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                        <span>正在删除记录...</span>
                    </div>
                </div>
            `;
            $('#recordsContainer').before(loadingHTML);
            
            // 发送删除请求 - 使用 POST 而不是 DELETE
            $.ajax({
                url: `/admin/remove_record/${recordId}`,  // 使用管理员路由
                type: 'POST',
                success: function(response) {
                    // 移除加载指示器
                    $('#deleteLoadingAlert').remove();
                    
                    // 成功删除后的操作
                    recordItem.slideUp(300, function() {
                        recordItem.remove();
                        // 更新记录计数
                        updateFilterCounts();
                    });
                    
                    // 显示成功消息
                    const alertHTML = `
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>记录删除成功
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    `;
                    $('#recordsContainer').before(alertHTML);
                    
                    // 5秒后自动关闭提示
                    setTimeout(function() {
                        $('.alert').alert('close');
                    }, 5000);
                },
                error: function(xhr, status, error) {
                    // 移除加载指示器
                    $('#deleteLoadingAlert').remove();
                    
                    // 处理错误
                    const alertHTML = `
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>删除失败: ${xhr.responseJSON?.message || error}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    `;
                    $('#recordsContainer').before(alertHTML);
                }
            });
        });

        // 处理高级搜索功能
        $('#searchRecordsBtn').on('click', function() {
            // 获取表单数据
            const formData = {
                user_id: {{ user.id }},  // 获取当前用户ID
                types: [], // 题目类型
                result: $('input[name="resultFilter"]:checked').val(), // 答题结果
                date_from: $('#dateFrom').val(), // 开始日期
                date_to: $('#dateTo').val(), // 结束日期
                keyword: $('#keywordSearch').val().trim() // 关键词
            };
            
            // 获取选中的题目类型
            if ($('#typeSingle').prop('checked')) formData.types.push('single');
            if ($('#typeMultiple').prop('checked')) formData.types.push('multiple');
            if ($('#typeJudgment').prop('checked')) formData.types.push('judgment');
            if ($('#typeFillBlank').prop('checked')) formData.types.push('fillBlank');
            
            // 显示加载指示器
            $('#searchRecordsModal').modal('hide');
            const loadingHTML = `
                <div class="alert alert-info alert-dismissible fade show" role="alert" id="searchLoadingAlert">
                    <div class="d-flex align-items-center">
                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                        <span>正在搜索记录...</span>
                    </div>
                </div>
            `;
            $('#recordsContainer').before(loadingHTML);
            
            // 发送AJAX请求
            $.ajax({
                url: '/admin/api/records/search',
                type: 'GET',
                data: formData,
                success: function(response) {
                    // 移除加载指示器
                    $('#searchLoadingAlert').remove();
                    
                    // 更新记录列表
                    updateRecordsList(response.records);
                    
                    // 显示搜索结果信息
                    const alertHTML = `
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>搜索完成，共找到 ${response.total} 条记录
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    `;
                    $('#recordsContainer').before(alertHTML);
                    
                    // 5秒后自动关闭提示
                    setTimeout(function() {
                        $('.alert').alert('close');
                    }, 5000);
                },
                error: function(xhr, status, error) {
                    // 移除加载指示器
                    $('#searchLoadingAlert').remove();
                    
                    // 处理错误
                    const alertHTML = `
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>搜索失败: ${xhr.responseJSON?.error || error}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    `;
                    $('#recordsContainer').before(alertHTML);
                }
            });
        });
        
        // 更新记录列表的函数
        function updateRecordsList(records) {
            const recordsList = $('#activityRecordsList');
            
            // 清空现有记录
            recordsList.empty();
            
            if (records && records.length > 0) {
                // 添加新记录
                records.forEach(function(record) {
                    const recordHTML = `
                        <div class="list-group-item record-item p-2" data-record-id="${record.id}"
                            data-record-type="${record.question_type}"
                            data-record-result="${record.is_correct ? 'correct' : 'wrong'}">
                            <div class="d-flex justify-content-between align-items-center record-header" style="cursor: pointer;">
                                <div class="d-flex align-items-center">
                                    <span class="badge 
                                                ${record.is_correct ? 'bg-success' : 'bg-danger'}
                                                me-2">
                                        ${record.is_correct ? '<i class="fas fa-check"></i>' : '<i class="fas fa-times"></i>'}
                                    </span>
                                    <span class="badge 
                                                ${record.question_type === 'single' ? 'bg-primary' : 
                                                  record.question_type === 'multiple' ? 'bg-warning' :
                                                  record.question_type === 'judgment' ? 'bg-success' : 'bg-info'}
                                                me-2">
                                        ${record.question_type === 'single' ? '单选题' : 
                                          record.question_type === 'multiple' ? '多选题' :
                                          record.question_type === 'judgment' ? '判断题' : '填空题'}
                                    </span>
                                    <span class="small text-secondary">题#${record.id}</span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <small class="text-muted me-2">${record.created_at}</small>
                                    <div class="dropdown record-actions">
                                        <button class="btn btn-sm btn-link text-muted" type="button"
                                            data-bs-toggle="dropdown">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end">
                                            <li><a class="dropdown-item view-record" href="#"
                                                    data-record-id="${record.id}"><i
                                                        class="fas fa-eye me-2"></i>查看详情</a></li>
                                            <li><a class="dropdown-item delete-record" href="#"
                                                    data-record-id="${record.id}"><i
                                                        class="fas fa-trash-alt me-2"></i>删除记录</a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="record-detail mt-1 ps-4" style="display: none;">
                                <div class="small">
                                    <div class="mb-1"><strong>问题:</strong> ${record.question_text || '(题目内容未记录)'}</div>
                                    <div class="mb-1"><strong>用户答案:</strong> <span
                                            class="text-${record.is_correct ? 'success' : 'danger'}"
                                            data-answer='${JSON.stringify(record.user_answer)}'>${formatAnswerOptions(record.user_answer)}</span></div>
                                    ${!record.is_correct ? `<div><strong>正确答案:</strong> <span class="text-success"
                                            data-answer='${JSON.stringify(record.correct_answer)}'>${formatAnswerOptions(record.correct_answer)}</span></div>` : ''}
                                </div>
                            </div>
                        </div>
                    `;
                    recordsList.append(recordHTML);
                });
                
                // 更新记录计数
                $("#recordsCounter").text(`共 ${records.length} 条记录`);
            } else {
                // 显示无记录提示
                recordsList.html(`
                    <div class="text-center py-4">
                        <i class="fas fa-info-circle text-muted mb-2" style="font-size: 2rem;"></i>
                        <p class="mb-0 text-muted">没有找到符合条件的记录</p>
                    </div>
                `);
                $("#recordsCounter").text(`共 0 条记录`);
            }
        }
    });
</script>
{% endblock %}
/**
 * 直接图表修复 - 用户活跃度分析
 * 这个脚本会直接渲染用户活跃度分析图表，不依赖于复杂的事件监听器
 */

// 立即执行函数，确保代码立即运行
(function() {
  console.log('直接图表修复脚本正在执行...');
  
  // 直接获取canvas元素
  var userActivityChartCanvas = document.getElementById('userActivityChart');
  
  // 检查元素是否存在
  if (!userActivityChartCanvas) {
    console.error('找不到用户活跃度分析图表元素! 将在1秒后重试...');
    
    // 如果元素不存在，可能是因为DOM还没有完全加载，设置一个短暂的延迟
    setTimeout(function() {
      userActivityChartCanvas = document.getElementById('userActivityChart');
      if (!userActivityChartCanvas) {
        console.error('重试后仍然找不到用户活跃度分析图表元素!');
        return;
      }
      fetchDataAndInitializeChart(userActivityChartCanvas);
    }, 1000);
    return;
  }
  
  // 如果元素存在，立即初始化图表
  fetchDataAndInitializeChart(userActivityChartCanvas);
  
  // 获取实时数据并初始化图表
  function fetchDataAndInitializeChart(canvas) {
    console.log('正在获取用户活跃度实时数据...');
    
    // 在图表容器中显示加载状态
    var container = canvas.parentElement;
    container.style.position = 'relative';
    
    // 创建加载指示器
    var loadingIndicator = document.createElement('div');
    loadingIndicator.className = 'chart-loading-indicator';
    loadingIndicator.style.position = 'absolute';
    loadingIndicator.style.top = '50%';
    loadingIndicator.style.left = '50%';
    loadingIndicator.style.transform = 'translate(-50%, -50%)';
    loadingIndicator.style.padding = '10px 20px';
    loadingIndicator.style.background = 'rgba(255, 255, 255, 0.9)';
    loadingIndicator.style.borderRadius = '8px';
    loadingIndicator.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
    loadingIndicator.style.fontSize = '14px';
    loadingIndicator.style.color = '#1e293b';
    loadingIndicator.style.display = 'flex';
    loadingIndicator.style.alignItems = 'center';
    loadingIndicator.style.zIndex = '5';
    loadingIndicator.innerHTML = '<div class="spinner" style="width: 20px; height: 20px; border: 2px solid rgba(79, 70, 229, 0.3); border-radius: 50%; border-top-color: #4f46e5; animation: spin 0.8s linear infinite; margin-right: 10px;"></div> 加载中...';
    
    // 添加动画样式
    var style = document.createElement('style');
    style.textContent = '@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }';
    document.head.appendChild(style);
    
    // 添加加载指示器到容器
    container.appendChild(loadingIndicator);
    
    // 获取从API获取真实数据
    fetch('/api/statistics/active-users-data')
      .then(function(response) {
        if (!response.ok) {
          throw new Error('API请求失败: ' + response.status);
        }
        return response.json();
      })
      .then(function(data) {
        // 移除加载指示器
        container.removeChild(loadingIndicator);
        
        // 如果成功获取数据，初始化图表
        if (data.status === 'success') {
          initializeChart(canvas, data);
        } else {
          throw new Error('获取数据失败: ' + (data.message || '未知错误'));
        }
      })
      .catch(function(error) {
        console.error('获取用户活跃度数据失败:', error);
        
        // 移除加载指示器
        if (loadingIndicator.parentNode === container) {
          container.removeChild(loadingIndicator);
        }
        
        // 从canvas的data属性获取备用数据
        var fallbackData = {
          labels: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
          active_users: []
        };
        
        try {
          if (canvas.dataset && canvas.dataset.chartValues) {
            var rawData = JSON.parse(canvas.dataset.chartValues);
            if (rawData && rawData.datasets && rawData.datasets.length > 0) {
              fallbackData.active_users = rawData.datasets[0].data;
              console.log('使用备用数据初始化图表');
            }
          }
        } catch (e) {
          console.error('解析备用数据失败:', e);
          // 如果无法解析备用数据，使用模拟数据
          fallbackData.active_users = [3, 5, 4, 2, 6, 7, 5];
        }
        
        // 使用备用数据初始化图表
        initializeChart(canvas, fallbackData);
        
        // 显示错误通知
        var errorNotification = document.createElement('div');
        errorNotification.className = 'chart-error-notification';
        errorNotification.style.position = 'absolute';
        errorNotification.style.top = '10px';
        errorNotification.style.right = '10px';
        errorNotification.style.padding = '5px 10px';
        errorNotification.style.background = 'rgba(239, 68, 68, 0.2)';
        errorNotification.style.color = '#ef4444';
        errorNotification.style.borderRadius = '4px';
        errorNotification.style.fontSize = '12px';
        errorNotification.style.opacity = '1';
        errorNotification.style.transition = 'opacity 0.5s ease';
        errorNotification.style.zIndex = '10';
        errorNotification.innerHTML = '<i class="fas fa-exclamation-triangle" style="margin-right: 5px;"></i> 使用备用数据';
        
        // 将错误通知添加到容器
        container.appendChild(errorNotification);
        
        // 设置通知自动消失
        setTimeout(function() {
          errorNotification.style.opacity = '0';
          setTimeout(function() {
            if (errorNotification.parentNode === container) {
              container.removeChild(errorNotification);
            }
          }, 500);
        }, 3000);
      });
  }
  
  // 图表初始化函数
  function initializeChart(canvas, data) {
    console.log('正在初始化用户活跃度分析图表...', data);
    
    try {
      // 获取图表容器样式
      var container = canvas.parentElement;
      container.style.position = 'relative';
      container.style.minHeight = '250px';
      
      // 获取当前日期的索引，用于高亮当前天数据点
      var currentDayIndex = data.current_day_index !== undefined ? data.current_day_index : new Date().getDay();
      if (currentDayIndex === 0) currentDayIndex = 6; // JS的getDay()返回0为周日，我们要转换为6
      else currentDayIndex--; // 其他天减1匹配我们的数据索引
      
      // 准备图表数据
      var chartData = {
        labels: data.labels || ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
        datasets: [{
          label: "活跃用户数",
          data: data.active_users || [3, 5, 4, 2, 6, 7, 5],
          backgroundColor: "rgba(79, 70, 229, 0.2)",
          borderColor: "#4f46e5",
          borderWidth: 2,
          tension: 0.4,
          pointBackgroundColor: function(context) {
            // 突出显示当前日期的点
            var index = context.dataIndex;
            return index === currentDayIndex ? '#ff6a00' : '#4f46e5';
          },
          pointBorderColor: function(context) {
            var index = context.dataIndex;
            return index === currentDayIndex ? '#ff6a00' : '#4f46e5';
          },
          pointRadius: function(context) {
            var index = context.dataIndex;
            return index === currentDayIndex ? 6 : 4;
          },
          pointHoverRadius: 8,
          pointHoverBackgroundColor: "#ff6a00",
          pointHoverBorderColor: "#ff6a00",
          pointHoverBorderWidth: 2
        }]
      };
      
      // 获取绘图上下文
      var ctx = canvas.getContext('2d');
      
      // 创建渐变填充
      var gradient = ctx.createLinearGradient(0, 0, 0, 250);
      gradient.addColorStop(0, 'rgba(79, 70, 229, 0.4)');
      gradient.addColorStop(1, 'rgba(79, 70, 229, 0.0)');
      
      // 更新图表配置中的背景色
      chartData.datasets[0].backgroundColor = gradient;
      
      // 创建Chart实例
      console.log('创建Chart实例...');
      
      // 找到当前日指示器元素并更新它
      var currentDayIndicator = container.parentElement.querySelector('.current-day-indicator');
      if (currentDayIndicator) {
        // 显示指示器
        currentDayIndicator.style.display = 'block';
        
        // 更新当前日数据
        var currentDayValue = data.active_users ? data.active_users[currentDayIndex] : chartData.datasets[0].data[currentDayIndex];
        currentDayIndicator.innerHTML = '今日活跃: ' + currentDayValue + ' 人';
      }
      
      window.userActivityChart = new Chart(ctx, {
        type: 'line',
        data: chartData,
        options: {
          responsive: true,
          maintainAspectRatio: false,
          animation: {
            duration: 1500,
            easing: 'easeOutQuart'
          },
          layout: {
            padding: {
              left: 10,
              right: 20,
              top: 25, // 增加顶部间距，为数据标签腾出空间
              bottom: 10
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              grid: {
                color: 'rgba(0, 0, 0, 0.05)',
                drawBorder: false
              },
              ticks: {
                padding: 10,
                font: {
                  size: 12,
                  family: "'Inter', 'Helvetica', 'Arial', sans-serif"
                },
                color: '#64748b'
              }
            },
            x: {
              grid: {
                display: false,
                drawBorder: false
              },
              ticks: {
                padding: 10,
                font: {
                  size: 12,
                  family: "'Inter', 'Helvetica', 'Arial', sans-serif"
                },
                color: '#64748b'
              }
            }
          },
          plugins: {
            legend: {
              display: false
            },
            // 添加数据标签插件，始终显示数据值
            datalabels: {
              display: function(context) {
                var index = context.dataIndex;
                // 只显示当前日的数据标签，确保它始终可见
                return index === currentDayIndex;
              },
              align: 'top',
              anchor: 'end',
              offset: 0,
              padding: 6,
              backgroundColor: 'rgba(255, 106, 0, 0.9)',
              borderRadius: 4,
              color: 'white',
              font: {
                size: 13,
                weight: 'bold',
                family: "'Inter', 'Helvetica', 'Arial', sans-serif"
              },
              // 当前日期的标签格式
              formatter: function(value, context) {
                return '今日活跃: ' + value + ' 人';
              }
            },
            tooltip: {
              backgroundColor: '#1e293b',
              titleColor: '#ffffff',
              bodyColor: '#e2e8f0',
              padding: 12,
              cornerRadius: 8,
              displayColors: false,
              titleFont: {
                size: 14,
                family: "'Inter', 'Helvetica', 'Arial', sans-serif",
                weight: 'bold'
              },
              bodyFont: {
                size: 13,
                family: "'Inter', 'Helvetica', 'Arial', sans-serif"
              },
              callbacks: {
                title: function(context) {
                  // 当前日期添加"(今天)"标识
                  var index = context[0].dataIndex;
                  var day = chartData.labels[index];
                  return index === currentDayIndex ? day + " (今天)" : day;
                },
                label: function(context) {
                  var value = context.raw;
                  // 创建带有更丰富格式的提示
                  var index = context.dataIndex;
                  var comparisonText = '';
                  
                  // 比较与昨天的变化
                  if (index > 0) {
                    var yesterday = chartData.datasets[0].data[index-1];
                    var difference = value - yesterday;
                    var percentChange = yesterday !== 0 ? ((difference / yesterday) * 100).toFixed(1) : 0;
                    
                    if (difference > 0) {
                      comparisonText = `较前日增长: ${difference} 人 (+${percentChange}%)`;
                    } else if (difference < 0) {
                      comparisonText = `较前日减少: ${-difference} 人 (${percentChange}%)`;
                    } else {
                      comparisonText = `较前日持平`;
                    }
                  }
                  
                  // 返回多行提示文本
                  var result = [`活跃用户: ${value} 人`];
                  if (comparisonText) {
                    result.push(comparisonText);
                  }
                  return result;
                },
                afterLabel: function(context) {
                  var index = context.dataIndex;
                  
                  // 为当前日期添加特殊说明
                  if (index === currentDayIndex) {
                    return ['', '这是今天的实时数据'];
                  }
                  return null;
                }
              }
            }
          },
          // 交互设置
          interaction: {
            mode: 'index',
            intersect: false,
            // 鼠标悬停时显示垂直参考线
            axis: 'x'
          },
          // 添加标注插件
          annotation: {
            annotations: {
              // 添加当前日期的垂直线
              line1: {
                type: 'line',
                xMin: currentDayIndex,
                xMax: currentDayIndex,
                borderColor: 'rgba(255, 106, 0, 0.3)',
                borderWidth: 2,
                borderDash: [5, 5],
                label: {
                  content: '今天',
                  enabled: true,
                  position: 'top',
                  backgroundColor: 'rgba(255, 106, 0, 0.7)',
                  font: {
                    size: 11
                  }
                }
              }
            }
          }
        },
        // 注册插件 - 必需的步骤
        plugins: [ChartDataLabels]
      });
      
      // 创建一个更明显的今天标记
      function createTodayMarker() {
        // 只有在图表可用时执行
        if (!window.userActivityChart || !window.userActivityChart.canvas) return;
        
        var chart = window.userActivityChart;
        var ctx = chart.ctx;
        var todayPoint = chart.getDatasetMeta(0).data[currentDayIndex];
        
        if (!todayPoint) return;
        
        // 获取当前日的数据点位置
        var x = todayPoint.x;
        var y = todayPoint.y - 45; // 位于数据点上方
        
        // 绘制高亮指示器
        ctx.save();
        
        // 绘制指向箭头
        ctx.fillStyle = 'rgba(255, 106, 0, 0.9)';
        ctx.beginPath();
        ctx.moveTo(x, y + 20);
        ctx.lineTo(x - 10, y);
        ctx.lineTo(x + 10, y);
        ctx.closePath();
        ctx.fill();
        
        // 绘制更醒目的"今天"标记
        ctx.fillStyle = 'rgba(255, 106, 0, 0.15)';
        ctx.beginPath();
        ctx.arc(x, todayPoint.y, 16, 0, Math.PI * 2);
        ctx.fill();
        
        // 添加脉动动画效果
        var time = new Date().getTime() / 1000;
        var pulseFactor = Math.sin(time * 3) * 0.2 + 0.8; // 0.6 到 1.0 之间脉动
        ctx.fillStyle = 'rgba(255, 106, 0, 0.3)';
        ctx.beginPath();
        ctx.arc(x, todayPoint.y, 16 * pulseFactor, 0, Math.PI * 2);
        ctx.fill();
        
        ctx.restore();
      }
      
      // 定期刷新脉动效果
      function startPulseAnimation() {
        if (window.pulseAnimationInterval) {
          clearInterval(window.pulseAnimationInterval);
        }
        
        window.pulseAnimationInterval = setInterval(function() {
          if (window.userActivityChart) {
            createTodayMarker();
          } else {
            clearInterval(window.pulseAnimationInterval);
          }
        }, 50);
      }
      
      // 在图表渲染完成后添加标记和启动动画
      window.userActivityChart.options.animation.onComplete = function() {
        createTodayMarker();
        startPulseAnimation();
        
        // 更新当前日指示器位置
        updateCurrentDayIndicatorPosition();
      };
      
      // 定位当前日指示器的位置
      function updateCurrentDayIndicatorPosition() {
        var currentDayIndicator = container.parentElement.querySelector('.current-day-indicator');
        if (!currentDayIndicator || !window.userActivityChart) return;
        
        var chart = window.userActivityChart;
        var todayPoint = chart.getDatasetMeta(0).data[currentDayIndex];
        
        if (!todayPoint) return;
        
        // 将指示器放在数据点正上方
        currentDayIndicator.style.position = 'absolute';
        currentDayIndicator.style.left = todayPoint.x + 'px';
        currentDayIndicator.style.top = (todayPoint.y - 45) + 'px';
        currentDayIndicator.style.transform = 'translate(-50%, -100%)';
      }
      
      // 监听窗口大小变化，更新指示器位置
      window.addEventListener('resize', function() {
        // 使用节流函数避免频繁调用
        if (this.resizeTimeout) clearTimeout(this.resizeTimeout);
        this.resizeTimeout = setTimeout(function() {
          updateCurrentDayIndicatorPosition();
        }, 200);
      });
      
      // 确保更新图表时重绘标记
      window.userActivityChart.options.plugins.tooltip.callbacks.afterDraw = function() {
        createTodayMarker();
      };
      
      console.log('用户活跃度分析图表初始化成功!');
      
      // 添加成功指示器
      var successIndicator = document.createElement('div');
      successIndicator.className = 'chart-success-indicator';
      successIndicator.style.position = 'absolute';
      successIndicator.style.top = '10px';
      successIndicator.style.right = '10px';
      successIndicator.style.padding = '5px 10px';
      successIndicator.style.background = 'rgba(16, 185, 129, 0.2)';
      successIndicator.style.color = '#10b981';
      successIndicator.style.borderRadius = '4px';
      successIndicator.style.fontSize = '12px';
      successIndicator.style.fontWeight = 'bold';
      successIndicator.style.opacity = '1';
      successIndicator.style.transition = 'opacity 0.5s ease';
      successIndicator.style.zIndex = '10';
      successIndicator.innerHTML = '<i class="fas fa-check-circle" style="margin-right: 5px;"></i> 图表已加载';
      
      // 将指示器添加到容器
      container.appendChild(successIndicator);
      
      // 设置一段时间后隐藏指示器
      setTimeout(function() {
        successIndicator.style.opacity = '0';
        setTimeout(function() {
          container.removeChild(successIndicator);
        }, 500);
      }, 3000);
      
      // 添加图表交互效果
      canvas.addEventListener('mousemove', function(event) {
        var rect = canvas.getBoundingClientRect();
        var x = event.clientX - rect.left;
        var y = event.clientY - rect.top;
        
        // 添加光晕效果
        container.style.background = `radial-gradient(circle at ${x}px ${y}px, rgba(248, 250, 252, 0.9) 0%, rgba(241, 245, 249, 0.4) 70%)`;
      });
      
      // 鼠标移出时恢复原样
      canvas.addEventListener('mouseleave', function() {
        container.style.background = 'linear-gradient(to bottom, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.3) 100%)';
      });
      
    } catch (error) {
      console.error('初始化用户活跃度分析图表时出错:', error);
      
      // 显示错误信息
      var errorMessage = document.createElement('div');
      errorMessage.className = 'chart-error-message';
      errorMessage.style.position = 'absolute';
      errorMessage.style.top = '50%';
      errorMessage.style.left = '50%';
      errorMessage.style.transform = 'translate(-50%, -50%)';
      errorMessage.style.padding = '20px';
      errorMessage.style.background = 'rgba(239, 68, 68, 0.1)';
      errorMessage.style.color = '#ef4444';
      errorMessage.style.borderRadius = '8px';
      errorMessage.style.fontSize = '14px';
      errorMessage.style.fontWeight = 'bold';
      errorMessage.style.textAlign = 'center';
      errorMessage.style.zIndex = '20';
      errorMessage.innerHTML = '<i class="fas fa-exclamation-triangle" style="margin-right: 8px;"></i> 图表加载失败，请刷新页面重试';
      
      // 将错误信息添加到容器
      canvas.parentElement.appendChild(errorMessage);
    }
  }
})(); 
/* Admin Dashboard - Enhanced Modern UI Style */

/* Custom Card Styles */
.stats-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
    border-radius: 0.5rem;
    background: linear-gradient(120deg, #5a67d8, #4c51bf);
    color: white;
    transition: all 0.3s ease;
    border: none;
    height: 100%;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.stats-card .number {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    line-height: 1;
}

.stats-card .label {
    font-size: 1.125rem;
    font-weight: 500;
    opacity: 0.9;
}

/* Enhanced Table Styles */
.table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
    border-radius: 0.5rem;
    overflow: hidden;
}

.table th {
    background-color: #f9fafb;
    color: #4a5568;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    padding: 1rem;
    border-bottom: 2px solid #e2e8f0;
}

.table td {
    padding: 1rem;
    vertical-align: middle;
    border-bottom: 1px solid #edf2f7;
}

.table tr:last-child td {
    border-bottom: none;
}

.table tr:hover td {
    background-color: #f7fafc;
}

/* Better Button Styles */
.btn {
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    transition: all 0.2s;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.btn-primary {
    background-color: #4c51bf;
    border-color: #4c51bf;
}

.btn-primary:hover {
    background-color: #434190;
    border-color: #434190;
    transform: translateY(-1px);
}

.btn-danger {
    background-color: #e53e3e;
    border-color: #e53e3e;
}

.btn-danger:hover {
    background-color: #c53030;
    border-color: #c53030;
    transform: translateY(-1px);
}

.btn-outline-primary {
    color: #4c51bf;
    border-color: #4c51bf;
}

.btn-outline-primary:hover {
    background-color: #4c51bf;
    color: white;
}

.btn-outline-danger {
    color: #e53e3e;
    border-color: #e53e3e;
}

.btn-outline-danger:hover {
    background-color: #e53e3e;
    color: white;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Modal Enhancements */
.modal-content {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.modal-header {
    border-bottom: 1px solid #edf2f7;
    padding: 1.25rem 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid #edf2f7;
    padding: 1.25rem 1.5rem;
}

/* DataTables Customization */
.dataTables_wrapper .dataTables_length, 
.dataTables_wrapper .dataTables_filter, 
.dataTables_wrapper .dataTables_info, 
.dataTables_wrapper .dataTables_processing, 
.dataTables_wrapper .dataTables_paginate {
    margin-bottom: 1rem;
    color: #4a5568;
}

/* 优化显示选择和搜索框样式 */
.dataTables_length,
.dataTables_filter {
    padding: 0.75rem 1.25rem;
    background-color: #ffffff;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
    display: inline-flex;
    align-items: center;
}

.dataTables_length {
    float: left;
}

.dataTables_filter {
    float: right;
}

.dataTables_length label,
.dataTables_filter label {
    margin: 0;
    font-weight: 500;
    color: #4a5568;
    display: flex;
    align-items: center;
    white-space: nowrap;
}

.dataTables_wrapper .dataTables_filter input {
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
    margin-left: 0.75rem;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    min-width: 240px;
    height: 38px;
}

.dataTables_wrapper .dataTables_filter input:focus {
    outline: none;
    border-color: #4c51bf;
    box-shadow: 0 0 0 3px rgba(76, 81, 191, 0.2);
}

.dataTables_wrapper .dataTables_filter input::placeholder {
    color: #a0aec0;
}

.dataTables_wrapper .dataTables_length select {
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    padding: 0.5rem 2rem 0.5rem 1rem;
    margin: 0 0.5rem;
    background-color: white;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    color: #4a5568;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%234a5568'%3E%3Cpath fill-rule='evenodd' d='M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z' clip-rule='evenodd'/%3E%3C/svg%3E");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    transition: all 0.3s ease;
    height: 38px;
}

.dataTables_wrapper .dataTables_length select:focus {
    outline: none;
    border-color: #4c51bf;
    box-shadow: 0 0 0 3px rgba(76, 81, 191, 0.2);
}

/* 美化搜索框标签 */
.dataTables_filter label::before {
    content: "\f002";
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-right: 0.5rem;
    color: #718096;
}

/* 美化显示标签 */
.dataTables_length label::before {
    content: "\f0ca";
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-right: 0.5rem;
    color: #718096;
}

/* 紧凑型控件显示 */
.dataTables_wrapper {
    position: relative;
    clear: both;
    padding-top: 0.5rem;
}

.dataTables_wrapper::after {
    content: "";
    display: table;
    clear: both;
}

/* 改进分页按钮样式 */
.dataTables_wrapper .dataTables_paginate {
    margin-top: 1rem;
    display: flex;
    justify-content: flex-end;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem;
    margin: 0 0.25rem;
    border: 1px solid #e2e8f0;
    color: #4a5568 !important;
    background: white;
    cursor: pointer;
    transition: all 0.2s ease;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background: #f7fafc !important;
    color: #4c51bf !important;
    border-color: #4c51bf;
    transform: translateY(-1px);
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: linear-gradient(to bottom, #5a67d8 0%, #4c51bf 100%) !important;
    border: 1px solid #4c51bf;
    color: white !important;
    font-weight: 600;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    background: linear-gradient(to bottom, #6875ed 0%, #5661d6 100%) !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.dataTables_wrapper .dataTables_info {
    padding: 0.75rem 1.25rem;
    background-color: #f9fafb;
    border-radius: 0.5rem;
    color: #718096;
    font-size: 0.875rem;
    margin-top: 1rem;
    display: inline-block;
}

/* 响应式处理 */
@media (max-width: 768px) {
    .dataTables_length, 
    .dataTables_filter {
        float: none;
        width: 100%;
        margin-bottom: 1rem;
        text-align: left;
    }
    
    .dataTables_filter label,
    .dataTables_length label {
        display: flex;
        align-items: center;
        width: 100%;
    }
    
    .dataTables_filter input {
        flex: 1;
        margin-left: 0.75rem;
    }
    
    .dataTables_length select {
        margin-left: 0.75rem;
    }
}

/* 自定义下拉按钮样式 */
.custom-select-wrapper {
    position: relative;
    display: inline-block;
}

.custom-select-wrapper select {
    cursor: pointer;
}

/* Card Body and Description Improvements */
.card-body {
    padding: 1.5rem;
}

.description-text {
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: all 0.3s ease;
}

.description-text:hover {
    overflow: visible;
    white-space: normal;
    z-index: 1;
    position: relative;
    background-color: white;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    border-radius: 0.25rem;
    padding: 0.5rem;
    max-width: 500px;
}

/* 内联删除确认样式 */
.confirm-delete-container {
    animation: fadeIn 0.2s ease-in-out;
    margin-top: 0.5rem;
    position: relative;
    z-index: 10;
}

.confirm-delete-container .alert {
    margin-bottom: 0;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.confirm-delete-container .alert-danger {
    border-left: 4px solid #e53e3e;
}

.confirm-delete-container .alert-warning {
    border-left: 4px solid #f59f00;
    background-color: rgba(253, 230, 138, 0.5);
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
    from { opacity: 1; transform: translateY(0); }
    to { opacity: 0; transform: translateY(-10px); }
} 
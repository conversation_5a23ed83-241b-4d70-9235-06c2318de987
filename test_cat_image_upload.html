<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>猫友圈图片上传测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .upload-section {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 10px;
            font-weight: bold;
            color: #555;
        }
        input[type="file"] {
            width: 100%;
            padding: 10px;
            border: 2px dashed #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-top: 15px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .preview {
            margin-top: 15px;
            text-align: center;
        }
        .preview img {
            max-width: 200px;
            max-height: 200px;
            border-radius: 10px;
            border: 2px solid #ddd;
        }
        .upload-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .upload-item {
            position: relative;
        }
        .upload-item img {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 8px;
            border: 2px solid #ddd;
        }
        .delete-btn {
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(255, 0, 0, 0.8);
            color: white;
            border: none;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>猫友圈图片上传测试</h1>
        
        <div class="upload-section">
            <label for="imageFiles">选择图片文件（可多选，最多10张）:</label>
            <input type="file" id="imageFiles" accept="image/*" multiple>
        </div>
        
        <button onclick="uploadImages()" id="uploadBtn">上传图片</button>
        
        <div id="result" class="result"></div>
        
        <div id="uploadedImages" class="upload-grid"></div>
    </div>

    <script>
        let uploadedImageUrls = [];
        
        function uploadImages() {
            const fileInput = document.getElementById('imageFiles');
            const uploadBtn = document.getElementById('uploadBtn');
            const resultDiv = document.getElementById('result');
            
            if (!fileInput.files.length) {
                showResult('请选择图片文件', 'error');
                return;
            }
            
            if (fileInput.files.length > 10) {
                showResult('最多只能选择10张图片', 'error');
                return;
            }
            
            uploadBtn.disabled = true;
            uploadBtn.textContent = '上传中...';
            
            const uploadPromises = Array.from(fileInput.files).map(file => uploadSingleImage(file));
            
            Promise.all(uploadPromises)
                .then(results => {
                    const successResults = results.filter(r => r.success);
                    const failedResults = results.filter(r => !r.success);
                    
                    if (successResults.length > 0) {
                        uploadedImageUrls.push(...successResults.map(r => r.url));
                        showResult(`成功上传 ${successResults.length} 张图片`, 'success');
                        displayUploadedImages();
                    }
                    
                    if (failedResults.length > 0) {
                        showResult(`${failedResults.length} 张图片上传失败`, 'error');
                    }
                })
                .catch(error => {
                    console.error('批量上传错误:', error);
                    showResult(`上传失败: ${error.message}`, 'error');
                })
                .finally(() => {
                    uploadBtn.disabled = false;
                    uploadBtn.textContent = '上传图片';
                });
        }
        
        function uploadSingleImage(file) {
            return new Promise((resolve) => {
                // 检查文件类型
                if (!file.type.startsWith('image/')) {
                    resolve({ success: false, error: '不是图片文件' });
                    return;
                }
                
                // 检查文件大小 (5MB)
                if (file.size > 5 * 1024 * 1024) {
                    resolve({ success: false, error: '文件大小超过5MB' });
                    return;
                }
                
                const formData = new FormData();
                formData.append('image', file);
                
                fetch('http://localhost:5000/api/cat_circle/upload/image', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    console.log('单张图片上传响应:', data);
                    
                    if (data.code === 200) {
                        resolve({ 
                            success: true, 
                            url: data.data.image_url,
                            filename: data.data.filename
                        });
                    } else {
                        resolve({ 
                            success: false, 
                            error: data.message || '上传失败'
                        });
                    }
                })
                .catch(error => {
                    console.error('单张图片上传错误:', error);
                    resolve({ 
                        success: false, 
                        error: error.message || '网络错误'
                    });
                });
            });
        }
        
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }
        
        function displayUploadedImages() {
            const container = document.getElementById('uploadedImages');
            container.innerHTML = '';
            
            uploadedImageUrls.forEach((url, index) => {
                const item = document.createElement('div');
                item.className = 'upload-item';
                item.innerHTML = `
                    <img src="${url}" alt="上传的图片 ${index + 1}" onerror="this.style.display='none'">
                    <button class="delete-btn" onclick="removeImage(${index})">×</button>
                `;
                container.appendChild(item);
            });
            
            if (uploadedImageUrls.length > 0) {
                const summary = document.createElement('div');
                summary.style.marginTop = '20px';
                summary.style.padding = '15px';
                summary.style.background = '#e9ecef';
                summary.style.borderRadius = '5px';
                summary.innerHTML = `
                    <h3>上传结果汇总:</h3>
                    <p>共上传 ${uploadedImageUrls.length} 张图片</p>
                    <p>图片URL列表:</p>
                    <ul>
                        ${uploadedImageUrls.map(url => `<li><a href="${url}" target="_blank">${url}</a></li>`).join('')}
                    </ul>
                `;
                container.appendChild(summary);
            }
        }
        
        function removeImage(index) {
            uploadedImageUrls.splice(index, 1);
            displayUploadedImages();
        }
        
        // 文件选择预览
        document.getElementById('imageFiles').addEventListener('change', function(e) {
            const files = e.target.files;
            if (files.length > 0) {
                showResult(`已选择 ${files.length} 张图片，点击上传按钮开始上传`, 'success');
            }
        });
    </script>
</body>
</html>

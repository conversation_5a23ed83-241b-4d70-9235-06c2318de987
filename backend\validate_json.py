import json

try:
    with open('questions.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    print("JSON file is valid!")
    
    # Output some basic stats
    single_choice = data.get('singleChoice', [])
    multiple_choice = data.get('multipleChoice', [])
    print(f"Single choice questions: {len(single_choice)}")
    print(f"Multiple choice questions: {len(multiple_choice)}")
except Exception as e:
    print(f"Error: {e}") 
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天布局测试 - 简化版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 375px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 20px;
        }

        .message-item {
            margin-bottom: 20px;
            width: 100%;
        }

        .message-content {
            display: flex;
            align-items: flex-end;
            gap: 12px;
            max-width: 85%;
        }

        /* 接收的消息 - 头像在左，气泡在右 */
        .received-message {
            flex-direction: row;
            justify-content: flex-start;
        }

        /* 发送的消息 - 气泡在左，头像在右 */
        .sent-message {
            flex-direction: row;
            justify-content: flex-end;
            margin-left: auto;
        }

        .message-avatar {
            width: 42px;
            height: 42px;
            border-radius: 21px;
            border: 2px solid #fff;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .message-bubble {
            padding: 14px 18px;
            border-radius: 20px;
            position: relative;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        /* 接收的消息气泡 */
        .received-message .message-bubble {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-bottom-left-radius: 8px;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        /* 发送的消息气泡 */
        .sent-message .message-bubble {
            background: linear-gradient(135deg, #4C84FF 0%, #6c5ce7 100%);
            border-bottom-right-radius: 8px;
            color: #fff;
        }

        .message-text {
            font-size: 16px;
            line-height: 1.5;
            color: #333;
            word-wrap: break-word;
            margin: 0;
        }

        .sent-message .message-text {
            color: #fff;
        }

        .message-time {
            font-size: 11px;
            color: #999;
            margin-top: 6px;
            display: block;
        }

        .sent-message .message-time {
            color: rgba(255, 255, 255, 0.8);
        }

        .test-label {
            background: #333;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            margin-bottom: 10px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>聊天布局测试</h2>
        <p>测试发送消息的头像是否在右侧</p>
        <br>

        <!-- 接收的消息 -->
        <div class="test-label">接收的消息（头像应该在左侧）</div>
        <div class="message-item">
            <div class="message-content received-message">
                <img class="message-avatar" src="https://via.placeholder.com/42x42/8B4513/FFFFFF?text=他" alt="对方头像">
                <div class="message-bubble">
                    <div class="message-text">你好！看到你发布的动态了</div>
                    <div class="message-time">5分钟前</div>
                </div>
            </div>
        </div>

        <!-- 发送的消息 -->
        <div class="test-label">发送的消息（头像应该在右侧）</div>
        <div class="message-item">
            <div class="message-content sent-message">
                <div class="message-bubble">
                    <div class="message-text">你好！有什么可以帮助你的吗？</div>
                    <div class="message-time">4分钟前</div>
                </div>
                <img class="message-avatar" src="https://via.placeholder.com/42x42/FFD700/000000?text=我" alt="我的头像">
            </div>
        </div>

        <!-- 再次测试发送的消息 -->
        <div class="test-label">再次测试发送的消息（头像应该在右侧）</div>
        <div class="message-item">
            <div class="message-content sent-message">
                <div class="message-bubble">
                    <div class="message-text">这是我发送的第二条消息，头像应该在右侧</div>
                    <div class="message-time">刚刚</div>
                </div>
                <img class="message-avatar" src="https://via.placeholder.com/42x42/FFD700/000000?text=我" alt="我的头像">
            </div>
        </div>

        <br>
        <div style="background: #f0f0f0; padding: 15px; border-radius: 8px;">
            <h3>布局说明：</h3>
            <p><strong>接收消息：</strong> flex-direction: row（头像在左，气泡在右）</p>
            <p><strong>发送消息：</strong> flex-direction: row + margin-left: auto（气泡在左，头像在右，整体右对齐）</p>
            <p><strong>HTML结构：</strong> 发送消息的HTML是 &lt;气泡&gt;&lt;头像&gt; 的顺序</p>
        </div>
    </div>
</body>
</html>

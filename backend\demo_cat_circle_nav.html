<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>猫友圈管理导航 - 样式演示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }
        
        .demo-container {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 280px;
            background: linear-gradient(180deg, #4c51bf 0%, #553c9a 100%);
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            padding: 20px 0;
        }
        
        .sidebar-header {
            text-align: center;
            padding: 20px;
            color: white;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }
        
        .sidebar-header h3 {
            margin: 0;
            font-size: 1.2rem;
            font-weight: 600;
        }
        
        .content {
            flex: 1;
            padding: 40px;
            color: white;
        }
        
        .nav-link {
            color: white !important;
            padding: 12px 15px;
            border-radius: 8px;
            margin: 2px 8px;
            text-decoration: none;
            display: block;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateX(3px);
        }
        
        .nav-link.active {
            background-color: rgba(255, 255, 255, 0.15);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .transition-transform {
            transition: transform 0.3s ease;
        }
        
        .nav-link[aria-expanded="true"] .transition-transform {
            transform: rotate(180deg);
        }
        
        .nav-link[aria-expanded="false"] .transition-transform {
            transform: rotate(0deg);
        }
        
        /* 折叠动画优化 */
        .collapse {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            margin-top: 5px;
        }
        
        .collapsing {
            transition: height 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        /* 主菜单项图标样式 */
        .nav-item > .nav-link > span > i {
            margin-right: 10px;
            width: 18px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .nav-item > .nav-link:hover > span > i {
            transform: scale(1.1);
        }
        
        /* 二级菜单样式优化 */
        .nav .nav {
            padding-left: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
            border-radius: 8px;
            margin: 5px 8px;
            padding: 8px 0;
            border-left: 3px solid rgba(255, 255, 255, 0.2);
        }
        
        .nav .nav .nav-item {
            margin: 1px 0;
        }
        
        .nav .nav .nav-link {
            padding: 8px 16px 8px 24px;
            font-size: 0.85rem;
            color: rgba(255, 255, 255, 0.8) !important;
            border-radius: 6px;
            margin: 1px 8px;
            transition: all 0.3s ease;
            position: relative;
            display: flex;
            align-items: center;
        }
        
        .nav .nav .nav-link:before {
            content: '';
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 4px;
            background-color: rgba(255, 255, 255, 0.4);
            border-radius: 50%;
            transition: all 0.3s ease;
        }
        
        .nav .nav .nav-link i {
            margin-right: 8px;
            font-size: 0.8rem;
            width: 16px;
            text-align: center;
            opacity: 0.8;
            transition: all 0.3s ease;
        }
        
        .nav .nav .nav-link:hover {
            color: white !important;
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }
        
        .nav .nav .nav-link:hover:before {
            background-color: white;
            transform: translateY(-50%) scale(1.5);
        }
        
        .nav .nav .nav-link:hover i {
            opacity: 1;
            transform: scale(1.1);
        }
        
        .nav .nav .nav-link.active {
            color: white !important;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
            font-weight: 500;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
            border-left: 3px solid white;
        }
        
        .nav .nav .nav-link.active:before {
            background-color: white;
            transform: translateY(-50%) scale(1.8);
        }
        
        .nav .nav .nav-link.active i {
            opacity: 1;
            color: white;
        }
        
        /* 待开发项目样式 */
        .nav .nav .nav-link.disabled {
            color: rgba(255, 255, 255, 0.4) !important;
            cursor: not-allowed;
        }
        
        .nav .nav .nav-link.disabled:hover {
            transform: none;
            background-color: transparent;
        }
        
        .nav .nav .nav-link.disabled small {
            font-size: 0.7rem;
            opacity: 0.6;
        }
        
        .demo-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 5px 0;
            display: flex;
            align-items: center;
        }
        
        .feature-list li i {
            margin-right: 10px;
            color: #4ade80;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- 侧边栏 -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h3>题库管理系统</h3>
            </div>
            
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="#">
                        <i class="fas fa-tachometer-alt"></i> 仪表盘
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#">
                        <i class="fas fa-users"></i> 用户管理
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#">
                        <i class="fas fa-chart-bar"></i> 统计分析
                    </a>
                </li>
                
                <!-- 猫友圈管理 -->
                <li class="nav-item">
                    <a class="nav-link active d-flex justify-content-between align-items-center"
                       data-bs-toggle="collapse"
                       href="#catCircleSubmenu"
                       role="button"
                       aria-expanded="true"
                       aria-controls="catCircleSubmenu">
                        <span><i class="fas fa-cat"></i> 猫友圈</span>
                        <i class="fas fa-chevron-down transition-transform"></i>
                    </a>
                    <div class="collapse show" id="catCircleSubmenu">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link active" href="#">
                                    <i class="fas fa-tachometer-alt"></i> 仪表盘
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#">
                                    <i class="fas fa-edit"></i> 动态管理
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#">
                                    <i class="fas fa-comments"></i> 评论管理
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#">
                                    <i class="fas fa-heart"></i> 点赞管理
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#">
                                    <i class="fas fa-star"></i> 收藏管理
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link disabled" href="#">
                                    <i class="fas fa-bullhorn"></i> 舆论管理 <small>(待开发)</small>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#">
                                    <i class="fas fa-filter"></i> 关键词管理
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link" href="#">
                        <i class="fas fa-history"></i> 登录记录
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#">
                        <i class="fas fa-sign-out-alt"></i> 退出登录
                    </a>
                </li>
            </ul>
        </nav>
        
        <!-- 主内容区 -->
        <div class="content">
            <div class="demo-info">
                <h2><i class="fas fa-cat"></i> 猫友圈管理导航 - 样式优化</h2>
                <p>这是优化后的猫友圈管理二级导航样式演示</p>
            </div>
            
            <div class="demo-info">
                <h4><i class="fas fa-star"></i> 主要特性</h4>
                <ul class="feature-list">
                    <li><i class="fas fa-check"></i> 箭头位置在右侧，点击时有旋转动画</li>
                    <li><i class="fas fa-check"></i> 二级菜单有渐变背景和左侧边框</li>
                    <li><i class="fas fa-check"></i> 菜单项有悬停动画效果（向右滑动）</li>
                    <li><i class="fas fa-check"></i> 活跃状态有特殊样式和阴影</li>
                    <li><i class="fas fa-check"></i> 图标有缩放动画效果</li>
                    <li><i class="fas fa-check"></i> 小圆点指示器动画</li>
                    <li><i class="fas fa-check"></i> 待开发项目有特殊的禁用样式</li>
                    <li><i class="fas fa-check"></i> 响应式设计，适配移动端</li>
                </ul>
            </div>
            
            <div class="demo-info">
                <h4><i class="fas fa-info-circle"></i> 使用说明</h4>
                <p>点击左侧的"猫友圈"菜单项可以展开/收起二级菜单，箭头会有旋转动画。</p>
                <p>鼠标悬停在菜单项上可以看到各种动画效果。</p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 猫友圈导航折叠功能
            const catCircleToggle = document.querySelector('[data-bs-toggle="collapse"][href="#catCircleSubmenu"]');
            const catCircleSubmenu = document.getElementById('catCircleSubmenu');
            
            if (catCircleToggle && catCircleSubmenu) {
                // 监听折叠状态变化
                catCircleSubmenu.addEventListener('show.bs.collapse', function() {
                    catCircleToggle.setAttribute('aria-expanded', 'true');
                });
                
                catCircleSubmenu.addEventListener('hide.bs.collapse', function() {
                    catCircleToggle.setAttribute('aria-expanded', 'false');
                });
            }
        });
    </script>
</body>
</html>

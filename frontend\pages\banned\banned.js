// pages/banned/banned.js
const app = getApp();
const userManager = require('../../utils/activity');

Page({
  data: {
    userInfo: null,
    lastLoginTime: '',
    supportEmail: '<EMAIL>',  // 更新为指定的支持邮箱
  },

  onLoad: function (options) {
    // 尝试从本地存储获取用户信息
    const localUserInfo = userManager.getLocalUserInfo();
    if (localUserInfo) {
      this.setData({
        userInfo: localUserInfo,
        lastLoginTime: new Date().toLocaleString()
      });
    }
  },

  // 联系客服
  contactSupport: function() {
    // 复制支持邮箱到剪贴板
    wx.setClipboardData({
      data: this.data.supportEmail,
      success: () => {
        wx.showToast({
          title: '邮箱已复制',
          icon: 'success'
        });
      }
    });
  },

  // 退出到登录页
  goToLogin: function() {
    // 清除所有登录状态
    app.globalData.token = '';
    app.globalData.userInfo = null;
    wx.removeStorageSync('token');
    wx.removeStorageSync('userInfo');
    
    wx.redirectTo({
      url: '/pages/login/login'
    });
  }
}); 
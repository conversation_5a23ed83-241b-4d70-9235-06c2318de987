{% extends "admin/base.html" %}

{% block title %}猫友圈可视化大屏{% endblock %}

{% block extra_css %}
<style>
    /* 全屏模式样式 */
    .fullscreen-mode {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: linear-gradient(135deg, #0c1426 0%, #1a2332 50%, #0f1419 100%);
        z-index: 9999;
        overflow: hidden;
    }

    .analytics-container {
        background: linear-gradient(135deg, #0c1426 0%, #1a2332 50%, #0f1419 100%);
        min-height: 100vh;
        color: #ffffff;
        font-family: 'Microsoft YaHei', sans-serif;
    }

    .analytics-header {
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(10px);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        padding: 20px 0;
        position: relative;
    }

    .analytics-title {
        font-size: 2.5rem;
        font-weight: 700;
        text-align: center;
        background: linear-gradient(45deg, #00d4ff, #5b73ff, #ff6b9d);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin: 0;
        text-shadow: 0 0 30px rgba(0, 212, 255, 0.3);
    }

    .fullscreen-btn {
        position: absolute;
        right: 30px;
        top: 50%;
        transform: translateY(-50%);
        background: linear-gradient(45deg, #00d4ff, #5b73ff);
        border: none;
        color: white;
        padding: 12px 20px;
        border-radius: 25px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
    }

    .fullscreen-btn:hover {
        transform: translateY(-50%) scale(1.05);
        box-shadow: 0 6px 20px rgba(0, 212, 255, 0.5);
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 25px;
        padding: 30px;
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 20px;
        padding: 25px;
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        border-color: rgba(255, 255, 255, 0.2);
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #00d4ff, #5b73ff, #ff6b9d);
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        margin-bottom: 15px;
        position: relative;
    }

    .stat-icon.posts {
        background: linear-gradient(45deg, #ff6b9d, #ff8a80);
        box-shadow: 0 8px 25px rgba(255, 107, 157, 0.3);
    }

    .stat-icon.users {
        background: linear-gradient(45deg, #00d4ff, #0099cc);
        box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3);
    }

    .stat-icon.comments {
        background: linear-gradient(45deg, #5b73ff, #3f51b5);
        box-shadow: 0 8px 25px rgba(91, 115, 255, 0.3);
    }

    .stat-icon.likes {
        background: linear-gradient(45deg, #ff9800, #ff5722);
        box-shadow: 0 8px 25px rgba(255, 152, 0, 0.3);
    }

    .stat-value {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 10px 0 5px 0;
        background: linear-gradient(45deg, #ffffff, #e0e0e0);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .stat-label {
        color: #b0b0b0;
        font-size: 14px;
        margin-bottom: 10px;
    }

    .stat-trend {
        display: flex;
        align-items: center;
        font-size: 12px;
        color: #4caf50;
    }

    .chart-container {
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 20px;
        padding: 25px;
        margin: 25px 30px;
        position: relative;
        overflow: hidden;
    }

    .chart-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #00d4ff, #5b73ff, #ff6b9d);
    }

    .chart-title {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 20px;
        color: #ffffff;
    }

    .chart-placeholder {
        height: 300px;
        background: rgba(255, 255, 255, 0.02);
        border-radius: 15px;
        position: relative;
        overflow: hidden;
    }

    .chart-placeholder canvas {
        width: 100% !important;
        height: 100% !important;
    }

    .charts-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-template-rows: 1fr 1fr;
        gap: 25px;
        margin: 25px 30px;
    }

    .chart-container.full-width {
        grid-column: 1 / -1;
    }

    /* 全屏模式样式 */
    .analytics-container.fullscreen-mode {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        z-index: 9999;
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        overflow-y: auto;
    }

    .analytics-container.fullscreen-mode .analytics-header {
        padding: 20px 40px;
    }

    .analytics-container.fullscreen-mode .stats-grid {
        margin: 20px 40px;
    }

    .analytics-container.fullscreen-mode .charts-grid {
        margin: 20px 40px;
    }

    .analytics-container.fullscreen-mode .activity-feed {
        margin: 20px 40px;
    }

    @keyframes shimmer {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    .activity-feed {
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 20px;
        padding: 25px;
        margin: 25px 30px;
        max-height: 400px;
        overflow-y: auto;
    }

    .activity-item {
        display: flex;
        align-items: center;
        padding: 15px 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    }

    .activity-item:last-child {
        border-bottom: none;
    }

    .activity-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(45deg, #00d4ff, #5b73ff);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        color: white;
        font-weight: bold;
    }

    .activity-content {
        flex: 1;
    }

    .activity-text {
        color: #ffffff;
        margin-bottom: 5px;
    }

    .activity-time {
        color: #888;
        font-size: 12px;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .analytics-title {
            font-size: 1.8rem;
        }
        
        .fullscreen-btn {
            position: static;
            transform: none;
            margin-top: 15px;
            display: block;
            margin-left: auto;
            margin-right: auto;
        }
        
        .stats-grid {
            grid-template-columns: 1fr;
            padding: 15px;
        }
        
        .chart-container,
        .activity-feed {
            margin: 15px;
        }
    }

    /* 滚动条样式 */
    .activity-feed::-webkit-scrollbar {
        width: 6px;
    }

    .activity-feed::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 3px;
    }

    .activity-feed::-webkit-scrollbar-thumb {
        background: linear-gradient(45deg, #00d4ff, #5b73ff);
        border-radius: 3px;
    }
</style>
{% endblock %}

{% block content %}
<div class="analytics-container" id="analyticsContainer">
    <!-- 头部 -->
    <div class="analytics-header">
        <h1 class="analytics-title">猫友圈数据可视化大屏</h1>
        <button class="fullscreen-btn" onclick="toggleFullscreen()">
            <i class="fas fa-expand"></i> 全屏显示
        </button>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon posts">
                <i class="fas fa-edit"></i>
            </div>
            <div class="stat-value" id="totalPosts">{{ stats.total_posts or 0 }}</div>
            <div class="stat-label">总动态数</div>
            <div class="stat-trend">
                <i class="fas fa-arrow-up"></i>
                <span>+{{ stats.posts_growth or 0 }}% 本周</span>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon users">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-value" id="activeUsers">{{ stats.active_users or 0 }}</div>
            <div class="stat-label">活跃用户</div>
            <div class="stat-trend">
                <i class="fas fa-arrow-up"></i>
                <span>+{{ stats.users_growth or 0 }}% 本周</span>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon comments">
                <i class="fas fa-comments"></i>
            </div>
            <div class="stat-value" id="totalComments">{{ stats.total_comments or 0 }}</div>
            <div class="stat-label">总评论数</div>
            <div class="stat-trend">
                <i class="fas fa-arrow-up"></i>
                <span>+{{ stats.comments_growth or 0 }}% 本周</span>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon likes">
                <i class="fas fa-heart"></i>
            </div>
            <div class="stat-value" id="totalLikes">{{ stats.total_likes or 0 }}</div>
            <div class="stat-label">总点赞数</div>
            <div class="stat-trend">
                <i class="fas fa-arrow-up"></i>
                <span>+{{ stats.likes_growth or 0 }}% 本周</span>
            </div>
        </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-grid">
        <div class="chart-container">
            <h3 class="chart-title">
                <i class="fas fa-chart-line" style="color: #00d4ff; margin-right: 10px;"></i>
                动态发布趋势
            </h3>
            <div class="chart-placeholder">
                <canvas id="postsChart"></canvas>
            </div>
        </div>

        <div class="chart-container">
            <h3 class="chart-title">
                <i class="fas fa-chart-pie" style="color: #ff6b9d; margin-right: 10px;"></i>
                动态类型分布
            </h3>
            <div class="chart-placeholder">
                <canvas id="categoryChart"></canvas>
            </div>
        </div>

        <div class="chart-container">
            <h3 class="chart-title">
                <i class="fas fa-users" style="color: #4caf50; margin-right: 10px;"></i>
                用户活跃度
            </h3>
            <div class="chart-placeholder">
                <canvas id="userActivityChart"></canvas>
            </div>
        </div>

        <div class="chart-container">
            <h3 class="chart-title">
                <i class="fas fa-chart-bar" style="color: #ffa726; margin-right: 10px;"></i>
                互动统计
            </h3>
            <div class="chart-placeholder">
                <canvas id="interactionChart"></canvas>
            </div>
        </div>
    </div>

    <!-- 用户活跃度图表 -->
    <div class="chart-container full-width" style="margin: 25px 30px;">
        <h3 class="chart-title">
            <i class="fas fa-users" style="color: #4caf50; margin-right: 10px;"></i>
            用户活跃度分析
        </h3>
        <div class="chart-placeholder">
            <canvas id="userActivityChart"></canvas>
        </div>
    </div>

    <!-- 实时活动 -->
    <div class="activity-feed">
        <h3 class="chart-title">
            <i class="fas fa-stream" style="color: #5b73ff; margin-right: 10px;"></i>
            实时活动
        </h3>
        <div id="activityList">
            {% for activity in recent_activities %}
            <div class="activity-item">
                <div class="activity-avatar">
                    {{ activity.user_name[0] if activity.user_name else 'U' }}
                </div>
                <div class="activity-content">
                    <div class="activity-text">
                        <strong>{{ activity.user_name or '匿名用户' }}</strong> 
                        {{ activity.action_text }}
                    </div>
                    <div class="activity-time">{{ activity.time_ago }}</div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
<script>
// 调试信息
console.log('Chart.js loaded:', typeof Chart !== 'undefined');
console.log('Chart data:', {
    dates: {{ chart_data.dates | tojson | safe }},
    posts_count: {{ chart_data.posts_count | tojson | safe }},
    categories: {{ chart_data.categories | tojson | safe }},
    category_counts: {{ chart_data.category_counts | tojson | safe }}
});
console.log('Stats data:', {{ stats | tojson | safe }});
// 全屏功能
function toggleFullscreen() {
    const container = document.getElementById('analyticsContainer');
    const btn = document.querySelector('.fullscreen-btn');

    if (!document.fullscreenElement && !document.webkitFullscreenElement && !document.mozFullScreenElement && !document.msFullscreenElement) {
        // 进入全屏
        if (container.requestFullscreen) {
            container.requestFullscreen();
        } else if (container.webkitRequestFullscreen) {
            container.webkitRequestFullscreen();
        } else if (container.mozRequestFullScreen) {
            container.mozRequestFullScreen();
        } else if (container.msRequestFullscreen) {
            container.msRequestFullscreen();
        }

        container.classList.add('fullscreen-mode');
        btn.innerHTML = '<i class="fas fa-compress"></i> 退出全屏';

        // 全屏后重新调整图表大小
        setTimeout(() => {
            if (postsChart) postsChart.resize();
            if (categoryChart) categoryChart.resize();
            if (userActivityChart) userActivityChart.resize();
            if (interactionChart) interactionChart.resize();
        }, 100);
    } else {
        // 退出全屏
        if (document.exitFullscreen) {
            document.exitFullscreen();
        } else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen();
        } else if (document.mozCancelFullScreen) {
            document.mozCancelFullScreen();
        } else if (document.msExitFullscreen) {
            document.msExitFullscreen();
        }

        container.classList.remove('fullscreen-mode');
        btn.innerHTML = '<i class="fas fa-expand"></i> 全屏显示';

        // 退出全屏后重新调整图表大小
        setTimeout(() => {
            if (postsChart) postsChart.resize();
            if (categoryChart) categoryChart.resize();
            if (userActivityChart) userActivityChart.resize();
            if (interactionChart) interactionChart.resize();
        }, 100);
    }
}

// 监听全屏状态变化
document.addEventListener('fullscreenchange', handleFullscreenChange);
document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
document.addEventListener('mozfullscreenchange', handleFullscreenChange);
document.addEventListener('MSFullscreenChange', handleFullscreenChange);

function handleFullscreenChange() {
    const container = document.getElementById('analyticsContainer');
    const btn = document.querySelector('.fullscreen-btn');

    if (!document.fullscreenElement && !document.webkitFullscreenElement && !document.mozFullScreenElement && !document.msFullscreenElement) {
        container.classList.remove('fullscreen-mode');
        btn.innerHTML = '<i class="fas fa-expand"></i> 全屏显示';

        // 退出全屏后重新调整图表大小
        setTimeout(() => {
            if (postsChart) postsChart.resize();
            if (categoryChart) categoryChart.resize();
            if (userActivityChart) userActivityChart.resize();
            if (interactionChart) interactionChart.resize();
        }, 100);
    }
}

// 全局图表变量
let postsChart, categoryChart, userActivityChart, interactionChart;

// 初始化图表
document.addEventListener('DOMContentLoaded', function() {
    initCharts();

    // 实时数据更新
    setInterval(updateStats, 30000); // 每30秒更新一次
});

function initCharts() {
    try {
        // 检查Chart.js是否加载
        if (typeof Chart === 'undefined') {
            console.error('Chart.js 未加载');
            return;
        }

        // 准备数据
        const chartDates = {{ chart_data.dates | tojson | safe }} || [];
        const chartPostsCount = {{ chart_data.posts_count | tojson | safe }} || [];
        const chartCategories = {{ chart_data.categories | tojson | safe }} || [];
        const chartCategoryCounts = {{ chart_data.category_counts | tojson | safe }} || [];

        console.log('初始化图表数据:', {
            dates: chartDates,
            posts_count: chartPostsCount,
            categories: chartCategories,
            category_counts: chartCategoryCounts
        });

        // 动态发布趋势图
        const postsCtx = document.getElementById('postsChart');
        if (postsCtx) {
            postsChart = new Chart(postsCtx.getContext('2d'), {
                type: 'line',
                data: {
                    labels: chartDates,
                    datasets: [{
                        label: '每日动态数',
                        data: chartPostsCount,
                        borderColor: '#00d4ff',
                        backgroundColor: 'rgba(0, 212, 255, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#00d4ff',
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2,
                        pointRadius: 5
                    }]
                },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        color: '#ffffff',
                        font: {
                            size: 14
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#00d4ff',
                    borderWidth: 1
                }
            },
            scales: {
                x: {
                    ticks: {
                        color: '#ffffff',
                        font: {
                            size: 12
                        }
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                },
                y: {
                    ticks: {
                        color: '#ffffff',
                        font: {
                            size: 12
                        }
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                }
            },
            animation: {
                duration: 2000,
                easing: 'easeInOutQuart'
            }
        }
            });
            console.log('动态发布趋势图初始化成功');
        } else {
            console.error('找不到 postsChart 元素');
        }

        // 动态类型分布图
        const categoryCtx = document.getElementById('categoryChart');
        if (categoryCtx && chartCategories.length > 0) {
            categoryChart = new Chart(categoryCtx.getContext('2d'), {
                type: 'doughnut',
                data: {
                    labels: chartCategories,
                    datasets: [{
                        data: chartCategoryCounts,
                        backgroundColor: [
                            '#ff6b9d',
                            '#00d4ff',
                            '#5b73ff',
                            '#ff9800',
                            '#4caf50',
                            '#9c27b0'
                        ],
                        borderWidth: 3,
                        borderColor: 'rgba(255, 255, 255, 0.1)',
                        hoverBorderWidth: 5,
                        hoverBorderColor: '#ffffff'
                    }]
                },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right',
                    labels: {
                        color: '#ffffff',
                        usePointStyle: true,
                        padding: 20,
                        font: {
                            size: 12
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#ff6b9d',
                    borderWidth: 1
                }
            },
            animation: {
                animateRotate: true,
                duration: 2000
            }
            });
            console.log('动态类型分布图初始化成功');
        } else {
            console.error('找不到 categoryChart 元素或没有分类数据');
        }

        // 用户活跃度图表
        const userActivityCtx = document.getElementById('userActivityChart');
        if (userActivityCtx && chartDates.length > 0) {
            userActivityChart = new Chart(userActivityCtx.getContext('2d'), {
                type: 'bar',
                data: {
                    labels: chartDates,
                    datasets: [{
                        label: '每日动态数',
                        data: chartPostsCount,
                        backgroundColor: 'rgba(76, 175, 80, 0.6)',
                        borderColor: '#4caf50',
                        borderWidth: 2,
                        borderRadius: 8,
                        borderSkipped: false,
                    }]
                },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        color: '#ffffff',
                        font: {
                            size: 14
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#4caf50',
                    borderWidth: 1
                }
            },
            scales: {
                x: {
                    ticks: {
                        color: '#ffffff',
                        font: {
                            size: 12
                        }
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                },
                y: {
                    ticks: {
                        color: '#ffffff',
                        font: {
                            size: 12
                        }
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                }
            },
            animation: {
                duration: 2000,
                easing: 'easeInOutQuart'
            }
        }
        });
        console.log('用户活跃度图表初始化成功');
    } else {
        console.error('找不到 userActivityChart 元素或没有日期数据');
    }

    // 互动统计图表
    const interactionCtx = document.getElementById('interactionChart');
    if (interactionCtx) {
        const statsData = {{ stats | tojson | safe }} || {};
        interactionChart = new Chart(interactionCtx.getContext('2d'), {
            type: 'doughnut',
            data: {
                labels: ['点赞', '评论', '收藏'],
                datasets: [{
                    data: [
                        statsData.total_likes || 0,
                        statsData.total_comments || 0,
                        statsData.total_collections || 0
                    ],
                    backgroundColor: [
                        'rgba(255, 107, 157, 0.8)',
                        'rgba(0, 212, 255, 0.8)',
                        'rgba(255, 167, 38, 0.8)'
                    ],
                    borderColor: [
                        '#ff6b9d',
                        '#00d4ff',
                        '#ffa726'
                    ],
                    borderWidth: 3,
                    hoverBackgroundColor: [
                        'rgba(255, 107, 157, 1)',
                        'rgba(0, 212, 255, 1)',
                        'rgba(255, 167, 38, 1)'
                    ],
                    hoverBorderWidth: 5,
                    hoverBorderColor: '#ffffff'
                }]
            },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        color: '#ffffff',
                        usePointStyle: true,
                        padding: 20,
                        font: {
                            size: 12
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#ffa726',
                    borderWidth: 1
                }
            },
            animation: {
                animateRotate: true,
                duration: 2000
            },
            cutout: '50%'
        }
        });
        console.log('互动统计图表初始化成功');
    } else {
        console.error('找不到 interactionChart 元素');
    }

    } catch (error) {
        console.error('图表初始化失败:', error);
    }
}

// 更新统计数据
function updateStats() {
    fetch('/admin/cat-circle/analytics/data')
        .then(response => response.json())
        .then(data => {
            console.log('获取到实时数据:', data);

            // 更新统计卡片
            document.getElementById('totalPosts').textContent = data.total_posts || 0;
            document.getElementById('activeUsers').textContent = data.active_users || 0;
            document.getElementById('totalComments').textContent = data.total_comments || 0;
            document.getElementById('totalLikes').textContent = data.total_likes || 0;

            // 更新图表数据
            if (data.chart_data) {
                updateChartData(data.chart_data);
            }

            // 更新互动统计图表
            updateInteractionChart(data);

            // 更新活动列表
            if (data.recent_activities) {
                updateActivityList(data.recent_activities);
            }

            // 添加数据更新动画效果
            animateStatCards();
        })
        .catch(error => console.error('更新数据失败:', error));
}

// 更新图表数据
function updateChartData(chartData) {
    if (postsChart && chartData.dates && chartData.posts_count) {
        postsChart.data.labels = chartData.dates;
        postsChart.data.datasets[0].data = chartData.posts_count;
        postsChart.update('none');
    }

    if (categoryChart && chartData.categories && chartData.category_counts) {
        categoryChart.data.labels = chartData.categories;
        categoryChart.data.datasets[0].data = chartData.category_counts;
        categoryChart.update('none');
    }

    if (userActivityChart && chartData.dates && chartData.posts_count) {
        userActivityChart.data.labels = chartData.dates;
        userActivityChart.data.datasets[0].data = chartData.posts_count;
        userActivityChart.update('none');
    }
}

// 更新互动统计数据
function updateInteractionChart(data) {
    if (interactionChart) {
        interactionChart.data.datasets[0].data = [
            data.total_likes || 0,
            data.total_comments || 0,
            data.total_collections || 0
        ];
        interactionChart.update('none');
    }
}

// 统计卡片动画效果
function animateStatCards() {
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach((card, index) => {
        setTimeout(() => {
            card.style.transform = 'scale(1.05)';
            setTimeout(() => {
                card.style.transform = 'scale(1)';
            }, 200);
        }, index * 100);
    });
}

// 更新活动列表
function updateActivityList(activities) {
    const activityList = document.getElementById('activityList');
    activityList.innerHTML = '';
    
    activities.forEach(activity => {
        const item = document.createElement('div');
        item.className = 'activity-item';
        item.innerHTML = `
            <div class="activity-avatar">
                ${activity.user_name ? activity.user_name[0] : 'U'}
            </div>
            <div class="activity-content">
                <div class="activity-text">
                    <strong>${activity.user_name || '匿名用户'}</strong> 
                    ${activity.action_text}
                </div>
                <div class="activity-time">${activity.time_ago}</div>
            </div>
        `;
        activityList.appendChild(item);
    });
}

// 数字动画效果
function animateValue(element, start, end, duration) {
    let startTimestamp = null;
    const step = (timestamp) => {
        if (!startTimestamp) startTimestamp = timestamp;
        const progress = Math.min((timestamp - startTimestamp) / duration, 1);
        element.textContent = Math.floor(progress * (end - start) + start);
        if (progress < 1) {
            window.requestAnimationFrame(step);
        }
    };
    window.requestAnimationFrame(step);
}

// 页面加载时启动数字动画
window.addEventListener('load', function() {
    const statValues = document.querySelectorAll('.stat-value');
    statValues.forEach(element => {
        const finalValue = parseInt(element.textContent);
        element.textContent = '0';
        animateValue(element, 0, finalValue, 2000);
    });
});
</script>
{% endblock %}

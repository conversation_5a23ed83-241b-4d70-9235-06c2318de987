// pages/result/result.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    totalQuestions: 0,
    correctCount: 0,
    wrongCount: 0,
    accuracy: 0,
    timeTaken: '0分钟',
    questionType: '', // 'single' 或 'multiple'
    records: [],
    originalQuestions: [], // 保存原始题目数据用于再次练习
    courseId: null,
    courseName: '',
    courseColor: '#4e8df7',
    mode: 'normal'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const eventChannel = this.getOpenerEventChannel();
    eventChannel.on('acceptResultData', (data) => {
      console.log('接收到的答题结果数据:', data);
      
      // 处理数据
      const totalQuestions = data.totalQuestions || 0;
      const correctCount = data.correctCount || 0;
      const wrongCount = totalQuestions - correctCount;
      const accuracy = totalQuestions > 0 ? Math.round((correctCount / totalQuestions) * 100) : 0;
      
      this.setData({
        totalQuestions: totalQuestions,
        correctCount: correctCount,
        wrongCount: wrongCount,
        accuracy: accuracy,
        timeTaken: data.timeTaken || '0分钟',
        questionType: data.questionType || '',
        records: data.records || [],
        originalQuestions: data.questions || [], // 保存原始题目数据
        courseId: data.courseId,
        courseName: data.courseName || '',
        courseColor: data.courseColor || '#4e8df7',
        mode: data.mode || 'normal'
      });
      
      // 如果题目来自错题模式，保存标记
      if (data.mode === 'wrong') {
        wx.setStorageSync('fromWrongQuestions', true);
      }
      
      // 如果没有存储currentCourse，但传入了课程信息，则保存
      if (data.courseId && !wx.getStorageSync('currentCourse')) {
        wx.setStorageSync('currentCourse', {
          id: data.courseId,
          name: data.courseName || '',
          color: data.courseColor || '#4e8df7'
        });
      }
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 返回首页
  backToHome: function() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },
  
  // 再次练习
  practiceAgain: function() {
    // 获取题目类型和课程信息
    const questionType = this.data.questionType;
    const courseName = this.data.courseName;
    const courseId = this.data.courseId;
    const mode = this.data.mode;
    
    console.log('再次练习，当前模式:', mode, '题目类型:', questionType, '原始题目数量:', 
                this.data.originalQuestions ? this.data.originalQuestions.length : 0);
    
    // 检查是否有原始题目数据可用
    if (this.data.originalQuestions && this.data.originalQuestions.length > 0) {
      console.log('使用保存的题目数据进行再次练习，题目数量:', this.data.originalQuestions.length);
      
      // 清除可能存在的干扰缓存
      wx.removeStorageSync('currentQuestions');
      
      // 确保题目包含正确的类型信息
      const enhancedQuestions = this.data.originalQuestions.map(q => {
        // 为每个题目显式设置类型
        return {
          ...q,
          type: questionType,
          question_type: questionType
        };
      });
      
      console.log('增强后的题目数据:', enhancedQuestions.length, '道题');
      
      // 保存题目数据到缓存，供题目页面使用
      wx.setStorageSync('currentQuestions', enhancedQuestions);
      
      // 根据题目类型确定正确的页面路径
      let pageType = questionType;
      if (questionType === 'fill_blank') {
        pageType = 'fillblank';
      }
      
      // 构建URL参数 - 使用retry模式来指示使用缓存数据
      let url = `/pages/quiz/${pageType}/${pageType}?mode=retry`;
      
      // 保持原始模式信息
      if (mode === 'wrong') {
        url += '&fromWrong=true';
      }
      
      // 如果有课程信息，添加到URL
      if (courseId) {
        url += `&courseId=${courseId}&courseTitle=${encodeURIComponent(courseName)}`;
      }
      
      console.log('再次练习，跳转URL:', url);
      
      // 重定向到相应页面
      wx.redirectTo({
        url: url
      });
      return;
    }
    
    // 如果没有原始题目数据，使用常规方式再次练习
    console.log('没有保存的题目数据，使用常规方式再次练习');
    
    // 根据题目类型确定正确的页面路径
    let pageType = questionType;
    if (questionType === 'fill_blank') {
      pageType = 'fillblank';
    }
    
    // 构建URL参数
    let url = `/pages/quiz/${pageType}/${pageType}`;
    
    // 如果是从错题练习过来的，保持wrong模式
    if (mode === 'wrong') {
      url += `?mode=wrong`;
      if (courseId) {
        url += `&courseId=${courseId}&courseTitle=${encodeURIComponent(courseName)}`;
      }
    }
    // 如果存在课程ID，则使用course模式
    else if (courseId) {
      url += `?mode=course&courseId=${courseId}&courseTitle=${encodeURIComponent(courseName)}`;
    } 
    // 如果没有课程ID，则只添加模式参数
    else {
      url += `?mode=normal`;
    }
    
    console.log('再次练习，跳转URL:', url);
    
    // 重定向到相应页面
    wx.redirectTo({
      url: url
    });
  }
})
#!/bin/bash

# 设置部署目录
DEPLOY_DIR="/www/wwwroot/www.qiangs.xyz"

# 创建部署目录(如果不存在)
if [ ! -d "$DEPLOY_DIR" ]; then
    mkdir -p "$DEPLOY_DIR"
    echo "创建部署目录 $DEPLOY_DIR"
fi

# 复制所有文件到部署目录
echo "复制文件到部署目录..."
cp -r * "$DEPLOY_DIR/"

# 创建静态文件目录(如果不存在)
if [ ! -d "$DEPLOY_DIR/static" ]; then
    mkdir -p "$DEPLOY_DIR/static"
    echo "创建静态文件目录 $DEPLOY_DIR/static"
fi

# 进入部署目录
cd "$DEPLOY_DIR"

# 安装Python依赖
echo "安装Python依赖..."
pip3 install -r requirements.txt

# 确保questions.json文件存在
if [ ! -f "$DEPLOY_DIR/questions.json" ]; then
    echo "questions.json文件不存在，尝试在上级目录查找..."
    if [ -f "../questions.json" ]; then
        cp "../questions.json" "$DEPLOY_DIR/"
        echo "已复制questions.json文件"
    else
        echo "警告: questions.json文件未找到！"
    fi
fi

# 重启uWSGI
echo "重启uWSGI..."
if [ -f "$DEPLOY_DIR/uwsgi.pid" ]; then
    uwsgi --stop "$DEPLOY_DIR/uwsgi.pid"
    echo "已停止旧的uWSGI进程"
fi

uwsgi --ini uwsgi.ini
echo "uWSGI已重启"

# 设置权限
echo "设置文件权限..."
chown -R www:www "$DEPLOY_DIR"
chmod -R 755 "$DEPLOY_DIR"

echo "部署完成！" 
<!-- 错题页面 -->
<view class="wrong-container">
  <view class="header">
    <view class="title">错题解析</view>
    <view class="subtitle">共{{wrongQuestions.length}}道错题</view>
  </view>

  <!-- 添加轻提示 -->
  <view class="tip-container" wx:if="{{showTip}}">
    <view class="tip-content">
      <view class="tip-icon">
        <text class="iconfont icon-tip">i</text>
      </view>
      <view class="tip-text">
        <text>绿色背景表示选择正确，红色背景表示选择错误，未选择的选项是无色的。</text>
      </view>
      <view class="tip-close" bindtap="hideTip">×</view>
    </view>
  </view>

  <!-- 主要内容区域，添加滚动条 -->
  <view class="scroll-container">
    <scroll-view scroll-y="true" class="wrong-list" scroll-with-animation="true" enhanced="true" show-scrollbar="true" fast-deceleration="true" bindscroll="onScroll">
      <block wx:if="{{wrongQuestions.length > 0}}">
        <view class="question-card" wx:for="{{wrongQuestions}}" wx:key="index" id="question-{{index+1}}">
          <view class="question-header">
            <text class="question-index">第{{item.index}}题</text>
            <text class="question-type">
              <block wx:if="{{item.question.type === 'single'}}">单选题</block>
              <block wx:elif="{{item.question.type === 'multiple'}}">多选题</block>
              <block wx:elif="{{item.question.type === 'judgment'}}">判断题</block>
              <block wx:else>填空题</block>
            </text>
          </view>
          
          <view class="question-content">{{item.question.content}}</view>
          
          <!-- 选项区域 - 只高亮用户选择的选项 -->
          <view class="options-container" wx:if="{{item.question.type === 'single' || item.question.type === 'multiple'}}">
            <view wx:for="{{item.question.options}}" 
                  wx:for-item="option" 
                  wx:for-index="optionIndex" 
                  wx:key="optionIndex"
                  class="option-item {{item.optionStyles[optionIndex]}}">
              <view class="option-circle {{item.optionCircleStyles[optionIndex]}}">
                {{['A', 'B', 'C', 'D', 'E', 'F'][optionIndex]}}
              </view>
              <text class="option-text">{{option}}</text>
            </view>
          </view>
          
          <!-- 判断题 - 只高亮用户选择的选项 -->
          <view class="options-container" wx:if="{{item.question.type === 'judgment'}}">
            <view class="option-item {{item.judgmentStyles[0]}}">
              <view class="option-circle {{item.judgmentCircleStyles[0]}}">对</view>
              <text class="option-text">正确</text>
            </view>
            <view class="option-item {{item.judgmentStyles[1]}}">
              <view class="option-circle {{item.judgmentCircleStyles[1]}}">错</view>
              <text class="option-text">错误</text>
            </view>
          </view>
          
          <!-- 填空题 - 显示用户的答案 -->
          <view class="options-container" wx:if="{{item.question.type === 'fill' || item.question.type === 'fillblank'}}">
            <view class="option-item {{item.fillblankStyle}}">
              <view class="answer-header">您的答案：</view>
              <view class="user-answer">{{item.userAnswer && item.userAnswer.length > 0 ? item.userAnswer[0] : '未作答'}}</view>
            </view>
          </view>
          
          <!-- 正确答案区域 -->
          <view class="answer-section">
            <view class="answer-header">答案解析</view>
            
            <!-- 选择题答案 -->
            <view wx:if="{{item.question.type === 'single'}}" class="correct-answer">
              <text class="answer-label">正确答案：</text>
              <text class="answer-value">{{item.question.answerLetter || ['A', 'B', 'C', 'D', 'E', 'F'][item.question.answer]}}</text>
            </view>
            
            <!-- 多选题答案 -->
            <view wx:if="{{item.question.type === 'multiple'}}" class="correct-answer">
              <text class="answer-label">正确答案：</text>
              <text class="answer-value">{{item.question.answerLetters || '未知'}}</text>
            </view>
            
            <!-- 判断题答案 -->
            <view wx:if="{{item.question.type === 'judgment'}}" class="correct-answer">
              <text class="answer-label">正确答案：</text>
              <text class="answer-value">{{item.question.formattedAnswer || (item.question.answer === 0 || item.question.answer === '0' || item.question.answer === '对' || item.question.answer === true ? '对' : '错')}}</text>
            </view>
            
            <!-- 填空题答案 -->
            <view wx:if="{{item.question.type === 'fill' || item.question.type === 'fillblank'}}" class="correct-answer">
              <text class="answer-label">正确答案：</text>
              <text class="answer-value">{{item.question.formattedAnswer || item.question.answer}}</text>
            </view>

            <!-- 答案解析内容 -->
            <view class="analysis-content">
              <text wx:if="{{item.question.analysis}}">{{item.question.analysis}}</text>
              <text wx:else class="no-analysis">暂无解析</text>
            </view>
          </view>
        </view>
      </block>
      
      <view class="empty-state" wx:else>
        <image class="empty-icon" src="/assets/images/empty.png"></image>
        <text class="empty-text">没有错题记录</text>
      </view>
    </scroll-view>

    <!-- 右侧导航滚动条 -->
    <view class="scroll-navigator">
      <view class="scroll-indicator-container">
        <view class="scroll-indicator {{currentQuestionIndex === index ? 'active' : ''}}" 
              wx:for="{{wrongQuestions}}" 
              wx:key="index" 
              bindtap="scrollToQuestion" 
              data-index="{{index}}"
              hover-class="scroll-indicator-hover">
          {{index + 1}}
        </view>
      </view>
    </view>
  </view>
  
  <view class="bottom-btns">
    <button class="back-btn" bindtap="navigateBack">返回</button>
  </view>
</view> 
{% extends "admin/base.html" %}

{% block title %}用户管理 - 大数据题库管理后台{% endblock %}

{% block header %}用户管理{% endblock %}

{% block header_buttons %}
<div class="d-flex align-items-center">
    <button type="button" class="btn btn-primary me-3 d-flex align-items-center shadow-sm" data-bs-toggle="modal" data-bs-target="#exportModal">
        <i class="fas fa-file-export me-2"></i> 导出数据
    </button>
    <form class="d-flex" role="search">
        <div class="input-group shadow-sm">
            <span class="input-group-text bg-white border-end-0">
                <i class="fas fa-search text-muted"></i>
            </span>
            <input class="form-control border-start-0" type="search" id="searchInput" placeholder="搜索用户..." aria-label="Search">
        </div>
    </form>
</div>
{% endblock %}

{% macro user_status_indicator(user_id, is_online, show_text=false, extra_class='') %}
<div class="d-flex align-items-center {{ extra_class }}">
    <div class="status-indicator me-1">
        <span class="user-status-indicator {{ 'user-online' if is_online else 'user-offline' }}" 
              data-user-id="{{ user_id }}"></span>
    </div>
    {% if show_text %}
    <span class="user-status-text-{{ user_id }} {{ 'text-success' if is_online else 'text-secondary' }} small">
        {{ '在线' if is_online else '离线' }}
    </span>
    {% endif %}
</div>
{% endmacro %}

{% macro user_avatar_with_status(user, size=50) %}
<div class="user-avatar-with-status">
    <div class="user-avatar" style="width:{{ size }}px;height:{{ size }}px;">
        {% if user.avatar %}
        <img src="{{ user.avatar }}" alt="头像" class="rounded-circle" style="width:100%;height:100%;object-fit:cover;">
        {% else %}
        <div class="d-flex align-items-center justify-content-center h-100 w-100 bg-light rounded-circle">
            <i class="fas fa-user text-secondary"></i>
        </div>
        {% endif %}
    </div>
    <span class="position-absolute bottom-0 end-0 user-status-indicator {{ 'user-online' if user.is_online else 'user-offline' }}" 
          data-user-id="{{ user.id }}" 
          style="width:{{ size//4 }}px;height:{{ size//4 }}px;border:2px solid white;"></span>
</div>
{% endmacro %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/users-modern.css') }}">
<style>
    /* 动画效果 */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .animate-fade-in {
        animation: fadeIn 0.5s ease-out forwards;
    }
    
    .col:nth-child(1) { animation-delay: 0.05s; }
    .col:nth-child(2) { animation-delay: 0.1s; }
    .col:nth-child(3) { animation-delay: 0.15s; }
    .col:nth-child(4) { animation-delay: 0.2s; }
    .col:nth-child(5) { animation-delay: 0.25s; }
    .col:nth-child(6) { animation-delay: 0.3s; }
    .col:nth-child(7) { animation-delay: 0.35s; }
    .col:nth-child(8) { animation-delay: 0.4s; }
    .col:nth-child(9) { animation-delay: 0.45s; }
    
    /* 视图切换按钮样式 */
    .view-mode-toggle .card {
        border: none;
        background-color: #f8f9fa;
    }
    
    .view-mode-toggle .btn-group {
        box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    }
    
    .view-mode-toggle .btn-outline-primary {
        border-color: #dee2e6;
        color: #6c757d;
    }
    
    .view-mode-toggle .btn-outline-primary.active {
        background-color: #6366f1;
        border-color: #6366f1;
        color: white;
    }
    
    .view-mode-toggle .btn-outline-primary:hover:not(.active) {
        background-color: #f8f9fa;
        color: #6366f1;
        border-color: #6366f1;
    }
    
    /* 状态按钮样式 */
    .btn.btn-primary.rounded-pill {
        background-color: #6366f1;
        border-color: #6366f1;
        font-weight: 500;
    }
    
    .btn.btn-success.rounded-pill {
        background-color: #10b981;
        border-color: #10b981;
        font-weight: 500;
    }
    
    /* 状态指示器 */
    .badge.bg-primary-subtle {
        background-color: rgba(13, 110, 253, 0.1);
        border: 1px solid rgba(13, 110, 253, 0.2);
    }
    
    .badge.bg-success-subtle {
        background-color: rgba(25, 135, 84, 0.1);
        border: 1px solid rgba(25, 135, 84, 0.2);
    }
    
    .badge.small {
        font-size: 0.85rem;
        font-weight: 500;
    }
    
    .badge i.small {
        font-size: 0.8rem;
    }
    
    /* 卡片样式 */
    .user-card {
        transition: all 0.2s ease;
        border: 1px solid #dee2e6;
        box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    }
    
    .user-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.07);
        border-color: #c2c7d0;
    }
    
    .online-badge {
        position: absolute;
        top: -6px;
        right: -6px;
        z-index: 5;
    }
    
    .online-badge .badge {
        border-radius: 50px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .user-avatar {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
    }
    
    .user-status-indicator {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 5px;
    }
    
    .user-online {
        background-color: #10b981;
    }
    
    .user-offline {
        background-color: #9ca3af;
    }
    
    /* 列表视图样式 */
    .user-list-table th {
        font-weight: 600;
        padding: 0.75rem 1rem;
    }
    
    .user-list-table td {
        padding: 0.75rem 1rem;
        vertical-align: middle;
    }
    
    /* 过渡动画 */
    #gridView, #listView {
        transition: opacity 0.3s ease;
    }
    
    /* 网格布局改进 */
    .user-grid {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        grid-gap: 15px;
        margin-top: 20px;
    }
    
    /* 响应式布局 */
    @media (max-width: 1400px) {
        .user-grid {
            grid-template-columns: repeat(5, 1fr);
        }
    }
    
    @media (max-width: 1200px) {
        .user-grid {
            grid-template-columns: repeat(4, 1fr);
        }
    }
    
    @media (max-width: 992px) {
        .user-grid {
            grid-template-columns: repeat(3, 1fr);
        }
    }
    
    @media (max-width: 768px) {
        .user-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }
    
    @media (max-width: 576px) {
        .user-grid {
            grid-template-columns: 1fr;
        }
    }
    
    .user-grid-card {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        overflow: hidden;
        transition: transform 0.2s, box-shadow 0.2s;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 15px;
        text-align: center;
        position: relative;
    }
    
    .user-grid-avatar {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f0f5ff;
        color: #409eff;
        font-size: 26px;
        overflow: hidden;
    }
    
    .user-grid-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .user-grid-info {
        flex: 1;
        width: 100%;
    }
    
    .user-grid-info h4 {
        margin: 4px 0;
        font-size: 14px;
        color: #333;
    }
    
    .user-grid-info p {
        font-size: 12px;
        margin: 4px 0;
    }
    
    .user-info {
        font-size: 12px !important;
    }
    
    .user-grid-actions {
        margin-top: 12px;
        display: flex;
        gap: 6px;
    }
    
    .user-grid-actions .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
    
    /* 确保视图切换正常工作 */
    #gridView {
        display: block;
    }
    
    #listView {
        display: none;
    }
    
    /* 导出模态框样式 */
    .border-hover {
        transition: all 0.2s ease;
        cursor: pointer;
    }
    
    .border-hover:hover {
        border-color: #0d6efd;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .format-option {
        text-align: center;
        position: relative;
    }
    
    .format-option .form-check-input {
        position: absolute;
        top: 10px;
        right: 10px;
    }
    
    .step-indicator {
        display: flex;
        align-items: center;
        padding: 12px 8px;
        margin-bottom: 10px;
        border-radius: 4px;
        transition: all 0.2s ease;
    }
    
    .step-indicator.active {
        background-color: #e9ecef;
    }
    
    .step-number {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background-color: #dee2e6;
        margin-right: 10px;
        font-weight: bold;
    }
    
    .step-indicator.active .step-number {
        background-color: #0d6efd;
        color: white;
    }
    
    .export-step {
        display: none;
    }
    
    /* 显示第一个步骤 */
    #step1 {
        display: block;
    }

    /* 导出模态框样式优化 */
    .export-sidebar {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        min-height: 500px;
        border-right: 1px solid rgba(0,0,0,0.05);
    }

    .export-steps {
        padding: 2rem 1rem;
    }

    .step-indicator {
        display: flex;
        align-items: center;
        padding: 1rem;
        margin-bottom: 1rem;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .step-indicator:hover {
        background-color: rgba(255,255,255,0.7);
    }

    .step-indicator.active {
        background-color: white;
        box-shadow: 0 4px 10px rgba(0,0,0,0.05);
    }

    .step-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        font-size: 1.25rem;
        color: #6c757d;
        transition: all 0.3s ease;
    }

    .step-indicator.active .step-icon {
        background: linear-gradient(135deg, #0d6efd 0%, #0a58ca 100%);
        color: white;
    }

    .step-info {
        display: flex;
        flex-direction: column;
    }

    .step-number {
        font-weight: bold;
        color: #212529;
        font-size: 0.75rem;
        margin-bottom: 0.25rem;
    }

    .step-text {
        color: #6c757d;
        font-weight: 500;
    }

    .step-indicator.active .step-text {
        color: #212529;
    }

    .export-format-card {
        height: 100%;
        position: relative;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .export-format-label {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border: 2px solid #e9ecef;
        border-radius: 1rem;
        padding: 2rem 1rem;
        height: 100%;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .export-format-card:hover .export-format-label {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.05);
        border-color: rgba(13, 110, 253, 0.25);
    }

    .export-format-card input:checked + .export-format-label {
        border-color: #0d6efd;
        background-color: rgba(13, 110, 253, 0.05);
    }

    .format-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .export-format-card[data-format="excel"] .format-icon {
        color: #1D6F42;
    }

    .export-format-card[data-format="csv"] .format-icon {
        color: #4285F4;
    }

    .export-format-card[data-format="json"] .format-icon {
        color: #F7DF1E;
    }

    .format-info {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .format-name {
        font-weight: bold;
        font-size: 1.1rem;
    }

    .format-ext {
        color: #6c757d;
        font-size: 0.85rem;
    }

    .export-content-card {
        position: relative;
        background-color: #fff;
        border: 1px solid #e9ecef;
        border-radius: 0.75rem;
        padding: 1rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .export-content-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        border-color: rgba(13, 110, 253, 0.25);
    }

    .export-content-card .form-check-input {
        position: absolute;
        top: 1rem;
        right: 1rem;
    }

    .export-content-card input:checked ~ label {
        color: #0d6efd;
    }

    .content-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        background-color: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        font-size: 1.25rem;
        color: #6c757d;
        flex-shrink: 0;
    }

    .export-content-card:nth-child(1) .content-icon {
        color: #0d6efd;
        background-color: rgba(13, 110, 253, 0.1);
    }

    .export-content-card:nth-child(2) .content-icon {
        color: #198754;
        background-color: rgba(25, 135, 84, 0.1);
    }

    .export-content-card:nth-child(3) .content-icon {
        color: #fd7e14;
        background-color: rgba(253, 126, 20, 0.1);
    }

    .export-content-card:nth-child(4) .content-icon {
        color: #6f42c1;
        background-color: rgba(111, 66, 193, 0.1);
    }

    .content-info {
        display: flex;
        flex-direction: column;
    }

    .content-name {
        font-weight: 600;
        margin-bottom: 0.25rem;
    }

    .content-desc {
        font-size: 0.85rem;
        color: #6c757d;
    }

    .filter-section {
        border-radius: 0.75rem;
        transition: all 0.3s ease;
    }

    .filter-section:hover {
        box-shadow: 0 10px 20px rgba(0,0,0,0.05) !important;
    }

    /* 隐藏和显示的辅助类 */
    .d-none {
        display: none;
    }

    /* 全新现代导出模态框样式 */
    .bg-gradient-primary {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    }

    .export-wrapper {
        display: flex;
        flex-direction: column;
        height: 480px;
        background: #f8f9fc;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    }

    /* 进度指示器 */
    .export-progress {
        padding: 1.5rem;
        background: #fff;
        position: relative;
    }

    .progress-track {
        position: absolute;
        top: 50%;
        left: 50px;
        right: 50px;
        height: 4px;
        background: #e9ecef;
        transform: translateY(-50%);
        z-index: 1;
    }

    .progress-fill {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        background: linear-gradient(90deg, #4e73df 0%, #36b9cc 100%);
        width: 0%;
        transition: width 0.5s cubic-bezier(0.65, 0, 0.35, 1);
    }

    .progress-steps {
        display: flex;
        justify-content: space-between;
        position: relative;
        z-index: 2;
    }

    .progress-step {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        width: 33.333%;
    }

    .step-icon-wrapper {
        width: 60px;
        height: 60px;
        background: #fff;
        border-radius: 50%;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        z-index: 2;
        transition: all 0.3s ease;
        box-shadow: 0 3px 6px rgba(0,0,0,0.05);
    }

    .step-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        color: #6c757d;
        transition: all 0.3s ease;
    }

    .progress-step.active .step-icon-wrapper {
        transform: scale(1.1);
    }

    .progress-step.active .step-icon {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        color: white;
    }

    .progress-step.completed .step-icon {
        background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
        color: white;
    }

    .step-label {
        font-weight: 600;
        color: #6c757d;
        transition: all 0.3s ease;
        font-size: 0.9rem;
        letter-spacing: 0.2px;
    }

    .progress-step.active .step-label {
        color: #4e73df;
    }

    .progress-step.completed .step-label {
        color: #1cc88a;
    }

    /* 内容区域 */
    .export-content {
        flex: 1;
        overflow-y: auto;
        padding: 0;
        position: relative;
        display: flex;
    }

    .export-step {
        display: none;
        padding: 1.5rem 2rem;
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.5s ease;
        width: 100%;
    }

    .export-step.active {
        display: flex;
        flex-direction: column;
        opacity: 1;
        transform: translateY(0);
    }

    .step-header {
        margin-bottom: 2rem;
        text-align: center;
    }

    .step-header h5 {
        font-size: 1.5rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 0.5rem;
        letter-spacing: -0.5px;
    }

    .step-header p {
        font-size: 1rem;
        color: #6c757d;
        max-width: 500px;
        margin: 0 auto;
        line-height: 1.5;
    }

    /* 导出格式选择卡片 */
    .format-cards {
        display: flex;
        gap: 1.5rem;
        padding: 0.5rem;
        margin: 0 -0.5rem;
        flex: 1;
    }

    .format-card {
        flex: 1;
        min-width: 250px;
        position: relative;
        cursor: pointer;
    }

    .format-input {
        position: absolute;
        opacity: 0;
        width: 0;
        height: 0;
    }

    .format-card-content {
        background: #fff;
        border-radius: 1rem;
        padding: 1.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border: 2px solid transparent;
        display: flex;
        align-items: flex-start;
        height: 100%;
        overflow: hidden;
        position: relative;
    }

    .format-input:checked + .format-card-content {
        border-color: #4e73df;
        box-shadow: 0 0.5rem 1rem rgba(78, 115, 223, 0.15);
        transform: translateY(-5px);
    }

    .format-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        margin-right: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.75rem;
        flex-shrink: 0;
        transition: all 0.3s ease;
    }

    .format-icon.excel {
        background-color: rgba(29, 111, 66, 0.1);
        color: #1D6F42;
    }

    .format-icon.csv {
        background-color: rgba(66, 133, 244, 0.1);
        color: #4285F4;
    }

    .format-icon.json {
        background-color: rgba(247, 223, 30, 0.1);
        color: #f0c412;
    }

    .format-details {
        flex: 1;
    }

    .format-name {
        font-weight: 700;
        color: #333;
        margin-bottom: 0.5rem;
        font-size: 1.15rem;
        letter-spacing: -0.3px;
    }

    .format-desc {
        color: #6c757d;
        font-size: 0.85rem;
        margin-bottom: 0.75rem;
        line-height: 1.5;
    }

    .format-ext {
        font-size: 0.75rem;
        color: #adb5bd;
        padding: 0.25rem 0.75rem;
        background: #f8f9fa;
        border-radius: 1rem;
        display: inline-block;
        font-weight: 600;
        letter-spacing: 0.5px;
    }

    .selected-indicator {
        position: absolute;
        top: 1rem;
        right: 1rem;
        opacity: 0;
        transform: scale(0.5);
        transition: all 0.3s ease;
    }

    .selected-mark {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background: #4e73df;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.75rem;
    }

    .format-input:checked + .format-card-content .selected-indicator {
        opacity: 1;
        transform: scale(1);
    }

    /* 导出内容选择 */
    .content-options {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.25rem;
    }

    .content-option {
        position: relative;
    }

    .content-checkbox {
        position: absolute;
        opacity: 0;
        width: 0;
        height: 0;
    }

    .content-label {
        display: flex;
        align-items: flex-start;
        padding: 1.25rem;
        background: #fff;
        border-radius: 1rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        transition: all 0.3s ease;
        cursor: pointer;
        border: 2px solid transparent;
        margin: 0;
    }

    .content-checkbox:checked + .content-label {
        border-color: #4e73df;
        box-shadow: 0 0.5rem 1rem rgba(78, 115, 223, 0.15);
        transform: translateY(-3px);
    }

    .content-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        flex-shrink: 0;
        font-size: 1.5rem;
        transition: all 0.3s ease;
    }

    .content-icon.user {
        background-color: rgba(78, 115, 223, 0.1);
        color: #4e73df;
    }

    .content-icon.login {
        background-color: rgba(28, 200, 138, 0.1);
        color: #1cc88a;
    }

    .content-icon.activity {
        background-color: rgba(246, 194, 62, 0.1);
        color: #f6c23e;
    }

    .content-icon.results {
        background-color: rgba(231, 74, 59, 0.1);
        color: #e74a3b;
    }

    .content-details {
        flex: 1;
    }

    .content-details h6 {
        font-weight: 700;
        color: #333;
        margin-bottom: 0.5rem;
        font-size: 1rem;
        letter-spacing: -0.3px;
    }

    .content-details p {
        color: #6c757d;
        font-size: 0.875rem;
        margin-bottom: 0;
        line-height: 1.5;
    }

    .content-check {
        margin-left: 0.75rem;
        width: 22px;
        height: 22px;
        border-radius: 6px;
        border: 2px solid #dee2e6;
        display: flex;
        align-items: center;
        justify-content: center;
        color: transparent;
        transition: all 0.3s ease;
        transform: scale(0.8);
        opacity: 0.5;
    }

    .content-checkbox:checked + .content-label .content-check {
        border-color: #4e73df;
        background-color: #4e73df;
        color: white;
        transform: scale(1);
        opacity: 1;
    }

    /* 筛选条件 */
    .filter-toggle {
        display: flex;
        background: #fff;
        border-radius: 1rem;
        padding: 0.25rem;
        margin-bottom: 2rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    .toggle-option {
        flex: 1;
        padding: 1rem 0.5rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        border-radius: 0.75rem;
        transition: all 0.3s ease;
    }

    .toggle-option:hover {
        background-color: #f8f9fa;
    }

    .toggle-option.active {
        background-color: #4e73df;
    }

    .toggle-icon {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
        color: #6c757d;
        transition: all 0.3s ease;
    }

    .toggle-label {
        font-weight: 600;
        color: #6c757d;
        transition: all 0.3s ease;
        font-size: 0.9rem;
        letter-spacing: 0.2px;
    }

    .toggle-option.active .toggle-icon,
    .toggle-option.active .toggle-label {
        color: #fff;
    }

    .filter-options {
        display: none;
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.3s ease;
    }

    .filter-options.show {
        display: block;
        opacity: 1;
        transform: translateY(0);
    }

    .filter-section {
        background: #fff;
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        transition: all 0.3s ease;
    }

    .filter-section:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
    }

    .filter-header {
        display: flex;
        align-items: center;
        margin-bottom: 1.25rem;
    }

    .filter-header i {
        font-size: 1.25rem;
        color: #4e73df;
        margin-right: 0.75rem;
    }

    .filter-header h6 {
        font-weight: 700;
        color: #333;
        margin-bottom: 0;
        font-size: 1rem;
        letter-spacing: -0.2px;
    }

    .form-floating > .form-control,
    .form-floating > .form-select {
        border-radius: 0.75rem;
        border: 1px solid #d1d3e2;
        background-color: #f8f9fa;
        transition: all 0.2s ease;
        font-size: 0.95rem;
    }

    .form-floating > label {
        color: #6c757d;
        padding: 0.85rem 1rem;
        font-weight: 500;
    }

    .form-floating > .form-control:focus,
    .form-floating > .form-select:focus {
        border-color: #4e73df;
        box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
        background-color: #fff;
    }

    /* 导航按钮 */
    .export-footer {
        padding: 1rem 2rem;
        background: #fff;
        border-top: 1px solid #e3e6f0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .btn-prev,
    .btn-next {
        transition: all 0.3s ease;
        min-width: 120px;
        font-weight: 600;
        padding: 0.6rem 1.25rem;
        border-radius: 0.5rem;
        letter-spacing: 0.3px;
    }

    .btn-prev:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .btn-next {
        box-shadow: 0 4px 10px rgba(78, 115, 223, 0.15);
    }

    .btn-next.btn-success {
        box-shadow: 0 4px 10px rgba(28, 200, 138, 0.15);
    }

    .step-indicator {
        font-weight: 600;
        color: #6c757d;
    }

    .step-indicator .current-step {
        color: #4e73df;
        font-size: 1.25rem;
    }

    .step-indicator .total-steps {
        color: #adb5bd;
    }
    

</style>
{% endblock %}

{% block content %}
<!-- 用户管理页面 -->
<div class="user-management-container">
    <!-- 视图切换按钮 -->
    <div class="view-mode-toggle mb-4">
        <div class="card shadow-sm bg-light">
            <div class="card-body py-2 px-3">
                <div class="d-flex align-items-center justify-content-between flex-wrap gap-3">
                    <div class="d-flex align-items-center">
                        <span class="text-secondary me-2">视图:</span>
                        <div class="btn-group">
                            <button type="button" class="btn btn-sm btn-outline-primary active" id="gridViewBtn">
                                <i class="fas fa-th me-1"></i> 网格
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-primary" id="listViewBtn">
                                <i class="fas fa-list me-1"></i> 列表
                            </button>
                        </div>
                    </div>
                    
                    <div class="d-flex align-items-center gap-3 flex-wrap">
                        <!-- 搜索框 -->
                        <div class="d-flex" role="search">
                            <div class="input-group shadow-sm">
                                <span class="input-group-text bg-white border-end-0">
                                    <i class="fas fa-search text-muted"></i>
                                </span>
                                <input class="form-control border-start-0" type="search" id="searchInput" placeholder="搜索用户..." aria-label="Search" style="width: 200px;">
                            </div>
                        </div>
                        
                        <!-- 筛选器 -->
                        <div class="d-flex gap-2">
                            <select id="classFilter" class="form-select form-select-sm shadow-sm" style="width: 130px;">
                                <option value="all">所有班级</option>
                                {% for class_ in classes %}
                                <option value="{{ class_.id }}">班级: {{ class_.name }}</option>
                                {% endfor %}
                            </select>
                            <select id="statusFilter" class="form-select form-select-sm shadow-sm" style="width: 100px;">
                                <option value="all">所有状态</option>
                                <option value="online">在线</option>
                                <option value="active">已启用</option>
                                <option value="inactive">已禁用</option>
                            </select>
                        </div>

                        <!-- 用户统计 -->
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-primary rounded-pill px-3">
                                <i class="fas fa-users me-1"></i> 用户总数: {{ users|length }}
                            </button>
                            <button class="btn btn-sm btn-success rounded-pill px-3">
                                <i class="fas fa-circle-dot me-1"></i> 在线用户: <span id="online-user-count">0</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    

    
    <!-- 网格视图 -->
    <div class="user-grid-body" id="gridView">
        <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 row-cols-xl-4 g-3 animate-fade-in">
        {% for user in users %}
            <div class="col animate-fade-in">
                <div class="user-card card h-100 position-relative" data-user-id="{{ user.id }}" data-class-id="{{ user.class_id or '' }}" data-is-active="{{ 'true' if user.is_active else 'false' }}">
                    {% if user.is_online %}
                    <div class="online-badge">
                        <span class="badge bg-success small px-2 py-1">
                            <i class="fas fa-circle-dot me-1"></i>在线
                        </span>
                    </div>
                    {% endif %}
                    
                    <div class="card-body p-3 d-flex flex-column">
                        <div class="d-flex mb-2">
                            <div class="flex-shrink-0 me-3">
                                <div class="rounded-circle user-avatar" style="width:45px;height:45px;">
                {% if user.avatar %}
                                    <img src="{{ user.avatar }}" alt="头像" style="width:100%;height:100%;object-fit:cover;">
                {% else %}
                                    <div class="d-flex align-items-center justify-content-center h-100">
                                        <i class="fas fa-user text-secondary"></i>
                                    </div>
                {% endif %}
            </div>
                            </div>
                            <div>
                                <div class="fw-medium text-truncate mb-1">{{ user.nickname or "用户" + user.id|string }}</div>
                                <div class="text-muted small">ID: {{ user.id }}</div>
                            </div>
                        </div>
                        
                        <div class="user-stats">
                            <div class="row g-2 small mb-2">
                                <div class="col-6">
                                    <div class="d-flex flex-column">
                                        <span class="text-muted">注册时间:</span>
                                        <span>{{ user.created_at.strftime('%Y-%m-%d') if user.created_at else '' }}</span>
                    </div>
                    </div>
                                <div class="col-6">
                                    <div class="d-flex flex-column">
                                        <span class="text-muted">答题次数:</span>
                                        <span>{{ user.answer_count }}次</span>
                    </div>
                                </div>
                </div>
                
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-muted small">正确率:</span>
                                <span class="badge {{ 'bg-success' if user.accuracy >= 60 else 'bg-warning' if user.accuracy >= 40 else 'bg-danger' }}">
                                    {{ user.accuracy }}%
                                </span>
                </div>
            </div>
                        
                        <div class="mt-auto pt-2 border-top d-flex gap-1">
                            <a href="{{ url_for('admin_user_detail', user_id=user.id) }}" class="btn btn-sm btn-outline-primary flex-grow-1">
                                <i class="fas fa-eye"></i> 查看
                            </a>
                            <form action="{{ url_for('toggle_user_status', user_id=user.id) }}" method="post" class="flex-grow-1">
                                <button type="submit" class="btn btn-sm btn-outline-{{ 'danger' if user.is_active else 'success' }} w-100">
                                    <i class="fas fa-{{ 'ban' if user.is_active else 'check' }}"></i> 
                                    {{ '禁用' if user.is_active else '启用' }}
                </button>
                            </form>
                        </div>
                    </div>
            </div>
        </div>
        {% endfor %}
        </div>
    </div>
    
    <!-- 列表视图 -->
    <div class="user-list-body" id="listView" style="display: none;">
        <table class="user-list-table">
                <thead>
                    <tr>
                    <th style="width: 60px">ID</th>
                    <th style="width: 25%">用户</th>
                    <th style="width: 15%">状态</th>
                    <th style="width: 15%">注册时间</th>
                    <th style="width: 15%">活跃度</th>
                    <th style="width: 15%">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                <tr data-user-id="{{ user.id }}" data-class-id="{{ user.class_id or '' }}" data-is-active="{{ 'true' if user.is_active else 'false' }}">
                        <td>{{ user.id }}</td>
                        <td>
                            <div class="d-flex align-items-center">
                            <div class="user-avatar me-3">
                                {% if user.avatar %}
                                <img src="{{ user.avatar }}" alt="头像">
                                {% else %}
                                    <i class="fas fa-user"></i>
                                {% endif %}
                            </div>
                            <div>
                                <div class="fw-medium">{{ user.nickname or "用户" + user.id|string }}</div>
                            </div>
                            </div>
                        </td>
                        <td>
                        {{ user_status_indicator(user.id, user.is_online, true) }}
                        <div class="small text-muted mt-1">
                            {% if user.is_active %}
                            <span class="badge bg-success-subtle text-success">已启用</span>
                            {% else %}
                            <span class="badge bg-danger-subtle text-danger">已禁用</span>
                            {% endif %}
                            </div>
                        </td>
                        <td>
                        <div class="small text-muted mb-1">
                            {{ user.created_at.strftime('%Y-%m-%d %H:%M') if user.created_at else '' }}
                        </div>
                        <div class="small">
                            {% if user.last_active %}
                            最后活跃: {{ user.last_active.strftime('%Y-%m-%d %H:%M') }}
                            {% else %}
                            未活跃
                            {% endif %}
                            </div>
                        </td>
                    <td>
                        <div>答题: {{ user.answer_count }}次</div>
                        <div>正确率: {{ user.accuracy }}%</div>
                        </td>
                        <td>
                        <div class="d-flex gap-1">
                            <a href="{{ url_for('admin_user_detail', user_id=user.id) }}" class="user-action-button">
                                    <i class="fas fa-eye"></i>
                                </a>
                            <form action="{{ url_for('toggle_user_status', user_id=user.id) }}" method="post" class="d-inline">
                                <button type="submit" class="user-action-button {{ 'text-danger' if user.is_active else 'text-success' }}">
                                    <i class="fas fa-{{ 'ban' if user.is_active else 'check' }}"></i>
                                </button>
                            </form>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
    // 搜索和筛选功能实现
    const searchInput = document.getElementById('searchInput');
    const classFilter = document.getElementById('classFilter');
    const statusFilter = document.getElementById('statusFilter');
    
    // 执行筛选函数
    function applyFilters() {
        const searchTerm = searchInput.value.toLowerCase().trim();
        const classId = classFilter.value;
        const userStatus = statusFilter.value;
        filterUsers(searchTerm, classId, userStatus);
    }
    
    // 添加事件监听器
    searchInput.addEventListener('input', applyFilters);
    classFilter.addEventListener('change', applyFilters);
    statusFilter.addEventListener('change', applyFilters);
    

    
    function filterUsers(searchTerm, classId, userStatus) {
        // 获取所有用户卡片和行
        const userCards = document.querySelectorAll('.user-card');
        const userRows = document.querySelectorAll('#listView tbody tr');
        let visibleCount = 0;
        let visibleUsers = new Set(); // 使用Set存储可见用户ID，避免重复计数
        
        // 网格视图过滤
        userCards.forEach(card => {
            const userId = card.getAttribute('data-user-id');
            const userName = card.querySelector('.fw-medium').textContent.toLowerCase();
            const userIdText = card.querySelector('.text-muted.small').textContent.toLowerCase();
            const registerDate = card.querySelector('.user-stats .col-6:first-child span:last-child').textContent.toLowerCase();
            
            // 检查是否匹配搜索词
            const matchesSearch = searchTerm === '' || 
                                userName.includes(searchTerm) || 
                                userIdText.includes(searchTerm) || 
                                registerDate.includes(searchTerm);
            
            // 检查班级匹配
            const classAttr = card.getAttribute('data-class-id') || '';
            const matchesClass = classId === 'all' || classId === classAttr;
            
            // 检查状态匹配
            const isActive = card.getAttribute('data-is-active') === 'true';
            const isOnline = card.querySelector('.online-badge') !== null;
            
            let matchesStatus = false;
            if (userStatus === 'all') {
                matchesStatus = true;
            } else if (userStatus === 'active') {
                matchesStatus = isActive;
            } else if (userStatus === 'inactive') {
                matchesStatus = !isActive;
            } else if (userStatus === 'online') {
                matchesStatus = isOnline;
            }
            
            // 综合判断
            const isVisible = matchesSearch && matchesClass && matchesStatus;
            
            // 显示或隐藏卡片
            card.closest('.col').style.display = isVisible ? '' : 'none';
            
            // 如果用户可见，将ID添加到Set中
            if (isVisible) {
                visibleUsers.add(userId);
            }
        });
        
        // 列表视图过滤
        userRows.forEach(row => {
            const userId = row.getAttribute('data-user-id');
            const userName = row.querySelector('.fw-medium').textContent.toLowerCase();
            const registerDate = row.querySelector('td:nth-child(4)').textContent.toLowerCase();
            
            // 检查是否匹配搜索词
            const matchesSearch = searchTerm === '' || 
                                userName.includes(searchTerm) || 
                                userId.includes(searchTerm) || 
                                registerDate.includes(searchTerm);
            
            // 检查班级匹配
            const classAttr = row.getAttribute('data-class-id') || '';
            const matchesClass = classId === 'all' || classId === classAttr;
            
            // 检查状态匹配
            const isActive = row.getAttribute('data-is-active') === 'true';
            const isOnline = row.querySelector('.user-online') !== null;
            
            let matchesStatus = false;
            if (userStatus === 'all') {
                matchesStatus = true;
            } else if (userStatus === 'active') {
                matchesStatus = isActive;
            } else if (userStatus === 'inactive') {
                matchesStatus = !isActive;
            } else if (userStatus === 'online') {
                matchesStatus = isOnline;
            }
            
            // 综合判断
            const isVisible = matchesSearch && matchesClass && matchesStatus;
            
            // 显示或隐藏行
            row.style.display = isVisible ? '' : 'none';
            
            // 如果用户可见，将ID添加到Set中(已经在网格视图中添加过的不会重复)
            if (isVisible) {
                visibleUsers.add(userId);
            }
        });
        
        // 使用Set的大小作为实际可见用户数
        visibleCount = visibleUsers.size;
        
        // 更新用户总数显示
        if (searchTerm || classId !== 'all' || userStatus !== 'all') {
            document.querySelector('.btn-primary.rounded-pill').innerHTML = 
                `<i class="fas fa-users me-1"></i> 筛选结果: ${visibleCount}`;
        } else {
            document.querySelector('.btn-primary.rounded-pill').innerHTML = 
                `<i class="fas fa-users me-1"></i> 用户总数: {{ users|length }}`;
        }
    }
    
    // 处理视图切换
    const gridViewBtn = document.getElementById('gridViewBtn');
    const listViewBtn = document.getElementById('listViewBtn');
    const gridView = document.getElementById('gridView');
    const listView = document.getElementById('listView');
        
    function setViewMode(mode) {
        if (mode === 'grid') {
            gridView.style.display = 'block';
            listView.style.display = 'none';
        gridViewBtn.classList.add('active');
        listViewBtn.classList.remove('active');
            localStorage.setItem('userViewMode', 'grid');
        } else {
        gridView.style.display = 'none';
        listView.style.display = 'block';
            gridViewBtn.classList.remove('active');
            listViewBtn.classList.add('active');
            localStorage.setItem('userViewMode', 'list');
        }
    }
    
    // 从本地存储加载上次使用的视图模式
    const savedViewMode = localStorage.getItem('userViewMode') || 'grid';
    setViewMode(savedViewMode);
    
    gridViewBtn.addEventListener('click', function() {
        setViewMode('grid');
    });
    
    listViewBtn.addEventListener('click', function() {
        setViewMode('list');
    });
    
    // 在线用户统计
    function updateOnlineCount() {
        const onlineIndicators = document.querySelectorAll('.user-online');
        const onlineCountElement = document.getElementById('online-user-count');
        onlineCountElement.textContent = onlineIndicators.length;
    }
    
    updateOnlineCount();
    
    // 检查用户在线状态
    function checkOnlineStatus() {
        fetch('/api/users/online')
            .then(response => response.json())
            .then(data => {
                const onlineUserIds = data.online_users || [];
                const statusIndicators = document.querySelectorAll('[data-user-id]');
                let onlineCount = 0;
                
                statusIndicators.forEach(indicator => {
                    const userId = indicator.getAttribute('data-user-id');
                    const isOnline = onlineUserIds.includes(parseInt(userId));
                    
                    // 更新指示器的类
                    if (indicator.classList.contains('user-status-indicator')) {
                        if (isOnline) {
                            indicator.classList.remove('user-offline');
                            indicator.classList.add('user-online');
                        } else {
                            indicator.classList.remove('user-online');
                            indicator.classList.add('user-offline');
                        }
                    }
                    
                    // 更新文本状态
                    const statusText = document.querySelector(`.user-status-text-${userId}`);
                    if (statusText) {
                        if (isOnline) {
                            statusText.textContent = '在线';
                            statusText.classList.remove('text-secondary');
                            statusText.classList.add('text-success');
                        } else {
                            statusText.textContent = '离线';
                            statusText.classList.remove('text-success');
                            statusText.classList.add('text-secondary');
                        }
                    }
                    
                    // 更新行背景色
                    const row = document.querySelector(`tr[data-user-id="${userId}"]`);
                    if (row) {
                        if (isOnline) {
                            row.classList.add('table-success', 'bg-opacity-10');
                        } else {
                            row.classList.remove('table-success', 'bg-opacity-10');
                        }
                    }
                    
                    // 更新卡片边框和在线标签
                    const card = document.querySelector(`.user-card[data-user-id="${userId}"]`);
                    if (card) {
                        if (isOnline) {
                            card.classList.add('border-success');
                            // 如果不存在在线标签，则添加
                            if (!card.querySelector('.online-badge')) {
                                const badge = document.createElement('div');
                                badge.className = 'online-badge';
                                badge.innerHTML = '<span class="badge bg-success small px-2 py-1"><i class="fas fa-circle-dot me-1"></i>在线</span>';
                                card.prepend(badge);
                            }
                        } else {
                            card.classList.remove('border-success');
                            // 如果存在在线标签，则移除
                            const badge = card.querySelector('.online-badge');
                            if (badge) {
                                badge.remove();
                            }
                        }
                    }
                });
                
                // 更新在线用户数量
                const onlineCountElement = document.getElementById('online-user-count');
                onlineCountElement.textContent = onlineUserIds.length;
                
                // 重新应用过滤器，如果状态筛选为"在线"
                if (statusFilter.value === 'online') {
                    applyFilters();
                }
            })
            .catch(error => {
                console.error('Error fetching online status:', error);
            });
    }
    
    // 初始化页面时检查在线状态
    checkOnlineStatus();
    
    // 每30秒更新一次在线状态
    setInterval(checkOnlineStatus, 30000);
});
</script>
{% endblock %} 
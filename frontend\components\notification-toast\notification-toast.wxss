/* 全局消息通知组件样式 */
.notification-toast {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  transform: translateY(-100%);
  transition: transform 0.3s ease-in-out;
  padding: 0 20px;
  padding-top: env(safe-area-inset-top, 44px);
  padding-top: calc(env(safe-area-inset-top, 44px) + 10px);
}

.notification-toast.show {
  transform: translateY(0);
}

.toast-content {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 15px;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  margin-top: 10px;
}

.toast-icon {
  margin-right: 12px;
}

.toast-icon .icon {
  font-size: 24px;
  color: white;
}

.toast-text {
  flex: 1;
  color: white;
}

.toast-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 2px;
}

.toast-message {
  font-size: 14px;
  opacity: 0.9;
  line-height: 1.3;
}

.toast-close {
  margin-left: 12px;
  padding: 5px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  color: white;
  font-size: 18px;
  font-weight: bold;
  line-height: 1;
}

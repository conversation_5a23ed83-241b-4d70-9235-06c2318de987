/* 用户在线状态样式 */
.user-status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 5px;
    position: relative;
}

.user-status-indicator.user-online {
    background-color: #4cd964;
    box-shadow: 0 0 0 2px rgba(76, 217, 100, 0.2);
}

.user-status-indicator.user-offline {
    background-color: #8e8e93;
    box-shadow: 0 0 0 2px rgba(142, 142, 147, 0.2);
}

/* 在线状态动画效果 */
.user-status-indicator.user-online::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: rgba(76, 217, 100, 0.4);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.7;
    }
    70% {
        transform: scale(2);
        opacity: 0;
    }
    100% {
        transform: scale(2.5);
        opacity: 0;
    }
}

/* 列表视图中的在线状态指示器 */
.user-list-table .user-status-container {
    display: flex;
    align-items: center;
}

/* 卡片视图中的在线状态指示器 */
.user-grid-card .user-status-container {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 2;
}

/* 用户头像在线状态指示器 */
.user-avatar-with-status {
    position: relative;
}

.user-avatar-with-status .user-status-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    border: 2px solid white;
    margin: 0;
}

/* 文字标签 */
.status-label {
    font-size: 12px;
    margin-left: 2px;
}

.status-label.online {
    color: #4cd964;
}

.status-label.offline {
    color: #8e8e93;
} 
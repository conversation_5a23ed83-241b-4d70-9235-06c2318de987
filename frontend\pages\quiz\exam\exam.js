// exam.js - 考试模式
const app = getApp();
const util = require('../../../utils/util.js');  // 导入工具函数

// 洗牌算法，用于随机打乱数组
function shuffleArray(array) {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

Page({
  data: {
    courseId: '',
    courseTitle: '',
    courseColor: '#2b85e4',
    questions: [],
    currentIndex: 0,
    remainingTime: 0, // 剩余考试时间（秒）
    formattedTime: '00:00:00', // 格式化后的时间
    answers: [], // 考生的答案
    isSubmitted: false,
    showResult: false,
    userAnswers: [], // 用户的答案
    correctAnswers: [], // 正确答案
    timer: null, // 定时器
    isLoading: true,
    statusBarHeight: 0,
    showExitConfirm: false, // 退出确认对话框
    showSubmitConfirm: false, // 交卷确认对话框
    unansweredCount: 0, // 未作答题目数量
    totalQuestions: 0,
    examCompleted: false, // 考试是否完成
    currentQuestion: null, // 当前题目
    forceUpdate: 0, // 强制更新UI的标识
    multipleSelections: {}, // 创建新的多选题选择记录
    // 新增多选题选择状态跟踪
    multiSelected: {},
    multiSelectedStatus: {},
    util: util,  // 将util添加到data中，使其在wxml中可用
    examStartTime: 0, // 记录考试开始时间戳
    examEndTime: 0,   // 记录考试结束时间戳
    examTimeUsed: '00:00:00', // 答题用时
    answeredCount: 0, // 已答题数量
    // 初始化题型统计数据
    typeCounts: {
      single: 0,
      multiple: 0,
      judgment: 0,
      fillblank: 0
    },
    currentTypeCounts: {
      single: 0,
      multiple: 0,
      judgment: 0,
      fillblank: 0
    },
    // 进度百分比初始化
    singlePercent: 0,
    multiplePercent: 0,
    judgmentPercent: 0,
    fillblankPercent: 0,
    // 进度完成度初始化
    singleProgress: 0,
    multipleProgress: 0,
    judgmentProgress: 0,
    fillblankProgress: 0
  },

  onLoad: function(options) {
    // 获取状态栏高度
    wx.getSystemInfo({
      success: (res) => {
        this.setData({
          statusBarHeight: res.statusBarHeight
        });
      }
    });

    const courseId = options.courseId || '';
    const courseTitle = options.courseTitle ? decodeURIComponent(options.courseTitle) : '';
    const currentCourse = wx.getStorageSync('currentCourse') || {};
    const courseColor = currentCourse.color || '#4e8df7';

    // 获取科目名称 - 优先使用本地存储的currentSubject，如果没有尝试从多个来源获取
    let subject = wx.getStorageSync('currentSubject') || '';
    
    // 多重备选方案来确保获取到科目名称
    if (!subject || subject === '未知') {
      // 尝试从URL参数获取
      if (courseTitle && courseTitle !== '考试') {
        subject = courseTitle;
      } 
      // 尝试从currentCourse获取
      else if (currentCourse && currentCourse.name) {
        subject = currentCourse.name;
      }
      // 尝试从currentCourse的title获取
      else if (currentCourse && currentCourse.title) {
        subject = currentCourse.title;
      }
      // 如果仍然没有获取到，设置默认值
      else {
        subject = '未知课程';
      }
      
      // 保存到本地存储以便后续使用
      wx.setStorageSync('currentSubject', subject);
    }
    
    console.log('当前科目:', subject);

    this.setData({
      courseId: courseId,
      courseTitle: subject,
      courseColor: this.getSubjectColor(subject),
      isLoading: true,
      multiSelectedStatus: {}, // 初始化多选状态对象
      // 初始化题型统计数据
      typeCounts: {
        single: 0,
        multiple: 0,
        judgment: 0,
        fillblank: 0
      },
      currentTypeCounts: {
        single: 0,
        multiple: 0,
        judgment: 0,
        fillblank: 0
      },
      // 进度百分比初始化
      singlePercent: 0,
      multiplePercent: 0,
      judgmentPercent: 0,
      fillblankPercent: 0,
      // 进度完成度初始化
      singleProgress: 0,
      multipleProgress: 0,
      judgmentProgress: 0,
      fillblankProgress: 0
    });

    // 设置导航栏
    wx.setNavigationBarTitle({
      title: '考试模式'
    });

    wx.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: courseColor
    });

    // 获取所有题目并准备考试
    this.prepareExam();
  },

  // 准备考试题目
  prepareExam: function() {
    try {
      // 记录考试开始时间
      this.setData({
        examStartTime: new Date().getTime()
      });
      
      // 从缓存中获取题目数据
      let singleQuestions = wx.getStorageSync('currentSingleQuestions') || [];
      let multipleQuestions = wx.getStorageSync('currentMultipleQuestions') || [];
      let judgmentQuestions = wx.getStorageSync('currentJudgmentQuestions') || [];
      let fillBlankQuestions = wx.getStorageSync('currentFillBlankQuestions') || [];
      
      console.log('获取到题目数量:', {
        single: singleQuestions.length,
        multiple: multipleQuestions.length,
        judgment: judgmentQuestions.length,
        fillBlank: fillBlankQuestions.length
      });

      // 检查题目数据
      if (!singleQuestions.length && !multipleQuestions.length && 
          !judgmentQuestions.length && !fillBlankQuestions.length) {
        console.error('没有获取到任何题目数据');
        wx.showToast({
          title: '未找到题目数据',
          icon: 'none',
          duration: 2000
        });
        
        setTimeout(() => {
          wx.navigateBack();
        }, 2000);
        
        return;
      }

      // 预处理每种题型，确保数据格式正确，但不包含用户答案
      singleQuestions = this.preprocessQuestions(singleQuestions, 'single');
      multipleQuestions = this.preprocessQuestions(multipleQuestions, 'multiple');
      judgmentQuestions = this.preprocessQuestions(judgmentQuestions, 'judgment');
      fillBlankQuestions = this.preprocessQuestions(fillBlankQuestions, 'fillblank');

      // 动态计算各题型权重
      const typeWeights = this.calculateTypeWeights(
        singleQuestions.length, 
        multipleQuestions.length, 
        judgmentQuestions.length, 
        fillBlankQuestions.length
      );

      // 根据动态权重抽取题目
      let selectedSingle = this.selectRandomQuestions(singleQuestions, typeWeights.single);
      let selectedMultiple = this.selectRandomQuestions(multipleQuestions, typeWeights.multiple);
      let selectedJudgment = this.selectRandomQuestions(judgmentQuestions, typeWeights.judgment);
      let selectedFillBlank = this.selectRandomQuestions(fillBlankQuestions, typeWeights.fillBlank);
      
      console.log('抽取题目数量:', {
        single: selectedSingle.length,
        multiple: selectedMultiple.length,
        judgment: selectedJudgment.length,
        fillBlank: selectedFillBlank.length
      });

      // 按照单选->多选->判断->填空的顺序组织题目，不再随机打乱
      let allQuestions = [];
      
      // 确保每种题型内的题目顺序是随机的
      if (selectedSingle.length > 0) {
        allQuestions = allQuestions.concat(shuffleArray(selectedSingle));
      }
      
      if (selectedMultiple.length > 0) {
        allQuestions = allQuestions.concat(shuffleArray(selectedMultiple));
      }
      
      if (selectedJudgment.length > 0) {
        allQuestions = allQuestions.concat(shuffleArray(selectedJudgment));
      }
      
      if (selectedFillBlank.length > 0) {
        allQuestions = allQuestions.concat(shuffleArray(selectedFillBlank));
      }
      
      if (allQuestions.length === 0) {
        console.error('抽取后没有任何题目');
        wx.showToast({
          title: '题目抽取失败',
          icon: 'none',
          duration: 2000
        });
        
        setTimeout(() => {
          wx.navigateBack();
        }, 2000);
        
        return;
      }

      // 检查第一道题的详细结构
      if (allQuestions.length > 0) {
        console.log('第一道题详细结构:', JSON.stringify(allQuestions[0]));
        
        // 清除可能导致默认选中的属性
        allQuestions.forEach(q => {
          delete q.userAnswer;
          delete q.userSelected;
          delete q.selected;
          delete q.selectedOptions; 
          delete q.answered;
          delete q.isAnswered;
          
          if (q.options) {
            q.options.forEach((opt, idx) => {
              if (typeof opt === 'object') {
                // 如果选项是对象，清除选中状态
                delete opt.selected;
                delete opt.isSelected;
                delete opt.checked;
              }
            });
          }
        });
      }

      // 彻底重置用户答案数组 - 使用简单直接的方式
      const initAnswers = [];
      
      // 手动构建每个答案对象，确保完全空白的初始状态
      for (let i = 0; i < allQuestions.length; i++) {
        initAnswers.push({
          selected: [],
          isCorrect: false,
          isAnswered: false
        });
      }
      
      console.log('初始化的答案状态:', JSON.stringify(initAnswers[0]));

      // 计算考试时间（每题1分钟）
      const examTimeInSeconds = allQuestions.length * 60;

      // 立即清理所有可能影响的缓存
      wx.removeStorageSync('examAnswers');
      wx.removeStorageSync('tempAnswers');
      wx.removeStorageSync('multipleAnswers');
      
      // 创建一个全新的this.data对象，然后一次性设置
      const newData = {
        questions: allQuestions,
        answers: initAnswers,
        userAnswers: JSON.parse(JSON.stringify(initAnswers)),
        remainingTime: examTimeInSeconds,
        formattedTime: this.formatTime(examTimeInSeconds),
        totalQuestions: allQuestions.length,
        isLoading: false,
        currentIndex: 0,
        currentQuestion: allQuestions[0],
        multipleSelections: {}
      };
      
      // 一次性设置所有数据
      this.setData(newData);
      
      // 如果第一题是多选题，初始化多选状态
      if (allQuestions.length > 0 && allQuestions[0].type === 'multiple') {
        this.initMultiSelectStatus(0);
      }
      
      // 再次检查确认多选题选项为空
      setTimeout(() => {
        const multipleIndex = allQuestions.findIndex(q => q.type === 'multiple');
        if (multipleIndex !== -1) {
          console.log('多选题索引:', multipleIndex);
          console.log('多选题答案状态:', JSON.stringify(this.data.answers[multipleIndex]));
          console.log('是否有选中选项:', this.data.answers[multipleIndex].selected.length > 0);
        }
      }, 100);

      // 开始计时
      this.startTimer();

      console.log(`考试准备完成，共${allQuestions.length}道题，考试时间${Math.floor(examTimeInSeconds/60)}分钟`);

      // 计算各题型数量和进度
      this.calculateTypeProgress();

    } catch (error) {
      console.error('准备考试出错:', error);
      wx.showToast({
        title: '准备试题失败',
        icon: 'none',
        duration: 2000
      });
    }
  },

  // 预处理题目数据，确保格式正确
  preprocessQuestions: function(questions, type) {
    if (!questions || !Array.isArray(questions) || questions.length === 0) {
      return [];
    }
    
    console.log(`预处理${type}类型题目，数量:`, questions.length);
    if (questions.length > 0) {
      console.log(`第一题原始数据:`, JSON.stringify(questions[0]));
    }
    
    return questions.map((question, index) => {
      // 创建一个新对象，避免修改原对象
      const processedQuestion = {...question};
      
      // 禁止直接复制answer的数据到用户选择
      if (processedQuestion.userAnswer || processedQuestion.userSelected || processedQuestion.selected) {
        delete processedQuestion.userAnswer;
        delete processedQuestion.userSelected;
        delete processedQuestion.selected;
      }
      
      // 确保题目有type属性
      processedQuestion.type = type;
      
      // 确保题目有content属性
      if (!processedQuestion.content && processedQuestion.title) {
        processedQuestion.content = processedQuestion.title;
      } else if (!processedQuestion.content && processedQuestion.question) {
        processedQuestion.content = processedQuestion.question;
      } else if (!processedQuestion.content) {
        processedQuestion.content = `题目${type}(ID:${processedQuestion.id || '未知'})`;
      }
      
      // 确保选项格式正确
      if (type === 'single' || type === 'multiple') {
        if (!processedQuestion.options || !Array.isArray(processedQuestion.options) || processedQuestion.options.length === 0) {
          // 尝试从不同的属性名获取选项
          if (processedQuestion.choices && Array.isArray(processedQuestion.choices)) {
            processedQuestion.options = processedQuestion.choices;
          } else {
            processedQuestion.options = ['选项A', '选项B', '选项C', '选项D'];
          }
        }
      }
      
      // 根据不同题型获取正确答案
      // 优先使用对应的correct_answer/correct_answers字段，其次使用answer字段
      let correctAnswer;
      
        if (type === 'single') {
        // 单选题使用correct_answer字段
        correctAnswer = processedQuestion.correct_answer !== undefined ? 
                         processedQuestion.correct_answer : 
                         processedQuestion.answer;
        } else if (type === 'multiple') {
        // 多选题使用correct_answers字段
        correctAnswer = processedQuestion.correct_answers !== undefined ? 
                         processedQuestion.correct_answers : 
                         processedQuestion.answer;
        } else if (type === 'judgment') {
        // 判断题使用correct_answer字段
        correctAnswer = processedQuestion.correct_answer !== undefined ? 
                         processedQuestion.correct_answer : 
                         processedQuestion.answer;
      } else if (type === 'fillblank' || type === 'fill') {
        // 填空题使用correct_answer字段
        correctAnswer = processedQuestion.correct_answer !== undefined ? 
                         processedQuestion.correct_answer : 
                         processedQuestion.answer;
      }
      
      // 如果没有找到正确答案，设置默认值
      if (correctAnswer === undefined || correctAnswer === null) {
        if (type === 'single') {
          correctAnswer = 0; // 默认第一个选项为答案
        } else if (type === 'multiple') {
          correctAnswer = [0]; // 默认第一个选项为答案
        } else if (type === 'judgment') {
          correctAnswer = 0; // 默认"正确"
        } else if (type === 'fillblank' || type === 'fill') {
          correctAnswer = '答案'; // 默认答案
        }
      }
      
      // 标准化答案格式
      if (type === 'multiple') {
        // 处理多选题答案
        if (typeof correctAnswer === 'string') {
          try {
            // 尝试解析JSON格式的数组字符串
            if (correctAnswer.startsWith('[') && correctAnswer.endsWith(']')) {
              correctAnswer = JSON.parse(correctAnswer);
            } 
            // 尝试解析逗号分隔的数字字符串
            else if (correctAnswer.includes(',')) {
              correctAnswer = correctAnswer.split(',').map(item => {
                const num = parseInt(item.trim());
                return isNaN(num) ? item.trim() : num;
              });
            }
            // 单个数字或字符
            else {
              const num = parseInt(correctAnswer);
              correctAnswer = [isNaN(num) ? correctAnswer : num];
            }
          } catch (e) {
            console.error('预处理多选题答案出错:', e);
            correctAnswer = [correctAnswer]; // 如果解析失败，将其作为单元素数组
          }
        } 
        // 如果已经是数组，不做处理
        else if (!Array.isArray(correctAnswer)) {
          correctAnswer = [correctAnswer]; // 转换为数组
        }
      }
      
      // 标准化单选题和判断题答案
      else if (type === 'single' || type === 'judgment') {
        if (typeof correctAnswer === 'string' && !isNaN(parseInt(correctAnswer))) {
          correctAnswer = parseInt(correctAnswer);
        }
      }
      
      // 保存答案到各个字段以确保兼容性
      processedQuestion._originalAnswer = correctAnswer;
      processedQuestion.standardAnswer = correctAnswer;
      processedQuestion._answer = correctAnswer;
      processedQuestion.answer = correctAnswer;
      
      // 保留原始的correct_answer和correct_answers字段
      if (processedQuestion.correct_answer !== undefined) {
        processedQuestion._correct_answer = processedQuestion.correct_answer;
      }
      if (processedQuestion.correct_answers !== undefined) {
        processedQuestion._correct_answers = processedQuestion.correct_answers;
      }
      
      console.log(`题目${index+1}，ID:${processedQuestion.id || '未知'}，类型:${type}，标准答案:`, correctAnswer);
      
      return processedQuestion;
    });
  },

  // 根据比例随机抽取题目
  selectRandomQuestions: function(questions, ratio) {
    if (!questions || questions.length === 0) return [];
    
    const count = Math.max(1, Math.round(questions.length * ratio));
    const shuffled = shuffleArray(questions);
    return shuffled.slice(0, count);
  },

  // 开始计时器
  startTimer: function() {
    // 清除可能存在的旧计时器
    if (this.data.timer) {
      clearInterval(this.data.timer);
    }

    const timer = setInterval(() => {
      if (this.data.remainingTime <= 0) {
        // 时间用完，自动提交考试
        clearInterval(timer);
        this.submitExam();
        return;
      }

      const newTime = this.data.remainingTime - 1;
      this.setData({
        remainingTime: newTime,
        formattedTime: this.formatTime(newTime)
      });
    }, 1000);

    this.setData({ timer });
  },

  // 格式化时间为 HH:MM:SS
  formatTime: function(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  },

  // 选择选项（单选题和判断题）
  selectOption: function(e) {
    const { optionIndex } = e.currentTarget.dataset;
    const currentIndex = this.data.currentIndex;
    const currentQuestion = this.data.questions[currentIndex];
    const currentType = currentQuestion.type;

    console.log('选择单选/判断选项:', optionIndex, '当前题目索引:', currentIndex, '题目类型:', currentType);
    
    // 深拷贝答案数组
    const newAnswers = JSON.parse(JSON.stringify(this.data.answers));
    
    // 确保selected是数组
    if (!newAnswers[currentIndex].selected) {
      newAnswers[currentIndex].selected = [];
    }
    
    if (currentType === 'single' || currentType === 'judgment') {
      // 单选题或判断题只能选一个答案
      // 如果点击已选中的选项，则取消选中
      if (newAnswers[currentIndex].selected[0] === optionIndex) {
        newAnswers[currentIndex].selected = [];
        newAnswers[currentIndex].isAnswered = false;
      } else {
        newAnswers[currentIndex].selected = [optionIndex];
        newAnswers[currentIndex].isAnswered = true;
      }
      
      console.log('单选/判断题选择后:', newAnswers[currentIndex].selected);
    }

    this.setData({
      answers: newAnswers
    });
  },

  // 工具函数：检查选项是否被选中
  isOptionSelected: function(questionIndex, optionIndex) {
    const answers = this.data.answers;
    if (!answers || !answers[questionIndex] || !answers[questionIndex].selected) {
      return false;
    }
    
    return answers[questionIndex].selected.includes(optionIndex);
  },

  // 多选题选择 - 最终版本
  selectMultiOption: function(e) {
    const optIdx = e.currentTarget.dataset.optionIndex; // 修正参数名
    const quesIdx = this.data.currentIndex;
    
    // 复制答案
    const newAnswers = [...this.data.answers];
    
    // 确保选项数组存在
    if (!newAnswers[quesIdx].selected) {
      newAnswers[quesIdx].selected = [];
    }
    
    // 切换选中状态
    const isSelected = newAnswers[quesIdx].selected.includes(optIdx);
    
    if (isSelected) {
      // 移除选中
      newAnswers[quesIdx].selected = newAnswers[quesIdx].selected.filter(i => i !== optIdx);
    } else {
      // 添加选中
      newAnswers[quesIdx].selected.push(optIdx);
    }
    
    // 更新已答题状态
    newAnswers[quesIdx].isAnswered = newAnswers[quesIdx].selected.length > 0;
    
    // 保存新的multiSelected状态
    const key = `multi_${quesIdx}_${optIdx}`;
    const multiSelected = {...this.data.multiSelected};
    multiSelected[key] = !isSelected;
    
    // 更新数据，强制刷新
    this.setData({
      answers: newAnswers,
      multiSelected: multiSelected,
      forceUpdate: Date.now() // 使用forceUpdate强制刷新UI
    });
  },

  // 填写填空题答案
  inputBlankAnswer: function(e) {
    const { value } = e.detail;
    const currentIndex = this.data.currentIndex;
    
    // 深拷贝答案数组
    const newAnswers = JSON.parse(JSON.stringify(this.data.answers));
    
    newAnswers[currentIndex].selected = [value];
    newAnswers[currentIndex].isAnswered = value.trim().length > 0;
    
    this.setData({
      answers: newAnswers
    });
  },

  // 上一题
  prevQuestion: function() {
    if (this.data.currentIndex > 0) {
      const newIndex = this.data.currentIndex - 1;
      this.setData({
        currentIndex: newIndex,
        currentQuestion: this.data.questions[newIndex]
      });
      
      // 如果是多选题，初始化多选状态
      if (this.data.questions[newIndex].type === 'multiple') {
        this.initMultiSelectStatus(newIndex);
      }
      
      // 更新进度条
      this.calculateTypeProgress();
    }
  },

  // 下一题
  nextQuestion: function() {
    if (this.data.currentIndex < this.data.questions.length - 1) {
      const newIndex = this.data.currentIndex + 1;
      this.setData({
        currentIndex: newIndex,
        currentQuestion: this.data.questions[newIndex]
      });
      
      // 如果是多选题，初始化多选状态
      if (this.data.questions[newIndex].type === 'multiple') {
        this.initMultiSelectStatus(newIndex);
      }
      
      // 更新进度条
      this.calculateTypeProgress();
    }
  },

  // 跳转到指定题目
  goToQuestion: function(e) {
    // 获取题目索引
    const index = typeof e === 'object' ? e.currentTarget.dataset.index : e;
    if (index === undefined || index < 0 || index >= this.data.questions.length) {
      return;
    }
    
    this.setData({
      currentIndex: index,
      currentQuestion: this.data.questions[index]
    });
    
    // 如果是多选题，初始化多选状态
    if (this.data.questions[index].type === 'multiple') {
      this.initMultiSelectStatus(index);
    }
    
    // 更新进度条
    this.calculateTypeProgress();
  },

  // 辅助函数：初始化多选状态
  initMultiSelectStatus: function(questionIndex) {
    // 获取已选答案
    const selectedOptions = this.data.answers[questionIndex].selected || [];
    let multiStatus = {};
    
    // 设置选中状态
    selectedOptions.forEach(optIndex => {
      multiStatus[optIndex] = true;
    });
    
    this.setData({
      multiSelectedStatus: multiStatus
    });
    
    console.log('初始化多选状态:', JSON.stringify(multiStatus));
  },

  // 尝试退出考试
  tryExitExam: function() {
    // 如果考试已完成，直接返回
    if (this.data.examCompleted) {
      this.navigateBack();
      return;
    }

    // 否则显示确认对话框
    this.setData({
      showExitConfirm: true
    });
  },

  // 确认退出考试
  confirmExit: function() {
    this.setData({
      showExitConfirm: false
    });
    this.navigateBack();
  },

  // 取消退出考试
  cancelExit: function() {
    this.setData({
      showExitConfirm: false
    });
  },

  // 尝试提交考试
  trySubmitExam: function() {
    // 检查是否所有题目都已作答
    const unanswered = this.checkUnansweredQuestions();
    
    // 设置未作答题目数量
    this.setData({
      unansweredCount: unanswered.length
    });
    
    if (unanswered.length > 0) {
      // 显示确认对话框
      this.setData({
        showSubmitConfirm: true
      });
    } else {
      // 如果所有题目都已作答，直接提交
      this.submitExam();
    }
  },
  
  // 检查未作答的题目
  checkUnansweredQuestions: function() {
    const { answers, questions } = this.data;
    const unanswered = [];
    
    answers.forEach((answer, index) => {
      if (!answer.isAnswered) {
        unanswered.push(index + 1); // 保存题号（从1开始）
      }
    });
    
    return unanswered;
  },
  
  // 确认提交考试
  confirmSubmit: function() {
    this.setData({
      showSubmitConfirm: false
    });
    this.submitExam();
  },
  
  // 取消提交考试
  cancelSubmit: function() {
    this.setData({
      showSubmitConfirm: false
    });
  },

  // 提交考试
  submitExam: function() {
    // 停止计时器
    if (this.data.timer) {
      clearInterval(this.data.timer);
      this.setData({ timer: null });
    }

    // 记录考试结束时间
    const endTime = new Date().getTime();
    const timeUsed = this.calculateTimeUsed(this.data.examStartTime, endTime);
    
    // 计算已答题和未答题数量
    const { answers } = this.data;
    let answeredCount = 0;
    let unansweredCount = 0;
    
    answers.forEach(answer => {
      if (answer.isAnswered) {
        answeredCount++;
      } else {
        unansweredCount++;
      }
    });
    
    this.setData({
      examEndTime: endTime,
      examTimeUsed: timeUsed,
      answeredCount: answeredCount,
      unansweredCount: unansweredCount
    });

    // 计算得分
    this.calculateScore();
    
    this.setData({
      isSubmitted: true,
      examCompleted: true
    });

    // 保存考试结果
    this.saveExamResult();
  },

  // 计算答题用时
  calculateTimeUsed: function(startTime, endTime) {
    const timeUsedInSeconds = Math.floor((endTime - startTime) / 1000);
    const hours = Math.floor(timeUsedInSeconds / 3600);
    const minutes = Math.floor((timeUsedInSeconds % 3600) / 60);
    const seconds = timeUsedInSeconds % 60;
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  },

  // 计算得分
  calculateScore: function() {
    const { questions, answers } = this.data;
    let correctCount = 0;
    let totalPossibleScore = 0;
    let actualScore = 0;
    
    // 各题型分值
    const questionValues = {
      'single': 1,     // 单选题1分
      'multiple': 1.5, // 多选题1.5分
      'judgment': 1,   // 判断题1分
      'fillblank': 2,  // 填空题2分
      'fill': 2        // 填空题2分（兼容另一种类型名）
    };
    
    const newAnswers = JSON.parse(JSON.stringify(answers));
    
    // 首先检查所有题目的答案格式
    console.log("开始评分，总题目数:", questions.length);
    
    questions.forEach((question, index) => {
      const userAnswer = answers[index].selected || [];
      let isCorrect = false;
      
      // 获取正确答案 - 根据题型选择正确的字段
      let correctAnswer;
      
      if (question.type === 'single') {
        // 单选题使用correct_answer字段
        correctAnswer = question._correct_answer !== undefined ? 
                        question._correct_answer : 
                        (question.standardAnswer || question._answer || question.answer);
      } else if (question.type === 'multiple') {
        // 多选题使用correct_answers字段
        correctAnswer = question._correct_answers !== undefined ? 
                        question._correct_answers : 
                        (question.standardAnswer || question._answer || question.answer);
      } else if (question.type === 'judgment') {
        // 判断题使用correct_answer字段
        correctAnswer = question._correct_answer !== undefined ? 
                        question._correct_answer : 
                        (question.standardAnswer || question._answer || question.answer);
      } else if (question.type === 'fillblank' || question.type === 'fill') {
        // 填空题使用correct_answer字段
        correctAnswer = question._correct_answer !== undefined ? 
                        question._correct_answer : 
                        (question.standardAnswer || question._answer || question.answer);
      }
      
      if (correctAnswer === undefined || correctAnswer === null) {
        console.error(`题目${index+1}没有找到正确答案`);
      }
      
      // 获取题型分值
      const questionValue = questionValues[question.type] || 1;
      
      // 累加可能的总分
      totalPossibleScore += questionValue;
      
      console.log(`题目${index + 1}，类型: ${question.type}，正确答案:`, correctAnswer, `用户答案:`, userAnswer);
      
      switch (question.type) {
        case 'single':
          // 单选题：用户选择必须匹配正确答案
          if (userAnswer.length === 1) {
            const ua = typeof userAnswer[0] === 'string' && !isNaN(parseInt(userAnswer[0])) 
                     ? parseInt(userAnswer[0]) : userAnswer[0];
            const ca = typeof correctAnswer === 'string' && !isNaN(parseInt(correctAnswer)) 
                     ? parseInt(correctAnswer) : correctAnswer;
            isCorrect = ua === ca;
            console.log(`单选题比较: ${ua} === ${ca}, 结果: ${isCorrect}`);
          } else {
            isCorrect = false;
          }
          break;
        case 'multiple':
          // 多选题：用户选择必须完全匹配所有正确答案（顺序无关）
          if (Array.isArray(correctAnswer) && Array.isArray(userAnswer)) {
            // 确保两个数组元素类型一致
            const normalizedUserAnswer = userAnswer.map(a => typeof a === 'string' && !isNaN(parseInt(a)) ? parseInt(a) : a).sort();
            const normalizedCorrectAnswer = correctAnswer.map(a => typeof a === 'string' && !isNaN(parseInt(a)) ? parseInt(a) : a).sort();
            
            // 比较标准化后的数组
            isCorrect = normalizedUserAnswer.length === normalizedCorrectAnswer.length &&
                       JSON.stringify(normalizedUserAnswer) === JSON.stringify(normalizedCorrectAnswer);
                       
            console.log(`多选题比较: [${normalizedUserAnswer}] 和 [${normalizedCorrectAnswer}], 结果: ${isCorrect}`);
          } else {
            isCorrect = false;
            console.log(`多选题答案格式不正确, 用户: ${Array.isArray(userAnswer)}, 正确: ${Array.isArray(correctAnswer)}`);
          }
          break;
        case 'judgment':
          // 判断题：需要特殊处理可能的多种表示方法
          if (userAnswer.length === 1) {
            // 统一转换为布尔值进行比较
            const userIsTrue = userAnswer[0] === 0 || userAnswer[0] === '0' || 
                              userAnswer[0] === '正确' || userAnswer[0] === '对' || 
                              userAnswer[0] === true || userAnswer[0] === 'true';
            const correctIsTrue = correctAnswer === 0 || correctAnswer === '0' || 
                                  correctAnswer === '正确' || correctAnswer === '对' || 
                                  correctAnswer === true || correctAnswer === 'true';
            isCorrect = userIsTrue === correctIsTrue;
            console.log(`判断题比较: ${userIsTrue} === ${correctIsTrue}, 结果: ${isCorrect}`);
          } else {
            isCorrect = false;
          }
          break;
        case 'fillblank':
        case 'fill':
          // 填空题：支持多种模糊匹配
          if (userAnswer.length > 0) {
            const userInputStr = String(userAnswer[0] || "").trim().toLowerCase();
            const correctAnswerStr = String(correctAnswer || "").trim().toLowerCase();
            
            // 完全匹配或者正确答案是用户输入的子集
            isCorrect = userInputStr === correctAnswerStr || 
                      userInputStr.includes(correctAnswerStr) || 
                      correctAnswerStr.includes(userInputStr);
                      
            console.log(`填空题比较: "${userInputStr}" 和 "${correctAnswerStr}", 结果: ${isCorrect}`);
          } else {
            isCorrect = false;
          }
          break;
      }
      
      if (isCorrect) {
        correctCount++;
        actualScore += questionValue;
      }
      
      newAnswers[index].isCorrect = isCorrect;
    });
    
    // 计算百分比得分（满分100分）
    const percentageScore = Math.round((actualScore / totalPossibleScore) * 100);
    
    this.setData({
      score: correctCount,
      totalScore: questions.length,
      actualScore: actualScore.toFixed(1),
      totalPossibleScore: totalPossibleScore.toFixed(1),
      percentageScore: percentageScore,
      answers: newAnswers
    });
    
    console.log(`计算完成：正确题数 ${correctCount}，总题数 ${questions.length}，得分 ${actualScore}/${totalPossibleScore}，百分比 ${percentageScore}%`);
  },

  // 保存考试结果
  saveExamResult: function() {
    const { 
      questions, answers, courseId, courseTitle, 
      actualScore, totalPossibleScore, percentageScore, 
      examStartTime, examEndTime, examTimeUsed,
      answeredCount, unansweredCount 
    } = this.data;
    
    // 构建考试结果对象
    const examResult = {
      courseId,
      courseTitle,
      courseSubject: courseTitle,
      examDate: new Date().toISOString(),
      questions,
      answers,
      score: this.data.score,
      totalScore: this.data.totalScore,
      actualScore: actualScore,
      totalPossibleScore: totalPossibleScore,
      percentageScore: percentageScore,
      examStartTime: examStartTime,
      examEndTime: examEndTime,
      examTimeUsed: examTimeUsed,
      answeredCount: answeredCount || 0,
      unansweredCount: unansweredCount || 0
    };
    
    // 获取历史考试记录
    const examHistory = wx.getStorageSync('examHistory') || [];
    examHistory.push(examResult);
    
    // 保存到本地存储
    wx.setStorageSync('examHistory', examHistory);
    
    console.log('考试结果已保存，科目:', courseTitle);
  },

  // 查看考试结果
  viewResults: function() {
    // 跳转到结果页面
    wx.navigateTo({
      url: `/pages/result/result?examId=${new Date().getTime()}`
    });
  },

  // 返回上一页
  navigateBack: function() {
    // 清除定时器
    if (this.data.timer) {
      clearInterval(this.data.timer);
    }
    
    wx.navigateBack();
  },

  // 当页面卸载时，清除定时器
  onUnload: function() {
    if (this.data.timer) {
      clearInterval(this.data.timer);
    }
  },

  // 动态计算各题型的权重
  calculateTypeWeights: function(singleCount, multipleCount, judgmentCount, fillBlankCount) {
    // 默认比例
    const defaultWeights = {
      single: 0.35,
      multiple: 0.30,
      judgment: 0.25,
      fillBlank: 0.10
    };
    
    // 检查哪些题型可用
    const availableTypes = [];
    if (singleCount > 0) availableTypes.push('single');
    if (multipleCount > 0) availableTypes.push('multiple');
    if (judgmentCount > 0) availableTypes.push('judgment');
    if (fillBlankCount > 0) availableTypes.push('fillBlank');
    
    // 如果所有题型都可用，使用默认权重
    if (availableTypes.length === 4) {
      return defaultWeights;
    }
    
    // 计算缺失题型的总权重
    let missingWeight = 0;
    if (singleCount === 0) missingWeight += defaultWeights.single;
    if (multipleCount === 0) missingWeight += defaultWeights.multiple;
    if (judgmentCount === 0) missingWeight += defaultWeights.judgment;
    if (fillBlankCount === 0) missingWeight += defaultWeights.fillBlank;
    
    // 根据可用题型数量重新分配权重
    const weightPerType = missingWeight / availableTypes.length;
    
    // 创建调整后的权重对象
    const adjustedWeights = {
      single: singleCount > 0 ? defaultWeights.single + weightPerType : 0,
      multiple: multipleCount > 0 ? defaultWeights.multiple + weightPerType : 0,
      judgment: judgmentCount > 0 ? defaultWeights.judgment + weightPerType : 0,
      fillBlank: fillBlankCount > 0 ? defaultWeights.fillBlank + weightPerType : 0
    };
    
    console.log('调整后的题型权重:', adjustedWeights);
    
    return adjustedWeights;
  },

  // 切换多选题选项
  toggleMultiOption: function(e) {
      const optionIndex = parseInt(e.currentTarget.dataset.optionIndex);
    const currentIndex = this.data.currentIndex;
      
    console.log('多选题选择', currentIndex, optionIndex);
    
    // 深拷贝答案数组
      const newAnswers = JSON.parse(JSON.stringify(this.data.answers));
      
      // 确保selected属性是数组
    if (!Array.isArray(newAnswers[currentIndex].selected)) {
      newAnswers[currentIndex].selected = [];
      }
      
    // 查找选项在已选数组中的位置
    const selectedIndex = newAnswers[currentIndex].selected.indexOf(optionIndex);
      
    // 如果已选中则移除，否则添加
      if (selectedIndex !== -1) {
      newAnswers[currentIndex].selected.splice(selectedIndex, 1);
      } else {
      newAnswers[currentIndex].selected.push(optionIndex);
    }
    
    // 更新已答题状态
    newAnswers[currentIndex].isAnswered = newAnswers[currentIndex].selected.length > 0;
    
    // 打印调试信息
    console.log('更新后的选中状态', JSON.stringify(newAnswers[currentIndex].selected));
      
      this.setData({
      answers: newAnswers,
      forceUpdate: Date.now() // 强制刷新
      });
  },

  /**
   * 将索引转换为字母（A,B,C,D等）
   */
  indexToLetter(index) {
    return String.fromCharCode(65 + index); // 从A(65)开始
  },

  /**
   * 检查指定问题的选项是否被选中
   */
  isIndexInSelected(questionIndex, optionIndex) {
    const answers = this.data.answers;
    if (!answers || !answers[questionIndex] || !Array.isArray(answers[questionIndex].selected)) {
      return false;
    }
    
    const result = answers[questionIndex].selected.indexOf(optionIndex) !== -1;
    return result;
  },

  // 多选题选择函数 - 重写版本
  selectMultipleOption: function(e) {
    // 获取选中的选项索引
    const index = parseInt(e.currentTarget.dataset.index);
    const currentIndex = this.data.currentIndex;
    
    // 输出调试信息
    console.log('多选题：点击选项', index, '题目索引', currentIndex);
    wx.showToast({
      title: '点击选项' + index,
      icon: 'none',
      duration: 1000
    });
    
    // 创建当前答案的深拷贝
    let newAnswers = JSON.parse(JSON.stringify(this.data.answers));
    
    // 确保选中数组存在
    if (!newAnswers[currentIndex].selected) {
      newAnswers[currentIndex].selected = [];
    }
    
    // 查找选项是否已被选中
    let selectedIndex = newAnswers[currentIndex].selected.indexOf(index);
    console.log('当前选中状态', JSON.stringify(newAnswers[currentIndex].selected), '查找结果', selectedIndex);
    
    // 切换选中状态
    if (selectedIndex !== -1) {
      // 如果已选中，则移除
      newAnswers[currentIndex].selected.splice(selectedIndex, 1);
      console.log('移除选中状态', index);
    } else {
      // 如果未选中，则添加
      newAnswers[currentIndex].selected.push(index);
      console.log('添加选中状态', index);
    }
    
    // 更新答题状态
    newAnswers[currentIndex].isAnswered = newAnswers[currentIndex].selected.length > 0;
    
    // 输出最终结果
    console.log('更新后选中状态', JSON.stringify(newAnswers[currentIndex].selected));
    
    // 更新数据
    this.setData({
      answers: newAnswers
    });
    
    // 强制更新UI
    setTimeout(() => {
      this.setData({
        forceUpdateUI: new Date().getTime()
      });
    }, 50);
  },

  // 多选题选择最终版本
  multiSelect: function(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    const currentIndex = this.data.currentIndex;
    
    // 创建或获取临时状态数组
    let multiSelectedStatus = this.data.multiSelectedStatus || {};
    
    // 切换选中状态
    if (multiSelectedStatus[index]) {
      multiSelectedStatus[index] = false;
    } else {
      multiSelectedStatus[index] = true;
    }
    
    // 根据multiSelectedStatus更新answers数组
    const newAnswers = [...this.data.answers];
    newAnswers[currentIndex].selected = [];
    
    // 将所有选中的选项索引添加到答案中
    Object.keys(multiSelectedStatus).forEach(key => {
      if (multiSelectedStatus[key]) {
        newAnswers[currentIndex].selected.push(parseInt(key));
      }
    });
    
    // 更新答题状态
    newAnswers[currentIndex].isAnswered = newAnswers[currentIndex].selected.length > 0;
    
    // 更新数据
    this.setData({
      multiSelectedStatus: multiSelectedStatus,
      answers: newAnswers
    });
    
    console.log("多选题状态:", JSON.stringify(multiSelectedStatus));
    console.log("更新的答案:", JSON.stringify(newAnswers[currentIndex].selected));
  },

  // 根据科目名称生成颜色
  getSubjectColor: function(subject) {
    // 为不同科目设置不同颜色
    const colorMap = {
      '语文': '#f44336',
      '数学': '#2196f3',
      '英语': '#4caf50',
      '物理': '#9c27b0',
      '化学': '#ff9800',
      '生物': '#009688',
      '历史': '#795548',
      '地理': '#607d8b',
      '政治': '#e91e63'
    };
    
    return colorMap[subject] || '#2b85e4'; // 未找到匹配的科目则返回默认蓝色
  },

  // 查看错题
  viewWrongQuestions: function() {
    // 构建错题列表
    const wrongQuestions = [];
    const { questions, answers } = this.data;
    
    for (let i = 0; i < questions.length; i++) {
      const question = questions[i];
      const answer = answers[i];
      
      // 只有答案不正确的题目才加入错题列表
      if (!answer.isCorrect) {
        // 额外确认判断题和填空题正确性，防止误判
        let isReallyWrong = true;
        
        // 判断题二次检查
        if (question.type === 'judgment') {
          const userAnswer = answer.selected[0];
          const correctAnswer = question.answer;
          
          // 判断题有多种可能的表示方式
          const isUserTrue = userAnswer === 0 || userAnswer === '0' || userAnswer === '对' || userAnswer === true;
          const isCorrectTrue = correctAnswer === 0 || correctAnswer === '0' || correctAnswer === '对' || correctAnswer === true;
          
          // 如果用户选择和正确答案相符（都是"对"或都是"错"），则不加入错题
          if ((isUserTrue && isCorrectTrue) || (!isUserTrue && !isCorrectTrue)) {
            isReallyWrong = false;
            console.log(`判断题${i+1}答案正确，不加入错题`);
          }
        }
        
        // 填空题二次检查
        else if (question.type === 'fill' || question.type === 'fillblank') {
          if (answer.selected && answer.selected.length > 0) {
            const userInput = String(answer.selected[0] || "").trim().toLowerCase();
            const correctAnswer = String(question.answer || "").trim().toLowerCase();
            
            // 如果用户输入与正确答案完全匹配，则不加入错题
            if (userInput === correctAnswer) {
              isReallyWrong = false;
              console.log(`填空题${i+1}答案正确，不加入错题`);
            }
          }
        }
        
        // 如果确实是错题，才添加到列表
        if (isReallyWrong) {
          wrongQuestions.push({
            index: i + 1,
            question: question,
            userAnswer: answer.selected
          });
          console.log(`添加错题: 第${i+1}题，类型：${question.type}`);
        }
      }
    }
    
    console.log('错题列表:', wrongQuestions);
    
    // 如果没有错题，提示用户
    if (wrongQuestions.length === 0) {
      wx.showToast({
        title: '恭喜！没有错题',
        icon: 'success',
        duration: 2000
      });
      return;
    }
    
    // 将错题列表存入缓存
    wx.setStorageSync('wrongQuestions', wrongQuestions);
    
    // 跳转到错题页面
    wx.navigateTo({
      url: '../wrong/wrong'
    });
  },

  // 计算各题型数量和进度
  calculateTypeProgress: function() {
    const { questions, currentIndex } = this.data;
    
    // 计算各题型总数
    const typeCounts = {
      single: 0,
      multiple: 0,
      judgment: 0,
      fillblank: 0
    };
    
    // 计算当前已完成的各题型数量
    const currentTypeCounts = {
      single: 0,
      multiple: 0,
      judgment: 0,
      fillblank: 0
    };
    
    // 统计题型总数
    questions.forEach(question => {
      if (typeCounts.hasOwnProperty(question.type)) {
        typeCounts[question.type]++;
      }
    });
    
    // 统计当前进度
    for (let i = 0; i <= currentIndex; i++) {
      if (i < questions.length && currentTypeCounts.hasOwnProperty(questions[i].type)) {
        currentTypeCounts[questions[i].type]++;
      }
    }
    
    // 计算各题型所占百分比
    const totalQuestions = questions.length;
    const singlePercent = totalQuestions > 0 ? (typeCounts.single / totalQuestions * 100) : 0;
    const multiplePercent = totalQuestions > 0 ? (typeCounts.multiple / totalQuestions * 100) : 0;
    const judgmentPercent = totalQuestions > 0 ? (typeCounts.judgment / totalQuestions * 100) : 0;
    const fillblankPercent = totalQuestions > 0 ? (typeCounts.fillblank / totalQuestions * 100) : 0;
    
    // 计算各题型完成进度
    const singleProgress = typeCounts.single > 0 ? (currentTypeCounts.single / typeCounts.single * 100) : 0;
    const multipleProgress = typeCounts.multiple > 0 ? (currentTypeCounts.multiple / typeCounts.multiple * 100) : 0;
    const judgmentProgress = typeCounts.judgment > 0 ? (currentTypeCounts.judgment / typeCounts.judgment * 100) : 0;
    const fillblankProgress = typeCounts.fillblank > 0 ? (currentTypeCounts.fillblank / typeCounts.fillblank * 100) : 0;
    
    this.setData({
      typeCounts,
      currentTypeCounts,
      singlePercent,
      multiplePercent,
      judgmentPercent,
      fillblankPercent,
      singleProgress,
      multipleProgress,
      judgmentProgress,
      fillblankProgress
    });
  },

  // 随机抽取题目后更新进度条
  updateQuestionProgress: function() {
    // 计算各题型数量和进度
    this.calculateTypeProgress();
  },
}); 
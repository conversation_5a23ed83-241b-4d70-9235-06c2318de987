// pages/quiz/wrong/wrong.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    wrongQuestions: [],
    showTip: true, // 默认显示提示
    currentQuestionIndex: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 获取错题列表
    const wrongQuestions = wx.getStorageSync('wrongQuestions') || [];
    console.log('错题列表:', wrongQuestions);
    this.setData({
      wrongQuestions: this.processQuestions(wrongQuestions)
    });

    // 显示提示，15秒后自动隐藏
    setTimeout(() => {
      this.setData({
        showTip: false
      });
    }, 15000);

    // 初始化当前活跃指示器
    this.setData({
      currentQuestionIndex: 0
    });
  },

  /**
   * 隐藏提示
   */
  hideTip: function() {
    this.setData({
      showTip: false
    });
  },

  /**
   * 判断题选项检查 - 更智能地判断用户是否选择了正确或错误
   */
  checkJudgmentSelection: function(value, isTrue) {
    if (value === undefined || value === null) return false;
    
    // 处理数组形式的答案（如[0]表示选择了"对"）
    if (Array.isArray(value)) {
      if (value.length === 0) return false;
      
      if (isTrue) {
        return value[0] === 0 || value[0] === '0' || value[0] === '对' || value[0] === true;
      } else {
        return value[0] === 1 || value[0] === '1' || value[0] === '错' || value[0] === false;
      }
    }
    
    // 处理各种可能的单值形式
    if (isTrue) {
      return value === 0 || value === '0' || value === '对' || value === true;
    } else {
      return value === 1 || value === '1' || value === '错' || value === false;
    }
  },

  /**
   * 检查选项是否被选中
   */
  isOptionSelected: function(selected, index) {
    // 如果selected为undefined或null，直接返回false
    if (selected === undefined || selected === null) return false;
    
    // 直接数字或字符串匹配
    if (selected === index) return true;
    if (selected === String(index)) return true;
    
    // 数组匹配
    if (Array.isArray(selected) && (selected.indexOf(index) >= 0 || selected.indexOf(String(index)) >= 0)) return true;
    
    // 字母匹配 (A,B,C...)
    if (typeof selected === 'string') {
      // 单个字母
      if (selected.length === 1) {
        const letterIndex = selected.toUpperCase().charCodeAt(0) - 65; // 'A'的ASCII码是65
        if (letterIndex === index) return true;
      }
      
      // 逗号分隔的字母字符串 "A,B,C"
      if (selected.indexOf(',') >= 0) {
        const letters = selected.split(',');
        for (let letter of letters) {
          const letterIndex = letter.trim().toUpperCase().charCodeAt(0) - 65;
          if (letterIndex === index) return true;
        }
      }
    }
    
    // 特殊情况：如果index是字母，将其转换为数字索引
    if (typeof index === 'string' && index.length === 1) {
      const letterIndex = index.toUpperCase().charCodeAt(0) - 65;
      if (selected === letterIndex || selected === String(letterIndex)) return true;
    }
    
    return false;
  },

  /**
   * 将索引转为字母选项 (0->A, 1->B, 等)
   */
  indexToLetter: function(index) {
    if (typeof index === 'number' && index >= 0 && index <= 25) {
      return String.fromCharCode(65 + index); // 65是A的ASCII码
    }
    // 如果已经是字母，则返回大写形式
    if (typeof index === 'string' && index.length === 1) {
      return index.toUpperCase();
    }
    return String(index); // 返回字符串形式作为后备
  },

  /**
   * 返回上一页
   */
  navigateBack: function () {
    wx.navigateBack();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
  },

  /**
   * 滚动到指定问题
   */
  scrollToQuestion: function(e) {
    const index = e.currentTarget.dataset.index;
    
    // 更新当前问题索引
    this.setData({
      currentQuestionIndex: index
    });
    
    // 滚动到指定问题
    wx.createSelectorQuery()
      .select(`#question-${index + 1}`)
      .boundingClientRect(function(rect) {
        // 获取滚动区域并滚动
        wx.createSelectorQuery()
          .select('.wrong-list')
          .boundingClientRect(function(scrollRect) {
            wx.createSelectorQuery()
              .select('.wrong-list')
              .scrollOffset(function(res) {
                const scrollTop = res.scrollTop;
                const offset = rect.top - scrollRect.top + scrollTop;
                
                wx.createSelectorQuery()
                  .select('.wrong-list')
                  .node()
                  .exec((result) => {
                    const scrollView = result[0].node;
                    scrollView.scrollTo({
                      top: offset,
                      behavior: 'smooth'
                    });
                  });
              })
              .exec();
          })
          .exec();
      })
      .exec();
  },

  /**
   * 监听滚动事件更新导航指示器
   */
  onScroll: function(e) {
    const scrollTop = e.detail.scrollTop;
    
    // 获取所有问题的位置
    wx.createSelectorQuery()
      .selectAll('.question-card')
      .boundingClientRect((rects) => {
        if (!rects || rects.length === 0) return;
        
        wx.createSelectorQuery()
          .select('.wrong-list')
          .boundingClientRect((scrollRect) => {
            // 找到当前可见的问题
            for (let i = 0; i < rects.length; i++) {
              const rect = rects[i];
              if (rect.top - scrollRect.top >= -rect.height / 2) {
                if (this.data.currentQuestionIndex !== i) {
                  this.setData({
                    currentQuestionIndex: i
                  });
                }
                break;
              }
            }
          })
          .exec();
      })
      .exec();
  },

  processQuestions: function(questions) {
    // 处理数据，确保正确显示
    const processedQuestions = questions.map((item, questionIndex) => {
      // 确保有index属性（题目序号）
      if (!item.index) {
        item.index = questionIndex + 1;
      }
      
      // 调试输出每个问题的原始数据
      console.log(`问题${item.index}原始数据:`, {
        问题: item.question.content,
        类型: item.question.type,
        正确答案: item.question.answer,
        用户答案: item.userAnswer
      });
      
      // 确保答案正确处理
      if (item.question) {
        // 单选题处理
        if (item.question.type === 'single') {
          if (typeof item.question.answer === 'number') {
            item.question.answerLetter = this.indexToLetter(item.question.answer);
          } else if (typeof item.question.answer === 'string' && item.question.answer.length === 1) {
            // 如果答案已经是字母形式
            item.question.answerLetter = item.question.answer.toUpperCase();
          }
          console.log(`问题${item.index}单选题答案处理后:`, item.question.answerLetter);
        } 
        // 多选题处理
        else if (item.question.type === 'multiple' && Array.isArray(item.question.answer)) {
          item.question.answerLetters = item.question.answer
            .map(ans => {
              if (typeof ans === 'number') {
                return this.indexToLetter(ans);
              } else if (typeof ans === 'string' && ans.length === 1) {
                return ans.toUpperCase();
              }
              return ans;
            })
            .join('、');
          console.log(`问题${item.index}多选题答案处理后:`, item.question.answerLetters);
        }
        // 判断题处理
        else if (item.question.type === 'judgment') {
          // 确保判断题答案是"对"或"错"
          if (item.question.answer === 0 || item.question.answer === '0' || 
              item.question.answer === '对' || item.question.answer === true) {
            item.question.formattedAnswer = '对';
          } else {
            item.question.formattedAnswer = '错';
          }
          console.log(`问题${item.index}判断题答案处理后:`, item.question.formattedAnswer);
        }
        // 填空题处理
        else if (item.question.type === 'fill' || item.question.type === 'fillblank') {
          // 确保填空题答案是字符串形式
          item.question.formattedAnswer = String(item.question.answer || '');
          console.log(`问题${item.index}填空题答案处理后:`, item.question.formattedAnswer);
        }
      }
      
      // 如果没有userAnswer字段或为空，尝试从其他可能的字段获取用户答案
      if (!item.userAnswer && item.userAnswer !== 0) {
        if (item.selectedAnswer !== undefined) {
          item.userAnswer = item.selectedAnswer;
        } else if (item.selected !== undefined) {
          item.userAnswer = item.selected;
        } else if (item.answer !== undefined) {
          item.userAnswer = item.answer;
        }
      }
      
      console.log(`问题${item.index}用户答案:`, item.userAnswer);
      
      // 处理选项样式 - 仅高亮用户的选择
      if (item.question && (item.question.type === 'single' || item.question.type === 'multiple') && item.question.options) {
        item.optionStyles = [];
        item.optionCircleStyles = [];
        
        item.question.options.forEach((option, index) => {
          // 检查是否是用户选择
          const isUserSelected = this.isOptionSelected(item.userAnswer, index);
          // 检查是否是正确答案
          const isCorrectAnswer = this.isOptionSelected(item.question.answer, index);
          
          console.log(`问题${item.index}选项${['A','B','C','D','E','F'][index]}: 用户选择=${isUserSelected}, 正确答案=${isCorrectAnswer}`);
          
          // 计算样式类
          let styleClass = '';
          let circleClass = '';
          
          if (isUserSelected) {
            // 用户选择了此选项
            if (isCorrectAnswer) {
              // 用户选择正确
              styleClass = 'correct-selected';
              circleClass = 'correct-circle';
            } else {
              // 用户选择错误
              styleClass = 'wrong-selected';
              circleClass = 'wrong-circle';
            }
            console.log(`问题${item.index}选项${['A','B','C','D','E','F'][index]} 应用样式: ${styleClass}`);
          }
          
          item.optionStyles.push(styleClass);
          item.optionCircleStyles.push(circleClass);
        });
      }
      
      // 处理判断题样式 - 仅高亮用户的选择
      if (item.question && item.question.type === 'judgment') {
        item.judgmentStyles = ['', ''];
        item.judgmentCircleStyles = ['', ''];
        
        // 对"对"选项的处理
        const isTrueUserSelected = this.checkJudgmentSelection(item.userAnswer, true);
        const isTrueCorrect = this.checkJudgmentSelection(item.question.answer, true);
        
        console.log(`问题${item.index}"对"选项: 用户选择=${isTrueUserSelected}, 正确答案=${isTrueCorrect}`);
        
        if (isTrueUserSelected) {
          if (isTrueCorrect) {
            item.judgmentStyles[0] = 'correct-selected';
            item.judgmentCircleStyles[0] = 'correct-circle';
          } else {
            item.judgmentStyles[0] = 'wrong-selected';
            item.judgmentCircleStyles[0] = 'wrong-circle';
          }
          console.log(`问题${item.index}"对"选项应用样式: ${item.judgmentStyles[0]}`);
        }
        
        // 对"错"选项的处理
        const isFalseUserSelected = this.checkJudgmentSelection(item.userAnswer, false);
        const isFalseCorrect = this.checkJudgmentSelection(item.question.answer, false);
        
        console.log(`问题${item.index}"错"选项: 用户选择=${isFalseUserSelected}, 正确答案=${isFalseCorrect}`);
        
        if (isFalseUserSelected) {
          if (isFalseCorrect) {
            item.judgmentStyles[1] = 'correct-selected';
            item.judgmentCircleStyles[1] = 'correct-circle';
          } else {
            item.judgmentStyles[1] = 'wrong-selected';
            item.judgmentCircleStyles[1] = 'wrong-circle';
          }
          console.log(`问题${item.index}"错"选项应用样式: ${item.judgmentStyles[1]}`);
        }
      }
      
      // 处理填空题
      if (item.question && (item.question.type === 'fill' || item.question.type === 'fillblank')) {
        // 检查用户答案是否正确
        if (item.userAnswer && item.userAnswer.length > 0) {
          const userAnswerStr = String(item.userAnswer[0] || '').trim().toLowerCase();
          const correctAnswerStr = String(item.question.answer || '').trim().toLowerCase();
          
          // 设置正确或错误的样式
          item.fillblankStyle = userAnswerStr === correctAnswerStr ? 'correct-selected' : 'wrong-selected';
          
          console.log(`问题${item.index}填空题: 用户答案="${userAnswerStr}", 正确答案="${correctAnswerStr}", 应用样式=${item.fillblankStyle}`);
        }
      }
      
      return item;
    });
    
    return processedQuestions;
  }
}) 
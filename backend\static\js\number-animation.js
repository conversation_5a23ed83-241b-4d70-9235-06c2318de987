// Number Animation Effect
document.addEventListener('DOMContentLoaded', function() {
    // Function to animate a number from 0 to target
    function animateNumber(element, target, duration = 1500, prefix = '', suffix = '') {
        // Convert target to a number if it's not already
        const targetNum = parseFloat(target);
        if (isNaN(targetNum)) return;
        
        // Start from 1 (or 0 for percentages)
        let start = targetNum < 1 ? 0 : 1;
        let current = start;
        
        // Calculate increment based on target size
        const isDecimal = String(targetNum).includes('.');
        const increment = targetNum / (duration / 16);
        
        // Store original text color and font size
        const originalColor = window.getComputedStyle(element).color;
        const originalSize = window.getComputedStyle(element).fontSize;
        
        // Add subtle highlight effect
        element.style.transition = 'color 0.3s, transform 0.3s, font-size 0.3s';
        element.style.color = '#4e73df'; // Highlight color during animation
        
        // Create timestamp for animation start
        const startTime = performance.now();
        
        // Animation function
        function updateNumber(timestamp) {
            // Calculate progress
            const elapsed = timestamp - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Apply easing function for smooth animation
            const easedProgress = ease(progress);
            
            // Calculate current value
            current = start + (targetNum - start) * easedProgress;
            
            // Format the number appropriately (with or without decimals)
            let displayValue;
            if (isDecimal) {
                // Determine how many decimal places to show
                const numDecimals = String(targetNum).split('.')[1].length;
                displayValue = current.toFixed(numDecimals);
            } else {
                displayValue = Math.floor(current).toString();
            }
            
            // Update the element text with prefix and suffix
            element.textContent = prefix + displayValue + suffix;
            
            // Continue animation if not complete
            if (progress < 1) {
                requestAnimationFrame(updateNumber);
            } else {
                // Animation complete, apply pop effect and revert to original color
                element.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    element.style.transform = 'scale(1)';
                    element.style.color = originalColor;
                }, 100);
            }
        }
        
        // Easing function for smooth animation
        function ease(t) {
            // Cubic easing: makes the animation start slow, speed up in the middle, and slow down at the end
            return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
        }
        
        // Start the animation
        requestAnimationFrame(updateNumber);
    }
    
    // Add a small delay to start animations after page loads
    setTimeout(() => {
        // Animate all numbers in the welcome stats section
        const welcomeStatValues = document.querySelectorAll('.welcome-stat-value');
        welcomeStatValues.forEach((element, index) => {
            // Get the target number directly from the HTML
            const target = element.textContent.trim();
            
            // Check if it's a percentage
            const isPercentage = target.includes('%');
            const suffix = isPercentage ? '%' : '';
            
            // Add stagger effect with small delay between each number
            setTimeout(() => {
                // Start animation with appropriate settings
                animateNumber(element, parseFloat(target.replace('%', '')), 1500, '', suffix);
            }, index * 150); // Stagger by 150ms
        });
        
        // Animate all card values in the top stat cards with a sequential effect
        const cardValues = document.querySelectorAll('.card-value');
        cardValues.forEach((element, index) => {
            // Get the target number directly from the HTML
            const targetText = element.textContent.trim();
            
            // Check if it's a percentage
            const isPercentage = targetText.includes('%');
            const suffix = isPercentage ? '%' : '';
            
            // Sequential animation with different delay for each card
            setTimeout(() => {
                // Animate with appropriate settings
                animateNumber(element, parseFloat(targetText.replace('%', '')), 2000, '', suffix);
            }, 300 + index * 200); // Start after welcome stats with 200ms stagger
        });
    }, 300); // Initial delay for the whole animation sequence
}); 
// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 云函数入口函数
exports.main = async (event, context) => {
  try {
    const cityName = event.city || '乌鲁木齐'; // 默认乌鲁木齐
    
    // 使用和风天气API (免费版)
    // 注册地址: https://dev.qweather.com/
    const apiKey = 'YOUR_HEWEATHER_KEY'; // 请替换为您的和风天气API密钥
    
    try {
      const got = require('got');
      
      // 1. 先通过城市名称查询城市ID
      const geoUrl = `https://geoapi.qweather.com/v2/city/lookup?location=${encodeURIComponent(cityName)}&key=${apiKey}&number=1`;
      
      const geoResponse = await got(geoUrl, { responseType: 'json' });
      
      if (!geoResponse.body || geoResponse.body.code !== '200' || !geoResponse.body.location || geoResponse.body.location.length === 0) {
        console.log('城市查询失败:', geoResponse.body);
        // 找不到城市，返回错误
        return {
          success: false,
          message: '无法找到该城市的地理信息'
        };
      }
      
      const locationInfo = geoResponse.body.location[0];
      const locationId = locationInfo.id;
      
      // 2. 获取实时天气
      const weatherUrl = `https://devapi.qweather.com/v7/weather/now?location=${locationId}&key=${apiKey}`;
      const weatherResponse = await got(weatherUrl, { responseType: 'json' });
      
      // 3. 获取天气预报
      const forecastUrl = `https://devapi.qweather.com/v7/weather/3d?location=${locationId}&key=${apiKey}`;
      const forecastResponse = await got(forecastUrl, { responseType: 'json' });
      
      if (weatherResponse.body.code === '200' && forecastResponse.body.code === '200') {
        const weatherData = weatherResponse.body;
        const forecastData = forecastResponse.body;
        
        // 将API返回的数据转换为我们需要的格式
        const now = new Date();
        const formattedWeatherData = {
          city: locationInfo.name,
          current: {
            temp: parseInt(weatherData.now.temp),
            weather: weatherData.now.text,
            wind_dir: weatherData.now.windDir,
            wind_speed: parseInt(weatherData.now.windScale),
            humidity: parseInt(weatherData.now.humidity)
          },
          forecast: [
            // 今天
            {
              date: formatDate(forecastData.daily[0].fxDate),
              high: parseInt(forecastData.daily[0].tempMax),
              low: parseInt(forecastData.daily[0].tempMin),
              weather: forecastData.daily[0].textDay
            },
            // 明天
            {
              date: formatDate(forecastData.daily[1].fxDate),
              high: parseInt(forecastData.daily[1].tempMax),
              low: parseInt(forecastData.daily[1].tempMin),
              weather: forecastData.daily[1].textDay
            }
          ],
          source: '和风天气',
          updated_at: `${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}`
        };
        
        return {
          success: true,
          data: formattedWeatherData
        };
      } else {
        console.log('天气API错误:', weatherResponse.body, forecastResponse.body);
        // API调用成功但返回错误
        return {
          success: false,
          message: '获取天气数据失败，API返回错误代码'
        };
      }
    } catch (apiError) {
      console.error('API调用错误:', apiError);
      
      // API调用失败，返回错误信息
      return {
        success: false,
        message: '获取天气数据失败: ' + apiError.message
      };
    }
  } catch (error) {
    // 函数执行过程中的任何错误
    console.error('云函数执行错误:', error);
    return {
      success: false,
      message: '云函数执行错误: ' + error.message
    };
  }
}

// 辅助函数：格式化日期为 YYYY/MM/DD
function formatDate(dateString) {
  const parts = dateString.split('-');
  if (parts.length === 3) {
    return `${parts[0]}/${parts[1]}/${parts[2]}`;
  }
  // 如果格式不符合预期，返回原始字符串
  return dateString;
} 
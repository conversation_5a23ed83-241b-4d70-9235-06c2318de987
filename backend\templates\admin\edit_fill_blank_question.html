{% extends "admin/base.html" %}

{% block title %}编辑填空题 - 大数据题库管理后台{% endblock %}

{% block header %}编辑填空题{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <form method="post" action="/admin/questions/edit-fill-blank/{{ question.id }}">
                    <div class="mb-3">
                        <label for="question" class="form-label">题目文本</label>
                        <textarea class="form-control" id="question" name="question" rows="3" required>{{ question.question }}</textarea>
                        <small class="form-text text-muted">填空位置可以使用下划线或空格表示，例如："计算机网络分为____层结构。"</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="answer" class="form-label">答案</label>
                        <input type="text" class="form-control" id="answer" name="answer" value="{{ answer_text }}" required>
                        <small class="form-text text-muted">多个可能的答案请用逗号分隔，例如："七,7,七层"</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="category" class="form-label">分类</label>
                        <input type="text" class="form-control" id="category" name="category" value="{{ question.category }}">
                    </div>
                    
                    <div class="mb-3">
                        <label for="difficulty" class="form-label">难度</label>
                        <select class="form-select" id="difficulty" name="difficulty">
                            <option value="1" {% if question.difficulty == 1 %}selected{% endif %}>简单</option>
                            <option value="2" {% if question.difficulty == 2 %}selected{% endif %}>中等</option>
                            <option value="3" {% if question.difficulty == 3 %}selected{% endif %}>困难</option>
                        </select>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="classSelect" class="form-label">班级</label>
                            <select class="form-select" id="classSelect">
                                <option value="">选择班级</option>
                                {% for class_item in classes %}
                                <option value="{{ class_item.id }}" {% if selected_class_id == class_item.id %}selected{% endif %}>{{ class_item.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="courseSelect" class="form-label">课程</label>
                            <select class="form-select" id="courseSelect" name="course_id" required>
                                <option value="">选择课程</option>
                                {% for course in courses %}
                                <option value="{{ course.id }}" {% if selected_course_id == course.id %}selected{% endif %}>{{ course.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    
                    <div class="text-end">
                        <a href="/admin/questions?tab=fill_blank" class="btn btn-secondary">取消</a>
                        <button type="submit" class="btn btn-primary">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 班级选择变更时获取对应课程
    document.getElementById('classSelect').addEventListener('change', function() {
        const classId = this.value;
        const courseSelect = document.getElementById('courseSelect');
        const selectedCourseId = {{ selected_course_id|default('null') }};
        
        // 清空课程选择框
        courseSelect.innerHTML = '<option value="">选择课程</option>';
        
        if (classId) {
            // 获取该班级下的课程
            fetch(`/admin/courses/search?class_id=${classId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.courses && data.courses.length > 0) {
                        data.courses.forEach(course => {
                            const option = document.createElement('option');
                            option.value = course.id;
                            option.textContent = course.name;
                            if (course.id === selectedCourseId) {
                                option.selected = true;
                            }
                            courseSelect.appendChild(option);
                        });
                    }
                })
                .catch(error => console.error('获取课程失败:', error));
        }
    });
    
    // 页面加载时如果已选择班级，则加载对应课程
    document.addEventListener('DOMContentLoaded', function() {
        const classSelect = document.getElementById('classSelect');
        if (classSelect.value) {
            classSelect.dispatchEvent(new Event('change'));
        }
    });
</script>
{% endblock %} 
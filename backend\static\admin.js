/**
 * 大数据题库管理系统前端交互脚本
 */

// 在DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 初始化所有组件
    initComponents();
    
    // 移除加载覆盖层
    removeLoadingOverlay();
});

/**
 * 初始化所有组件
 */
function initComponents() {
    initStatsCardAnimations();
    initTableRowEffects();
    initMobileSidebar();
    initDropdowns();
    initCharts();
    initTooltips();
    initDatePickers();
    initFormValidation();
}

/**
 * 移除加载覆盖层
 */
function removeLoadingOverlay() {
    const loadingOverlay = document.querySelector('.loading-overlay');
    if (loadingOverlay) {
        setTimeout(() => {
            loadingOverlay.style.opacity = '0';
            setTimeout(() => {
                loadingOverlay.style.display = 'none';
            }, 500);
        }, 500);
    }
}

/**
 * 平滑过渡函数 - easeOutQuart
 */
function easeOutQuart(x) {
    return 1 - Math.pow(1 - x, 4);
}

/**
 * 初始化统计卡片动画效果
 */
function initStatsCardAnimations() {
    const statsCards = document.querySelectorAll('.stats-card');
    
    statsCards.forEach(card => {
        const numberElement = card.querySelector('.number .counter-number');
        
        if (numberElement) {
            // 获取最终值
            const finalValueText = numberElement.textContent.trim();
            let finalValue;
            
            // 检查是否为百分比值
            const isPercentage = finalValueText.includes('%');
            
            // 解析最终值
            if (isPercentage) {
                finalValue = parseFloat(finalValueText.replace('%', ''));
            } else {
                finalValue = parseInt(finalValueText, 10);
            }
            
            // 确保有有效的值
            if (isNaN(finalValue)) {
                console.error('Invalid number for animation:', finalValueText);
                return;
            }
            
            // 存储初始文本
            const originalText = numberElement.textContent;
            
            // 设置为0 (动画起点)
            if (isPercentage) {
                numberElement.textContent = '0.0%';
            } else {
                numberElement.textContent = '0';
            }
            
            // 如果数字无效或太小，直接显示原始值而不是动画
            if (isNaN(finalValue) || finalValue < 1) {
                numberElement.textContent = originalText;
                return;
            }
            
            // 动画到最终值
            animateValue(numberElement, 0, finalValue, 1500);
            
            // 添加悬停效果
            card.addEventListener('mouseenter', function() {
                numberElement.style.transform = 'scale(1.1)';
                numberElement.style.transition = 'transform 0.3s ease';
            });
            
            card.addEventListener('mouseleave', function() {
                numberElement.style.transform = 'scale(1)';
            });
        }
    });
}

/**
 * 数字增长动画效果
 */
function animateValue(element, start, end, duration) {
    let startTimestamp = null;
    const isPercentage = element.textContent.includes('%') || end.toString().includes('.');
    const step = (timestamp) => {
        if (!startTimestamp) startTimestamp = timestamp;
        const progress = Math.min((timestamp - startTimestamp) / duration, 1);
        const easedProgress = easeOutQuart(progress);
        
        let value;
        if (isPercentage) {
            value = easedProgress * (end - start) + start;
            element.textContent = value.toFixed(1) + '%';
        } else {
            value = Math.floor(easedProgress * (end - start) + start);
            element.textContent = value.toString();
        }
        
        if (progress < 1) {
            window.requestAnimationFrame(step);
        } else {
            // 动画结束，确保显示准确的最终值
            if (isPercentage) {
                element.textContent = parseFloat(end).toFixed(1) + '%';
            } else {
                element.textContent = Math.floor(end).toString();
            }
        }
    };
    
    window.requestAnimationFrame(step);
}

/**
 * 表格交互效果
 */
function initTableRowEffects() {
    const tableRows = document.querySelectorAll('table tbody tr');
    
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.classList.add('highlight');
        });
        
        row.addEventListener('mouseleave', function() {
            this.classList.remove('highlight');
        });
        
        // If row is clickable
        if (row.classList.contains('clickable')) {
            row.addEventListener('click', function(e) {
                // Don't trigger if click was on a button, link, or input
                if (e.target.tagName.toLowerCase() !== 'button' && 
                    e.target.tagName.toLowerCase() !== 'a' && 
                    e.target.tagName.toLowerCase() !== 'input') {
                    const targetUrl = this.getAttribute('data-href');
                    if (targetUrl) {
                        window.location.href = targetUrl;
                    }
                }
            });
        }
    });
}

/**
 * 移动端侧边栏初始化
 */
function initMobileSidebar() {
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    const sidebar = document.querySelector('.sidebar');
    const mainContent = document.querySelector('.main-content');
    
    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('show');
            // Add overlay to the main content
            if (sidebar.classList.contains('show')) {
                const overlay = document.createElement('div');
                overlay.className = 'sidebar-overlay';
                mainContent.appendChild(overlay);
                
                overlay.addEventListener('click', function() {
                    sidebar.classList.remove('show');
                    overlay.remove();
                });
            } else {
                const overlay = document.querySelector('.sidebar-overlay');
                if (overlay) overlay.remove();
            }
        });
    }
    
    // Close sidebar on window resize if in mobile view
    window.addEventListener('resize', function() {
        if (window.innerWidth > 992 && sidebar && sidebar.classList.contains('show')) {
            sidebar.classList.remove('show');
            const overlay = document.querySelector('.sidebar-overlay');
            if (overlay) overlay.remove();
        }
    });
}

/**
 * 初始化下拉菜单
 */
function initDropdowns() {
    const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
    
    dropdownToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const parent = this.parentElement;
            const dropdownMenu = parent.querySelector('.dropdown-menu');
            
            // Close other dropdowns
            document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                if (menu !== dropdownMenu) {
                    menu.classList.remove('show');
                }
            });
            
            dropdownMenu.classList.toggle('show');
        });
    });
    
    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.matches('.dropdown-toggle')) {
            document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                menu.classList.remove('show');
            });
        }
    });
}

/**
 * 初始化图表
 */
function initCharts() {
    if (typeof Chart !== 'undefined') {
        initUserActivityChart();
        initAccuracyChart();
        initCategoryDistributionChart();
    }
}

/**
 * 初始化用户活动图表
 */
function initUserActivityChart() {
    const userActivityCanvas = document.getElementById('userActivityChart');
    
    if (userActivityCanvas) {
        const ctx = userActivityCanvas.getContext('2d');
        
        // Get data from the canvas element data attributes or use defaults
        const labels = JSON.parse(userActivityCanvas.getAttribute('data-labels') || '["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]');
        const data = JSON.parse(userActivityCanvas.getAttribute('data-values') || '[12, 19, 8, 15, 12, 8, 16]');
        
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'User Activity',
                    data: data,
                    backgroundColor: 'rgba(67, 97, 238, 0.1)',
                    borderColor: 'rgba(67, 97, 238, 1)',
                    borderWidth: 2,
                    pointBackgroundColor: 'rgba(67, 97, 238, 1)',
                    pointBorderColor: 'rgba(255, 255, 255, 1)',
                    pointHoverBackgroundColor: 'rgba(255, 255, 255, 1)',
                    pointHoverBorderColor: 'rgba(67, 97, 238, 1)',
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        titleColor: '#333',
                        bodyColor: '#666',
                        borderColor: 'rgba(67, 97, 238, 0.3)',
                        borderWidth: 1,
                        padding: 10,
                        cornerRadius: 8,
                        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                        titleFont: {
                            weight: 'bold'
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            borderDash: [2, 4],
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    }
                }
            }
        });
    }
}

/**
 * 初始化准确率图表
 */
function initAccuracyChart() {
    const accuracyCanvas = document.getElementById('accuracyChart');
    
    if (accuracyCanvas) {
        const ctx = accuracyCanvas.getContext('2d');
        
        // Get data from the canvas element data attributes or use defaults
        const value = parseFloat(accuracyCanvas.getAttribute('data-value') || '65');
        
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Correct', 'Incorrect'],
                datasets: [{
                    data: [value, 100 - value],
                    backgroundColor: [
                        'rgba(76, 201, 240, 0.9)',
                        'rgba(222, 226, 230, 0.5)'
                    ],
                    borderColor: [
                        'rgba(76, 201, 240, 1)',
                        'rgba(222, 226, 230, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '75%',
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        titleColor: '#333',
                        bodyColor: '#666',
                        borderColor: 'rgba(76, 201, 240, 0.3)',
                        borderWidth: 1,
                        padding: 10,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ' + context.raw + '%';
                            }
                        }
                    }
                }
            }
        });
        
        // Add center text
        if (ctx) {
            Chart.register({
                id: 'centerTextPlugin',
                beforeDraw: function(chart) {
                    const width = chart.width;
                    const height = chart.height;
                    const ctx = chart.ctx;
                    
                    ctx.restore();
                    const fontSize = (height / 114).toFixed(2);
                    ctx.font = fontSize + 'em sans-serif';
                    ctx.textBaseline = 'middle';
                    
                    const text = value + '%';
                    const textX = Math.round((width - ctx.measureText(text).width) / 2);
                    const textY = height / 2;
                    
                    ctx.fillStyle = '#333';
                    ctx.fillText(text, textX, textY);
                    ctx.save();
                }
            });
        }
    }
}

/**
 * 初始化类别分布图表
 */
function initCategoryDistributionChart() {
    const categoryCanvas = document.getElementById('categoryDistributionChart');
    
    if (categoryCanvas) {
        const ctx = categoryCanvas.getContext('2d');
        
        // Get data from the canvas element data attributes or use defaults
        const labels = JSON.parse(categoryCanvas.getAttribute('data-labels') || '["Science", "Math", "History", "Literature", "Art"]');
        const data = JSON.parse(categoryCanvas.getAttribute('data-values') || '[25, 20, 18, 15, 12]');
        
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Questions per Category',
                    data: data,
                    backgroundColor: [
                        'rgba(67, 97, 238, 0.7)',
                        'rgba(76, 201, 240, 0.7)',
                        'rgba(72, 149, 239, 0.7)', 
                        'rgba(247, 37, 133, 0.7)',
                        'rgba(114, 9, 183, 0.7)'
                    ],
                    borderColor: [
                        'rgba(67, 97, 238, 1)',
                        'rgba(76, 201, 240, 1)',
                        'rgba(72, 149, 239, 1)',
                        'rgba(247, 37, 133, 1)',
                        'rgba(114, 9, 183, 1)'
                    ],
                    borderWidth: 1,
                    borderRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        titleColor: '#333',
                        bodyColor: '#666',
                        borderColor: 'rgba(0, 0, 0, 0.1)',
                        borderWidth: 1,
                        padding: 10,
                        cornerRadius: 8
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            borderDash: [2, 4],
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    }
                }
            }
        });
    }
}

/**
 * 初始化工具提示
 */
function initTooltips() {
    const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    if (typeof bootstrap !== 'undefined') {
        tooltipTriggerList.forEach(tooltipTriggerEl => {
            new bootstrap.Tooltip(tooltipTriggerEl, {
                boundary: document.body
            });
        });
    }
}

/**
 * 初始化日期选择器
 */
function initDatePickers() {
    const datePickers = document.querySelectorAll('.date-picker');
    
    if (typeof flatpickr !== 'undefined') {
        datePickers.forEach(input => {
            flatpickr(input, {
                dateFormat: "Y-m-d",
                allowInput: true,
                disableMobile: true
            });
        });
    }
}

/**
 * 初始化表单验证
 */
function initFormValidation() {
    const forms = document.querySelectorAll('.needs-validation');
    
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        }, false);
        
        // Custom validation messages
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('invalid', function() {
                if (!this.validity.valid) {
                    this.classList.add('is-invalid');
                }
            });
            
            input.addEventListener('input', function() {
                if (this.validity.valid) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                }
            });
        });
    });
}

/**
 * 显示通知消息
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型（success, warning, danger等）
 * @param {number} duration - 显示时间（毫秒）
 */
function showNotification(message, type = 'success', duration = 5000) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    
    // Create content
    const icon = document.createElement('div');
    icon.className = 'notification-icon';
    
    // Set icon based on type
    let iconClass = '';
    switch(type) {
        case 'success':
            iconClass = 'fa-check-circle';
            break;
        case 'warning':
            iconClass = 'fa-exclamation-triangle';
            break;
        case 'error':
            iconClass = 'fa-times-circle';
            break;
        case 'info':
            iconClass = 'fa-info-circle';
            break;
        default:
            iconClass = 'fa-bell';
    }
    
    icon.innerHTML = `<i class="fas ${iconClass}"></i>`;
    
    const content = document.createElement('div');
    content.className = 'notification-content';
    content.textContent = message;
    
    const closeBtn = document.createElement('button');
    closeBtn.className = 'notification-close';
    closeBtn.innerHTML = '&times;';
    
    // Append elements
    notification.appendChild(icon);
    notification.appendChild(content);
    notification.appendChild(closeBtn);
    
    // Add to DOM
    document.body.appendChild(notification);
    
    // Show with animation
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
    
    // Set auto close
    const timeout = setTimeout(() => {
        closeNotification(notification);
    }, duration);
    
    // Close button event
    closeBtn.addEventListener('click', () => {
        clearTimeout(timeout);
        closeNotification(notification);
    });
    
    // Return notification element
    return notification;
}

/**
 * 关闭通知
 * @param {Element} notification - 通知元素
 */
function closeNotification(notification) {
    notification.classList.remove('show');
    setTimeout(() => {
        notification.remove();
    }, 300);
} 
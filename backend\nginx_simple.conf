server {
    listen 80;
    server_name www.qiangs.xyz qiangs.xyz;
    
    access_log /www/wwwlogs/www.qiangs.xyz.access.log;
    error_log /www/wwwlogs/www.qiangs.xyz.error.log;
    
    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_read_timeout 60s;
        proxy_send_timeout 60s;
    }
    
    # 静态文件处理
    location /static {
        alias /www/wwwroot/www.qiangs.xyz/static;
        expires 30d;
    }
    
    # 避免favicon.ico的404错误
    location = /favicon.ico {
        log_not_found off;
        access_log off;
    }
    
    # robots.txt处理
    location = /robots.txt {
        log_not_found off;
        access_log off;
    }
}

# HTTPS配置 (宝塔面板会自动管理这部分，可以通过面板设置SSL) 
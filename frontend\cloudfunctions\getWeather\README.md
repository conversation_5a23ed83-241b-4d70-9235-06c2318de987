# 天气查询云函数

这个云函数用于获取实时天气数据，通过和风天气API获取全国主要城市的天气情况。

## 功能

- 根据城市名称获取实时天气数据
- 支持查询当前天气和未来3天预报
- 提供格式化的天气信息，包括温度、天气状况、风向风力等

## 部署步骤

1. 确保已经安装了微信开发者工具
2. 在微信开发者工具中打开项目
3. 右键点击 `cloudfunctions/getWeather` 目录
4. 选择 "上传并部署"
5. 填写云函数相关配置

## 配置 和风天气 API 密钥

1. 注册和风天气开发者账号：https://dev.qweather.com/
2. 创建应用并获取API密钥
3. 在 `index.js` 文件中，找到以下代码：

```javascript
const apiKey = 'YOUR_HEWEATHER_KEY';
```

将 `YOUR_HEWEATHER_KEY` 替换为您在和风天气平台获取的API密钥。

## 免费版说明

和风天气提供免费的开发者套餐，每天3000次调用额度。对于大多数小型应用已经足够使用。如需更多调用次数或商用，请查看和风天气的付费方案。

## 使用方法

在小程序中调用云函数：

```javascript
wx.cloud.callFunction({
  name: 'getWeather',
  data: {
    city: '城市名称' // 例如：'北京'、'上海'、'乌鲁木齐'等
  },
  success: function(res) {
    console.log('天气数据:', res.result);
    // 处理返回的天气数据
  },
  fail: function(err) {
    console.error('获取天气数据失败:', err);
  }
});
```

## 返回数据格式

成功时返回的数据格式：

```json
{
  "success": true,
  "data": {
    "city": "乌鲁木齐",
    "current": {
      "temp": 17,
      "weather": "晴",
      "wind_dir": "东南风",
      "wind_speed": 3,
      "humidity": 52
    },
    "forecast": [
      {
        "date": "2023/05/15",
        "high": 23,
        "low": 14,
        "weather": "晴"
      },
      {
        "date": "2023/05/16",
        "high": 22,
        "low": 15,
        "weather": "多云"
      }
    ],
    "source": "和风天气",
    "updated_at": "14:30"
  }
}
```

## 注意事项

- 和风天气API免费版有调用次数限制，默认为3000次/天
- API密钥请妥善保管，不要泄露
- 确保云函数有足够的调用次数配额
- 天气数据更新可能有延迟，请在界面上标注数据更新时间和数据来源
- 如需使用商业版本，请联系和风天气获取商业授权 